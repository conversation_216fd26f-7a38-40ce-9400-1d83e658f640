
import { useState } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'

interface UraTaxFiling {
  id: string
  org_id: string
  filing_type: string
  period: string
  filing_date: string
  due_date: string
  amount: number
  total_paid: number
  penalty: number | null
  interest: number | null
  status: string
  ura_ref_no: string | null
  payment_ref_no: string | null
  filed_at: string | null
  filed_by: string | null
  created_at: string
  updated_at: string | null
}

interface UraTaxFilingFormProps {
  uraTaxFiling?: UraTaxFiling | null
  onClose: () => void
  onSave: () => void
}

interface UraTaxFilingFormData {
  filing_type: string
  period: string
  filing_date: string
  due_date: string
  amount: string
  total_paid: string
  penalty: string
  interest: string
  status: string
  ura_ref_no: string
  payment_ref_no: string
}

export function UraTaxFilingForm({ uraTaxFiling, onClose, onSave }: UraTaxFilingFormProps) {
  const { profile } = useAuth()
  const [formData, setFormData] = useState<UraTaxFilingFormData>({
    filing_type: uraTaxFiling?.filing_type || '',
    period: uraTaxFiling?.period || '',
    filing_date: uraTaxFiling?.filing_date || new Date().toISOString().split('T')[0],
    due_date: uraTaxFiling?.due_date || '',
    amount: uraTaxFiling?.amount?.toString() || '',
    total_paid: uraTaxFiling?.total_paid?.toString() || '',
    penalty: uraTaxFiling?.penalty?.toString() || '0',
    interest: uraTaxFiling?.interest?.toString() || '0',
    status: uraTaxFiling?.status || 'draft',
    ura_ref_no: uraTaxFiling?.ura_ref_no || '',
    payment_ref_no: uraTaxFiling?.payment_ref_no || ''
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    setIsLoading(true)
    try {
      const uraTaxFilingData = {
        filing_type: formData.filing_type,
        period: formData.period,
        filing_date: formData.filing_date,
        due_date: formData.due_date,
        amount: Number(formData.amount),
        total_paid: Number(formData.total_paid),
        penalty: Number(formData.penalty),
        interest: Number(formData.interest),
        status: formData.status,
        ura_ref_no: formData.ura_ref_no || null,
        payment_ref_no: formData.payment_ref_no || null,
        org_id: profile.org_id,
        filed_at: formData.status === 'filed' ? new Date().toISOString() : null,
        filed_by: formData.status === 'filed' ? profile.id : null
      }

      if (uraTaxFiling) {
        const { error } = await supabase
          .from('ura_tax_filings')
          .update(uraTaxFilingData)
          .eq('id', uraTaxFiling.id)

        if (error) throw error
        toast.success('URA tax filing updated successfully')
      } else {
        const { error } = await supabase
          .from('ura_tax_filings')
          .insert([uraTaxFilingData])

        if (error) throw error
        toast.success('URA tax filing created successfully')
      }

      onSave()
    } catch (error) {
      console.error('Error saving URA tax filing:', error)
      toast.error('Failed to save URA tax filing')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {uraTaxFiling ? 'Edit URA Tax Filing' : 'New URA Tax Filing'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="filing_type">Filing Type</Label>
              <Select
                value={formData.filing_type}
                onValueChange={(value) => setFormData({ ...formData, filing_type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select filing type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="VAT">VAT</SelectItem>
                  <SelectItem value="PAYE">PAYE</SelectItem>
                  <SelectItem value="WHT">Withholding Tax</SelectItem>
                  <SelectItem value="CIT">Corporate Income Tax</SelectItem>
                  <SelectItem value="PIT">Personal Income Tax</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="period">Period</Label>
              <Input
                id="period"
                value={formData.period}
                onChange={(e) => setFormData({ ...formData, period: e.target.value })}
                placeholder="e.g., 2024-01"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="filing_date">Filing Date</Label>
              <Input
                id="filing_date"
                type="date"
                value={formData.filing_date}
                onChange={(e) => setFormData({ ...formData, filing_date: e.target.value })}
                required
              />
            </div>

            <div>
              <Label htmlFor="due_date">Due Date</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => setFormData({ ...formData, due_date: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                required
              />
            </div>

            <div>
              <Label htmlFor="total_paid">Total Paid</Label>
              <Input
                id="total_paid"
                type="number"
                step="0.01"
                value={formData.total_paid}
                onChange={(e) => setFormData({ ...formData, total_paid: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="penalty">Penalty</Label>
              <Input
                id="penalty"
                type="number"
                step="0.01"
                value={formData.penalty}
                onChange={(e) => setFormData({ ...formData, penalty: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="interest">Interest</Label>
              <Input
                id="interest"
                type="number"
                step="0.01"
                value={formData.interest}
                onChange={(e) => setFormData({ ...formData, interest: e.target.value })}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData({ ...formData, status: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="filed">Filed</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="ura_ref_no">URA Reference Number</Label>
              <Input
                id="ura_ref_no"
                value={formData.ura_ref_no}
                onChange={(e) => setFormData({ ...formData, ura_ref_no: e.target.value })}
                placeholder="URA reference number"
              />
            </div>

            <div>
              <Label htmlFor="payment_ref_no">Payment Reference Number</Label>
              <Input
                id="payment_ref_no"
                value={formData.payment_ref_no}
                onChange={(e) => setFormData({ ...formData, payment_ref_no: e.target.value })}
                placeholder="Payment reference number"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : uraTaxFiling ? 'Update Filing' : 'Create Filing'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
