
import { useState, useEffect, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { Building2, Save, Calendar, Globe, DollarSign, Shield, Info, Edit, Check, X, CreditCard } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/hooks/useAuthHook'
import type { Organization, BankAccount } from '@/types/database'
import {
  validatePhoneNumber,
  validateTinNum<PERSON>,
  validateEmail,
  format<PERSON>hone<PERSON>umber,
  getPhoneNumberHelper,
  getTinNumberHelper
} from '@/lib/validators'
import { BankAccountForm } from '@/components/settings/BankAccountForm'
import { BankAccountList } from '@/components/settings/BankAccountList'
import { LoadingButton } from '@/components/ui/loading'

interface OrganizationFormData {
  name: string
  tin_number: string
  business_reg_number: string
  ura_tax_office: string
  currency_code: string
  timezone: string
  fiscal_year_start: string
  fiscal_year_end: string
  address?: string
  phone?: string
  email?: string
  website?: string
  description?: string
}

// Common currencies for Uganda and East Africa
const CURRENCIES = [
  { code: 'UGX', name: 'Ugandan Shilling', symbol: 'UGX' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh' },
  { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh' },
  { code: 'RWF', name: 'Rwandan Franc', symbol: 'RWF' },
]

// Common timezones for East Africa
const TIMEZONES = [
  { value: 'Africa/Kampala', label: 'Kampala (UTC+3)' },
  { value: 'Africa/Nairobi', label: 'Nairobi (UTC+3)' },
  { value: 'Africa/Dar_es_Salaam', label: 'Dar es Salaam (UTC+3)' },
  { value: 'Africa/Kigali', label: 'Kigali (UTC+2)' },
  { value: 'UTC', label: 'UTC (UTC+0)' },
]

// URA Tax Offices in Uganda
const URA_TAX_OFFICES = [
  'Kampala Tax Office',
  'Entebbe Tax Office',
  'Jinja Tax Office',
  'Mbale Tax Office',
  'Gulu Tax Office',
  'Mbarara Tax Office',
  'Fort Portal Tax Office',
  'Arua Tax Office',
  'Soroti Tax Office',
  'Masaka Tax Office',
]

export default function OrganizationSettings() {
  const [loading, setLoading] = useState(false)
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [isEditMode, setIsEditMode] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const { toast } = useToast()
  const { profile } = useAuth()

  // Bank account management state
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([])
  const [bankAccountFormOpen, setBankAccountFormOpen] = useState(false)
  const [editingBankAccount, setEditingBankAccount] = useState<BankAccount | null>(null)

  const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<OrganizationFormData>()

  const [validationErrors, setValidationErrors] = useState<{
    phone?: string
    email?: string
    tin_number?: string
  }>({})

  // Bank account management functions - moved to top to ensure consistent hook order
  const fetchBankAccounts = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      const { data, error } = await supabase
        .from('bank_accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setBankAccounts(data || [])
    } catch (error) {
      console.error('Error fetching bank accounts:', error)
    }
  }, [profile?.org_id])

  const fetchOrganization = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', profile.org_id)
        .single()

      if (error) throw error

      setOrganization(data)

      // Populate form with existing data
      setValue('name', data.name)
      setValue('tin_number', data.tin_number || '')
      setValue('business_reg_number', data.business_reg_number || '')
      setValue('ura_tax_office', data.ura_tax_office || '')
      setValue('currency_code', data.currency_code)
      setValue('timezone', data.timezone)
      setValue('fiscal_year_start', data.fiscal_year_start)
      setValue('fiscal_year_end', data.fiscal_year_end)
      // Set additional fields from the organization data
      setValue('address', data.address || '')
      setValue('phone', data.phone || '')
      setValue('email', data.email || '')
      setValue('website', data.website || '')
      setValue('description', data.description || '')

      // If organization exists and has data, show details view by default
      if (data.name && data.currency_code && data.timezone) {
        setIsEditMode(false)
      } else {
        // If incomplete data, show edit mode
        setIsEditMode(true)
      }
    } catch (error) {
      console.error('Error fetching organization:', error)
      toast({
        title: "Error",
        description: "Failed to load organization settings",
        variant: "destructive"
      })
      // Show edit mode if there's an error
      setIsEditMode(true)
    }
  }, [profile?.org_id, setValue, toast])

  useEffect(() => {
    if (profile?.org_id) {
      fetchOrganization()
    }
  }, [profile?.org_id, fetchOrganization])

  useEffect(() => {
    if (profile?.org_id) {
      fetchBankAccounts()
    }
  }, [profile?.org_id, fetchBankAccounts])

  const handleAddBankAccount = () => {
    setEditingBankAccount(null)
    setBankAccountFormOpen(true)
  }

  const handleEditBankAccount = (account: BankAccount) => {
    setEditingBankAccount(account)
    setBankAccountFormOpen(true)
  }

  const handleBankAccountSuccess = () => {
    fetchBankAccounts()
    setBankAccountFormOpen(false)
    setEditingBankAccount(null)
  }

  // Handle phone number input with auto-formatting
  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value)
    setValue('phone', formatted)

    // Clear validation error when user starts typing
    if (validationErrors.phone) {
      setValidationErrors({ ...validationErrors, phone: undefined })
    }
  }

  // Handle email input with validation
  const handleEmailChange = (value: string) => {
    setValue('email', value)

    // Clear validation error when user starts typing
    if (validationErrors.email) {
      setValidationErrors({ ...validationErrors, email: undefined })
    }
  }

  // Handle TIN input with validation
  const handleTinChange = (value: string) => {
    setValue('tin_number', value)

    // Clear validation error when user starts typing
    if (validationErrors.tin_number) {
      setValidationErrors({ ...validationErrors, tin_number: undefined })
    }
  }

  // Watch for form changes to detect unsaved changes
  useEffect(() => {
    const subscription = watch(() => {
      setHasUnsavedChanges(true)
    })
    return () => subscription.unsubscribe()
  }, [watch])

  const onSubmit = async (data: OrganizationFormData) => {
    if (!organization) return

    // Validate all fields
    const phoneValidation = validatePhoneNumber(data.phone || '', false) // Phone is optional for organizations
    const emailValidation = validateEmail(data.email || '')
    const tinValidation = validateTinNumber(data.tin_number || '')

    const errors: { phone?: string; email?: string; tin_number?: string } = {}

    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.message
    }

    if (!emailValidation.isValid) {
      errors.email = emailValidation.message
    }

    if (!tinValidation.isValid) {
      errors.tin_number = tinValidation.message
    }

    // If there are validation errors, show them and return
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors)
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors below and try again',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)
    try {
      // First, try updating with core fields only
      const coreUpdateData = {
        name: data.name,
        tin_number: data.tin_number || null,
        business_reg_number: data.business_reg_number || null,
        ura_tax_office: data.ura_tax_office || null,
        currency_code: data.currency_code,
        timezone: data.timezone,
        fiscal_year_start: data.fiscal_year_start,
        fiscal_year_end: data.fiscal_year_end,
        updated_at: new Date().toISOString(),
      }

      // Try to include extended fields if they exist in the database
      const extendedFields = {
        ...(data.address && { address: data.address }),
        ...(data.phone && { phone: 'formatted' in phoneValidation ? phoneValidation.formatted as string : data.phone }),
        ...(data.email && { email: data.email }),
        ...(data.website && { website: data.website }),
        ...(data.description && { description: data.description }),
        ...(data.tin_number && { tin_number: 'formatted' in tinValidation ? tinValidation.formatted as string : data.tin_number }),
      }

      // Combine core and extended fields
      const updateData = { ...coreUpdateData, ...extendedFields }

      // Debug logging
      console.log('Updating organization with data:', updateData)
      console.log('Organization ID:', organization.id)

      const { error } = await supabase
        .from('organizations')
        .update(updateData)
        .eq('id', organization.id)

      if (error) {
        // If error is due to missing columns, try with core fields only
        if (error.message?.includes('column') && error.message?.includes('does not exist')) {
          console.warn('Extended fields not available, updating core fields only:', error.message)

          const { error: coreError } = await supabase
            .from('organizations')
            .update(coreUpdateData)
            .eq('id', organization.id)

          if (coreError) throw coreError

          toast({
            title: "Partial Success",
            description: "Core organization settings updated. Some fields may require database migration.",
            variant: "default"
          })
        } else {
          throw error
        }
      } else {
        toast({
          title: "Success",
          description: "Organization settings updated successfully"
        })
      }

      // Switch to details view after successful save
      setIsEditMode(false)
      setHasUnsavedChanges(false)
      setValidationErrors({})

      // Refresh the organization data
      fetchOrganization()
    } catch (error) {
      console.error('Error updating organization:', error)

      // Provide more specific error messages
      let errorMessage = "Failed to update organization settings"

      if (error instanceof Error) {
        if (error.message?.includes('permission')) {
          errorMessage = "You don't have permission to update organization settings"
        } else if (error.message?.includes('constraint')) {
          errorMessage = "Invalid data format. Please check your inputs."
        } else if (error.message?.includes('network')) {
          errorMessage = "Network error. Please check your connection and try again."
        } else {
          errorMessage = `Update failed: ${error.message}`
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  if (!organization) {
    return <div className="p-6">Loading organization settings...</div>
  }

  const selectedCurrency = watch('currency_code')
  const currentCurrency = CURRENCIES.find(c => c.code === selectedCurrency)

  const handleEditClick = () => {
    setIsEditMode(true)
    setHasUnsavedChanges(false)
  }

  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        setIsEditMode(false)
        setHasUnsavedChanges(false)
        setValidationErrors({})
        // Reset form to original values
        fetchOrganization()
      }
    } else {
      setIsEditMode(false)
      setValidationErrors({})
    }
  }



  const formatDisplayValue = (value: string | null | undefined, fallback = 'Not specified') => {
    return value && value.trim() ? value : fallback
  }

  const getURAOfficeDisplay = (office: string | null | undefined) => {
    return office || 'Not specified'
  }

  const getCurrencyDisplay = (code: string) => {
    const currency = CURRENCIES.find(c => c.code === code)
    return currency ? `${currency.name} (${currency.symbol})` : code
  }

  const getTimezoneDisplay = (tz: string) => {
    const timezone = TIMEZONES.find(t => t.value === tz)
    return timezone ? timezone.label : tz
  }

  // Details View Component
  const renderDetailsView = () => {
    if (!organization) return null

    return (
      <div className="space-y-6">
        {/* Basic Information Details */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Basic Information
                </CardTitle>
                <CardDescription>
                  Organization details and contact information
                </CardDescription>
              </div>
              <Button onClick={handleEditClick} variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Organization Name</Label>
                <p className="text-lg font-semibold">{organization.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">TIN Number</Label>
                <p className="text-lg">{formatDisplayValue(organization.tin_number)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Business Registration Number</Label>
                <p className="text-lg">{formatDisplayValue(organization.business_reg_number)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">URA Tax Office</Label>
                <p className="text-lg">{getURAOfficeDisplay(organization.ura_tax_office)}</p>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Email Address</Label>
                <p className="text-lg">{formatDisplayValue(organization.email)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Phone Number</Label>
                <p className="text-lg">{formatDisplayValue(organization.phone)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Website</Label>
                <p className="text-lg">
                  {organization.website ? (
                    <a href={organization.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      {organization.website}
                    </a>
                  ) : (
                    'Not specified'
                  )}
                </p>
              </div>
            </div>

            {organization.address && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Business Address</Label>
                <p className="text-lg whitespace-pre-line">{organization.address}</p>
              </div>
            )}

            {organization.description && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Organization Description</Label>
                <p className="text-lg">{organization.description}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Fiscal Year Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Fiscal Year Configuration
            </CardTitle>
            <CardDescription>
              Financial year period for reporting and budgeting
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Fiscal Year Start</Label>
                <p className="text-lg font-semibold">
                  {new Date(organization.fiscal_year_start).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Fiscal Year End</Label>
                <p className="text-lg font-semibold">
                  {new Date(organization.fiscal_year_end).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-800">
                  Current fiscal year period: {Math.ceil((new Date(organization.fiscal_year_end).getTime() - new Date(organization.fiscal_year_start).getTime()) / (1000 * 60 * 60 * 24))} days
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Regional Settings Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Regional Settings
            </CardTitle>
            <CardDescription>
              Currency, timezone, and localization preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Primary Currency</Label>
                <div className="flex items-center gap-2 mt-1">
                  <DollarSign className="h-5 w-5 text-green-600" />
                  <p className="text-lg font-semibold">{getCurrencyDisplay(organization.currency_code)}</p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Timezone</Label>
                <p className="text-lg font-semibold">{getTimezoneDisplay(organization.timezone)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security & Compliance Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security & Compliance
            </CardTitle>
            <CardDescription>
              Organization security settings and compliance information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Data Retention</Label>
                <p className="text-sm text-muted-foreground">
                  Financial records are retained for 7 years as per URA requirements
                </p>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Audit Trail</Label>
                <p className="text-sm text-muted-foreground">
                  All transactions are automatically logged for audit purposes
                </p>
              </div>
            </div>
            <Separator />
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Check className="h-4 w-4 text-green-600 mt-0.5" />
                <div className="text-sm text-green-800">
                  <p className="font-medium">Compliance Status: Active</p>
                  <p className="mt-1">
                    Organization settings are complete and compliant with URA requirements.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bank Accounts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Bank Accounts
            </CardTitle>
            <CardDescription>
              Manage organization bank accounts for payment processing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BankAccountList
              accounts={bankAccounts}
              onEdit={handleEditBankAccount}
              onAdd={handleAddBankAccount}
              onRefresh={fetchBankAccounts}
            />
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Building2 className="h-8 w-8" />
              Organization Settings
            </h1>
            <p className="text-muted-foreground mt-2">
              {isEditMode
                ? "Update your organization's details, preferences, and configuration"
                : "View your organization's details, preferences, and configuration"
              }
            </p>
          </div>
          <div className="flex items-center gap-3">
            {organization && (
              <Badge variant="outline" className="text-sm">
                <Info className="h-4 w-4 mr-1" />
                Last updated: {new Date(organization.updated_at || organization.created_at).toLocaleDateString()}
              </Badge>
            )}
            {isEditMode && hasUnsavedChanges && (
              <Badge variant="secondary" className="text-sm">
                Unsaved changes
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Render either details view or edit form */}
      {!isEditMode ? renderDetailsView() : (

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Essential details about your organization
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="name">Organization Name *</Label>
              <Input
                id="name"
                {...register('name', { required: 'Organization name is required' })}
                className={errors.name ? 'border-red-500' : ''}
                placeholder="Enter your organization name"
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="tin_number">TIN Number</Label>
                <Input
                  id="tin_number"
                  value={watch('tin_number') || ''}
                  onChange={(e) => handleTinChange(e.target.value)}
                  placeholder="1000123456"
                  className={validationErrors.tin_number ? 'border-red-500' : ''}
                />
                {validationErrors.tin_number && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.tin_number}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  {getTinNumberHelper()}
                </p>
              </div>
              <div>
                <Label htmlFor="business_reg_number">Business Registration Number</Label>
                <Input
                  id="business_reg_number"
                  {...register('business_reg_number')}
                  placeholder="e.g., 80020001234567"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Company registration number from URSB
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="ura_tax_office">URA Tax Office</Label>
              <Select
                value={watch('ura_tax_office') || ''}
                onValueChange={(value) => setValue('ura_tax_office', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select your URA tax office" />
                </SelectTrigger>
                <SelectContent>
                  {URA_TAX_OFFICES.map((office) => (
                    <SelectItem key={office} value={office}>
                      {office}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground mt-1">
                The URA tax office where you file your returns
              </p>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={watch('email') || ''}
                  onChange={(e) => handleEmailChange(e.target.value)}
                  placeholder="<EMAIL>"
                  className={validationErrors.email ? 'border-red-500' : ''}
                />
                {validationErrors.email && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.email}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Optional. Organization's email address for official communication
                </p>
              </div>
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={watch('phone') || ''}
                  onChange={(e) => handlePhoneChange(e.target.value)}
                  placeholder="777123456"
                  className={validationErrors.phone ? 'border-red-500' : ''}
                />
                {validationErrors.phone && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.phone}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  {getPhoneNumberHelper()}
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                {...register('website')}
                placeholder="https://www.yourcompany.com"
              />
            </div>

            <div>
              <Label htmlFor="address">Business Address</Label>
              <Textarea
                id="address"
                {...register('address')}
                placeholder="Enter your business address"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="description">Organization Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Brief description of your organization's activities"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Fiscal Year Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Fiscal Year Configuration
            </CardTitle>
            <CardDescription>
              Define your organization's financial year period for reporting and budgeting
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="fiscal_year_start">Fiscal Year Start *</Label>
                <Input
                  id="fiscal_year_start"
                  type="date"
                  {...register('fiscal_year_start', { required: 'Fiscal year start is required' })}
                  className={errors.fiscal_year_start ? 'border-red-500' : ''}
                />
                {errors.fiscal_year_start && (
                  <p className="text-sm text-red-500 mt-1">{errors.fiscal_year_start.message}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  The beginning of your financial year
                </p>
              </div>
              <div>
                <Label htmlFor="fiscal_year_end">Fiscal Year End *</Label>
                <Input
                  id="fiscal_year_end"
                  type="date"
                  {...register('fiscal_year_end', { required: 'Fiscal year end is required' })}
                  className={errors.fiscal_year_end ? 'border-red-500' : ''}
                />
                {errors.fiscal_year_end && (
                  <p className="text-sm text-red-500 mt-1">{errors.fiscal_year_end.message}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  The end of your financial year
                </p>
              </div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium">Common Fiscal Years in Uganda:</p>
                  <ul className="mt-1 space-y-1 text-xs">
                    <li>• Calendar Year: January 1 - December 31</li>
                    <li>• Government FY: July 1 - June 30</li>
                    <li>• Custom periods based on business needs</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Regional Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Regional Settings
            </CardTitle>
            <CardDescription>
              Configure currency, timezone, and localization preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="currency_code">Primary Currency *</Label>
                <Select
                  value={watch('currency_code') || ''}
                  onValueChange={(value) => setValue('currency_code', value)}
                >
                  <SelectTrigger className={errors.currency_code ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {CURRENCIES.map((currency) => (
                      <SelectItem key={currency.code} value={currency.code}>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{currency.code}</span>
                          <span className="text-muted-foreground">({currency.symbol})</span>
                          <span className="text-sm">{currency.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.currency_code && (
                  <p className="text-sm text-red-500 mt-1">{errors.currency_code.message}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Primary currency for financial transactions and reporting
                </p>
              </div>
              <div>
                <Label htmlFor="timezone">Timezone *</Label>
                <Select
                  value={watch('timezone') || ''}
                  onValueChange={(value) => setValue('timezone', value)}
                >
                  <SelectTrigger className={errors.timezone ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    {TIMEZONES.map((tz) => (
                      <SelectItem key={tz.value} value={tz.value}>
                        {tz.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.timezone && (
                  <p className="text-sm text-red-500 mt-1">{errors.timezone.message}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Timezone for timestamps and scheduling
                </p>
              </div>
            </div>

            {selectedCurrency && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Selected Currency: {currentCurrency?.name} ({currentCurrency?.symbol || selectedCurrency})
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Security & Compliance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security & Compliance
            </CardTitle>
            <CardDescription>
              Organization security settings and compliance information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Data Retention</Label>
                <p className="text-sm text-muted-foreground">
                  Financial records are retained for 7 years as per URA requirements
                </p>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Audit Trail</Label>
                <p className="text-sm text-muted-foreground">
                  All transactions are automatically logged for audit purposes
                </p>
              </div>
            </div>
            <Separator />
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="h-4 w-4 text-amber-600 mt-0.5" />
                <div className="text-sm text-amber-800">
                  <p className="font-medium">Compliance Notice:</p>
                  <p className="mt-1">
                    Ensure all information is accurate and up-to-date for URA compliance.
                    Changes to fiscal year settings may affect existing reports and budgets.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-between items-center pt-6">
          <div className="text-sm text-muted-foreground">
            * Required fields
          </div>
          <div className="flex gap-3">
            <Button type="button" variant="outline" onClick={handleCancelEdit}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <LoadingButton
              type="submit"
              loading={loading}
              size="lg"
              loadingText="Saving Changes..."
            >
              <Save className="h-4 w-4 mr-2" />
              Save Organization Settings
            </LoadingButton>
          </div>
        </div>
      </form>
      )}

      {/* Bank Account Form Modal */}
      <BankAccountForm
        open={bankAccountFormOpen}
        onOpenChange={setBankAccountFormOpen}
        editingAccount={editingBankAccount}
        onSuccess={handleBankAccountSuccess}
      />
    </div>
  )
}
