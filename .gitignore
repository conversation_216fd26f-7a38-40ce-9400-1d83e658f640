# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Environment files (keep .env.example)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Production secrets (never commit these)
.env.production
.env.staging

# Build and deployment artifacts
build-report.txt
deployment-info.json
coverage/
.nyc_output/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Security and certificates
*.pem
*.key
*.crt
*.p12
*.pfx

# Temporary files
*.tmp
*.temp
.cache/
