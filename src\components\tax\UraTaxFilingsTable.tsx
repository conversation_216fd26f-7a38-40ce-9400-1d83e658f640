
import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash } from 'lucide-react'
import { toast } from 'sonner'

interface UraTaxFiling {
  id: string
  org_id: string
  filing_type: string
  period: string
  filing_date: string
  due_date: string
  amount: number
  total_paid: number
  penalty: number | null
  interest: number | null
  status: string
  ura_ref_no: string | null
  payment_ref_no: string | null
  filed_at: string | null
  filed_by: string | null
  created_at: string
  updated_at: string | null
}

interface UraTaxFilingsTableProps {
  uraTaxFilings: UraTaxFiling[]
  onEdit: (uraTaxFiling: UraTaxFiling) => void
  onRefresh: () => void
}

export function UraTaxFilingsTable({ uraTaxFilings, onEdit, onRefresh }: UraTaxFilingsTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (id: string) => {
    setDeletingId(id)
    try {
      const { error } = await supabase
        .from('ura_tax_filings')
        .delete()
        .eq('id', id)

      if (error) throw error
      
      toast.success('URA tax filing deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting URA tax filing:', error)
      toast.error('Failed to delete URA tax filing')
    } finally {
      setDeletingId(null)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'filed':
        return 'default'
      case 'draft':
        return 'secondary'
      case 'overdue':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  if (uraTaxFilings.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No URA tax filings found. Create your first tax filing to get started.
      </div>
    )
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Filing Type</TableHead>
          <TableHead>Period</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Due Date</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>URA Ref</TableHead>
          <TableHead className="w-[50px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {uraTaxFilings.map((filing) => (
          <TableRow key={filing.id}>
            <TableCell className="font-medium">{filing.filing_type}</TableCell>
            <TableCell>{filing.period}</TableCell>
            <TableCell>{formatCurrency(filing.amount)}</TableCell>
            <TableCell>{new Date(filing.due_date).toLocaleDateString()}</TableCell>
            <TableCell>
              <Badge variant={getStatusColor(filing.status)}>
                {filing.status}
              </Badge>
            </TableCell>
            <TableCell>{filing.ura_ref_no || '-'}</TableCell>
            <TableCell>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(filing)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDelete(filing.id)}
                    disabled={deletingId === filing.id}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
