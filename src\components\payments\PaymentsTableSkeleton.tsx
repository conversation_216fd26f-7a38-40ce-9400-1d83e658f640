import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export function PaymentsTableSkeleton() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment Records</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">Date</th>
                <th className="text-left p-2">Payee</th>
                <th className="text-left p-2">Amount</th>
                <th className="text-left p-2">Related Documents</th>
                <th className="text-left p-2">Channel</th>
                <th className="text-left p-2">Applied</th>
                <th className="text-left p-2">Balance</th>
                <th className="text-left p-2">Status</th>
                <th className="text-left p-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: 5 }).map((_, index) => (
                <tr key={index} className="border-b">
                  <td className="p-2">
                    <Skeleton className="h-4 w-20" />
                  </td>
                  <td className="p-2">
                    <Skeleton className="h-4 w-32" />
                  </td>
                  <td className="p-2">
                    <Skeleton className="h-4 w-24" />
                  </td>
                  <td className="p-2">
                    <div className="flex gap-1">
                      <Skeleton className="h-5 w-16" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                  </td>
                  <td className="p-2">
                    <Skeleton className="h-5 w-20" />
                  </td>
                  <td className="p-2">
                    <Skeleton className="h-4 w-20" />
                  </td>
                  <td className="p-2">
                    <Skeleton className="h-4 w-20" />
                  </td>
                  <td className="p-2">
                    <Skeleton className="h-5 w-16" />
                  </td>
                  <td className="p-2">
                    <div className="flex space-x-1">
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}

interface PaymentsLoadingStateProps {
  paymentsLoading: boolean
  customersLoading: boolean
  vendorsLoading: boolean
}

export function PaymentsLoadingState({ 
  paymentsLoading, 
  customersLoading, 
  vendorsLoading 
}: PaymentsLoadingStateProps) {
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Payments</h1>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Skeleton className="h-10 w-64" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Loading indicators for different data types */}
      <div className="flex gap-4 text-sm text-muted-foreground">
        <div className="flex items-center gap-2">
          {paymentsLoading ? (
            <>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span>Loading payments...</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span>Payments loaded</span>
            </>
          )}
        </div>
        <div className="flex items-center gap-2">
          {customersLoading ? (
            <>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span>Loading customers...</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span>Customers loaded</span>
            </>
          )}
        </div>
        <div className="flex items-center gap-2">
          {vendorsLoading ? (
            <>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span>Loading vendors...</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span>Vendors loaded</span>
            </>
          )}
        </div>
      </div>

      <PaymentsTableSkeleton />
    </div>
  )
}
