/**
 * Comprehensive Logging System
 * Structured logging with multiple levels and outputs
 */

import { config } from '@/lib/config'

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal'

// Define a more specific type for log context values
export type LogContextValue = string | number | boolean | null | undefined | Date | LogContextValue[] | { [key: string]: LogContextValue }

export interface LogEntry {
  timestamp: Date
  level: LogLevel
  message: string
  context?: Record<string, LogContextValue>
  userId?: string
  orgId?: string
  component?: string
  action?: string
  duration?: number
  metadata?: Record<string, LogContextValue>
}

export interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableStorage: boolean
  enableRemote: boolean
  maxStorageEntries: number
  remoteEndpoint?: string
}

export class Logger {
  private static instance: Logger
  private config: LoggerConfig
  private logEntries: LogEntry[] = []
  private logLevels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    fatal: 4
  }

  private constructor() {
    this.config = {
      level: this.getLogLevelFromEnv(),
      enableConsole: true,
      enableStorage: config.app.environment !== 'production',
      enableRemote: config.app.environment === 'production',
      maxStorageEntries: 1000,
      remoteEndpoint: import.meta.env.VITE_LOG_ENDPOINT
    }
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  private getLogLevelFromEnv(): LogLevel {
    const envLevel = import.meta.env.VITE_LOG_LEVEL?.toLowerCase()
    if (envLevel && envLevel in this.logLevels) {
      return envLevel as LogLevel
    }
    return config.app.environment === 'production' ? 'warn' : 'debug'
  }

  private shouldLog(level: LogLevel): boolean {
    return this.logLevels[level] >= this.logLevels[this.config.level]
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, LogContextValue>
  ): LogEntry {
    return {
      timestamp: new Date(),
      level,
      message,
      context,
      userId: context?.userId as string,
      orgId: context?.orgId as string,
      component: context?.component as string,
      action: context?.action as string,
      duration: context?.duration as number,
      metadata: context?.metadata as Record<string, LogContextValue>
    }
  }

  private outputLog(entry: LogEntry): void {
    // Console output
    if (this.config.enableConsole) {
      this.logToConsole(entry)
    }

    // Local storage
    if (this.config.enableStorage) {
      this.logToStorage(entry)
    }

    // Remote logging
    if (this.config.enableRemote && this.config.remoteEndpoint) {
      this.logToRemote(entry)
    }
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString()
    const prefix = `[${timestamp}] [${entry.level.toUpperCase()}]`
    const message = `${prefix} ${entry.message}`

    const logData = {
      ...entry.context,
      component: entry.component,
      action: entry.action,
      duration: entry.duration,
      metadata: entry.metadata
    }

    switch (entry.level) {
      case 'debug':
        console.debug(message, logData)
        break
      case 'info':
        console.info(message, logData)
        break
      case 'warn':
        console.warn(message, logData)
        break
      case 'error':
      case 'fatal':
        console.error(message, logData)
        break
      default:
        console.log(message, logData)
    }
  }

  private logToStorage(entry: LogEntry): void {
    this.logEntries.push(entry)

    // Maintain maximum entries
    if (this.logEntries.length > this.config.maxStorageEntries) {
      this.logEntries = this.logEntries.slice(-this.config.maxStorageEntries)
    }

    // Store in localStorage for persistence across sessions
    try {
      const recentLogs = this.logEntries.slice(-100) // Keep last 100 entries
      localStorage.setItem('app_logs', JSON.stringify(recentLogs))
    } catch (error) {
      console.warn('Failed to store logs in localStorage:', error)
    }
  }

  private logToRemote(entry: LogEntry): void {
    // Send logs to remote endpoint asynchronously
    if (this.config.remoteEndpoint) {
      fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry)
      }).catch(error => {
        console.warn('Failed to send log to remote endpoint:', error)
      })
    }
  }

  public debug(message: string, context?: Record<string, LogContextValue>): void {
    if (this.shouldLog('debug')) {
      const entry = this.createLogEntry('debug', message, context)
      this.outputLog(entry)
    }
  }

  public info(message: string, context?: Record<string, LogContextValue>): void {
    if (this.shouldLog('info')) {
      const entry = this.createLogEntry('info', message, context)
      this.outputLog(entry)
    }
  }

  public warn(message: string, context?: Record<string, LogContextValue>): void {
    if (this.shouldLog('warn')) {
      const entry = this.createLogEntry('warn', message, context)
      this.outputLog(entry)
    }
  }

  public error(message: string, context?: Record<string, LogContextValue>): void {
    if (this.shouldLog('error')) {
      const entry = this.createLogEntry('error', message, context)
      this.outputLog(entry)
    }
  }

  public fatal(message: string, context?: Record<string, LogContextValue>): void {
    if (this.shouldLog('fatal')) {
      const entry = this.createLogEntry('fatal', message, context)
      this.outputLog(entry)
    }
  }

  // Specialized logging methods
  public logApiCall(
    method: string,
    url: string,
    status: number,
    duration: number,
    context?: Record<string, LogContextValue>
  ): void {
    const level = status >= 400 ? 'error' : 'info'
    this[level](`API ${method} ${url} - ${status}`, {
      ...context,
      component: 'API',
      action: `${method} ${url}`,
      duration,
      metadata: { status, method, url }
    })
  }

  public logUserAction(
    action: string,
    component: string,
    userId?: string,
    context?: Record<string, LogContextValue>
  ): void {
    this.info(`User action: ${action}`, {
      ...context,
      userId,
      component,
      action,
      metadata: { userAction: true }
    })
  }

  public logBusinessEvent(
    event: string,
    entityType: string,
    entityId: string,
    context?: Record<string, LogContextValue>
  ): void {
    this.info(`Business event: ${event}`, {
      ...context,
      component: 'Business',
      action: event,
      metadata: { entityType, entityId, businessEvent: true }
    })
  }

  public logPerformance(
    operation: string,
    duration: number,
    context?: Record<string, LogContextValue>
  ): void {
    const level = duration > 5000 ? 'warn' : 'info' // Warn if operation takes > 5s
    this[level](`Performance: ${operation}`, {
      ...context,
      component: 'Performance',
      action: operation,
      duration,
      metadata: { performanceLog: true }
    })
  }

  public logSecurity(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    context?: Record<string, LogContextValue>
  ): void {
    const level = severity === 'critical' ? 'fatal' : severity === 'high' ? 'error' : 'warn'
    this[level](`Security event: ${event}`, {
      ...context,
      component: 'Security',
      action: event,
      metadata: { securityEvent: true, severity }
    })
  }

  // Log retrieval and management
  public getLogs(filters?: {
    level?: LogLevel
    component?: string
    since?: Date
    limit?: number
  }): LogEntry[] {
    let logs = [...this.logEntries]

    if (filters) {
      if (filters.level) {
        const minLevel = this.logLevels[filters.level]
        logs = logs.filter(log => this.logLevels[log.level] >= minLevel)
      }
      if (filters.component) {
        logs = logs.filter(log => log.component === filters.component)
      }
      if (filters.since) {
        logs = logs.filter(log => log.timestamp >= filters.since!)
      }
      if (filters.limit) {
        logs = logs.slice(-filters.limit)
      }
    }

    return logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  public getLogStats(): {
    total: number
    byLevel: Record<string, number>
    byComponent: Record<string, number>
    recent: number
  } {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)

    const byLevel: Record<string, number> = {}
    const byComponent: Record<string, number> = {}
    let recent = 0

    this.logEntries.forEach(log => {
      byLevel[log.level] = (byLevel[log.level] || 0) + 1
      if (log.component) {
        byComponent[log.component] = (byComponent[log.component] || 0) + 1
      }
      if (log.timestamp >= oneHourAgo) {
        recent++
      }
    })

    return {
      total: this.logEntries.length,
      byLevel,
      byComponent,
      recent
    }
  }

  public clearLogs(): void {
    this.logEntries = []
    localStorage.removeItem('app_logs')
  }

  public exportLogs(): string {
    return JSON.stringify(this.logEntries, null, 2)
  }

  public setLogLevel(level: LogLevel): void {
    this.config.level = level
  }

  public getLogLevel(): LogLevel {
    return this.config.level
  }
}

// Export singleton instance
export const logger = Logger.getInstance()

// Convenience functions
export const logDebug = (message: string, context?: Record<string, LogContextValue>) =>
  logger.debug(message, context)

export const logInfo = (message: string, context?: Record<string, LogContextValue>) =>
  logger.info(message, context)

export const logWarn = (message: string, context?: Record<string, LogContextValue>) =>
  logger.warn(message, context)

export const logError = (message: string, context?: Record<string, LogContextValue>) =>
  logger.error(message, context)

export const logFatal = (message: string, context?: Record<string, LogContextValue>) =>
  logger.fatal(message, context)

export const logApiCall = (method: string, url: string, status: number, duration: number, context?: Record<string, LogContextValue>) =>
  logger.logApiCall(method, url, status, duration, context)

export const logUserAction = (action: string, component: string, userId?: string, context?: Record<string, LogContextValue>) =>
  logger.logUserAction(action, component, userId, context)

export const logBusinessEvent = (event: string, entityType: string, entityId: string, context?: Record<string, LogContextValue>) =>
  logger.logBusinessEvent(event, entityType, entityId, context)

export const logPerformance = (operation: string, duration: number, context?: Record<string, LogContextValue>) =>
  logger.logPerformance(operation, duration, context)

export const logSecurity = (event: string, severity: 'low' | 'medium' | 'high' | 'critical', context?: Record<string, LogContextValue>) =>
  logger.logSecurity(event, severity, context)
