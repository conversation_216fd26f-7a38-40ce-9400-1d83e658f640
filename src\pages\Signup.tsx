import { useState } from 'react'
import { Navigate, Link } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuthHook'
import { LoadingButton } from '@/components/ui/loading'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { PasswordInput, PasswordConfirmInput } from '@/components/ui/password-input'
import { BookOpen, Eye, EyeOff } from 'lucide-react'
import { isPasswordSecure, validatePasswordConfirmation, type PasswordValidationResult } from '@/lib/passwordValidation'

export const Signup = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [role, setRole] = useState<'admin' | 'accountant'>('accountant')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [passwordValidation, setPasswordValidation] = useState<PasswordValidationResult | null>(null)
  const { user, signUp } = useAuth()

  if (user) {
    return <Navigate to="/" replace />
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // Validate password security
    if (!isPasswordSecure(password)) {
      setError('Password does not meet security requirements. Please ensure it has at least 8 characters, including uppercase and lowercase letters.')
      setLoading(false)
      return
    }

    // Validate password confirmation
    const confirmValidation = validatePasswordConfirmation(password, confirmPassword)
    if (!confirmValidation.isValid) {
      setError(confirmValidation.message || 'Passwords do not match')
      setLoading(false)
      return
    }

    try {
      await signUp(email, password, role)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Signup failed')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-green-600 p-3 rounded-full">
              <BookOpen className="h-8 w-8 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl text-green-600">Join KAYA</CardTitle>
          <CardDescription>
            Create your accounting account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {error}
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                placeholder="Enter your email"
              />
              <p className="text-sm text-gray-600">We'll use this email for your account and important notifications</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Select value={role} onValueChange={(value: 'admin' | 'accountant') => setRole(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="accountant">Accountant</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <PasswordInput
              id="password"
              label="Password"
              placeholder="Create a secure password"
              value={password}
              onChange={setPassword}
              onValidationChange={setPasswordValidation}
              showRequirements={true}
              showStrengthIndicator={true}
              required={true}
            />

            <PasswordConfirmInput
              id="confirmPassword"
              label="Confirm Password"
              placeholder="Confirm your password"
              value={confirmPassword}
              originalPassword={password}
              onChange={setConfirmPassword}
              required={true}
            />
            
            <LoadingButton
              type="submit"
              className="w-full bg-green-600 hover:bg-green-700"
              loading={loading}
              loadingText="Creating account..."
            >
              Create Account
            </LoadingButton>
          </form>
          
          <div className="mt-6 text-center text-sm">
            <span className="text-gray-600">Already have an account? </span>
            <Link to="/login" className="text-green-600 hover:text-green-700 font-medium">
              Sign in
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
