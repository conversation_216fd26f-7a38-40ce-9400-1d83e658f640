import { supabase } from './supabase'

// Type definitions for backup operations
interface BackupResponse {
  success: boolean;
  backup_id?: string;
  message?: string;
  error?: string;
}

interface RestoreOptions {
  restoreType?: 'full' | 'partial';
  selectedTables?: string[];
  preserveExisting?: boolean;
}

interface BackupListResponse {
  success: boolean;
  backups?: Array<{
    id: string;
    org_id: string;
    backup_type: string;
    created_at: string;
    size: number;
    status: string;
  }>;
  error?: string;
}

interface CleanupResponse {
  success: boolean;
  deleted_count?: number;
  error?: string;
}

// Encryption utilities for backup security
export class BackupEncryption {
  private static async generateKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256,
      },
      true,
      ['encrypt', 'decrypt']
    )
  }

  private static async exportKey(key: CryptoKey): Promise<string> {
    const exported = await crypto.subtle.exportKey('raw', key)
    return btoa(String.fromCharCode(...new Uint8Array(exported)))
  }

  private static async importKey(keyData: string): Promise<CryptoKey> {
    const keyBuffer = Uint8Array.from(atob(keyData), c => c.charCodeAt(0))
    return await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'AES-GCM' },
      true,
      ['encrypt', 'decrypt']
    )
  }

  static async encryptData(data: string, orgId: string): Promise<{
    encryptedData: string
    keyId: string
    iv: string
    algorithm: string
    version: number
  }> {
    // Use enhanced encryption manager
    const result = await BackupEncryptionManager.encryptBackupData(data, orgId)

    return {
      encryptedData: result.encryptedData,
      keyId: result.keyId,
      iv: result.iv,
      algorithm: result.algorithm,
      version: result.version
    }
  }

  static async decryptData(
    encryptedData: string,
    keyId: string,
    iv: string,
    algorithm?: string,
    version?: number
  ): Promise<string> {
    // Use enhanced encryption manager
    const context = {
      encryptedData,
      keyId,
      iv,
      algorithm: algorithm || 'AES-GCM-256',
      version: version || 1
    }

    return await BackupEncryptionManager.decryptBackupData(context)
  }

  private static async getOrCreateOrgKey(orgId: string): Promise<{
    cryptoKey: CryptoKey
    keyId: string
  }> {
    // Check if key exists in database
    const { data: existingKey } = await supabase
      .from('backup_encryption_keys')
      .select('*')
      .eq('org_id', orgId)
      .eq('is_active', true)
      .single()

    if (existingKey) {
      return {
        cryptoKey: await this.importKey(existingKey.key_data),
        keyId: existingKey.id
      }
    }

    // Generate new key
    const cryptoKey = await this.generateKey()
    const keyData = await this.exportKey(cryptoKey)
    
    // Store key in database
    const { data: newKey, error } = await supabase
      .from('backup_encryption_keys')
      .insert({
        org_id: orgId,
        key_data: keyData,
        algorithm: 'AES-GCM-256',
        is_active: true,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    return {
      cryptoKey,
      keyId: newKey.id
    }
  }

  private static async getOrgKey(keyId: string, orgId: string): Promise<CryptoKey | null> {
    const { data: keyRecord } = await supabase
      .from('backup_encryption_keys')
      .select('*')
      .eq('id', keyId)
      .eq('org_id', orgId)
      .single()

    if (!keyRecord) return null

    return await this.importKey(keyRecord.key_data)
  }
}

// Backup service for handling Edge Function calls
export class BackupService {
  private static async getAuthToken(): Promise<string> {
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('No authentication token available')
    }
    return session.access_token
  }

  private static getEdgeFunctionUrl(): string {
    // In development, use local Supabase
    if (process.env.NODE_ENV === 'development') {
      return 'http://localhost:54321/functions/v1/backup-system'
    }
    // In production, use deployed Edge Function
    return `${process.env.VITE_SUPABASE_URL}/functions/v1/backup-system`
  }

  static async createBackup(orgId: string, backupType: 'full' | 'incremental' | 'differential', createdBy: string): Promise<BackupResponse> {
    // Simplified version without advanced features for now
    const token = await this.getAuthToken()
    const url = `${this.getEdgeFunctionUrl()}/backup/create`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        org_id: orgId,
        backup_type: backupType,
        created_by: createdBy
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to create backup')
    }

    return await response.json()
  }

  static async verifyBackup(backupId: string, orgId: string): Promise<BackupResponse> {
    const token = await this.getAuthToken()
    const url = `${this.getEdgeFunctionUrl()}/backup/verify`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        backup_id: backupId,
        org_id: orgId
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to verify backup')
    }

    return await response.json()
  }

  static async restoreBackup(backupId: string, orgId: string, options: RestoreOptions): Promise<BackupResponse> {
    const token = await this.getAuthToken()
    const url = `${this.getEdgeFunctionUrl()}/backup/restore`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        backup_id: backupId,
        org_id: orgId,
        ...options
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to restore backup')
    }

    return await response.json()
  }

  static async listBackups(orgId: string): Promise<BackupListResponse> {
    const token = await this.getAuthToken()
    const url = `${this.getEdgeFunctionUrl()}/backup/list`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        org_id: orgId
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to list backups')
    }

    return await response.json()
  }

  static async cleanupOldBackups(orgId: string): Promise<CleanupResponse> {
    const token = await this.getAuthToken()
    const url = `${this.getEdgeFunctionUrl()}/backup/cleanup`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        org_id: orgId
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to cleanup backups')
    }

    return await response.json()
  }
}

// Utility functions for backup operations
export const calculateChecksum = async (data: string): Promise<string> => {
  const encoder = new TextEncoder()
  const dataBuffer = encoder.encode(data)
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

export const formatBackupSize = (bytes: number): string => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`
}
