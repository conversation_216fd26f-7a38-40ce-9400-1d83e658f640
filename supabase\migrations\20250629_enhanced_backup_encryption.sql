-- Enhanced Backup Encryption Key Management
-- Adds key rotation, versioning, and recovery capabilities

-- Drop existing table if it exists (for clean migration)
DROP TABLE IF EXISTS backup_encryption_keys CASCADE;

-- Create enhanced backup encryption keys table
CREATE TABLE backup_encryption_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    key_data TEXT NOT NULL, -- Base64 encoded key data
    algorithm VARCHAR(50) NOT NULL DEFAULT 'AES-GCM-256',
    version INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    rotated_at TIMESTAMP WITH TIME ZONE,
    rotated_by UUID REFERENCES auth.users(id),
    key_purpose VARCHAR(50) DEFAULT 'backup_encryption',
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT unique_active_key_per_org UNIQUE (org_id, is_active) DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT valid_algorithm CHECK (algorithm IN ('AES-GCM-256', 'AES-GCM-192', 'AES-GCM-128')),
    CONSTRAINT valid_version CHECK (version > 0),
    CONSTRAINT valid_expiry CHECK (expires_at IS NULL OR expires_at > created_at)
);

-- Create indexes for performance
CREATE INDEX idx_backup_encryption_keys_org_id ON backup_encryption_keys(org_id);
CREATE INDEX idx_backup_encryption_keys_active ON backup_encryption_keys(org_id, is_active) WHERE is_active = true;
CREATE INDEX idx_backup_encryption_keys_version ON backup_encryption_keys(org_id, version);
CREATE INDEX idx_backup_encryption_keys_expires ON backup_encryption_keys(expires_at) WHERE expires_at IS NOT NULL;

-- Enable RLS
ALTER TABLE backup_encryption_keys ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view encryption keys for their organization" ON backup_encryption_keys
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Admins can manage encryption keys for their organization" ON backup_encryption_keys
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Function to automatically rotate expired keys
CREATE OR REPLACE FUNCTION auto_rotate_expired_keys()
RETURNS INTEGER AS $$
DECLARE
    expired_key RECORD;
    rotation_count INTEGER := 0;
BEGIN
    -- Find expired active keys
    FOR expired_key IN 
        SELECT org_id, id 
        FROM backup_encryption_keys 
        WHERE is_active = true 
        AND expires_at IS NOT NULL 
        AND expires_at <= NOW()
    LOOP
        -- Deactivate expired key
        UPDATE backup_encryption_keys 
        SET is_active = false, 
            rotated_at = NOW(),
            rotated_by = NULL -- System rotation
        WHERE id = expired_key.id;
        
        -- Create new key (this would typically be done by the application)
        -- For now, just log that rotation is needed
        INSERT INTO audit_logs (
            entity_type, entity_id, action, description, severity, category, org_id
        ) VALUES (
            'backup_encryption', expired_key.org_id::text, 'key_rotation_needed',
            'Encryption key expired and needs rotation', 'warning', 'security', expired_key.org_id
        );
        
        rotation_count := rotation_count + 1;
    END LOOP;
    
    RETURN rotation_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get key rotation status
CREATE OR REPLACE FUNCTION get_key_rotation_status(org_id_param UUID)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    active_key RECORD;
    key_count INTEGER;
BEGIN
    -- Get active key info
    SELECT * INTO active_key
    FROM backup_encryption_keys
    WHERE org_id = org_id_param AND is_active = true
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Count total keys for organization
    SELECT COUNT(*) INTO key_count
    FROM backup_encryption_keys
    WHERE org_id = org_id_param;
    
    -- Build result
    result := jsonb_build_object(
        'has_active_key', active_key.id IS NOT NULL,
        'active_key_id', active_key.id,
        'active_key_version', active_key.version,
        'active_key_created', active_key.created_at,
        'active_key_expires', active_key.expires_at,
        'days_until_expiry', CASE 
            WHEN active_key.expires_at IS NOT NULL 
            THEN EXTRACT(days FROM active_key.expires_at - NOW())::INTEGER
            ELSE NULL
        END,
        'needs_rotation', CASE
            WHEN active_key.expires_at IS NOT NULL AND active_key.expires_at <= NOW() + INTERVAL '7 days'
            THEN true
            ELSE false
        END,
        'total_keys', key_count,
        'algorithm', active_key.algorithm
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to safely deactivate old keys (keeps them for recovery)
CREATE OR REPLACE FUNCTION cleanup_old_encryption_keys(org_id_param UUID, keep_versions INTEGER DEFAULT 5)
RETURNS INTEGER AS $$
DECLARE
    cleanup_count INTEGER := 0;
BEGIN
    -- Mark old inactive keys for cleanup (but don't delete them)
    WITH old_keys AS (
        SELECT id
        FROM backup_encryption_keys
        WHERE org_id = org_id_param
        AND is_active = false
        AND created_at < NOW() - INTERVAL '1 year'
        ORDER BY created_at DESC
        OFFSET keep_versions
    )
    UPDATE backup_encryption_keys
    SET metadata = metadata || jsonb_build_object('marked_for_cleanup', true, 'cleanup_date', NOW())
    WHERE id IN (SELECT id FROM old_keys)
    AND NOT (metadata ? 'marked_for_cleanup');
    
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    
    -- Log cleanup action
    IF cleanup_count > 0 THEN
        INSERT INTO audit_logs (
            entity_type, entity_id, action, description, severity, category, org_id
        ) VALUES (
            'backup_encryption', org_id_param::text, 'keys_marked_for_cleanup',
            format('Marked %s old encryption keys for cleanup', cleanup_count), 
            'info', 'security', org_id_param
        );
    END IF;
    
    RETURN cleanup_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to ensure only one active key per organization
CREATE OR REPLACE FUNCTION enforce_single_active_key()
RETURNS TRIGGER AS $$
BEGIN
    -- If inserting/updating to active, deactivate other active keys
    IF NEW.is_active = true THEN
        UPDATE backup_encryption_keys 
        SET is_active = false, 
            rotated_at = NOW()
        WHERE org_id = NEW.org_id 
        AND is_active = true 
        AND id != NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_enforce_single_active_key
    BEFORE INSERT OR UPDATE ON backup_encryption_keys
    FOR EACH ROW
    EXECUTE FUNCTION enforce_single_active_key();

-- Update backup_metadata table to reference encryption keys properly and add progress tracking
ALTER TABLE backup_metadata
ADD COLUMN IF NOT EXISTS encryption_key_version INTEGER,
ADD COLUMN IF NOT EXISTS encryption_algorithm VARCHAR(50),
ADD COLUMN IF NOT EXISTS progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
ADD COLUMN IF NOT EXISTS current_table VARCHAR(100),
ADD COLUMN IF NOT EXISTS started_at TIMESTAMP WITH TIME ZONE;

-- Create index for encryption key lookups
CREATE INDEX IF NOT EXISTS idx_backup_metadata_encryption_key 
ON backup_metadata(encryption_key_id) WHERE encryption_key_id IS NOT NULL;

-- Function to validate backup encryption integrity
CREATE OR REPLACE FUNCTION validate_backup_encryption_integrity(backup_id_param UUID)
RETURNS JSONB AS $$
DECLARE
    backup_record RECORD;
    key_record RECORD;
    result JSONB;
BEGIN
    -- Get backup metadata
    SELECT * INTO backup_record
    FROM backup_metadata
    WHERE id = backup_id_param;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('valid', false, 'error', 'Backup not found');
    END IF;
    
    -- If backup is not encrypted, return valid
    IF NOT backup_record.encryption_enabled THEN
        RETURN jsonb_build_object('valid', true, 'encrypted', false);
    END IF;
    
    -- Check if encryption key exists
    SELECT * INTO key_record
    FROM backup_encryption_keys
    WHERE id = backup_record.encryption_key_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'valid', false, 
            'error', 'Encryption key not found',
            'key_id', backup_record.encryption_key_id
        );
    END IF;
    
    -- Validate key belongs to same organization
    IF key_record.org_id != backup_record.org_id THEN
        RETURN jsonb_build_object(
            'valid', false,
            'error', 'Encryption key organization mismatch'
        );
    END IF;
    
    RETURN jsonb_build_object(
        'valid', true,
        'encrypted', true,
        'key_id', key_record.id,
        'key_version', key_record.version,
        'key_algorithm', key_record.algorithm,
        'key_created', key_record.created_at
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auto_rotate_expired_keys() TO authenticated;
GRANT EXECUTE ON FUNCTION get_key_rotation_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_encryption_keys(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_backup_encryption_integrity(UUID) TO authenticated;

-- Create backup approval requests table
CREATE TABLE backup_approval_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    requested_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    operation VARCHAR(100) NOT NULL,
    operation_data JSONB NOT NULL DEFAULT '{}',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejected_by UUID REFERENCES auth.users(id),
    rejected_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for approval requests
CREATE INDEX idx_backup_approval_requests_org_status ON backup_approval_requests(org_id, status);
CREATE INDEX idx_backup_approval_requests_expires ON backup_approval_requests(expires_at) WHERE status = 'pending';
CREATE INDEX idx_backup_approval_requests_requested_by ON backup_approval_requests(requested_by);

-- Enable RLS for approval requests
ALTER TABLE backup_approval_requests ENABLE ROW LEVEL SECURITY;

-- RLS policies for approval requests
CREATE POLICY "Users can view approval requests in their organization" ON backup_approval_requests
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can create approval requests in their organization" ON backup_approval_requests
    FOR INSERT WITH CHECK (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
        AND requested_by = auth.uid()
    );

CREATE POLICY "Admins and owners can manage approval requests" ON backup_approval_requests
    FOR UPDATE USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Function to automatically expire old approval requests
CREATE OR REPLACE FUNCTION expire_old_approval_requests()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER := 0;
BEGIN
    UPDATE backup_approval_requests
    SET status = 'expired',
        updated_at = NOW()
    WHERE status = 'pending'
    AND expires_at <= NOW();

    GET DIAGNOSTICS expired_count = ROW_COUNT;

    RETURN expired_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION expire_old_approval_requests() TO authenticated;

-- Create backup errors table for comprehensive error tracking
CREATE TABLE backup_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    backup_id UUID REFERENCES backup_metadata(id) ON DELETE SET NULL,
    error_type VARCHAR(50) NOT NULL,
    error_message TEXT NOT NULL,
    error_details JSONB DEFAULT '{}',
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    operation VARCHAR(50) NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    recoverable BOOLEAN DEFAULT false,
    retryable BOOLEAN DEFAULT false,
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES auth.users(id),
    stack_trace TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for backup errors
CREATE INDEX idx_backup_errors_org_id ON backup_errors(org_id);
CREATE INDEX idx_backup_errors_backup_id ON backup_errors(backup_id) WHERE backup_id IS NOT NULL;
CREATE INDEX idx_backup_errors_type_severity ON backup_errors(error_type, severity);
CREATE INDEX idx_backup_errors_created_at ON backup_errors(created_at);
CREATE INDEX idx_backup_errors_unresolved ON backup_errors(org_id, resolved) WHERE resolved = false;

-- Enable RLS for backup errors
ALTER TABLE backup_errors ENABLE ROW LEVEL SECURITY;

-- RLS policies for backup errors
CREATE POLICY "Users can view backup errors in their organization" ON backup_errors
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "System can insert backup errors" ON backup_errors
    FOR INSERT WITH CHECK (true); -- Allow system to insert errors

CREATE POLICY "Admins can manage backup errors in their organization" ON backup_errors
    FOR UPDATE USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Insert initial audit log entry
INSERT INTO audit_logs (
    entity_type, entity_id, action, description, severity, category, org_id
)
SELECT
    'backup_encryption',
    id::text,
    'encryption_system_initialized',
    'Enhanced backup encryption system initialized',
    'info',
    'security',
    id
FROM organizations;
