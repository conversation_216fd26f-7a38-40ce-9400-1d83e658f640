
import { useState } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import type { TaxRate } from '@/types/database'

interface TaxRateFormProps {
  taxRate?: TaxRate | null
  onClose: () => void
  onSave: () => void
}

interface TaxRateFormData {
  name: string
  rate_pct: string
  ura_code: string
  is_active: boolean
}

export function TaxRateForm({ taxRate, onClose, onSave }: TaxRateFormProps) {
  const { profile } = useAuth()
  const [formData, setFormData] = useState<TaxRateFormData>({
    name: taxRate?.name || '',
    rate_pct: taxRate?.rate_pct?.toString() || '',
    ura_code: taxRate?.ura_code || '',
    is_active: taxRate?.is_active ?? true
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    setIsLoading(true)
    try {
      const taxRateData = {
        name: formData.name,
        rate_pct: Number(formData.rate_pct),
        ura_code: formData.ura_code || null,
        is_active: formData.is_active,
        org_id: profile.org_id
      }

      if (taxRate) {
        const { error } = await supabase
          .from('tax_rates')
          .update(taxRateData)
          .eq('id', taxRate.id)

        if (error) throw error
        toast.success('Tax rate updated successfully')
      } else {
        const { error } = await supabase
          .from('tax_rates')
          .insert([taxRateData])

        if (error) throw error
        toast.success('Tax rate created successfully')
      }

      onSave()
    } catch (error) {
      console.error('Error saving tax rate:', error)
      toast.error('Failed to save tax rate')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{taxRate ? 'Edit Tax Rate' : 'New Tax Rate'}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., VAT Standard Rate"
              required
            />
          </div>

          <div>
            <Label htmlFor="rate_pct">Rate (%)</Label>
            <Input
              id="rate_pct"
              type="number"
              step="0.01"
              value={formData.rate_pct}
              onChange={(e) => setFormData({ ...formData, rate_pct: e.target.value })}
              placeholder="e.g., 18.00"
              required
            />
          </div>

          <div>
            <Label htmlFor="ura_code">URA Code</Label>
            <Input
              id="ura_code"
              value={formData.ura_code}
              onChange={(e) => setFormData({ ...formData, ura_code: e.target.value })}
              placeholder="e.g., VAT001"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked === true })}
            />
            <Label htmlFor="is_active">Active</Label>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : taxRate ? 'Update Tax Rate' : 'Create Tax Rate'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
