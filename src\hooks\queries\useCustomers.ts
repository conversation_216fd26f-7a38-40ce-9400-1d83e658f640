import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys, type QueryFilters } from '@/lib/queryKeys'
import { toast } from '@/hooks/use-toast'
import type { Customer } from '@/types/database'

/**
 * Hook to fetch all customers for the organization
 */
export function useCustomers(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: filters 
      ? queryKeys.customers.filtered(profile?.org_id || '', filters)
      : queryKeys.customers.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('customers')
        .select('*')
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`)
      }

      if (filters?.active !== undefined) {
        query = query.eq('is_active', filters.active)
      }

      // Apply ordering
      query = query.order('created_at', { ascending: false })

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit)
      }
      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch active customers only
 */
export function useActiveCustomers() {
  const { profile } = useAuth()

  return useQuery({
    queryKey: queryKeys.customers.active(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('name')

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes - active customers change less frequently
  })
}

/**
 * Hook to fetch a single customer by ID
 */
export function useCustomer(customerId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.customers.detail(profile?.org_id || '', customerId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !customerId) return null

      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .eq('id', customerId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id && !!customerId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to create a new customer
 */
export function useCreateCustomer() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (customerData: Omit<Customer, 'id' | 'org_id' | 'created_at' | 'updated_at'>) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('customers')
        .insert({
          ...customerData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (newCustomer) => {
      // Invalidate all customer-related queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers.all(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers.active(profile?.org_id || '')
      })
      // Invalidate any filtered queries
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === 'customers' &&
          query.queryKey[1] === profile?.org_id
      })

      // Add the new customer to the cache
      queryClient.setQueryData(
        queryKeys.customers.detail(profile?.org_id || '', newCustomer.id),
        newCustomer
      )

      toast({
        title: 'Success',
        description: 'Customer created successfully',
      })
    },
    onError: (error) => {
      console.error('Error creating customer:', error)
      toast({
        title: 'Error',
        description: 'Failed to create customer',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update an existing customer
 */
export function useUpdateCustomer() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      customerId, 
      customerData 
    }: { 
      customerId: string
      customerData: Partial<Omit<Customer, 'id' | 'org_id' | 'created_at'>>
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('customers')
        .update({
          ...customerData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', customerId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (updatedCustomer) => {
      // Update the customer in the cache
      queryClient.setQueryData(
        queryKeys.customers.detail(profile?.org_id || '', updatedCustomer.id),
        updatedCustomer
      )

      // Invalidate all customer-related queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers.all(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers.active(profile?.org_id || '')
      })
      // Invalidate any filtered queries
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === 'customers' &&
          query.queryKey[1] === profile?.org_id
      })

      toast({
        title: 'Success',
        description: 'Customer updated successfully',
      })
    },
    onError: (error) => {
      console.error('Error updating customer:', error)
      toast({
        title: 'Error',
        description: 'Failed to update customer',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a customer
 */
export function useDeleteCustomer() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (customerId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // First, check if customer has any related records
      const [invoicesCheck, paymentsCheck] = await Promise.all([
        supabase
          .from('invoices')
          .select('id')
          .eq('customer_id', customerId)
          .eq('org_id', profile.org_id)
          .limit(1),
        supabase
          .from('payments')
          .select('id')
          .eq('customer_id', customerId)
          .eq('org_id', profile.org_id)
          .limit(1)
      ])

      // Check for related records
      const hasInvoices = invoicesCheck.data && invoicesCheck.data.length > 0
      const hasPayments = paymentsCheck.data && paymentsCheck.data.length > 0

      if (hasInvoices || hasPayments) {
        const relatedItems = []
        if (hasInvoices) relatedItems.push('invoices')
        if (hasPayments) relatedItems.push('payments')

        throw new Error(`Cannot delete customer. Customer has existing ${relatedItems.join(' and ')}.`)
      }

      // If no related records, proceed with deletion
      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', customerId)
        .eq('org_id', profile.org_id)

      if (error) throw error
      return customerId
    },
    onSuccess: (deletedCustomerId) => {
      // Remove the customer from the cache
      queryClient.removeQueries({ 
        queryKey: queryKeys.customers.detail(profile?.org_id || '', deletedCustomerId) 
      })

      // Invalidate customers list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.customers.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: 'Customer deleted successfully',
      })
    },
    onError: (error: Error) => {
      console.error('Error deleting customer:', error)

      // Provide specific error message based on the error type
      let errorMessage = 'Failed to delete customer'

      if (error.message && error.message.includes('Cannot delete customer')) {
        errorMessage = error.message
      } else if (error.code === '23503') {
        errorMessage = 'Cannot delete customer. Customer has existing invoices or payments.'
      } else if (error.message) {
        errorMessage = error.message
      }

      toast({
        title: 'Cannot Delete Customer',
        description: errorMessage,
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to toggle customer active status
 */
export function useToggleCustomerStatus() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ customerId, isActive }: { customerId: string, isActive: boolean }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('customers')
        .update({
          is_active: isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('id', customerId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (updatedCustomer) => {
      // Update the customer in the cache
      queryClient.setQueryData(
        queryKeys.customers.detail(profile?.org_id || '', updatedCustomer.id),
        updatedCustomer
      )

      // Invalidate both all customers and active customers lists
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers.all(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers.active(profile?.org_id || '')
      })

      toast({
        title: 'Success',
        description: `Customer ${updatedCustomer.is_active ? 'activated' : 'deactivated'} successfully`,
      })
    },
    onError: (error) => {
      console.error('Error toggling customer status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update customer status',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to check if a customer can be deleted (has no related records)
 */
export function useCanDeleteCustomer(customerId: string | null) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: queryKeys.customers.canDelete(profile?.org_id || '', customerId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !customerId) return { canDelete: false, reasons: [] }

      const [invoicesCheck, paymentsCheck] = await Promise.all([
        supabase
          .from('invoices')
          .select('id')
          .eq('customer_id', customerId)
          .eq('org_id', profile.org_id)
          .limit(1),
        supabase
          .from('payments')
          .select('id')
          .eq('customer_id', customerId)
          .eq('org_id', profile.org_id)
          .limit(1)
      ])

      const hasInvoices = invoicesCheck.data && invoicesCheck.data.length > 0
      const hasPayments = paymentsCheck.data && paymentsCheck.data.length > 0

      const reasons = []
      if (hasInvoices) reasons.push('Has existing invoices')
      if (hasPayments) reasons.push('Has existing payments')

      return {
        canDelete: !hasInvoices && !hasPayments,
        reasons
      }
    },
    enabled: !!profile?.org_id && !!customerId,
    staleTime: 30 * 1000, // 30 seconds
  })
}
