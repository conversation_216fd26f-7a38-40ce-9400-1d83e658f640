import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { LoadingSpinner } from '@/components/ui/loading'
import { Calendar } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import type { Account } from '@/types/database'

interface BSData {
  account: Account
  amount: number
}

interface BSSection {
  title: string
  accounts: BSData[]
  total: number
}

export const BalanceSheetReport = () => {
  const { profile } = useAuth()
  const [data, setData] = useState<{
    assets: BSSection
    liabilities: BSSection
    equity: BSSection
  }>({
    assets: { title: 'Assets', accounts: [], total: 0 },
    liabilities: { title: 'Liabilities', accounts: [], total: 0 },
    equity: { title: 'Equity', accounts: [], total: 0 }
  })
  const [loading, setLoading] = useState(false)
  const [asOfDate, setAsOfDate] = useState(new Date().toISOString().split('T')[0])

  const fetchBalanceSheet = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      
      // Fetch balance sheet accounts
      const { data: accounts, error: accountsError } = await supabase
        .from('accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .in('type', ['asset', 'liability', 'equity'])
        .eq('is_active', true)
        .order('code')

      if (accountsError) throw accountsError

      const assetAccounts: BSData[] = []
      const liabilityAccounts: BSData[] = []
      const equityAccounts: BSData[] = []

      for (const account of accounts || []) {
        const { data: transactions, error: transError } = await supabase
          .from('transaction_lines')
          .select('debit, credit')
          .eq('org_id', profile.org_id)
          .eq('account_id', account.id)
          .lte('created_at', `${asOfDate}T23:59:59`)

        if (transError) throw transError

        const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
        const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0

        let amount = 0
        if (account.type === 'asset') {
          // Assets have debit balances
          amount = debitTotal - creditTotal
        } else {
          // Liabilities and equity have credit balances
          amount = creditTotal - debitTotal
        }

        if (amount !== 0) {
          const accountData = { account, amount }
          if (account.type === 'asset') {
            assetAccounts.push(accountData)
          } else if (account.type === 'liability') {
            liabilityAccounts.push(accountData)
          } else {
            equityAccounts.push(accountData)
          }
        }
      }

      // Calculate net income for current period and add to equity
      const currentYearStart = new Date(asOfDate).getFullYear() + '-01-01'
      const { data: revenueExpenseData } = await supabase
        .from('transaction_lines')
        .select(`
          debit,
          credit,
          accounts!inner(type)
        `)
        .eq('org_id', profile.org_id)
        .in('accounts.type', ['income', 'expense'])
        .gte('created_at', `${currentYearStart}T00:00:00`)
        .lte('created_at', `${asOfDate}T23:59:59`)

      let netIncome = 0
      if (revenueExpenseData) {
        netIncome = revenueExpenseData.reduce((sum, t) => {
          if (t.accounts?.type === 'income') {
            return sum + (t.credit - t.debit)
          } else {
            return sum - (t.debit - t.credit)
          }
        }, 0)
      }

      // Add retained earnings/net income to equity
      if (netIncome !== 0) {
        equityAccounts.push({
          account: {
            id: 'retained-earnings',
            code: 'RE',
            name: 'Retained Earnings (Current Year)',
            type: 'equity' as const,
            org_id: profile.org_id,
            is_active: true,
            is_tax_account: false,
            parent_id: null,
            created_at: new Date().toISOString(),
            created_by: null,
            updated_at: null
          },
          amount: netIncome
        })
      }

      const assetTotal = assetAccounts.reduce((sum, item) => sum + item.amount, 0)
      const liabilityTotal = liabilityAccounts.reduce((sum, item) => sum + item.amount, 0)
      const equityTotal = equityAccounts.reduce((sum, item) => sum + item.amount, 0)

      setData({
        assets: {
          title: 'Assets',
          accounts: assetAccounts,
          total: assetTotal
        },
        liabilities: {
          title: 'Liabilities',
          accounts: liabilityAccounts,
          total: liabilityTotal
        },
        equity: {
          title: 'Equity',
          accounts: equityAccounts,
          total: equityTotal
        }
      })
    } catch (error) {
      console.error('Error fetching balance sheet:', error)
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, asOfDate])

  useEffect(() => {
    if (profile?.org_id) {
      fetchBalanceSheet()
    }
  }, [profile?.org_id, asOfDate, fetchBalanceSheet])

  const totalAssetsEqualsLiabilitiesEquity = Math.abs(data.assets.total - (data.liabilities.total + data.equity.total)) < 0.01

  if (loading) {
    return (
      <div className="p-8">
        <LoadingSpinner text="Loading balance sheet..." showText />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Label htmlFor="asOfDate">As of Date:</Label>
          <Input
            id="asOfDate"
            type="date"
            value={asOfDate}
            onChange={(e) => setAsOfDate(e.target.value)}
            className="w-auto"
          />
        </div>
        <Button onClick={fetchBalanceSheet} size="sm">
          <Calendar className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Assets */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="font-bold">{data.assets.title}</TableHead>
                <TableHead className="text-right">Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.assets.accounts.map((item) => (
                <TableRow key={item.account.id}>
                  <TableCell>
                    {item.account.code} - {item.account.name}
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    {formatCurrency(item.amount)}
                  </TableCell>
                </TableRow>
              ))}
              <TableRow className="font-bold border-t-2 bg-primary/10">
                <TableCell>Total Assets</TableCell>
                <TableCell className="text-right font-mono">
                  {formatCurrency(data.assets.total)}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        {/* Liabilities & Equity */}
        <div className="space-y-4">
          {/* Liabilities */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="font-bold">{data.liabilities.title}</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.liabilities.accounts.map((item) => (
                  <TableRow key={item.account.id}>
                    <TableCell>
                      {item.account.code} - {item.account.name}
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      {formatCurrency(item.amount)}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow className="font-bold border-t">
                  <TableCell>Total Liabilities</TableCell>
                  <TableCell className="text-right font-mono">
                    {formatCurrency(data.liabilities.total)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          {/* Equity */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="font-bold">{data.equity.title}</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.equity.accounts.map((item) => (
                  <TableRow key={item.account.id}>
                    <TableCell>
                      {item.account.code} - {item.account.name}
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      {formatCurrency(item.amount)}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow className="font-bold border-t">
                  <TableCell>Total Equity</TableCell>
                  <TableCell className="text-right font-mono">
                    {formatCurrency(data.equity.total)}
                  </TableCell>
                </TableRow>
                <TableRow className="font-bold border-t-2 bg-primary/10">
                  <TableCell>Total Liabilities + Equity</TableCell>
                  <TableCell className="text-right font-mono">
                    {formatCurrency(data.liabilities.total + data.equity.total)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {!totalAssetsEqualsLiabilitiesEquity && (
        <div className="p-4 bg-destructive/10 border border-destructive rounded-md">
          <p className="text-sm text-destructive font-medium">
            Balance Sheet Error: Assets do not equal Liabilities + Equity
          </p>
          <p className="text-xs text-destructive">
            Difference: {formatCurrency(Math.abs(data.assets.total - (data.liabilities.total + data.equity.total)))}
          </p>
        </div>
      )}
    </div>
  )
}
