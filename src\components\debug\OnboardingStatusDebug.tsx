/**
 * Onboarding Status Debug Component
 * Displays detailed onboarding status information for debugging
 */

import React, { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { checkOnboardingStatus, completeOnboarding, resetOnboarding, getOnboardingProgress } from '@/lib/onboardingUtils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface OnboardingStatusData {
  needsOnboarding: boolean
  hasProfile: boolean
  hasOrganization: boolean
  hasCompletedOnboarding: boolean
  profile: Record<string, unknown> | null
  organization: Record<string, unknown> | null
  debugInfo: Record<string, unknown>
}

interface ProgressData {
  percentage: number
  completedSteps: string[]
  remainingSteps: string[]
}

export const OnboardingStatusDebug: React.FC = () => {
  const { user, profile, needsOnboarding, loading } = useAuth()
  const [statusData, setStatusData] = useState<OnboardingStatusData | null>(null)
  const [progressData, setProgressData] = useState<ProgressData | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  const [actionLoading, setActionLoading] = useState(false)

  const refreshStatus = useCallback(async () => {
    if (!user) return

    setRefreshing(true)
    try {
      const [status, progress] = await Promise.all([
        checkOnboardingStatus(user.id),
        getOnboardingProgress(user.id)
      ])
      setStatusData(status)
      setProgressData(progress)
    } catch (error) {
      console.error('Failed to refresh onboarding status:', error)
    } finally {
      setRefreshing(false)
    }
  }, [user])

  const handleCompleteOnboarding = async () => {
    if (!user) return

    setActionLoading(true)
    try {
      const success = await completeOnboarding(user.id)
      if (success) {
        await refreshStatus()
        window.location.reload() // Refresh to update auth state
      }
    } catch (error) {
      console.error('Failed to complete onboarding:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const handleResetOnboarding = async () => {
    if (!user) return

    setActionLoading(true)
    try {
      const success = await resetOnboarding(user.id)
      if (success) {
        await refreshStatus()
        window.location.reload() // Refresh to update auth state
      }
    } catch (error) {
      console.error('Failed to reset onboarding:', error)
    } finally {
      setActionLoading(false)
    }
  }

  useEffect(() => {
    if (user && !loading) {
      refreshStatus()
    }
  }, [user, loading, refreshStatus])

  if (!user) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-yellow-500" />
            Onboarding Debug
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">No user authenticated</p>
        </CardContent>
      </Card>
    )
  }

  if (loading || !statusData) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Loading Onboarding Status...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Fetching onboarding information...</p>
        </CardContent>
      </Card>
    )
  }

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    )
  }

  const getStatusBadge = (status: boolean, trueText: string, falseText: string) => {
    return (
      <Badge variant={status ? "default" : "destructive"}>
        {status ? trueText : falseText}
      </Badge>
    )
  }

  return (
    <div className="space-y-6 w-full max-w-2xl mx-auto">
      {/* Main Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              {getStatusIcon(!statusData.needsOnboarding)}
              Onboarding Status Debug
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshStatus}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Overall Status */}
          <div className="flex items-center justify-between">
            <span className="font-medium">Overall Status:</span>
            {getStatusBadge(!statusData.needsOnboarding, "Completed", "Needs Onboarding")}
          </div>

          <Separator />

          {/* Progress Bar */}
          {progressData && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">Progress:</span>
                <span className="text-sm text-muted-foreground">{progressData.percentage}%</span>
              </div>
              <Progress value={progressData.percentage} className="w-full" />
            </div>
          )}

          <Separator />

          {/* Detailed Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              {getStatusIcon(statusData.hasProfile)}
              <span className="text-sm">Has Profile</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(statusData.hasOrganization)}
              <span className="text-sm">Has Organization</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(statusData.hasCompletedOnboarding)}
              <span className="text-sm">Completed Onboarding</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(!statusData.needsOnboarding)}
              <span className="text-sm">Ready to Use</span>
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleCompleteOnboarding}
              disabled={actionLoading || statusData.hasCompletedOnboarding}
              size="sm"
            >
              Mark as Completed
            </Button>
            <Button
              variant="outline"
              onClick={handleResetOnboarding}
              disabled={actionLoading}
              size="sm"
            >
              Reset Onboarding
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Debug Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-xs font-mono">
            <div><strong>User ID:</strong> {statusData.debugInfo.userId}</div>
            <div><strong>Profile Org ID:</strong> {statusData.debugInfo.profileOrgId || 'null'}</div>
            <div><strong>Onboarding Completed At:</strong> {statusData.debugInfo.onboardingCompletedAt || 'null'}</div>
            <div><strong>Check Timestamp:</strong> {statusData.debugInfo.checkTimestamp}</div>
          </div>
        </CardContent>
      </Card>

      {/* Steps Progress */}
      {progressData && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Onboarding Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <h4 className="text-sm font-medium text-green-600 mb-2">Completed Steps:</h4>
                <ul className="space-y-1">
                  {progressData.completedSteps.map((step, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      {step}
                    </li>
                  ))}
                </ul>
              </div>
              
              {progressData.remainingSteps.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-orange-600 mb-2">Remaining Steps:</h4>
                  <ul className="space-y-1">
                    {progressData.remainingSteps.map((step, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm">
                        <XCircle className="h-3 w-3 text-red-500" />
                        {step}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default OnboardingStatusDebug
