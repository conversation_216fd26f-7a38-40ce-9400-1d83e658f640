
import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { Plus } from 'lucide-react'
import { BudgetLineItem, type BudgetLineData } from './BudgetLineItem'
import type { Budget, BudgetStatus } from '@/types/database'

// Define BudgetLine type based on the database structure
type BudgetLine = {
  id: string
  budget_id: string
  account_id: string
  amount: number
  notes: string | null
  created_at: string
  accounts?: {
    name: string
    code: string
  }
}

const budgetSchema = z.object({
  name: z.string().min(1, 'Budget name is required'),
  account_id: z.string().min(1, 'Account is required'),
  period: z.string().min(1, 'Period is required'),
  start_date: z.string().min(1, 'Start date is required'),
  end_date: z.string().min(1, 'End date is required'),
  approval_deadline: z.string().min(1, 'Approval deadline is required'),
  notes: z.string().optional(),
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected'] as const).default('draft')
})

type BudgetFormData = z.infer<typeof budgetSchema>

interface BudgetFormProps {
  budget?: Budget & { budget_lines?: BudgetLine[] } | null
  onClose: () => void
}

export const BudgetForm = ({ budget, onClose }: BudgetFormProps) => {
  const { profile } = useAuth()
  const [budgetLines, setBudgetLines] = useState<BudgetLineData[]>([])

  const form = useForm<BudgetFormData>({
    resolver: zodResolver(budgetSchema),
    defaultValues: {
      name: budget?.name || '',
      account_id: budget?.account_id || '',
      period: budget?.period || 'monthly',
      start_date: budget?.start_date || '',
      end_date: budget?.end_date || '',
      approval_deadline: budget?.approval_deadline || '',
      notes: budget?.notes || '',
      status: budget?.status || 'draft'
    }
  })

  // Fetch accounts for budget lines
  const { data: accounts = [] } = useQuery({
    queryKey: ['accounts', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('accounts')
        .select('id, name, code')
        .eq('org_id', profile.org_id)
        .order('code')

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id
  })

  // Initialize budget lines when budget changes
  useEffect(() => {
    if (budget?.budget_lines) {
      setBudgetLines(budget.budget_lines.map(line => ({
        item: line.notes || 'Budget Item', // Use notes as item name for backward compatibility
        amount: line.amount,
        notes: ''
      })))
    } else {
      setBudgetLines([{ item: '', amount: 0, notes: '' }])
    }
  }, [budget])

  const addBudgetLine = () => {
    setBudgetLines([...budgetLines, { item: '', amount: 0, notes: '' }])
  }

  const removeBudgetLine = (index: number) => {
    setBudgetLines(budgetLines.filter((_, i) => i !== index))
  }

  const updateBudgetLine = (index: number, field: keyof BudgetLineData, value: string | number) => {
    const updated = [...budgetLines]
    updated[index] = { ...updated[index], [field]: value }
    setBudgetLines(updated)
  }

  const calculateTotalAmount = () => {
    return budgetLines.reduce((total, line) => total + (line.amount || 0), 0)
  }

  const onSubmit = async (data: BudgetFormData) => {
    if (!profile?.org_id) {
      toast.error('Organization not found')
      return
    }

    try {
      const totalAmount = calculateTotalAmount()
      
      const budgetData = {
        name: data.name,
        account_id: data.account_id,
        period: data.period,
        start_date: data.start_date,
        end_date: data.end_date,
        approval_deadline: data.approval_deadline,
        notes: data.notes || '',
        status: data.status,
        org_id: profile.org_id,
        total_amount: totalAmount
      }

      let budgetId: string

      if (budget?.id) {
        // Update existing budget
        const { error } = await supabase
          .from('budgets')
          .update(budgetData)
          .eq('id', budget.id)

        if (error) throw error
        budgetId = budget.id
      } else {
        // Create new budget
        const { data: newBudget, error } = await supabase
          .from('budgets')
          .insert(budgetData)
          .select()
          .single()

        if (error) throw error
        budgetId = newBudget.id
      }

      // Delete existing budget lines if updating
      if (budget?.id) {
        const { error } = await supabase
          .from('budget_lines')
          .delete()
          .eq('budget_id', budget.id)

        if (error) throw error
      }

      // Insert budget lines
      if (budgetLines.length > 0) {
        const budgetLinesData = budgetLines
          .filter(line => line.item.trim() && line.amount > 0)
          .map(line => ({
            budget_id: budgetId,
            account_id: data.account_id, // Use the budget's account_id for all lines
            amount: line.amount,
            notes: `${line.item}${line.notes ? ` - ${line.notes}` : ''}` // Combine item and notes
          }))

        if (budgetLinesData.length > 0) {
          const { error } = await supabase
            .from('budget_lines')
            .insert(budgetLinesData)

          if (error) throw error
        }
      }

      toast.success(budget ? 'Budget updated successfully' : 'Budget created successfully')
      onClose()
    } catch (error) {
      console.error('Error saving budget:', error)
      toast.error('Failed to save budget')
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Budget Name</Label>
          <Input
            id="name"
            {...form.register('name')}
            placeholder="Enter budget name"
          />
          {form.formState.errors.name && (
            <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="account_id">Account *</Label>
          <Select value={form.watch('account_id')} onValueChange={(value) => form.setValue('account_id', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select account" />
            </SelectTrigger>
            <SelectContent>
              {accounts.map((account) => (
                <SelectItem key={account.id} value={account.id}>
                  {account.code} - {account.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {form.formState.errors.account_id && (
            <p className="text-sm text-red-600">{form.formState.errors.account_id.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="period">Period</Label>
          <Select value={form.watch('period')} onValueChange={(value) => form.setValue('period', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="start_date">Start Date</Label>
          <Input
            id="start_date"
            type="date"
            {...form.register('start_date')}
          />
          {form.formState.errors.start_date && (
            <p className="text-sm text-red-600">{form.formState.errors.start_date.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="end_date">End Date</Label>
          <Input
            id="end_date"
            type="date"
            {...form.register('end_date')}
          />
          {form.formState.errors.end_date && (
            <p className="text-sm text-red-600">{form.formState.errors.end_date.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="approval_deadline">Approval Deadline</Label>
          <Input
            id="approval_deadline"
            type="date"
            {...form.register('approval_deadline')}
          />
          {form.formState.errors.approval_deadline && (
            <p className="text-sm text-red-600">{form.formState.errors.approval_deadline.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={form.watch('status')} onValueChange={(value) => form.setValue('status', value as BudgetStatus)}>
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="pending_approval">Pending Approval</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          {...form.register('notes')}
          placeholder="Enter budget notes"
          rows={3}
        />
      </div>

      <Separator />

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Budget Lines</CardTitle>
            <Button type="button" onClick={addBudgetLine} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Line
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {budgetLines.map((line, index) => (
            <BudgetLineItem
              key={index}
              line={line}
              index={index}
              onUpdate={updateBudgetLine}
              onRemove={removeBudgetLine}
              isLast={budgetLines.length === 1}
            />
          ))}

          <div className="flex justify-end">
            <div className="text-lg font-semibold">
              Total Amount: UGX {calculateTotalAmount().toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit">
          {budget ? 'Update Budget' : 'Create Budget'}
        </Button>
      </div>
    </form>
  )
}
