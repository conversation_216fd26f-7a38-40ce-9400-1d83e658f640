/**
 * Notification Triggers Integration
 * Handles automatic notification creation for business events
 */

import { supabase } from '@/lib/supabase'
import {
  createNotificationFromTemplate,
  createNotificationWithPreferences,
  createOrganizationNotification
} from '@/lib/notificationHelpers'
import type { NotificationType, NotificationTemplateData } from '@/types/notifications'

// Business event types that trigger notifications
export type BusinessEvent = 
  | 'invoice_created'
  | 'invoice_sent'
  | 'invoice_paid'
  | 'invoice_overdue'
  | 'bill_created'
  | 'bill_approved'
  | 'bill_paid'
  | 'payment_created'
  | 'payment_approved'
  | 'payment_rejected'
  | 'user_invited'
  | 'budget_exceeded'
  | 'backup_completed'
  | 'backup_failed'

// Event to notification type mapping
const EVENT_NOTIFICATION_MAP: Record<BusinessEvent, NotificationType> = {
  invoice_created: 'invoice_due_soon',
  invoice_sent: 'invoice_due_soon',
  invoice_paid: 'invoice_paid',
  invoice_overdue: 'invoice_overdue',
  bill_created: 'bill_due_soon',
  bill_approved: 'bill_due_soon',
  bill_paid: 'invoice_paid', // Reuse template
  payment_created: 'payment_pending_approval',
  payment_approved: 'payment_approved',
  payment_rejected: 'payment_rejected',
  user_invited: 'user_invited',
  budget_exceeded: 'budget_exceeded',
  backup_completed: 'backup_completed',
  backup_failed: 'backup_failed'
}

/**
 * Trigger notification for business events
 */
export async function triggerBusinessEventNotification(
  event: BusinessEvent,
  orgId: string,
  eventData: NotificationTemplateData,
  entityType?: string,
  entityId?: string,
  userId?: string
): Promise<void> {
  try {
    const notificationType = EVENT_NOTIFICATION_MAP[event]
    if (!notificationType) {
      console.warn(`No notification type mapped for event: ${event}`)
      return
    }

    // Determine if this should be user-specific or organization-wide
    const isOrgWideEvent = [
      'backup_completed',
      'backup_failed',
      'user_invited',
      'budget_exceeded'
    ].includes(event)

    if (isOrgWideEvent) {
      // Create organization-wide notification
      await createOrganizationNotification(
        notificationType,
        orgId,
        eventData,
        entityType,
        entityId
      )
    } else if (userId) {
      // Create user-specific notification with preference checking
      await createNotificationWithPreferences(
        notificationType,
        orgId,
        userId,
        eventData,
        entityType,
        entityId
      )
    } else {
      // Create organization-wide notification for approval workflows
      await createNotificationFromTemplate(
        notificationType,
        orgId,
        null, // Organization-wide
        eventData,
        entityType,
        entityId
      )
    }
  } catch (error) {
    console.error(`Error triggering notification for event ${event}:`, error)
    // Don't throw - notification failures shouldn't break business operations
  }
}

/**
 * Invoice event handlers
 */
export const invoiceEventHandlers = {
  async onInvoiceCreated(invoice: { id: string; org_id: string; invoice_number: string; due_date: string; total_amount: number; created_by: string; customer?: { name: string } }): Promise<void> {
    const daysUntilDue = Math.ceil(
      (new Date(invoice.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    )

    await triggerBusinessEventNotification(
      'invoice_created',
      invoice.org_id,
      {
        invoice_number: invoice.invoice_number,
        customer_name: invoice.customer?.name || 'Unknown Customer',
        days_until_due: daysUntilDue,
        amount: invoice.total_amount?.toString() || '0'
      },
      'invoice',
      invoice.id,
      invoice.created_by
    )
  },

  async onInvoicePaid(invoice: { id: string; org_id: string; invoice_number: string; total_amount: number; created_by: string; customer?: { name: string } }): Promise<void> {
    await triggerBusinessEventNotification(
      'invoice_paid',
      invoice.org_id,
      {
        invoice_number: invoice.invoice_number,
        customer_name: invoice.customer?.name || 'Unknown Customer',
        amount: invoice.total_amount?.toString() || '0'
      },
      'invoice',
      invoice.id,
      invoice.created_by
    )
  }
}

/**
 * Bill event handlers
 */
export const billEventHandlers = {
  async onBillCreated(bill: { id: string; org_id: string; bill_number: string; due_date: string; total_amount: number; created_by: string; vendor?: { name: string } }): Promise<void> {
    const daysUntilDue = Math.ceil(
      (new Date(bill.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    )

    await triggerBusinessEventNotification(
      'bill_created',
      bill.org_id,
      {
        bill_number: bill.bill_number,
        vendor_name: bill.vendor?.name || 'Unknown Vendor',
        days_until_due: daysUntilDue,
        amount: bill.total_amount?.toString() || '0'
      },
      'bill',
      bill.id,
      bill.created_by
    )
  },

  async onBillApproved(bill: { id: string; org_id: string; bill_number: string; total_amount: number; created_by: string; vendor?: { name: string } }): Promise<void> {
    await triggerBusinessEventNotification(
      'bill_approved',
      bill.org_id,
      {
        bill_number: bill.bill_number,
        vendor_name: bill.vendor?.name || 'Unknown Vendor',
        amount: bill.total_amount?.toString() || '0'
      },
      'bill',
      bill.id,
      bill.created_by
    )
  }
}

/**
 * Payment event handlers
 */
export const paymentEventHandlers = {
  async onPaymentCreated(payment: { id: string; org_id: string; amount: number; status: string; description?: string; vendor?: { name: string }; customer?: { name: string } }): Promise<void> {
    // Only trigger for payments requiring approval
    if (payment.status === 'pending_approval') {
      const payeeName = payment.vendor?.name || payment.customer?.name || 'Unknown Payee'
      
      await triggerBusinessEventNotification(
        'payment_created',
        payment.org_id,
        {
          amount: payment.amount?.toString() || '0',
          payee_name: payeeName,
          reason: payment.description || 'Payment approval required'
        },
        'payment',
        payment.id
      )
    }
  },

  async onPaymentApproved(payment: { id: string; org_id: string; amount: number; created_by: string; vendor?: { name: string }; customer?: { name: string } }): Promise<void> {
    const payeeName = payment.vendor?.name || payment.customer?.name || 'Unknown Payee'
    
    await triggerBusinessEventNotification(
      'payment_approved',
      payment.org_id,
      {
        amount: payment.amount?.toString() || '0',
        payee_name: payeeName
      },
      'payment',
      payment.id,
      payment.created_by
    )
  },

  async onPaymentRejected(payment: { id: string; org_id: string; amount: number; created_by: string; vendor?: { name: string }; customer?: { name: string } }, reason?: string): Promise<void> {
    const payeeName = payment.vendor?.name || payment.customer?.name || 'Unknown Payee'
    
    await triggerBusinessEventNotification(
      'payment_rejected',
      payment.org_id,
      {
        amount: payment.amount?.toString() || '0',
        payee_name: payeeName,
        reason: reason || 'Payment rejected'
      },
      'payment',
      payment.id,
      payment.created_by
    )
  }
}

/**
 * System event handlers
 */
export const systemEventHandlers = {
  async onUserInvited(orgId: string, invitedEmail: string, invitedBy: string): Promise<void> {
    await triggerBusinessEventNotification(
      'user_invited',
      orgId,
      {
        email: invitedEmail,
        invited_by: invitedBy
      },
      undefined,
      undefined,
      invitedBy
    )
  },

  async onBackupCompleted(orgId: string, details: string): Promise<void> {
    await triggerBusinessEventNotification(
      'backup_completed',
      orgId,
      {
        details,
        timestamp: new Date().toISOString()
      }
    )
  },

  async onBackupFailed(orgId: string, error: string): Promise<void> {
    await triggerBusinessEventNotification(
      'backup_failed',
      orgId,
      {
        details: error,
        timestamp: new Date().toISOString()
      }
    )
  },

  async onBudgetExceeded(orgId: string, budgetName: string, amount: number, limit: number): Promise<void> {
    const percentage = Math.round((amount / limit) * 100)
    
    await triggerBusinessEventNotification(
      'budget_exceeded',
      orgId,
      {
        entity_name: budgetName,
        amount: amount.toString(),
        limit: limit.toString(),
        percentage: percentage.toString()
      },
      'budget'
    )
  }
}

/**
 * Run scheduled notification jobs (called by cron or scheduled tasks)
 */
export async function runScheduledNotificationJobs(): Promise<void> {
  try {
    // Call the database function that handles scheduled notifications
    const { data, error } = await supabase.rpc('run_notification_jobs')
    
    if (error) {
      console.error('Error running scheduled notification jobs:', error)
      return
    }
    
    console.log('Scheduled notification jobs completed:', data)
  } catch (error) {
    console.error('Error running scheduled notification jobs:', error)
  }
}

/**
 * Clean up old notifications (called by maintenance tasks)
 */
export async function cleanupOldNotifications(orgId?: string): Promise<void> {
  try {
    const { data, error } = await supabase.rpc('cleanup_notifications_advanced', {
      org_id_param: orgId,
      days_old: 90,
      cleanup_expired: true,
      cleanup_read: true
    })
    
    if (error) {
      console.error('Error cleaning up notifications:', error)
      return
    }
    
    console.log('Notification cleanup completed:', data)
  } catch (error) {
    console.error('Error cleaning up notifications:', error)
  }
}
