import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Shield } from 'lucide-react'

export default function SettingsSecurity() {
  return (
    <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Settings
          </CardTitle>
          <CardDescription>
            Manage security policies, session settings, and access controls
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Session Security</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Session Timeout</span>
                    <span className="text-sm text-muted-foreground">30 minutes</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Two-Factor Authentication</span>
                    <Badge variant="outline">Coming Soon</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Password Policy</span>
                    <Badge variant="secondary">Active</Badge>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Access Control</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Role-Based Access</span>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Audit Logging</span>
                    <Badge variant="secondary">Active</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">IP Restrictions</span>
                    <Badge variant="outline">Coming Soon</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
  )
}
