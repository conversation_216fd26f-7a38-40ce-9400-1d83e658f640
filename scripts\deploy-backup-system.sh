#!/bin/bash

# Backup System Deployment Script
# This script deploys the backup system Edge Functions and sets up the environment

set -e

echo "🚀 Starting Backup System Deployment..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

echo "📋 Checking Supabase project status..."
supabase status

echo "🔧 Running database migrations..."
supabase db push

echo "📦 Deploying Edge Functions..."

# Deploy the backup system Edge Function
echo "   Deploying backup-system function..."
supabase functions deploy backup-system

echo "🔐 Setting up environment secrets..."

# Set environment variables for Edge Functions
# Note: These should be set with actual values in production
echo "   Setting SUPABASE_URL..."
supabase secrets set SUPABASE_URL="$(supabase status | grep 'API URL' | awk '{print $3}')"

echo "   Setting SUPABASE_SERVICE_ROLE_KEY..."
supabase secrets set SUPABASE_SERVICE_ROLE_KEY="$(supabase status | grep 'service_role key' | awk '{print $3}')"

echo "🗄️ Creating storage bucket for backups..."
# Create the backups bucket if it doesn't exist
supabase storage create backups --public=false

echo "🔒 Setting up secure storage policies..."
# Set up secure RLS policies for the backups bucket with proper validation
cat << 'EOF' | supabase db sql
-- First, remove any existing policies to avoid conflicts
DELETE FROM storage.policies WHERE bucket_id = 'backups';

-- Create secure storage policies with proper path validation
-- Policy for backup uploads (admin only)
INSERT INTO storage.policies (name, bucket_id, command, definition)
VALUES (
  'Secure backup upload for admins',
  'backups',
  'INSERT',
  'auth.uid() IN (
    SELECT p.id FROM profiles p
    WHERE p.role IN (''admin'', ''owner'')
    AND p.org_id = validate_backup_path_org_id(name)
    AND validate_backup_file_path(name)
  )'
);

-- Policy for backup downloads (authenticated org members)
INSERT INTO storage.policies (name, bucket_id, command, definition)
VALUES (
  'Secure backup download for org members',
  'backups',
  'SELECT',
  'auth.uid() IN (
    SELECT p.id FROM profiles p
    WHERE p.org_id = validate_backup_path_org_id(name)
    AND validate_backup_file_path(name)
  )'
);

-- Policy for backup deletion (admin only with additional checks)
INSERT INTO storage.policies (name, bucket_id, command, definition)
VALUES (
  'Secure backup deletion for admins',
  'backups',
  'DELETE',
  'auth.uid() IN (
    SELECT p.id FROM profiles p
    WHERE p.role IN (''admin'', ''owner'')
    AND p.org_id = validate_backup_path_org_id(name)
    AND validate_backup_file_path(name)
    AND backup_can_be_deleted(name)
  )'
);

-- Create secure path validation functions
CREATE OR REPLACE FUNCTION validate_backup_path_org_id(file_path TEXT)
RETURNS UUID AS $$
DECLARE
    path_parts TEXT[];
    org_id_str TEXT;
    org_uuid UUID;
BEGIN
    -- Split path by '/' and validate structure
    path_parts := string_to_array(file_path, '/');

    -- Path should be: org_id/backup_id.json
    IF array_length(path_parts, 1) != 2 THEN
        RETURN NULL;
    END IF;

    org_id_str := path_parts[1];

    -- Validate UUID format
    BEGIN
        org_uuid := org_id_str::UUID;
    EXCEPTION WHEN invalid_text_representation THEN
        RETURN NULL;
    END;

    -- Verify organization exists
    IF NOT EXISTS (SELECT 1 FROM organizations WHERE id = org_uuid) THEN
        RETURN NULL;
    END IF;

    RETURN org_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION validate_backup_file_path(file_path TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    path_parts TEXT[];
    filename TEXT;
BEGIN
    path_parts := string_to_array(file_path, '/');

    -- Validate path structure
    IF array_length(path_parts, 1) != 2 THEN
        RETURN FALSE;
    END IF;

    filename := path_parts[2];

    -- Validate filename format (should be UUID.json)
    IF NOT (filename ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\.json$') THEN
        RETURN FALSE;
    END IF;

    -- Additional security: check for path traversal attempts
    IF file_path ~ '\.\.' OR file_path ~ '//' OR file_path ~ '^/' THEN
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION backup_can_be_deleted(file_path TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    path_parts TEXT[];
    backup_id_str TEXT;
    backup_uuid UUID;
    backup_record RECORD;
BEGIN
    path_parts := string_to_array(file_path, '/');

    IF array_length(path_parts, 1) != 2 THEN
        RETURN FALSE;
    END IF;

    -- Extract backup ID from filename
    backup_id_str := replace(path_parts[2], '.json', '');

    BEGIN
        backup_uuid := backup_id_str::UUID;
    EXCEPTION WHEN invalid_text_representation THEN
        RETURN FALSE;
    END;

    -- Check backup metadata
    SELECT * INTO backup_record
    FROM backup_metadata
    WHERE id = backup_uuid;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Don't allow deletion of backups that are currently being used for restoration
    IF EXISTS (
        SELECT 1 FROM backup_restorations
        WHERE backup_id = backup_uuid
        AND status IN ('pending', 'in_progress')
    ) THEN
        RETURN FALSE;
    END IF;

    -- Don't allow deletion of the most recent successful backup
    IF backup_record.status = 'completed' AND EXISTS (
        SELECT 1 FROM backup_metadata bm
        WHERE bm.org_id = backup_record.org_id
        AND bm.status = 'completed'
        AND bm.created_at = (
            SELECT MAX(created_at)
            FROM backup_metadata
            WHERE org_id = backup_record.org_id
            AND status = 'completed'
        )
        AND bm.id = backup_uuid
    ) THEN
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
EOF

echo "📊 Setting up backup statistics function..."
cat << 'EOF' | supabase db sql
-- Function to get comprehensive backup statistics
CREATE OR REPLACE FUNCTION get_backup_statistics_comprehensive(org_id_param UUID)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'summary', (
            SELECT jsonb_build_object(
                'total_backups', COUNT(*)::INTEGER,
                'successful_backups', COUNT(CASE WHEN status = 'completed' THEN 1 END)::INTEGER,
                'failed_backups', COUNT(CASE WHEN status = 'failed' THEN 1 END)::INTEGER,
                'pending_backups', COUNT(CASE WHEN status IN ('pending', 'in_progress') THEN 1 END)::INTEGER,
                'encrypted_backups', COUNT(CASE WHEN encryption_enabled = true THEN 1 END)::INTEGER,
                'total_size_gb', ROUND(COALESCE(SUM(size_bytes), 0) / 1073741824.0, 2),
                'avg_backup_size_mb', ROUND(COALESCE(AVG(size_bytes), 0) / 1048576.0, 1),
                'last_backup_date', MAX(created_at),
                'oldest_backup_date', MIN(created_at)
            )
            FROM backup_metadata
            WHERE org_id = org_id_param
        ),
        'encryption', (
            SELECT jsonb_build_object(
                'active_key_version', COALESCE(MAX(key_version), 0),
                'total_keys', COUNT(*),
                'active_keys', COUNT(CASE WHEN is_active THEN 1 END),
                'encryption_enabled', EXISTS(
                    SELECT 1 FROM backup_settings 
                    WHERE org_id = org_id_param AND encryption_enabled = true
                )
            )
            FROM backup_encryption_keys
            WHERE org_id = org_id_param
        ),
        'recent_backups', (
            SELECT jsonb_agg(jsonb_build_object(
                'id', id,
                'type', backup_type,
                'status', status,
                'encrypted', encryption_enabled,
                'size_mb', ROUND(size_bytes / 1048576.0, 1),
                'created_at', created_at,
                'completed_at', completed_at,
                'duration_seconds', CASE 
                    WHEN completed_at IS NOT NULL THEN 
                        EXTRACT(EPOCH FROM (completed_at - created_at))
                    ELSE NULL 
                END
            ))
            FROM (
                SELECT * FROM backup_metadata
                WHERE org_id = org_id_param
                ORDER BY created_at DESC
                LIMIT 10
            ) recent_backups
        ),
        'restoration_history', (
            SELECT jsonb_agg(jsonb_build_object(
                'id', id,
                'backup_id', backup_id,
                'restore_type', restore_type,
                'status', status,
                'progress_percentage', progress_percentage,
                'requested_at', requested_at,
                'completed_at', completed_at
            ))
            FROM (
                SELECT * FROM backup_restorations
                WHERE org_id = org_id_param
                ORDER BY requested_at DESC
                LIMIT 5
            ) recent_restorations
        ),
        'settings', (
            SELECT to_jsonb(bs) FROM backup_settings bs
            WHERE bs.org_id = org_id_param
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_backup_statistics_comprehensive(UUID) TO authenticated;
EOF

echo "🔄 Setting up automated backup cleanup..."
cat << 'EOF' | supabase db sql
-- Function for automated backup cleanup
CREATE OR REPLACE FUNCTION cleanup_old_backups_automated()
RETURNS INTEGER AS $$
DECLARE
    total_deleted INTEGER := 0;
    org_record RECORD;
    deleted_count INTEGER;
BEGIN
    -- Loop through all organizations with backup settings
    FOR org_record IN 
        SELECT org_id, retention_days 
        FROM backup_settings 
        WHERE retention_days > 0
    LOOP
        -- Clean up old backups for this organization
        WITH old_backups AS (
            SELECT id, storage_path
            FROM backup_metadata
            WHERE org_id = org_record.org_id
            AND created_at < NOW() - INTERVAL '1 day' * org_record.retention_days
            AND status IN ('completed', 'failed')
        ),
        deleted_metadata AS (
            DELETE FROM backup_metadata
            WHERE id IN (SELECT id FROM old_backups)
            RETURNING id, storage_path
        )
        SELECT COUNT(*) INTO deleted_count FROM deleted_metadata;
        
        total_deleted := total_deleted + deleted_count;
        
        -- Log the cleanup
        INSERT INTO audit_logs (
            org_id,
            action,
            resource_type,
            resource_id,
            details,
            created_at
        ) VALUES (
            org_record.org_id,
            'cleanup',
            'backup',
            NULL,
            jsonb_build_object(
                'deleted_count', deleted_count,
                'retention_days', org_record.retention_days
            ),
            NOW()
        );
    END LOOP;
    
    RETURN total_deleted;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION cleanup_old_backups_automated() TO authenticated;
EOF

echo "✅ Backup System Deployment Complete!"
echo ""
echo "📋 Deployment Summary:"
echo "   ✅ Database migrations applied"
echo "   ✅ Edge Functions deployed"
echo "   ✅ Environment secrets configured"
echo "   ✅ Storage bucket created"
echo "   ✅ Storage policies configured"
echo "   ✅ Statistics functions created"
echo "   ✅ Cleanup automation configured"
echo ""
echo "🔗 Edge Function URLs:"
echo "   Backup System: $(supabase status | grep 'Functions URL' | awk '{print $3}')/backup-system"
echo ""
echo "⚠️  Next Steps:"
echo "   1. Test the backup creation in your application"
echo "   2. Verify encryption is working correctly"
echo "   3. Test the restoration process"
echo "   4. Set up monitoring and alerting"
echo "   5. Configure automated backup schedules"
echo ""
echo "📚 Documentation:"
echo "   - Backup API: /functions/v1/backup-system/backup/create"
echo "   - Restore API: /functions/v1/backup-system/backup/restore"
echo "   - Verify API: /functions/v1/backup-system/backup/verify"
echo "   - Cleanup API: /functions/v1/backup-system/backup/cleanup"
echo ""
echo "🎉 Your backup system is now production-ready!"
