# Development Setup Guide

This guide will help you set up a complete development environment for Kaya Finance.

## Prerequisites

### Required Software

1. **Node.js** (v18.0.0 or later)
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version`

2. **pnpm** (v8.0.0 or later) - Recommended package manager
   ```bash
   npm install -g pnpm
   # Verify installation
   pnpm --version
   ```

3. **Git** (latest version)
   - Download from [git-scm.com](https://git-scm.com/)
   - Verify installation: `git --version`

4. **VS Code** (recommended IDE)
   - Download from [code.visualstudio.com](https://code.visualstudio.com/)
   - Install recommended extensions (see below)

### Optional but Recommended

1. **Docker** (for local Supabase development)
   - Download from [docker.com](https://www.docker.com/)

2. **Supabase CLI** (for database management)
   ```bash
   npm install -g supabase
   ```

## Environment Setup

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/your-username/kaya-finance.git
cd kaya-finance
```

### 2. Install Dependencies

```bash
pnpm install
```

This will install all project dependencies including:
- React and TypeScript
- Vite build tool
- Testing frameworks
- Linting and formatting tools

### 3. Environment Configuration

Create environment files:

```bash
cp .env.example .env.local
cp .env.example .env.test
```

Configure your `.env.local` file:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Environment
VITE_ENVIRONMENT=development

# Logging
VITE_LOG_LEVEL=debug

# Optional: Monitoring (for development)
VITE_SENTRY_DSN=your_development_sentry_dsn

# Optional: Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_MONITORING=true
```

### 4. Database Setup

#### Option A: Use Hosted Supabase (Recommended for beginners)

1. Create a Supabase project at [supabase.com](https://supabase.com)
2. Copy your project URL and anon key to `.env.local`
3. Run database migrations:
   ```bash
   pnpm db:migrate
   ```

#### Option B: Local Supabase Development

1. Start local Supabase:
   ```bash
   npx supabase start
   ```

2. This will provide local URLs:
   ```
   API URL: http://localhost:54321
   DB URL: postgresql://postgres:postgres@localhost:54322/postgres
   Studio URL: http://localhost:54323
   ```

3. Update `.env.local` with local URLs

### 5. Start Development Server

```bash
pnpm dev
```

The application will be available at [http://localhost:5173](http://localhost:5173)

## VS Code Setup

### Recommended Extensions

Install these extensions for the best development experience:

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "ms-playwright.playwright",
    "orta.vscode-jest",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-markdown",
    "yzhang.markdown-all-in-one",
    "streetsidesoftware.code-spell-checker"
  ]
}
```

### VS Code Settings

Create `.vscode/settings.json`:

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

## Development Workflow

### 1. Code Quality Checks

Before committing code, run these checks:

```bash
# Type checking
pnpm type-check

# Linting
pnpm lint

# Formatting
pnpm format:check

# All tests
pnpm test
```

### 2. Git Hooks

The project uses Husky for git hooks. These will run automatically:

- **Pre-commit**: Runs linting and formatting
- **Pre-push**: Runs type checking and tests

### 3. Branch Naming Convention

Use descriptive branch names:

```bash
# Features
git checkout -b feature/customer-management
git checkout -b feature/invoice-generation

# Bug fixes
git checkout -b fix/payment-calculation
git checkout -b fix/date-formatting

# Improvements
git checkout -b improve/performance-optimization
git checkout -b improve/ui-accessibility
```

### 4. Commit Message Convention

Follow Conventional Commits:

```bash
# Features
git commit -m "feat: add customer management functionality"

# Bug fixes
git commit -m "fix: resolve payment calculation error"

# Documentation
git commit -m "docs: update API documentation"

# Refactoring
git commit -m "refactor: improve invoice component structure"

# Tests
git commit -m "test: add unit tests for payment processing"
```

## Testing Setup

### Running Tests

```bash
# All tests
pnpm test

# Unit tests only
pnpm test:unit

# Integration tests
pnpm test:integration

# End-to-end tests
pnpm test:e2e

# Watch mode for development
pnpm test:watch

# Coverage report
pnpm test:coverage
```

### Test Database

For integration tests, use a separate test database:

1. Create a test Supabase project or use local instance
2. Configure `.env.test` with test database credentials
3. Run test migrations:
   ```bash
   NODE_ENV=test pnpm db:migrate
   ```

## Debugging

### Browser DevTools

1. **React Developer Tools**: Install the browser extension
2. **Redux DevTools**: For state management debugging
3. **Network Tab**: Monitor API requests
4. **Console**: Check for errors and logs

### VS Code Debugging

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug React App",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/vite",
      "args": ["dev"],
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "development"
      }
    }
  ]
}
```

## Performance Monitoring

### Development Tools

1. **React Profiler**: Built into React DevTools
2. **Lighthouse**: Built into Chrome DevTools
3. **Bundle Analyzer**: 
   ```bash
   pnpm analyze
   ```

### Performance Metrics

Monitor these metrics during development:
- First Contentful Paint (FCP)
- Largest Contentful Paint (LCP)
- Cumulative Layout Shift (CLS)
- First Input Delay (FID)

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill process on port 5173
   npx kill-port 5173
   ```

2. **Node modules issues**
   ```bash
   # Clear cache and reinstall
   rm -rf node_modules pnpm-lock.yaml
   pnpm install
   ```

3. **TypeScript errors**
   ```bash
   # Restart TypeScript server in VS Code
   Ctrl+Shift+P -> "TypeScript: Restart TS Server"
   ```

4. **Supabase connection issues**
   - Check environment variables
   - Verify Supabase project status
   - Check network connectivity

### Getting Help

1. Check the [troubleshooting guide](../operations/troubleshooting.md)
2. Search [GitHub issues](https://github.com/your-username/kaya-finance/issues)
3. Ask in [GitHub discussions](https://github.com/your-username/kaya-finance/discussions)
4. Contact the development team

## Next Steps

After setting up your development environment:

1. Read the [Architecture Overview](architecture.md)
2. Review [Coding Standards](coding-standards.md)
3. Check out the [Contributing Guide](contributing.md)
4. Explore the [API Documentation](../api/README.md)

---

*Need help? Contact the development team or check our [troubleshooting guide](../operations/troubleshooting.md).*
