/**
 * Notification Center - Full-page notification management
 * Advanced filtering, search, bulk operations, and history management
 */

import { useState, useMemo, useCallback } from 'react'
import { Search, Filter, Archive, CheckCheck, Trash2, Download, Settings, RefreshCw } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { LoadingSpinner } from '@/components/ui/loading'
import { toast } from '@/components/ui/toast-utils'
import { VirtualizedNotificationList } from './VirtualizedNotificationList'
import { NotificationFilters } from './NotificationFilters'
import { EmptyNotifications } from './EmptyNotifications'
import { 
  useNotifications, 
  useNotificationCount,
  useMarkAllNotificationsAsRead,
  useBulkArchiveNotifications,
  useBulkDeleteNotifications
} from '@/hooks/queries/useNotifications'
import { useNotificationRealtime } from '@/hooks/useNotificationRealtime'
import { NotificationAnalyticsAPI } from '@/lib/notificationApi'
import { useAuth } from '@/hooks/useAuthHook'
import type { NotificationFilters as FilterType, NotificationWithMeta } from '@/types/notifications'

interface NotificationCenterProps {
  className?: string
}

export function NotificationCenter({ className = '' }: NotificationCenterProps) {
  const { profile } = useAuth()
  const [activeTab, setActiveTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<FilterType>({})
  const [showFilters, setShowFilters] = useState(false)
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set())
  const [sortBy, setSortBy] = useState<'created_at' | 'priority' | 'category'>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // Real-time connection status
  const { isConnected, connectionError, reconnect, isUsingPolling } = useNotificationRealtime()

  // Queries
  const { data: allNotifications = [], isLoading, refetch } = useNotifications()
  const { data: unreadNotifications = [] } = useNotifications({ is_read: false })
  const { data: archivedNotifications = [] } = useNotifications({ is_archived: true })
  const { data: notificationCount = 0 } = useNotificationCount()

  // Mutations
  const markAllAsRead = useMarkAllNotificationsAsRead()
  const bulkArchive = useBulkArchiveNotifications()
  const bulkDelete = useBulkDeleteNotifications()

  // Get notifications based on active tab
  const getTabNotifications = useCallback(() => {
    switch (activeTab) {
      case 'unread':
        return unreadNotifications
      case 'archived':
        return archivedNotifications
      case 'financial':
        return allNotifications.filter(n => n.category === 'financial')
      case 'system':
        return allNotifications.filter(n => n.category === 'system')
      case 'approval':
        return allNotifications.filter(n => n.category === 'approval')
      default:
        return allNotifications.filter(n => !n.is_archived)
    }
  }, [activeTab, allNotifications, unreadNotifications, archivedNotifications])

  // Apply search and filters
  const filteredNotifications = useMemo(() => {
    let notifications = getTabNotifications()

    // Apply search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      notifications = notifications.filter(n => 
        n.title.toLowerCase().includes(query) ||
        n.message.toLowerCase().includes(query) ||
        n.type.toLowerCase().includes(query)
      )
    }

    // Apply filters
    if (filters.category) {
      notifications = notifications.filter(n => n.category === filters.category)
    }
    if (filters.priority) {
      notifications = notifications.filter(n => n.priority === filters.priority)
    }
    if (filters.entity_type) {
      notifications = notifications.filter(n => n.entity_type === filters.entity_type)
    }
    if (filters.date_from) {
      notifications = notifications.filter(n => new Date(n.created_at) >= new Date(filters.date_from!))
    }
    if (filters.date_to) {
      notifications = notifications.filter(n => new Date(n.created_at) <= new Date(filters.date_to!))
    }

    // Apply sorting
    notifications.sort((a, b) => {
      let aValue: string | number, bValue: string | number

      switch (sortBy) {
        case 'priority': {
          const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 }
          aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0
          bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0
          break
        }
        case 'category':
          aValue = a.category
          bValue = b.category
          break
        default:
          aValue = new Date(a.created_at).getTime()
          bValue = new Date(b.created_at).getTime()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    return notifications
  }, [getTabNotifications, searchQuery, filters, sortBy, sortOrder])

  // Selection handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedNotifications(new Set(filteredNotifications.map(n => n.id)))
    } else {
      setSelectedNotifications(new Set())
    }
  }, [filteredNotifications])

  const handleSelectNotification = useCallback((notificationId: string, checked: boolean) => {
    setSelectedNotifications(prev => {
      const newSet = new Set(prev)
      if (checked) {
        newSet.add(notificationId)
      } else {
        newSet.delete(notificationId)
      }
      return newSet
    })
  }, [])

  // Bulk operations
  const handleBulkMarkAsRead = useCallback(async () => {
    if (selectedNotifications.size === 0) return

    try {
      await markAllAsRead.mutateAsync(Array.from(selectedNotifications))
      setSelectedNotifications(new Set())
      toast.success(`Marked ${selectedNotifications.size} notifications as read`)
    } catch (error) {
      toast.error('Failed to mark notifications as read')
    }
  }, [selectedNotifications, markAllAsRead])

  const handleBulkArchive = useCallback(async () => {
    if (selectedNotifications.size === 0) return

    try {
      await bulkArchive.mutateAsync(Array.from(selectedNotifications))
      setSelectedNotifications(new Set())
      toast.success(`Archived ${selectedNotifications.size} notifications`)
    } catch (error) {
      toast.error('Failed to archive notifications')
    }
  }, [selectedNotifications, bulkArchive])

  const handleBulkDelete = useCallback(async () => {
    if (selectedNotifications.size === 0) return

    try {
      await bulkDelete.mutateAsync(Array.from(selectedNotifications))
      setSelectedNotifications(new Set())
      toast.success(`Deleted ${selectedNotifications.size} notifications`)
    } catch (error) {
      toast.error('Failed to delete notifications')
    }
  }, [selectedNotifications, bulkDelete])

  // Export notifications
  const handleExport = useCallback(async () => {
    if (!profile?.id || !profile?.org_id) return

    try {
      const response = await NotificationAnalyticsAPI.exportNotifications(
        profile.id,
        profile.org_id,
        filters
      )

      if (response.success && response.data) {
        const blob = new Blob([response.data], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `notifications-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        toast.success('Notifications exported successfully')
      } else {
        throw new Error(response.error || 'Export failed')
      }
    } catch (error) {
      toast.error('Failed to export notifications')
    }
  }, [profile, filters])

  const isAllSelected = selectedNotifications.size > 0 && selectedNotifications.size === filteredNotifications.length
  const isPartiallySelected = selectedNotifications.size > 0 && selectedNotifications.size < filteredNotifications.length

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Notification Center</h1>
          <p className="text-muted-foreground">
            Manage all your notifications in one place
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Connection status */}
          <Badge variant={isConnected ? 'default' : 'secondary'}>
            {isConnected ? 'Real-time' : isUsingPolling ? 'Polling' : 'Offline'}
          </Badge>
          
          <Button
            variant="outline"
            size="sm"
            onClick={reconnect}
            disabled={isConnected}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              
              <Select value={sortBy} onValueChange={(value: 'created_at' | 'priority' | 'category') => setSortBy(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Date</SelectItem>
                  <SelectItem value="priority">Priority</SelectItem>
                  <SelectItem value="category">Category</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </Button>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </CardHeader>
        
        {showFilters && (
          <CardContent>
            <NotificationFilters
              filters={filters}
              onFiltersChange={setFilters}
            />
          </CardContent>
        )}
      </Card>

      {/* Bulk Actions */}
      {selectedNotifications.size > 0 && (
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={isPartiallySelected}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm font-medium">
                  {selectedNotifications.size} selected
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkMarkAsRead}
                  disabled={markAllAsRead.isPending}
                >
                  <CheckCheck className="h-4 w-4 mr-2" />
                  Mark as Read
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkArchive}
                  disabled={bulkArchive.isPending}
                >
                  <Archive className="h-4 w-4 mr-2" />
                  Archive
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkDelete}
                  disabled={bulkDelete.isPending}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notification Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="all">
            All
            {allNotifications.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {allNotifications.filter(n => !n.is_archived).length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread">
            Unread
            {unreadNotifications.length > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadNotifications.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="approval">Approval</TabsTrigger>
          <TabsTrigger value="archived">
            Archived
            {archivedNotifications.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {archivedNotifications.length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        {/* Notification Content */}
        <div className="mt-6">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <LoadingSpinner size="md" text="Loading notifications..." />
            </div>
          ) : filteredNotifications.length > 0 ? (
            <VirtualizedNotificationList
              notifications={filteredNotifications}
              selectedNotifications={selectedNotifications}
              onSelectNotification={handleSelectNotification}
              enableVirtualScrolling={true}
              height={600}
            />
          ) : (
            <EmptyNotifications
              type={activeTab as 'all' | 'unread' | 'financial' | 'system' | 'archived' | 'approval'}
              isLoading={false}
              searchQuery={searchQuery}
            />
          )}
        </div>
      </Tabs>
    </div>
  )
}
