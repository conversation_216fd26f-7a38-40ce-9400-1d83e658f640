import { supabase } from '@/lib/supabase'
import type { 
  ApprovalInstance, 
  ApprovalAction, 
  WorkflowTemplate, 
  ApprovalStep,
  ApprovalStatus,
  DocumentType,
  UserRole
} from '@/types/database'
import type {
  ApprovalInstanceWithDetails,
  CreateApprovalActionPayload,
  ApprovalAuthority,
  DocumentDetails,
  EscalationEvent
} from '@/types/approval-workflow'

// Define types for approval engine
type ApprovalMetadata = Record<string, string | number | boolean | null | undefined>
type RuleConditions = {
  amount_min?: number
  amount_max?: number
  currency_code?: string
  department?: string
  category?: string
  [key: string]: string | number | boolean | null | undefined
}
type DocumentData = Record<string, string | number | boolean | null | undefined>

/**
 * Centralized Approval Engine Core
 * Orchestrates workflow execution, step transitions, and approval routing
 */
export class ApprovalEngine {
  
  /**
   * Submit a document for approval workflow
   */
  static async submitForApproval(
    documentType: DocumentType,
    documentId: string,
    documentAmount: number,
    currencyCode: string,
    submittedBy: string,
    orgId: string,
    metadata?: ApprovalMetadata
  ): Promise<ApprovalInstance> {
    try {
      // 1. Determine appropriate workflow template
      const workflowTemplate = await this.determineWorkflowTemplate(
        documentType, 
        documentAmount, 
        currencyCode, 
        orgId,
        metadata
      )

      if (!workflowTemplate) {
        throw new Error(`No workflow template found for ${documentType}`)
      }

      // 2. Get total steps count
      const { data: steps, error: stepsError } = await supabase
        .from('approval_steps')
        .select('*')
        .eq('workflow_template_id', workflowTemplate.id)
        .order('step_order')

      if (stepsError) throw stepsError

      // 3. Create approval instance
      const { data: instance, error: instanceError } = await supabase
        .from('approval_instances')
        .insert({
          workflow_template_id: workflowTemplate.id,
          document_type: documentType,
          document_id: documentId,
          document_amount: documentAmount,
          currency_code: currencyCode,
          status: 'pending',
          current_step_order: 1,
          total_steps: steps?.length || 0,
          submitted_by: submittedBy,
          submitted_at: new Date().toISOString(),
          metadata: metadata || {},
          org_id: orgId
        })
        .select()
        .single()

      if (instanceError) throw instanceError

      // 4. Trigger notifications for first step approvers
      await this.notifyStepApprovers(instance.id, 1)

      return instance
    } catch (error) {
      console.error('Error submitting for approval:', error)
      throw error
    }
  }

  /**
   * Process an approval action (approve/reject/delegate)
   */
  static async processApprovalAction(
    approvalInstanceId: string,
    approverId: string,
    actionPayload: CreateApprovalActionPayload
  ): Promise<{ success: boolean; message: string; instance?: ApprovalInstance }> {
    try {
      // 1. Get current approval instance
      const { data: instance, error: instanceError } = await supabase
        .from('approval_instances')
        .select(`
          *,
          workflow_template:workflow_templates(*),
          current_step:approval_steps!approval_steps_workflow_template_id_fkey(*)
        `)
        .eq('id', approvalInstanceId)
        .single()

      if (instanceError) throw instanceError

      // 2. Validate approver authority
      const authority = await this.checkApprovalAuthority(
        approverId, 
        approvalInstanceId,
        instance.document_amount
      )

      if (!authority.can_approve && actionPayload.action === 'approved') {
        return { success: false, message: 'Insufficient approval authority' }
      }

      // 3. Get approver profile
      const { data: approverProfile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', approverId)
        .single()

      if (profileError) throw profileError

      // 4. Create approval action record
      const { error: actionError } = await supabase
        .from('approval_actions')
        .insert({
          approval_instance_id: approvalInstanceId,
          step_order: instance.current_step_order,
          approver_id: approverId,
          approver_role: approverProfile.role,
          action: actionPayload.action,
          comments: actionPayload.comments,
          rejection_reason: actionPayload.rejection_reason,
          delegated_to: actionPayload.delegated_to,
          delegation_reason: actionPayload.delegation_reason,
          delegation_expires_at: actionPayload.delegation_expires_at,
          action_taken_at: new Date().toISOString(),
          org_id: instance.org_id
        })

      if (actionError) throw actionError

      // 5. Advance workflow based on action
      const updatedInstance = await this.advanceWorkflow(
        approvalInstanceId, 
        actionPayload.action,
        approverId
      )

      // 6. Send notifications
      await this.sendActionNotifications(approvalInstanceId, actionPayload.action, approverId)

      return { 
        success: true, 
        message: `Action ${actionPayload.action} processed successfully`,
        instance: updatedInstance
      }
    } catch (error) {
      console.error('Error processing approval action:', error)
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Determine the appropriate workflow template for a document
   */
  private static async determineWorkflowTemplate(
    documentType: DocumentType,
    documentAmount: number,
    currencyCode: string,
    orgId: string,
    metadata?: ApprovalMetadata
  ): Promise<WorkflowTemplate | null> {
    try {
      // Get all active templates for this document type
      const { data: templates, error } = await supabase
        .from('workflow_templates')
        .select(`
          *,
          approval_rules(*)
        `)
        .eq('document_type', documentType)
        .eq('org_id', orgId)
        .eq('is_active', true)
        .order('version', { ascending: false })

      if (error) throw error

      // If no templates found, return null
      if (!templates || templates.length === 0) {
        return null
      }

      // Find template with matching rules or return default
      for (const template of templates) {
        if (template.approval_rules && template.approval_rules.length > 0) {
          // Check if any rule matches
          const matchingRule = template.approval_rules.find(rule => 
            this.evaluateRuleConditions(rule.conditions, {
              amount: documentAmount,
              currency_code: currencyCode,
              ...metadata
            })
          )
          
          if (matchingRule) {
            return template
          }
        } else if (template.is_default) {
          return template
        }
      }

      // Return first default template if no rules match
      return templates.find(t => t.is_default) || templates[0]
    } catch (error) {
      console.error('Error determining workflow template:', error)
      return null
    }
  }

  /**
   * Evaluate rule conditions against document data
   */
  private static evaluateRuleConditions(
    conditions: RuleConditions,
    documentData: DocumentData
  ): boolean {
    try {
      // Amount range check
      if (conditions.amount_min !== undefined && documentData.amount < conditions.amount_min) {
        return false
      }
      if (conditions.amount_max !== undefined && documentData.amount > conditions.amount_max) {
        return false
      }

      // Currency check
      if (conditions.currency_code && documentData.currency_code !== conditions.currency_code) {
        return false
      }

      // Custom field checks
      if (conditions.custom_fields) {
        for (const [key, value] of Object.entries(conditions.custom_fields)) {
          if (documentData[key] !== value) {
            return false
          }
        }
      }

      return true
    } catch (error) {
      console.error('Error evaluating rule conditions:', error)
      return false
    }
  }

  /**
   * Advance workflow to next step or complete
   */
  private static async advanceWorkflow(
    approvalInstanceId: string,
    action: ApprovalStatus,
    approverId: string
  ): Promise<ApprovalInstance> {
    try {
      // Use the database function for workflow advancement
      const { data, error } = await supabase.rpc('advance_approval_workflow', {
        p_approval_instance_id: approvalInstanceId,
        p_action: action,
        p_approver_id: approverId
      })

      if (error) throw error

      // Get updated instance
      const { data: updatedInstance, error: fetchError } = await supabase
        .from('approval_instances')
        .select('*')
        .eq('id', approvalInstanceId)
        .single()

      if (fetchError) throw fetchError

      // If workflow moved to next step, notify new approvers
      if (updatedInstance.status === 'pending') {
        await this.notifyStepApprovers(approvalInstanceId, updatedInstance.current_step_order)
      }

      return updatedInstance
    } catch (error) {
      console.error('Error advancing workflow:', error)
      throw error
    }
  }

  /**
   * Check if user has approval authority for current step
   */
  static async checkApprovalAuthority(
    userId: string,
    approvalInstanceId: string,
    documentAmount?: number
  ): Promise<ApprovalAuthority> {
    try {
      // Use the database function to check approval authority
      const { data, error } = await supabase.rpc('can_user_approve_step', {
        p_user_id: userId,
        p_approval_instance_id: approvalInstanceId
      })

      if (error) throw error

      // Get user's role limits if amount is provided
      let withinLimits = true
      let limitInfo = {}

      if (documentAmount) {
        const limitCheck = await this.checkApprovalLimits(userId, documentAmount)
        withinLimits = limitCheck.within_limit
        limitInfo = limitCheck
      }

      return {
        can_approve: data && withinLimits,
        can_reject: data, // Can reject if can approve
        can_delegate: data, // Can delegate if can approve
        ...limitInfo
      }
    } catch (error) {
      console.error('Error checking approval authority:', error)
      return {
        can_approve: false,
        can_reject: false,
        can_delegate: false,
        reasons: ['Error checking authority']
      }
    }
  }

  /**
   * Check approval limits for user
   */
  private static async checkApprovalLimits(
    userId: string,
    amount: number
  ): Promise<{ within_limit: boolean; limit_type?: string; limit_amount?: number }> {
    // Implementation would check role_approval_limits table
    // This is a simplified version
    return { within_limit: true }
  }

  /**
   * Notify approvers for a specific step
   */
  private static async notifyStepApprovers(
    approvalInstanceId: string,
    stepOrder: number
  ): Promise<void> {
    // Implementation would integrate with notification system
    console.log(`Notifying approvers for instance ${approvalInstanceId}, step ${stepOrder}`)
  }

  /**
   * Send notifications after approval action
   */
  private static async sendActionNotifications(
    approvalInstanceId: string,
    action: ApprovalStatus,
    approverId: string
  ): Promise<void> {
    // Implementation would integrate with notification system
    console.log(`Sending ${action} notifications for instance ${approvalInstanceId}`)
  }
}
