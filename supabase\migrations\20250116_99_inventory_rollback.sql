-- =====================================================
-- INVENTORY MANAGEMENT - ROLLBACK SCRIPT
-- =====================================================
-- Date: 2025-01-16
-- Purpose: Complete rollback of inventory management implementation
-- WARNING: This will remove ALL inventory data and tables
-- Use with extreme caution in production environments

-- =====================================================
-- CONFIRMATION PROMPT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚠️  WARNING: This script will completely remove inventory management!';
    RAISE NOTICE '⚠️  ALL INVENTORY DATA WILL BE LOST!';
    RAISE NOTICE '⚠️  Make sure you have a backup before proceeding!';
    RAISE NOTICE '';
    RAISE NOTICE 'To proceed, uncomment the rollback sections below.';
END $$;

-- Uncomment the following sections to perform rollback

/*
-- =====================================================
-- STEP 1: DROP TRIGGERS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Dropping inventory triggers...';
END $$;

DROP TRIGGER IF EXISTS trigger_ensure_single_default_location ON inventory_locations;
DROP TRIGGER IF EXISTS trigger_update_stock_levels_timestamp ON stock_levels;
DROP TRIGGER IF EXISTS trigger_populate_invoice_item_from_product ON invoice_lines;
DROP TRIGGER IF EXISTS trigger_populate_bill_item_from_product ON bill_lines;
DROP TRIGGER IF EXISTS trigger_update_stock_levels_on_transaction ON inventory_transactions;

-- =====================================================
-- STEP 2: DROP FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Dropping inventory functions...';
END $$;

DROP FUNCTION IF EXISTS ensure_single_default_location();
DROP FUNCTION IF EXISTS update_stock_levels_timestamp();
DROP FUNCTION IF EXISTS populate_item_from_product();
DROP FUNCTION IF EXISTS update_stock_levels_on_transaction();

-- =====================================================
-- STEP 3: DROP INDEXES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📊 Dropping inventory indexes...';
END $$;

-- Product indexes
DROP INDEX IF EXISTS idx_products_org_sku;
DROP INDEX IF EXISTS idx_products_org_name;
DROP INDEX IF EXISTS idx_products_category;
DROP INDEX IF EXISTS idx_products_barcode;
DROP INDEX IF EXISTS idx_products_active;
DROP INDEX IF EXISTS idx_products_sellable;
DROP INDEX IF EXISTS idx_products_purchasable;
DROP INDEX IF EXISTS idx_products_name_search;
DROP INDEX IF EXISTS idx_products_description_search;

-- Category indexes
DROP INDEX IF EXISTS idx_product_categories_parent;
DROP INDEX IF EXISTS idx_product_categories_org;
DROP INDEX IF EXISTS idx_product_categories_sort;
DROP INDEX IF EXISTS idx_product_categories_name;
DROP INDEX IF EXISTS idx_product_categories_code;

-- Location indexes
DROP INDEX IF EXISTS idx_inventory_locations_org;
DROP INDEX IF EXISTS idx_inventory_locations_default;
DROP INDEX IF EXISTS idx_inventory_locations_code;
DROP INDEX IF EXISTS idx_inventory_locations_name;

-- Stock level indexes
DROP INDEX IF EXISTS idx_stock_levels_product;
DROP INDEX IF EXISTS idx_stock_levels_location;
DROP INDEX IF EXISTS idx_stock_levels_org_product;
DROP INDEX IF EXISTS idx_stock_levels_available;
DROP INDEX IF EXISTS idx_stock_levels_on_hand;
DROP INDEX IF EXISTS idx_stock_levels_reserved;
DROP INDEX IF EXISTS idx_stock_levels_low_stock;
DROP INDEX IF EXISTS idx_stock_levels_avg_cost;
DROP INDEX IF EXISTS idx_stock_levels_last_updated;

-- Transaction indexes
DROP INDEX IF EXISTS idx_inventory_transactions_product;
DROP INDEX IF EXISTS idx_inventory_transactions_location;
DROP INDEX IF EXISTS idx_inventory_transactions_org;
DROP INDEX IF EXISTS idx_inventory_transactions_date;
DROP INDEX IF EXISTS idx_inventory_transactions_type;
DROP INDEX IF EXISTS idx_inventory_transactions_org_date;
DROP INDEX IF EXISTS idx_inventory_transactions_reference;
DROP INDEX IF EXISTS idx_inventory_transactions_ref_number;
DROP INDEX IF EXISTS idx_inventory_transactions_created_by;
DROP INDEX IF EXISTS idx_inventory_transactions_batch;
DROP INDEX IF EXISTS idx_inventory_transactions_expiry;
DROP INDEX IF EXISTS idx_inventory_transactions_cost;
DROP INDEX IF EXISTS idx_inventory_transactions_total_cost;

-- Line item indexes
DROP INDEX IF EXISTS idx_invoice_lines_product_id;
DROP INDEX IF EXISTS idx_invoice_lines_product_invoice;
DROP INDEX IF EXISTS idx_bill_lines_product_id;
DROP INDEX IF EXISTS idx_bill_lines_product_bill;

-- Composite indexes
DROP INDEX IF EXISTS idx_product_stock_summary;
DROP INDEX IF EXISTS idx_transaction_history;
DROP INDEX IF EXISTS idx_cost_tracking;
DROP INDEX IF EXISTS idx_low_stock_alerts;

-- =====================================================
-- STEP 4: REMOVE PRODUCT REFERENCES FROM LINE ITEMS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔗 Removing product references from line items...';
END $$;

-- Remove product_id columns from line items
ALTER TABLE invoice_lines DROP COLUMN IF EXISTS product_id;
ALTER TABLE bill_lines DROP COLUMN IF EXISTS product_id;

-- Note: We keep the item columns as they may contain existing data

-- =====================================================
-- STEP 5: DROP INVENTORY TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🗑️  Dropping inventory tables...';
END $$;

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS inventory_transactions CASCADE;
DROP TABLE IF EXISTS stock_levels CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS inventory_locations CASCADE;
DROP TABLE IF EXISTS product_categories CASCADE;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
BEGIN
    -- Check if inventory tables still exist
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name IN ('products', 'product_categories', 'inventory_locations', 'stock_levels', 'inventory_transactions');
    
    IF table_count = 0 THEN
        RAISE NOTICE '✅ All inventory tables successfully removed';
        RAISE NOTICE '✅ Inventory management rollback completed';
    ELSE
        RAISE NOTICE '❌ Some inventory tables still exist: %', table_count;
    END IF;
END $$;

*/

-- =====================================================
-- MANUAL VERIFICATION QUERIES
-- =====================================================

-- Uncomment to check what would be removed:

/*
-- Check existing inventory tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public'
AND table_name IN ('products', 'product_categories', 'inventory_locations', 'stock_levels', 'inventory_transactions');

-- Check existing inventory indexes
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'public'
AND tablename IN ('products', 'product_categories', 'inventory_locations', 'stock_levels', 'inventory_transactions')
AND indexname LIKE 'idx_%';

-- Check existing inventory functions
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name IN ('ensure_single_default_location', 'update_stock_levels_timestamp', 'populate_item_from_product', 'update_stock_levels_on_transaction');

-- Check product_id columns in line items
SELECT column_name, table_name 
FROM information_schema.columns 
WHERE table_schema = 'public'
AND table_name IN ('invoice_lines', 'bill_lines')
AND column_name = 'product_id';
*/
