export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      account_mappings: {
        Row: {
          account_id: string
          category: string | null
          created_at: string | null
          id: string
          is_default: boolean | null
          mapping_type: string
          org_id: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          category?: string | null
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          mapping_type: string
          org_id: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          category?: string | null
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          mapping_type?: string
          org_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_mappings_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_mappings_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_mappings_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      accounts: {
        Row: {
          code: string
          created_at: string
          created_by: string | null
          id: string
          is_active: boolean
          is_tax_account: boolean
          name: string
          org_id: string
          parent_id: string | null
          type: Database["public"]["Enums"]["account_type"]
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string
          created_by?: string | null
          id?: string
          is_active?: boolean
          is_tax_account?: boolean
          name: string
          org_id: string
          parent_id?: string | null
          type: Database["public"]["Enums"]["account_type"]
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string
          created_by?: string | null
          id?: string
          is_active?: boolean
          is_tax_account?: boolean
          name?: string
          org_id?: string
          parent_id?: string | null
          type?: Database["public"]["Enums"]["account_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "accounts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      attachments: {
        Row: {
          attached_to_id: string
          attached_to_type: string
          created_at: string
          file_name: string
          file_size: number
          file_type: string
          id: string
          org_id: string
          uploaded_by: string | null
          url: string
        }
        Insert: {
          attached_to_id: string
          attached_to_type: string
          created_at?: string
          file_name: string
          file_size: number
          file_type: string
          id?: string
          org_id: string
          uploaded_by?: string | null
          url: string
        }
        Update: {
          attached_to_id?: string
          attached_to_type?: string
          created_at?: string
          file_name?: string
          file_size?: number
          file_type?: string
          id?: string
          org_id?: string
          uploaded_by?: string | null
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "attachments_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attachments_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attachments_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attachments_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: string
          category: string | null
          changed_data: Json | null
          created_at: string
          description: string | null
          entity_id: string
          entity_type: string
          id: string
          ip_address: string | null
          metadata: Json | null
          org_id: string
          profile_id: string | null
          session_id: string | null
          severity: string | null
          updated_at: string | null
          user_agent: string | null
        }
        Insert: {
          action: string
          category?: string | null
          changed_data?: Json | null
          created_at?: string
          description?: string | null
          entity_id: string
          entity_type: string
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          org_id: string
          profile_id?: string | null
          session_id?: string | null
          severity?: string | null
          updated_at?: string | null
          user_agent?: string | null
        }
        Update: {
          action?: string
          category?: string | null
          changed_data?: Json | null
          created_at?: string
          description?: string | null
          entity_id?: string
          entity_type?: string
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          org_id?: string
          profile_id?: string | null
          session_id?: string | null
          severity?: string | null
          updated_at?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audit_logs_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audit_logs_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audit_logs_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
        ]
      }
      backup_encryption_keys: {
        Row: {
          algorithm: string
          created_at: string | null
          id: string
          is_active: boolean
          key_data: string
          org_id: string
          updated_at: string | null
        }
        Insert: {
          algorithm?: string
          created_at?: string | null
          id?: string
          is_active?: boolean
          key_data: string
          org_id: string
          updated_at?: string | null
        }
        Update: {
          algorithm?: string
          created_at?: string | null
          id?: string
          is_active?: boolean
          key_data?: string
          org_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "backup_encryption_keys_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_encryption_keys_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      backup_errors: {
        Row: {
          backup_id: string | null
          error_code: string
          error_details: string
          id: string
          occurred_at: string
          org_id: string
          resolved: boolean
          resolved_at: string | null
          resolved_by: string | null
          severity: string
        }
        Insert: {
          backup_id?: string | null
          error_code: string
          error_details: string
          id?: string
          occurred_at?: string
          org_id: string
          resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          severity: string
        }
        Update: {
          backup_id?: string | null
          error_code?: string
          error_details?: string
          id?: string
          occurred_at?: string
          org_id?: string
          resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          severity?: string
        }
        Relationships: [
          {
            foreignKeyName: "backup_errors_backup_id_fkey"
            columns: ["backup_id"]
            isOneToOne: false
            referencedRelation: "backup_metadata"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_errors_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_errors_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_errors_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_errors_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
        ]
      }
      backup_metadata: {
        Row: {
          backup_type: Database["public"]["Enums"]["backup_type"]
          checksum: string | null
          completed_at: string | null
          compression_algorithm: string | null
          created_at: string
          created_by: string | null
          encryption_key_id: string | null
          encryption_method: string | null
          error_code: string | null
          error_message: string | null
          expires_at: string | null
          id: string
          org_id: string
          parent_backup_id: string | null
          record_count: number
          settings_id: string | null
          size_bytes: number
          started_at: string | null
          status: Database["public"]["Enums"]["backup_status"]
          storage_bucket: string
          storage_path: string
          storage_provider: Database["public"]["Enums"]["storage_provider"]
          storage_region: string | null
          table_count: number
        }
        Insert: {
          backup_type: Database["public"]["Enums"]["backup_type"]
          checksum?: string | null
          completed_at?: string | null
          compression_algorithm?: string | null
          created_at?: string
          created_by?: string | null
          encryption_key_id?: string | null
          encryption_method?: string | null
          error_code?: string | null
          error_message?: string | null
          expires_at?: string | null
          id?: string
          org_id: string
          parent_backup_id?: string | null
          record_count?: number
          settings_id?: string | null
          size_bytes?: number
          started_at?: string | null
          status?: Database["public"]["Enums"]["backup_status"]
          storage_bucket?: string
          storage_path: string
          storage_provider?: Database["public"]["Enums"]["storage_provider"]
          storage_region?: string | null
          table_count?: number
        }
        Update: {
          backup_type?: Database["public"]["Enums"]["backup_type"]
          checksum?: string | null
          completed_at?: string | null
          compression_algorithm?: string | null
          created_at?: string
          created_by?: string | null
          encryption_key_id?: string | null
          encryption_method?: string | null
          error_code?: string | null
          error_message?: string | null
          expires_at?: string | null
          id?: string
          org_id?: string
          parent_backup_id?: string | null
          record_count?: number
          settings_id?: string | null
          size_bytes?: number
          started_at?: string | null
          status?: Database["public"]["Enums"]["backup_status"]
          storage_bucket?: string
          storage_path?: string
          storage_provider?: Database["public"]["Enums"]["storage_provider"]
          storage_region?: string | null
          table_count?: number
        }
        Relationships: [
          {
            foreignKeyName: "backup_metadata_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_metadata_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_metadata_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_metadata_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_metadata_parent_backup_id_fkey"
            columns: ["parent_backup_id"]
            isOneToOne: false
            referencedRelation: "backup_metadata"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_metadata_settings_id_fkey"
            columns: ["settings_id"]
            isOneToOne: false
            referencedRelation: "backup_settings"
            referencedColumns: ["id"]
          },
        ]
      }
      backup_restorations: {
        Row: {
          approved_by: string | null
          backup_id: string
          backup_validated: boolean | null
          checksum_verified: boolean | null
          completed_at: string | null
          completed_tables: number | null
          error_code: string | null
          error_message: string | null
          estimated_completion: string | null
          exclude_tables: string[] | null
          id: string
          org_id: string
          pre_restore_snapshot_id: string | null
          progress_percentage: number | null
          requested_at: string
          requested_by: string
          restore_mode: Database["public"]["Enums"]["restore_mode"]
          restore_notes: string | null
          restore_point: string | null
          restore_type: Database["public"]["Enums"]["restore_type"]
          restored_records: number | null
          selected_tables: string[] | null
          started_at: string | null
          status: Database["public"]["Enums"]["restore_status"]
          total_records: number | null
          total_tables: number | null
          validation_errors: string[] | null
          warnings: string[] | null
        }
        Insert: {
          approved_by?: string | null
          backup_id: string
          backup_validated?: boolean | null
          checksum_verified?: boolean | null
          completed_at?: string | null
          completed_tables?: number | null
          error_code?: string | null
          error_message?: string | null
          estimated_completion?: string | null
          exclude_tables?: string[] | null
          id?: string
          org_id: string
          pre_restore_snapshot_id?: string | null
          progress_percentage?: number | null
          requested_at?: string
          requested_by: string
          restore_mode?: Database["public"]["Enums"]["restore_mode"]
          restore_notes?: string | null
          restore_point?: string | null
          restore_type?: Database["public"]["Enums"]["restore_type"]
          restored_records?: number | null
          selected_tables?: string[] | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["restore_status"]
          total_records?: number | null
          total_tables?: number | null
          validation_errors?: string[] | null
          warnings?: string[] | null
        }
        Update: {
          approved_by?: string | null
          backup_id?: string
          backup_validated?: boolean | null
          checksum_verified?: boolean | null
          completed_at?: string | null
          completed_tables?: number | null
          error_code?: string | null
          error_message?: string | null
          estimated_completion?: string | null
          exclude_tables?: string[] | null
          id?: string
          org_id?: string
          pre_restore_snapshot_id?: string | null
          progress_percentage?: number | null
          requested_at?: string
          requested_by?: string
          restore_mode?: Database["public"]["Enums"]["restore_mode"]
          restore_notes?: string | null
          restore_point?: string | null
          restore_type?: Database["public"]["Enums"]["restore_type"]
          restored_records?: number | null
          selected_tables?: string[] | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["restore_status"]
          total_records?: number | null
          total_tables?: number | null
          validation_errors?: string[] | null
          warnings?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "backup_restorations_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_restorations_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_restorations_backup_id_fkey"
            columns: ["backup_id"]
            isOneToOne: false
            referencedRelation: "backup_metadata"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_restorations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_restorations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_restorations_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_restorations_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
        ]
      }
      backup_settings: {
        Row: {
          auto_backup_enabled: boolean
          backup_days: number[]
          backup_frequency: Database["public"]["Enums"]["backup_frequency"]
          backup_time: string
          created_at: string
          encryption_enabled: boolean
          id: string
          incremental_frequency_hours: number
          last_backup_at: string | null
          notification_emails: string[] | null
          notification_enabled: boolean
          org_id: string
          retention_days: number
          storage_bucket: string
          storage_provider: Database["public"]["Enums"]["storage_provider"]
          storage_region: string | null
          updated_at: string
        }
        Insert: {
          auto_backup_enabled?: boolean
          backup_days?: number[]
          backup_frequency?: Database["public"]["Enums"]["backup_frequency"]
          backup_time?: string
          created_at?: string
          encryption_enabled?: boolean
          id?: string
          incremental_frequency_hours?: number
          last_backup_at?: string | null
          notification_emails?: string[] | null
          notification_enabled?: boolean
          org_id: string
          retention_days?: number
          storage_bucket?: string
          storage_provider?: Database["public"]["Enums"]["storage_provider"]
          storage_region?: string | null
          updated_at?: string
        }
        Update: {
          auto_backup_enabled?: boolean
          backup_days?: number[]
          backup_frequency?: Database["public"]["Enums"]["backup_frequency"]
          backup_time?: string
          created_at?: string
          encryption_enabled?: boolean
          id?: string
          incremental_frequency_hours?: number
          last_backup_at?: string | null
          notification_emails?: string[] | null
          notification_enabled?: boolean
          org_id?: string
          retention_days?: number
          storage_bucket?: string
          storage_provider?: Database["public"]["Enums"]["storage_provider"]
          storage_region?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "backup_settings_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: true
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backup_settings_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: true
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      bank_accounts: {
        Row: {
          account_no: string
          bank_name: string
          branch_name: string | null
          created_at: string
          created_by: string | null
          currency: string
          id: string
          is_active: boolean
          name: string
          opening_balance: number
          opening_date: string
          org_id: string
          updated_at: string | null
        }
        Insert: {
          account_no: string
          bank_name: string
          branch_name?: string | null
          created_at?: string
          created_by?: string | null
          currency?: string
          id?: string
          is_active?: boolean
          name: string
          opening_balance?: number
          opening_date?: string
          org_id: string
          updated_at?: string | null
        }
        Update: {
          account_no?: string
          bank_name?: string
          branch_name?: string | null
          created_at?: string
          created_by?: string | null
          currency?: string
          id?: string
          is_active?: boolean
          name?: string
          opening_balance?: number
          opening_date?: string
          org_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bank_accounts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_accounts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_accounts_currency_fkey"
            columns: ["currency"]
            isOneToOne: false
            referencedRelation: "currencies"
            referencedColumns: ["code"]
          },
          {
            foreignKeyName: "bank_accounts_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_accounts_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      bank_transactions: {
        Row: {
          amount: number
          balance: number
          bank_account_id: string
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          is_reconciled: boolean
          journal_entry_id: string | null
          org_id: string
          reference: string
          transaction_date: string
          type: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          balance: number
          bank_account_id: string
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          is_reconciled?: boolean
          journal_entry_id?: string | null
          org_id: string
          reference: string
          transaction_date: string
          type: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          balance?: number
          bank_account_id?: string
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          is_reconciled?: boolean
          journal_entry_id?: string | null
          org_id?: string
          reference?: string
          transaction_date?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bank_transactions_bank_account_id_fkey"
            columns: ["bank_account_id"]
            isOneToOne: false
            referencedRelation: "bank_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_transactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_transactions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_transactions_journal_entry_id_fkey"
            columns: ["journal_entry_id"]
            isOneToOne: false
            referencedRelation: "journal_entries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_transactions_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_transactions_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      bill_lines: {
        Row: {
          account_id: string | null
          bill_id: string
          created_at: string
          description: string
          id: string
          line_total: number | null
          quantity: number
          tax_rate_pct: number | null
          unit_price: number
        }
        Insert: {
          account_id?: string | null
          bill_id: string
          created_at?: string
          description: string
          id?: string
          line_total?: number | null
          quantity?: number
          tax_rate_pct?: number | null
          unit_price: number
        }
        Update: {
          account_id?: string | null
          bill_id?: string
          created_at?: string
          description?: string
          id?: string
          line_total?: number | null
          quantity?: number
          tax_rate_pct?: number | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "bill_lines_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bill_lines_bill_id_fkey"
            columns: ["bill_id"]
            isOneToOne: false
            referencedRelation: "bills"
            referencedColumns: ["id"]
          },
        ]
      }
      bills: {
        Row: {
          amount_due: number | null
          bill_number: string
          created_at: string
          created_by: string | null
          date_issued: string
          due_date: string
          id: string
          notes: string | null
          org_id: string
          status: Database["public"]["Enums"]["bill_status"]
          tax_amount: number
          total_amount: number
          updated_at: string | null
          vendor_id: string
          withholding_amount: number
          withholding_tax_rate_id: string | null
        }
        Insert: {
          amount_due?: number | null
          bill_number: string
          created_at?: string
          created_by?: string | null
          date_issued: string
          due_date: string
          id?: string
          notes?: string | null
          org_id: string
          status?: Database["public"]["Enums"]["bill_status"]
          tax_amount?: number
          total_amount: number
          updated_at?: string | null
          vendor_id: string
          withholding_amount?: number
          withholding_tax_rate_id?: string | null
        }
        Update: {
          amount_due?: number | null
          bill_number?: string
          created_at?: string
          created_by?: string | null
          date_issued?: string
          due_date?: string
          id?: string
          notes?: string | null
          org_id?: string
          status?: Database["public"]["Enums"]["bill_status"]
          tax_amount?: number
          total_amount?: number
          updated_at?: string | null
          vendor_id?: string
          withholding_amount?: number
          withholding_tax_rate_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bills_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bills_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bills_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bills_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bills_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bills_withholding_tax_rate_id_fkey"
            columns: ["withholding_tax_rate_id"]
            isOneToOne: false
            referencedRelation: "withholding_tax_rates"
            referencedColumns: ["id"]
          },
        ]
      }
      budget_approvals: {
        Row: {
          actioned_at: string
          approver_id: string
          budget_id: string
          comments: string | null
          id: string
          rejection_reason: string | null
          status: Database["public"]["Enums"]["budget_status"]
        }
        Insert: {
          actioned_at?: string
          approver_id: string
          budget_id: string
          comments?: string | null
          id?: string
          rejection_reason?: string | null
          status: Database["public"]["Enums"]["budget_status"]
        }
        Update: {
          actioned_at?: string
          approver_id?: string
          budget_id?: string
          comments?: string | null
          id?: string
          rejection_reason?: string | null
          status?: Database["public"]["Enums"]["budget_status"]
        }
        Relationships: [
          {
            foreignKeyName: "budget_approvals_approver_id_fkey"
            columns: ["approver_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budget_approvals_approver_id_fkey"
            columns: ["approver_id"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budget_approvals_budget_id_fkey"
            columns: ["budget_id"]
            isOneToOne: false
            referencedRelation: "budgets"
            referencedColumns: ["id"]
          },
        ]
      }
      budget_lines: {
        Row: {
          account_id: string
          amount: number
          budget_id: string
          created_at: string
          id: string
          notes: string | null
        }
        Insert: {
          account_id: string
          amount: number
          budget_id: string
          created_at?: string
          id?: string
          notes?: string | null
        }
        Update: {
          account_id?: string
          amount?: number
          budget_id?: string
          created_at?: string
          id?: string
          notes?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_lines_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budget_lines_budget_id_fkey"
            columns: ["budget_id"]
            isOneToOne: false
            referencedRelation: "budgets"
            referencedColumns: ["id"]
          },
        ]
      }
      budgets: {
        Row: {
          account_id: string | null
          approval_deadline: string | null
          created_at: string
          created_by: string | null
          end_date: string
          id: string
          name: string
          notes: string | null
          org_id: string
          period: string
          start_date: string
          status: Database["public"]["Enums"]["budget_status"]
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          account_id?: string | null
          approval_deadline?: string | null
          created_at?: string
          created_by?: string | null
          end_date: string
          id?: string
          name: string
          notes?: string | null
          org_id: string
          period: string
          start_date: string
          status?: Database["public"]["Enums"]["budget_status"]
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          account_id?: string | null
          approval_deadline?: string | null
          created_at?: string
          created_by?: string | null
          end_date?: string
          id?: string
          name?: string
          notes?: string | null
          org_id?: string
          period?: string
          start_date?: string
          status?: Database["public"]["Enums"]["budget_status"]
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budgets_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budgets_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budgets_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budgets_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budgets_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      countries: {
        Row: {
          business_reg_format: string | null
          created_at: string | null
          date_format: string | null
          default_currency_code: string
          default_timezone: string
          id: string
          is_active: boolean | null
          is_supported: boolean | null
          iso_code: string
          iso3_code: string
          name: string
          number_format: string | null
          official_name: string | null
          phone_prefix: string
          tax_authority_name: string | null
          tax_authority_website: string | null
          tin_format: string | null
          updated_at: string | null
          vat_rate_pct: number | null
          withholding_tax_rate_pct: number | null
        }
        Insert: {
          business_reg_format?: string | null
          created_at?: string | null
          date_format?: string | null
          default_currency_code: string
          default_timezone: string
          id?: string
          is_active?: boolean | null
          is_supported?: boolean | null
          iso_code: string
          iso3_code: string
          name: string
          number_format?: string | null
          official_name?: string | null
          phone_prefix: string
          tax_authority_name?: string | null
          tax_authority_website?: string | null
          tin_format?: string | null
          updated_at?: string | null
          vat_rate_pct?: number | null
          withholding_tax_rate_pct?: number | null
        }
        Update: {
          business_reg_format?: string | null
          created_at?: string | null
          date_format?: string | null
          default_currency_code?: string
          default_timezone?: string
          id?: string
          is_active?: boolean | null
          is_supported?: boolean | null
          iso_code?: string
          iso3_code?: string
          name?: string
          number_format?: string | null
          official_name?: string | null
          phone_prefix?: string
          tax_authority_name?: string | null
          tax_authority_website?: string | null
          tin_format?: string | null
          updated_at?: string | null
          vat_rate_pct?: number | null
          withholding_tax_rate_pct?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "countries_default_currency_code_fkey"
            columns: ["default_currency_code"]
            isOneToOne: false
            referencedRelation: "currencies"
            referencedColumns: ["code"]
          },
        ]
      }
      currencies: {
        Row: {
          code: string
          decimal_places: number
          is_active: boolean
          name: string
          symbol: string
        }
        Insert: {
          code: string
          decimal_places?: number
          is_active?: boolean
          name: string
          symbol: string
        }
        Update: {
          code?: string
          decimal_places?: number
          is_active?: boolean
          name?: string
          symbol?: string
        }
        Relationships: []
      }
      customers: {
        Row: {
          address: string | null
          created_at: string
          email: string | null
          id: string
          is_active: boolean
          name: string
          org_id: string
          payment_terms: number | null
          phone: string | null
          tin_number: string | null
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          is_active?: boolean
          name: string
          org_id: string
          payment_terms?: number | null
          phone?: string | null
          tin_number?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          is_active?: boolean
          name?: string
          org_id?: string
          payment_terms?: number | null
          phone?: string | null
          tin_number?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customers_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customers_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      email_logs: {
        Row: {
          bounced_at: string | null
          clicked_at: string | null
          created_at: string
          delivered_at: string | null
          email_type: string
          error_message: string | null
          external_id: string | null
          id: string
          opened_at: string | null
          org_id: string
          recipient: string
          retry_count: number | null
          sent_at: string
          status: Database["public"]["Enums"]["email_status"]
          subject: string
          updated_at: string | null
        }
        Insert: {
          bounced_at?: string | null
          clicked_at?: string | null
          created_at?: string
          delivered_at?: string | null
          email_type: string
          error_message?: string | null
          external_id?: string | null
          id?: string
          opened_at?: string | null
          org_id: string
          recipient: string
          retry_count?: number | null
          sent_at?: string
          status?: Database["public"]["Enums"]["email_status"]
          subject: string
          updated_at?: string | null
        }
        Update: {
          bounced_at?: string | null
          clicked_at?: string | null
          created_at?: string
          delivered_at?: string | null
          email_type?: string
          error_message?: string | null
          external_id?: string | null
          id?: string
          opened_at?: string | null
          org_id?: string
          recipient?: string
          retry_count?: number | null
          sent_at?: string
          status?: Database["public"]["Enums"]["email_status"]
          subject?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_logs_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_logs_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_lines: {
        Row: {
          account_id: string | null
          created_at: string
          description: string
          id: string
          invoice_id: string
          line_total: number | null
          quantity: number
          tax_rate_pct: number | null
          unit_price: number
        }
        Insert: {
          account_id?: string | null
          created_at?: string
          description: string
          id?: string
          invoice_id: string
          line_total?: number | null
          quantity?: number
          tax_rate_pct?: number | null
          unit_price: number
        }
        Update: {
          account_id?: string | null
          created_at?: string
          description?: string
          id?: string
          invoice_id?: string
          line_total?: number | null
          quantity?: number
          tax_rate_pct?: number | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoice_lines_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_lines_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          amount_due: number | null
          created_at: string
          created_by: string | null
          customer_id: string
          date_issued: string
          due_date: string
          id: string
          invoice_number: string
          notes: string | null
          org_id: string
          status: Database["public"]["Enums"]["invoice_status"]
          tax_amount: number
          total_amount: number
          updated_at: string | null
          withholding_amount: number
          withholding_tax_rate_id: string | null
        }
        Insert: {
          amount_due?: number | null
          created_at?: string
          created_by?: string | null
          customer_id: string
          date_issued: string
          due_date: string
          id?: string
          invoice_number: string
          notes?: string | null
          org_id: string
          status?: Database["public"]["Enums"]["invoice_status"]
          tax_amount?: number
          total_amount: number
          updated_at?: string | null
          withholding_amount?: number
          withholding_tax_rate_id?: string | null
        }
        Update: {
          amount_due?: number | null
          created_at?: string
          created_by?: string | null
          customer_id?: string
          date_issued?: string
          due_date?: string
          id?: string
          invoice_number?: string
          notes?: string | null
          org_id?: string
          status?: Database["public"]["Enums"]["invoice_status"]
          tax_amount?: number
          total_amount?: number
          updated_at?: string | null
          withholding_amount?: number
          withholding_tax_rate_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_withholding_tax_rate_id_fkey"
            columns: ["withholding_tax_rate_id"]
            isOneToOne: false
            referencedRelation: "withholding_tax_rates"
            referencedColumns: ["id"]
          },
        ]
      }
      journal_entries: {
        Row: {
          created_at: string
          created_by: string | null
          date: string
          description: string
          id: string
          is_posted: boolean
          org_id: string
          posted_at: string | null
          reference: string | null
          source_id: string | null
          source_type: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          date: string
          description: string
          id?: string
          is_posted?: boolean
          org_id: string
          posted_at?: string | null
          reference?: string | null
          source_id?: string | null
          source_type?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          date?: string
          description?: string
          id?: string
          is_posted?: boolean
          org_id?: string
          posted_at?: string | null
          reference?: string | null
          source_id?: string | null
          source_type?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "journal_entries_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "journal_entries_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "journal_entries_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "journal_entries_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      mobile_money_accounts: {
        Row: {
          account_name: string
          created_at: string
          created_by: string | null
          id: string
          is_default: boolean
          org_id: string
          phone_number: string
          provider: string
          updated_at: string | null
        }
        Insert: {
          account_name: string
          created_at?: string
          created_by?: string | null
          id?: string
          is_default?: boolean
          org_id: string
          phone_number: string
          provider: string
          updated_at?: string | null
        }
        Update: {
          account_name?: string
          created_at?: string
          created_by?: string | null
          id?: string
          is_default?: boolean
          org_id?: string
          phone_number?: string
          provider?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "mobile_money_accounts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "mobile_money_accounts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "mobile_money_accounts_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "mobile_money_accounts_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_preferences: {
        Row: {
          created_at: string | null
          email_enabled: boolean | null
          enabled: boolean | null
          id: string
          in_app_enabled: boolean | null
          notification_type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          email_enabled?: boolean | null
          enabled?: boolean | null
          id?: string
          in_app_enabled?: boolean | null
          notification_type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          email_enabled?: boolean | null
          enabled?: boolean | null
          id?: string
          in_app_enabled?: boolean | null
          notification_type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notification_preferences_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notification_preferences_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_templates: {
        Row: {
          category: string
          created_at: string | null
          default_priority: string | null
          expires_after_hours: number | null
          id: string
          is_active: boolean | null
          message_template: string
          title_template: string
          type: string
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          default_priority?: string | null
          expires_after_hours?: number | null
          id?: string
          is_active?: boolean | null
          message_template: string
          title_template: string
          type: string
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          default_priority?: string | null
          expires_after_hours?: number | null
          id?: string
          is_active?: boolean | null
          message_template?: string
          title_template?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          category: string
          created_at: string | null
          data: Json | null
          entity_id: string | null
          entity_type: string | null
          expires_at: string | null
          id: string
          is_archived: boolean | null
          is_read: boolean | null
          message: string
          org_id: string
          priority: string | null
          read_at: string | null
          title: string
          type: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          data?: Json | null
          entity_id?: string | null
          entity_type?: string | null
          expires_at?: string | null
          id?: string
          is_archived?: boolean | null
          is_read?: boolean | null
          message: string
          org_id: string
          priority?: string | null
          read_at?: string | null
          title: string
          type: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          data?: Json | null
          entity_id?: string | null
          entity_type?: string | null
          expires_at?: string | null
          id?: string
          is_archived?: boolean | null
          is_read?: boolean | null
          message?: string
          org_id?: string
          priority?: string | null
          read_at?: string | null
          title?: string
          type?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          address: string | null
          business_reg_number: string | null
          country_id: string | null
          created_at: string
          currency_code: string
          description: string | null
          email: string | null
          email_settings: Json | null
          fiscal_year_end: string
          fiscal_year_start: string
          id: string
          name: string
          phone: string | null
          timezone: string
          tin_number: string | null
          updated_at: string | null
          ura_tax_office: string | null
          website: string | null
        }
        Insert: {
          address?: string | null
          business_reg_number?: string | null
          country_id?: string | null
          created_at?: string
          currency_code?: string
          description?: string | null
          email?: string | null
          email_settings?: Json | null
          fiscal_year_end?: string
          fiscal_year_start?: string
          id?: string
          name: string
          phone?: string | null
          timezone?: string
          tin_number?: string | null
          updated_at?: string | null
          ura_tax_office?: string | null
          website?: string | null
        }
        Update: {
          address?: string | null
          business_reg_number?: string | null
          country_id?: string | null
          created_at?: string
          currency_code?: string
          description?: string | null
          email?: string | null
          email_settings?: Json | null
          fiscal_year_end?: string
          fiscal_year_start?: string
          id?: string
          name?: string
          phone?: string | null
          timezone?: string
          tin_number?: string | null
          updated_at?: string | null
          ura_tax_office?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organizations_country_id_fkey"
            columns: ["country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organizations_currency_code_fkey"
            columns: ["currency_code"]
            isOneToOne: false
            referencedRelation: "currencies"
            referencedColumns: ["code"]
          },
        ]
      }
      payment_applications: {
        Row: {
          amount_applied: number
          applied_to_id: string
          applied_to_type: string
          created_at: string
          id: string
          payment_id: string
        }
        Insert: {
          amount_applied: number
          applied_to_id: string
          applied_to_type: string
          created_at?: string
          id?: string
          payment_id: string
        }
        Update: {
          amount_applied?: number
          applied_to_id?: string
          applied_to_type?: string
          created_at?: string
          id?: string
          payment_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "payment_applications_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: false
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_approvals: {
        Row: {
          approver_id: string
          comments: string | null
          created_at: string | null
          id: string
          org_id: string
          payment_id: string
          rejection_reason: string | null
          status: Database["public"]["Enums"]["payment_status"]
          updated_at: string | null
        }
        Insert: {
          approver_id: string
          comments?: string | null
          created_at?: string | null
          id?: string
          org_id: string
          payment_id: string
          rejection_reason?: string | null
          status?: Database["public"]["Enums"]["payment_status"]
          updated_at?: string | null
        }
        Update: {
          approver_id?: string
          comments?: string | null
          created_at?: string | null
          id?: string
          org_id?: string
          payment_id?: string
          rejection_reason?: string | null
          status?: Database["public"]["Enums"]["payment_status"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payment_approvals_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: false
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount: number
          bank_account_id: string | null
          bill_amount: number | null
          bill_id: string | null
          channel: Database["public"]["Enums"]["payment_channel"]
          created_at: string
          created_by: string | null
          currency_code: string
          id: string
          invoice_amount: number | null
          invoice_id: string | null
          is_reconciled: boolean
          mobile_money_number: string | null
          notes: string | null
          org_id: string
          payee_id: string
          payee_type: string
          payment_date: string
          reconciled_at: string | null
          reconciled_by: string | null
          status: Database["public"]["Enums"]["payment_status"] | null
          transaction_id: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          bank_account_id?: string | null
          bill_amount?: number | null
          bill_id?: string | null
          channel: Database["public"]["Enums"]["payment_channel"]
          created_at?: string
          created_by?: string | null
          currency_code?: string
          id?: string
          invoice_amount?: number | null
          invoice_id?: string | null
          is_reconciled?: boolean
          mobile_money_number?: string | null
          notes?: string | null
          org_id: string
          payee_id: string
          payee_type: string
          payment_date: string
          reconciled_at?: string | null
          reconciled_by?: string | null
          status?: Database["public"]["Enums"]["payment_status"] | null
          transaction_id?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          bank_account_id?: string | null
          bill_amount?: number | null
          bill_id?: string | null
          channel?: Database["public"]["Enums"]["payment_channel"]
          created_at?: string
          created_by?: string | null
          currency_code?: string
          id?: string
          invoice_amount?: number | null
          invoice_id?: string | null
          is_reconciled?: boolean
          mobile_money_number?: string | null
          notes?: string | null
          org_id?: string
          payee_id?: string
          payee_type?: string
          payment_date?: string
          reconciled_at?: string | null
          reconciled_by?: string | null
          status?: Database["public"]["Enums"]["payment_status"] | null
          transaction_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payments_bank_account_id_fkey"
            columns: ["bank_account_id"]
            isOneToOne: false
            referencedRelation: "bank_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_bill_id_fkey"
            columns: ["bill_id"]
            isOneToOne: false
            referencedRelation: "bills"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_currency_code_fkey"
            columns: ["currency_code"]
            isOneToOne: false
            referencedRelation: "currencies"
            referencedColumns: ["code"]
          },
          {
            foreignKeyName: "payments_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_reconciled_by_fkey"
            columns: ["reconciled_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_reconciled_by_fkey"
            columns: ["reconciled_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string
          email: string
          id: string
          is_active: boolean
          onboarding_completed_at: string | null
          org_id: string | null
          phone: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string
          email: string
          id: string
          is_active?: boolean
          onboarding_completed_at?: string | null
          org_id?: string | null
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          is_active?: boolean
          onboarding_completed_at?: string | null
          org_id?: string | null
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      recurring_journals: {
        Row: {
          created_at: string
          created_by: string | null
          end_date: string | null
          frequency: string
          id: string
          is_active: boolean
          name: string
          next_date: string
          org_id: string
          start_date: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          end_date?: string | null
          frequency: string
          id?: string
          is_active?: boolean
          name: string
          next_date: string
          org_id: string
          start_date: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          end_date?: string | null
          frequency?: string
          id?: string
          is_active?: boolean
          name?: string
          next_date?: string
          org_id?: string
          start_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recurring_journals_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recurring_journals_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recurring_journals_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recurring_journals_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      recurring_lines: {
        Row: {
          account_id: string
          created_at: string
          credit: number
          debit: number
          description: string | null
          id: string
          recurring_journal_id: string
        }
        Insert: {
          account_id: string
          created_at?: string
          credit?: number
          debit?: number
          description?: string | null
          id?: string
          recurring_journal_id: string
        }
        Update: {
          account_id?: string
          created_at?: string
          credit?: number
          debit?: number
          description?: string | null
          id?: string
          recurring_journal_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "recurring_lines_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recurring_lines_recurring_journal_id_fkey"
            columns: ["recurring_journal_id"]
            isOneToOne: false
            referencedRelation: "recurring_journals"
            referencedColumns: ["id"]
          },
        ]
      }
      restoration_logs: {
        Row: {
          created_at: string
          details: Json | null
          id: string
          log_level: string
          message: string
          operation: string | null
          restoration_id: string
          table_name: string | null
        }
        Insert: {
          created_at?: string
          details?: Json | null
          id?: string
          log_level: string
          message: string
          operation?: string | null
          restoration_id: string
          table_name?: string | null
        }
        Update: {
          created_at?: string
          details?: Json | null
          id?: string
          log_level?: string
          message?: string
          operation?: string | null
          restoration_id?: string
          table_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "restoration_logs_restoration_id_fkey"
            columns: ["restoration_id"]
            isOneToOne: false
            referencedRelation: "backup_restorations"
            referencedColumns: ["id"]
          },
        ]
      }
      tax_rates: {
        Row: {
          created_at: string
          id: string
          is_active: boolean
          name: string
          org_id: string
          rate_pct: number
          updated_at: string | null
          ura_code: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          is_active?: boolean
          name: string
          org_id: string
          rate_pct: number
          updated_at?: string | null
          ura_code?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          is_active?: boolean
          name?: string
          org_id?: string
          rate_pct?: number
          updated_at?: string | null
          ura_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tax_rates_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tax_rates_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      transaction_lines: {
        Row: {
          account_id: string
          created_at: string
          credit: number
          debit: number
          description: string | null
          id: string
          journal_entry_id: string
          org_id: string
          tax_amount: number | null
          tax_rate_id: string | null
        }
        Insert: {
          account_id: string
          created_at?: string
          credit?: number
          debit?: number
          description?: string | null
          id?: string
          journal_entry_id: string
          org_id: string
          tax_amount?: number | null
          tax_rate_id?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string
          credit?: number
          debit?: number
          description?: string | null
          id?: string
          journal_entry_id?: string
          org_id?: string
          tax_amount?: number | null
          tax_rate_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transaction_lines_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transaction_lines_journal_entry_id_fkey"
            columns: ["journal_entry_id"]
            isOneToOne: false
            referencedRelation: "journal_entries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transaction_lines_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transaction_lines_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transaction_lines_tax_rate_id_fkey"
            columns: ["tax_rate_id"]
            isOneToOne: false
            referencedRelation: "withholding_tax_rates"
            referencedColumns: ["id"]
          },
        ]
      }
      ura_tax_filings: {
        Row: {
          amount: number
          created_at: string
          due_date: string
          filed_at: string | null
          filed_by: string | null
          filing_date: string
          filing_type: string
          id: string
          interest: number | null
          org_id: string
          payment_ref_no: string | null
          penalty: number | null
          period: string
          status: string
          total_paid: number
          updated_at: string | null
          ura_ref_no: string | null
        }
        Insert: {
          amount: number
          created_at?: string
          due_date: string
          filed_at?: string | null
          filed_by?: string | null
          filing_date: string
          filing_type: string
          id?: string
          interest?: number | null
          org_id: string
          payment_ref_no?: string | null
          penalty?: number | null
          period: string
          status?: string
          total_paid: number
          updated_at?: string | null
          ura_ref_no?: string | null
        }
        Update: {
          amount?: number
          created_at?: string
          due_date?: string
          filed_at?: string | null
          filed_by?: string | null
          filing_date?: string
          filing_type?: string
          id?: string
          interest?: number | null
          org_id?: string
          payment_ref_no?: string | null
          penalty?: number | null
          period?: string
          status?: string
          total_paid?: number
          updated_at?: string | null
          ura_ref_no?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ura_tax_filings_filed_by_fkey"
            columns: ["filed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ura_tax_filings_filed_by_fkey"
            columns: ["filed_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ura_tax_filings_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ura_tax_filings_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      user_invitations: {
        Row: {
          accepted_at: string | null
          accepted_by: string | null
          email: string
          expires_at: string
          id: string
          invited_at: string
          invited_by: string | null
          org_id: string
          phone: string | null
          role: Database["public"]["Enums"]["user_role"]
          status: Database["public"]["Enums"]["invitation_status"]
          token: string
        }
        Insert: {
          accepted_at?: string | null
          accepted_by?: string | null
          email: string
          expires_at?: string
          id?: string
          invited_at?: string
          invited_by?: string | null
          org_id: string
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          status?: Database["public"]["Enums"]["invitation_status"]
          token: string
        }
        Update: {
          accepted_at?: string | null
          accepted_by?: string | null
          email?: string
          expires_at?: string
          id?: string
          invited_at?: string
          invited_by?: string | null
          org_id?: string
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          status?: Database["public"]["Enums"]["invitation_status"]
          token?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_invitations_accepted_by_fkey"
            columns: ["accepted_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitations_accepted_by_fkey"
            columns: ["accepted_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitations_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitations_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "user_onboarding_status"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      vendors: {
        Row: {
          address: string | null
          created_at: string
          email: string | null
          id: string
          is_active: boolean
          name: string
          org_id: string
          payment_terms: number | null
          phone: string | null
          tin_number: string | null
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          is_active?: boolean
          name: string
          org_id: string
          payment_terms?: number | null
          phone?: string | null
          tin_number?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          is_active?: boolean
          name?: string
          org_id?: string
          payment_terms?: number | null
          phone?: string | null
          tin_number?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "vendors_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vendors_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      withholding_tax_rates: {
        Row: {
          created_at: string
          description: string | null
          id: string
          is_active: boolean
          name: string
          org_id: string
          rate_pct: number
          updated_at: string | null
          ura_code: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          name: string
          org_id: string
          rate_pct: number
          updated_at?: string | null
          ura_code?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          name?: string
          org_id?: string
          rate_pct?: number
          updated_at?: string | null
          ura_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "withholding_tax_rates_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "withholding_tax_rates_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      organization_country_info: {
        Row: {
          country_code: string | null
          country_name: string | null
          currency_code: string | null
          date_format: string | null
          id: string | null
          number_format: string | null
          organization_name: string | null
          phone_prefix: string | null
          tax_authority_name: string | null
          timezone: string | null
          vat_rate_pct: number | null
          withholding_tax_rate_pct: number | null
        }
        Relationships: [
          {
            foreignKeyName: "organizations_currency_code_fkey"
            columns: ["currency_code"]
            isOneToOne: false
            referencedRelation: "currencies"
            referencedColumns: ["code"]
          },
        ]
      }
      user_onboarding_status: {
        Row: {
          created_at: string | null
          email: string | null
          id: string | null
          needs_onboarding: boolean | null
          onboarding_completed: boolean | null
          onboarding_completed_at: string | null
          org_id: string | null
          role: Database["public"]["Enums"]["user_role"] | null
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          id?: string | null
          needs_onboarding?: never
          onboarding_completed?: never
          onboarding_completed_at?: string | null
          org_id?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
        }
        Update: {
          created_at?: string | null
          email?: string | null
          id?: string | null
          needs_onboarding?: never
          onboarding_completed?: never
          onboarding_completed_at?: string | null
          org_id?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization_country_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      analyze_payments_table_structure: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      audit_missing_journal_entries: {
        Args: { org_id_param: string }
        Returns: {
          entity_type: string
          entity_id: string
          entity_reference: string
          entity_date: string
          amount: number
          status: string
        }[]
      }
      cleanup_expired_backups: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_expired_invitations: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_old_email_logs: {
        Args: { retention_days?: number }
        Returns: number
      }
      complete_user_onboarding: {
        Args: { user_id: string }
        Returns: boolean
      }
      create_organization_with_profile: {
        Args: {
          org_name: string
          tin_number?: string
          business_reg_number?: string
          ura_tax_office?: string
          user_phone?: string
          user_role?: Database["public"]["Enums"]["user_role"]
          country_code?: string
          currency_code?: string
        }
        Returns: {
          success: boolean
          organization_id: string
          error_message: string
        }[]
      }
      create_payment_journal_entry: {
        Args: { payment_id: string }
        Returns: undefined
      }
      create_restoration_job: {
        Args: {
          backup_id_param: string
          restore_type_param?: Database["public"]["Enums"]["restore_type"]
          restore_mode_param?: Database["public"]["Enums"]["restore_mode"]
          selected_tables_param?: string[]
          restore_notes_param?: string
        }
        Returns: string
      }
      deactivate_profile: {
        Args: { profile_id: string }
        Returns: boolean
      }
      debug_auth_context: {
        Args: Record<PropertyKey, never>
        Returns: {
          current_user_id: string
          user_role: string
          is_authenticated: boolean
        }[]
      }
      fix_orphaned_users: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      generate_invitation_token: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_missing_journal_entries: {
        Args: Record<PropertyKey, never>
        Returns: {
          entity_type: string
          entity_id: string
          journal_entry_id: string
          status: string
        }[]
      }
      get_backup_statistics: {
        Args: { org_id_param: string }
        Returns: Json
      }
      get_email_stats: {
        Args: { org_uuid: string; days_back?: number }
        Returns: {
          total_sent: number
          total_delivered: number
          total_opened: number
          total_bounced: number
          delivery_rate: number
          open_rate: number
          bounce_rate: number
        }[]
      }
      get_restoration_statistics: {
        Args: { org_id_param: string }
        Returns: Json
      }
      is_profile_active: {
        Args: { profile_id: string }
        Returns: boolean
      }
      reactivate_profile: {
        Args: { profile_id: string }
        Returns: boolean
      }
      setup_default_account_mappings: {
        Args: { org_id_param: string }
        Returns: {
          mapping_type: string
          suggested_account_id: string
          suggested_account_name: string
          suggested_account_code: string
          account_type: string
          status: string
        }[]
      }
      update_email_status: {
        Args: {
          log_external_id: string
          new_status: Database["public"]["Enums"]["email_status"]
          event_timestamp?: string
        }
        Returns: boolean
      }
      update_restoration_progress: {
        Args: {
          restoration_id_param: string
          status_param?: Database["public"]["Enums"]["restore_status"]
          completed_tables_param?: number
          restored_records_param?: number
          error_message_param?: string
        }
        Returns: boolean
      }
      user_needs_onboarding: {
        Args: { user_id: string }
        Returns: boolean
      }
      validate_account_mappings: {
        Args: { org_id_param: string }
        Returns: {
          mapping_type: string
          account_name: string
          account_code: string
          status: string
        }[]
      }
      validate_invitation_token: {
        Args: { invitation_token: string }
        Returns: {
          invitation_id: string
          org_id: string
          email: string
          role: Database["public"]["Enums"]["user_role"]
          phone: string
          is_valid: boolean
          error_message: string
        }[]
      }
    }
    Enums: {
      account_type:
        | "asset"
        | "liability"
        | "equity"
        | "income"
        | "expense"
        | "tax"
      backup_frequency: "hourly" | "daily" | "weekly" | "custom"
      backup_status:
        | "pending"
        | "in_progress"
        | "completed"
        | "failed"
        | "deleted"
      backup_type: "full" | "incremental" | "differential"
      bill_status: "draft" | "approved" | "paid" | "overdue" | "cancelled"
      budget_status: "draft" | "pending_approval" | "approved" | "rejected"
      email_status:
        | "sent"
        | "delivered"
        | "opened"
        | "clicked"
        | "bounced"
        | "failed"
      invitation_status: "pending" | "accepted" | "expired" | "cancelled"
      invoice_status: "draft" | "sent" | "paid" | "overdue" | "cancelled"
      payment_channel: "mtn_momo" | "airtel_money" | "bank" | "cash" | "other"
      payment_status: "pending" | "approved" | "paid" | "rejected"
      restore_mode: "replace" | "merge" | "preview"
      restore_status:
        | "pending"
        | "validating"
        | "downloading"
        | "restoring"
        | "completed"
        | "failed"
        | "cancelled"
      restore_type: "full" | "partial" | "point_in_time"
      storage_provider: "supabase" | "s3" | "gcs" | "azure"
      user_role: "owner" | "admin" | "accountant" | "tax_agent" | "viewer"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      account_type: [
        "asset",
        "liability",
        "equity",
        "income",
        "expense",
        "tax",
      ],
      backup_frequency: ["hourly", "daily", "weekly", "custom"],
      backup_status: [
        "pending",
        "in_progress",
        "completed",
        "failed",
        "deleted",
      ],
      backup_type: ["full", "incremental", "differential"],
      bill_status: ["draft", "approved", "paid", "overdue", "cancelled"],
      budget_status: ["draft", "pending_approval", "approved", "rejected"],
      email_status: [
        "sent",
        "delivered",
        "opened",
        "clicked",
        "bounced",
        "failed",
      ],
      invitation_status: ["pending", "accepted", "expired", "cancelled"],
      invoice_status: ["draft", "sent", "paid", "overdue", "cancelled"],
      payment_channel: ["mtn_momo", "airtel_money", "bank", "cash", "other"],
      payment_status: ["pending", "approved", "paid", "rejected"],
      restore_mode: ["replace", "merge", "preview"],
      restore_status: [
        "pending",
        "validating",
        "downloading",
        "restoring",
        "completed",
        "failed",
        "cancelled",
      ],
      restore_type: ["full", "partial", "point_in_time"],
      storage_provider: ["supabase", "s3", "gcs", "azure"],
      user_role: ["owner", "admin", "accountant", "tax_agent", "viewer"],
    },
  },
} as const
