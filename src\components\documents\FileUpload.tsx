
import React, { useState } from 'react'
import { Upload, File, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/hooks/useAuthHook'

interface FileUploadProps {
  attachedToType: string
  attachedToId: string
  onUploadComplete?: () => void
}

export function FileUpload({ attachedToType, attachedToId, onUploadComplete }: FileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const { toast } = useToast()
  const { user } = useAuth()

  const handleFileUpload = async (files: FileList) => {
    if (!files.length || !user) return

    setUploading(true)
    try {
      for (const file of Array.from(files)) {
        // Upload file to Supabase storage
        const fileExt = file.name.split('.').pop()
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
        const filePath = `attachments/${fileName}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('documents')
          .upload(filePath, file)

        if (uploadError) throw uploadError

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('documents')
          .getPublicUrl(filePath)

        // Save attachment record
        const { error: dbError } = await supabase
          .from('attachments')
          .insert({
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            url: publicUrl,
            attached_to_type: attachedToType,
            attached_to_id: attachedToId,
            uploaded_by: user.id,
            org_id: user.user_metadata?.org_id
          })

        if (dbError) throw dbError
      }

      toast({
        title: "Success",
        description: `${files.length} file(s) uploaded successfully`
      })
      
      onUploadComplete?.()
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: "Upload failed",
        description: "Failed to upload files. Please try again.",
        variant: "destructive"
      })
    } finally {
      setUploading(false)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileUpload(e.dataTransfer.files)
    }
  }

  return (
    <Card className={`border-2 border-dashed transition-colors ${
      dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
    }`}>
      <CardContent 
        className="p-6"
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="text-center">
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <div className="mt-4">
            <label htmlFor="file-upload" className="cursor-pointer">
              <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                Upload files
              </span>
              <span className="text-sm text-gray-500"> or drag and drop</span>
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                className="sr-only"
                multiple
                onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                disabled={uploading}
              />
            </label>
          </div>
          <p className="text-xs text-gray-500">PDF, DOC, XLS, PNG, JPG up to 10MB</p>
          {uploading && (
            <p className="text-sm text-blue-600 mt-2">Uploading...</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
