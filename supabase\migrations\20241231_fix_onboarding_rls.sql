-- =====================================================
-- FIX ONBOARDING RLS ISSUES
-- =====================================================
-- This migration fixes RLS issues preventing organization creation during onboarding
-- Date: 2024-12-31
-- Purpose: Ensure onboarding flow works correctly with RLS policies

-- =====================================================
-- STEP 1: VERIFY AND FIX ORGANIZATIONS TABLE RLS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 STEP 1: Fixing organizations table RLS for onboarding...';
END $$;

-- Enable RLS and recreate organization policies
DO $$
BEGIN
    -- Enable RLS if not already enabled
    ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

    -- Remove existing policies
    DROP POLICY IF EXISTS "Authenticated users can create organizations" ON organizations;
    DROP POLICY IF EXISTS "Users can view their organization" ON organizations;
    DROP POLICY IF EXISTS "Owners and admins can update their organization" ON organizations;

    -- Critical policy for onboarding
    CREATE POLICY "Authenticated users can create organizations" ON organizations
        FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

    -- Standard access policies
    CREATE POLICY "Users can view their organization" ON organizations
        FOR SELECT USING (
            id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
        );

    CREATE POLICY "Owners and admins can update their organization" ON organizations
        FOR UPDATE USING (
            id IN (
                SELECT org_id FROM profiles 
                WHERE id = auth.uid() AND role IN ('owner', 'admin')
            )
        );
    
    RAISE NOTICE '✅ Organizations RLS policies updated';
END $$;

-- =====================================================
-- STEP 2: VERIFY AND FIX ACCOUNTS TABLE RLS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 STEP 2: Fixing accounts table RLS for default accounts creation...';
END $$;

-- Enable RLS and recreate accounts policies
DO $$
BEGIN
    ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;

    -- Remove existing policies
    DROP POLICY IF EXISTS "Users can view accounts in their organization" ON accounts;
    DROP POLICY IF EXISTS "Users can manage accounts in their organization" ON accounts;
    DROP POLICY IF EXISTS "Accountants and above can manage accounts" ON accounts;

    -- Standard access policies
    CREATE POLICY "Users can view accounts in their organization" ON accounts
        FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

    CREATE POLICY "Accountants and above can manage accounts" ON accounts
        FOR ALL USING (org_id IN (
            SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
        ));

    -- Special onboarding policy
    CREATE POLICY "Allow account creation during onboarding" ON accounts
        FOR INSERT WITH CHECK (
            auth.uid() IS NOT NULL AND
            created_by = auth.uid()
        );
    
    RAISE NOTICE '✅ Accounts RLS policies updated';
END $$;

-- =====================================================
-- STEP 3: VERIFY AND FIX PROFILES TABLE RLS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 STEP 3: Verifying profiles table RLS for onboarding...';
END $$;

-- Enable RLS and recreate profiles policies
DO $$
BEGIN
    ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

    -- Remove existing policies
    DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
    DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
    DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;

    -- Standard access policies
    CREATE POLICY "Users can view their own profile" ON profiles
        FOR SELECT USING (id = auth.uid());

    CREATE POLICY "Users can insert their own profile" ON profiles
        FOR INSERT WITH CHECK (id = auth.uid());

    CREATE POLICY "Users can update their own profile" ON profiles
        FOR UPDATE USING (id = auth.uid());
    
    RAISE NOTICE '✅ Profiles RLS policies updated';
END $$;

-- =====================================================
-- STEP 4: CREATE DEBUG FUNCTION (FIXED)
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 STEP 4: Creating authentication verification function...';
END $$;

CREATE OR REPLACE FUNCTION debug_auth_context()
RETURNS TABLE (
    current_user_id UUID,
    user_role TEXT,
    is_authenticated BOOLEAN
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        auth.uid() as current_user_id,
        auth.role() as user_role,  -- Changed to match the declared column name
        (auth.uid() IS NOT NULL) as is_authenticated;
END;
$$;

-- =====================================================
-- STEP 5: CREATE ONBOARDING HELPER FUNCTION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 STEP 5: Creating onboarding helper function...';
END $$;

CREATE OR REPLACE FUNCTION create_organization_with_profile(
    org_name TEXT,
    tin_number TEXT DEFAULT NULL,
    business_reg_number TEXT DEFAULT NULL,
    ura_tax_office TEXT DEFAULT NULL,
    user_phone TEXT DEFAULT NULL,
    user_role TEXT DEFAULT 'accountant'
)
RETURNS TABLE (
    organization_id UUID,
    profile_id UUID,
    success BOOLEAN,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY INVOKER  -- Changed from SECURITY DEFINER to preserve auth.uid() context
AS $$
DECLARE
    new_org_id UUID;
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, FALSE, 'User not authenticated';
        RETURN;
    END IF;
    
    BEGIN
        INSERT INTO organizations (name, tin_number, business_reg_number, ura_tax_office, currency_code, timezone)
        VALUES (org_name, tin_number, business_reg_number, ura_tax_office, 'UGX', 'Africa/Kampala')
        RETURNING id INTO new_org_id;
        
        INSERT INTO profiles (id, email, phone, role, org_id, onboarding_completed_at)
        VALUES (
            current_user_id,
            (SELECT email FROM auth.users WHERE id = current_user_id),
            user_phone,
            user_role::user_role,
            new_org_id,
            NOW()
        );
        
        RETURN QUERY SELECT new_org_id, current_user_id, TRUE, NULL::TEXT;
        
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, FALSE, SQLERRM;
    END;
END;
$$;

-- =====================================================
-- STEP 6: VERIFICATION AND SUMMARY
-- =====================================================

DO $$
DECLARE
    org_policies_count INTEGER;
    accounts_policies_count INTEGER;
    profiles_policies_count INTEGER;
BEGIN
    RAISE NOTICE '🔧 STEP 6: Verifying RLS policies...';
    
    SELECT COUNT(*) INTO org_policies_count FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'organizations';
    
    SELECT COUNT(*) INTO accounts_policies_count FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'accounts';
    
    SELECT COUNT(*) INTO profiles_policies_count FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'profiles';
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ ONBOARDING RLS FIX COMPLETED SUCCESSFULLY';
    RAISE NOTICE '============================================';
    RAISE NOTICE '📊 POLICY COUNTS:';
    RAISE NOTICE '  • Organizations: % policies', org_policies_count;
    RAISE NOTICE '  • Accounts: % policies', accounts_policies_count;
    RAISE NOTICE '  • Profiles: % policies', profiles_policies_count;
    RAISE NOTICE '';
    RAISE NOTICE '🚀 KEY IMPROVEMENTS:';
    RAISE NOTICE '  • Fixed organization creation during onboarding';
    RAISE NOTICE '  • Added proper account creation permissions';
    RAISE NOTICE '  • Ensured profile creation works correctly';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 DIAGNOSTIC TOOLS:';
    RAISE NOTICE '  • debug_auth_context() - Check auth state';
    RAISE NOTICE '  • create_organization_with_profile() - Atomic onboarding';
    RAISE NOTICE '';
    RAISE NOTICE 'Next Steps: Test the onboarding flow in development before production';
END $$;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================