
import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Check, X, Trash } from 'lucide-react'
import { toast } from 'sonner'
import type { Budget } from '@/types/database'

interface BudgetsTableProps {
  budgets: Budget[]
  onEdit: (budget: Budget) => void
  onApproval: (budget: Budget) => void
  onRefresh: () => void
}

export function BudgetsTable({ budgets, onEdit, onApproval, onRefresh }: BudgetsTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (id: string) => {
    setDeletingId(id)
    try {
      const { error } = await supabase
        .from('budgets')
        .delete()
        .eq('id', id)

      if (error) throw error
      
      toast.success('Budget deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting budget:', error)
      toast.error('Failed to delete budget')
    } finally {
      setDeletingId(null)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'default'
      case 'approved':
        return 'default'
      case 'draft':
        return 'secondary'
      case 'rejected':
        return 'destructive'
      case 'pending_approval':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  if (budgets.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No budgets found. Create your first budget to get started.
      </div>
    )
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Account</TableHead>
          <TableHead>Period</TableHead>
          <TableHead>Start Date</TableHead>
          <TableHead>End Date</TableHead>
          <TableHead>Total Amount</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Approval Deadline</TableHead>
          <TableHead className="w-[50px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {budgets.map((budget) => (
          <TableRow key={budget.id}>
            <TableCell className="font-medium">{budget.name}</TableCell>
            <TableCell>
              {budget.accounts ? `${budget.accounts.code} - ${budget.accounts.name}` : '-'}
            </TableCell>
            <TableCell>{budget.period}</TableCell>
            <TableCell>{new Date(budget.start_date).toLocaleDateString()}</TableCell>
            <TableCell>{new Date(budget.end_date).toLocaleDateString()}</TableCell>
            <TableCell>{formatCurrency(budget.total_amount)}</TableCell>
            <TableCell>
              <Badge variant={getStatusColor(budget.status)}>
                {budget.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </TableCell>
            <TableCell>
              {budget.approval_deadline
                ? new Date(budget.approval_deadline).toLocaleDateString()
                : '-'
              }
            </TableCell>
            <TableCell>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(budget)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  {(budget.status === 'draft' || budget.status === 'pending_approval') && (
                    <DropdownMenuItem onClick={() => onApproval(budget)}>
                      <Check className="mr-2 h-4 w-4" />
                      Review/Approve
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={() => handleDelete(budget.id)}
                    disabled={deletingId === budget.id}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
