
import { Button } from '@/components/ui/button'
import { TableCell, TableRow } from '@/components/ui/table'
import { Edit, Trash2 } from 'lucide-react'
import type { Account } from '@/types/database'

interface AccountRowProps {
  account: Account
  parentAccount?: Account
  onEdit: (account: Account) => void
  onDelete: (account: Account) => void
}

export const AccountRow = ({ account, parentAccount, onEdit, onDelete }: AccountRowProps) => {
  return (
    <TableRow>
      <TableCell className="font-mono">{account.code}</TableCell>
      <TableCell>{account.name}</TableCell>
      <TableCell className="capitalize">{account.type}</TableCell>
      <TableCell>
        {parentAccount ? `${parentAccount.code} - ${parentAccount.name}` : '-'}
      </TableCell>
      <TableCell>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onEdit(account)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onDelete(account)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  )
}
