import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Search, Filter, Eye, Edit, Trash2 } from 'lucide-react'
import { PurchaseOrderForm } from '@/components/purchase-orders/PurchaseOrderForm'
import { LoadingPage } from '@/components/ui/loading'
import { 
  usePurchaseOrders, 
  useCreatePurchaseOrder, 
  useUpdatePurchaseOrder,
  useDeletePurchaseOrder,
  useUpdatePurchaseOrderStatus
} from '@/hooks/queries/usePurchaseOrders'
import { useActiveVendors } from '@/hooks/queries'
import { toast } from 'sonner'
import type { 
  PurchaseOrderWithVendor, 
  PurchaseOrderWithDetails,
  PurchaseOrderFormData, 
  PurchaseOrderLineData,
  PurchaseOrderStatus 
} from '@/types/purchase-orders'

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  sent: 'bg-blue-100 text-blue-800',
  confirmed: 'bg-yellow-100 text-yellow-800',
  partially_received: 'bg-orange-100 text-orange-800',
  received: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800'
}

export function PurchaseOrders() {
  const [searchTerm, setSearchTerm] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingPO, setEditingPO] = useState<PurchaseOrderWithDetails | null>(null)

  const { data: purchaseOrders = [], isLoading } = usePurchaseOrders()
  const { data: vendors = [] } = useActiveVendors()
  const createPOMutation = useCreatePurchaseOrder()
  const updatePOMutation = useUpdatePurchaseOrder()
  const deletePOMutation = useDeletePurchaseOrder()
  const updateStatusMutation = useUpdatePurchaseOrderStatus()

  const filteredPOs = purchaseOrders.filter(po =>
    po.po_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    po.vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    po.notes?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSubmit = async (formData: PurchaseOrderFormData, lines: PurchaseOrderLineData[]) => {
    try {
      if (editingPO) {
        await updatePOMutation.mutateAsync({
          purchaseOrderId: editingPO.id,
          formData,
          lines
        })
      } else {
        await createPOMutation.mutateAsync({ formData, lines })
      }
      setIsDialogOpen(false)
      setEditingPO(null)
    } catch (error) {
      console.error('Error submitting purchase order:', error)
    }
  }

  const handleEdit = (po: PurchaseOrderWithVendor) => {
    // Convert to PurchaseOrderWithDetails format for editing
    const poWithDetails: PurchaseOrderWithDetails = {
      ...po,
      lines: [], // Will be loaded in the form
      receipts: []
    }
    setEditingPO(poWithDetails)
    setIsDialogOpen(true)
  }

  const handleDelete = async (poId: string) => {
    if (confirm('Are you sure you want to delete this purchase order?')) {
      try {
        await deletePOMutation.mutateAsync(poId)
      } catch (error) {
        console.error('Error deleting purchase order:', error)
      }
    }
  }

  const handleStatusChange = async (po: PurchaseOrderWithVendor, newStatus: PurchaseOrderStatus) => {
    try {
      await updateStatusMutation.mutateAsync({
        purchaseOrderId: po.id,
        status: newStatus
      })
    } catch (error) {
      console.error('Error updating purchase order status:', error)
    }
  }

  if (isLoading) {
    return <LoadingPage />
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Purchase Orders</h1>
          <p className="text-muted-foreground">
            Manage purchase orders and vendor procurement
          </p>
        </div>
        <Button onClick={() => setIsDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Purchase Order
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search purchase orders..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </Button>
      </div>

      <div className="grid gap-4">
        {filteredPOs.map((po) => (
          <Card key={po.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div>
                    <CardTitle className="text-lg">{po.po_number}</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {po.vendor.name}
                    </p>
                  </div>
                  <Badge className={statusColors[po.status]}>
                    {po.status.replace('_', ' ')}
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleEdit(po)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDelete(po.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Date Issued</p>
                  <p className="font-medium">{new Date(po.date_issued).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Expected Delivery</p>
                  <p className="font-medium">
                    {po.expected_delivery_date 
                      ? new Date(po.expected_delivery_date).toLocaleDateString()
                      : 'Not specified'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">Total Amount</p>
                  <p className="font-medium">UGX {po.total_amount.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Status Actions</p>
                  <div className="flex space-x-1">
                    {po.status === 'draft' && (
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleStatusChange(po, 'sent')}
                      >
                        Send
                      </Button>
                    )}
                    {po.status === 'sent' && (
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleStatusChange(po, 'confirmed')}
                      >
                        Confirm
                      </Button>
                    )}
                    {(po.status === 'confirmed' || po.status === 'partially_received') && (
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleStatusChange(po, 'received')}
                      >
                        Receive
                      </Button>
                    )}
                  </div>
                </div>
              </div>
              {po.notes && (
                <div className="mt-3 pt-3 border-t">
                  <p className="text-sm text-muted-foreground">{po.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPOs.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No purchase orders found.</p>
          <Button 
            className="mt-4" 
            onClick={() => setIsDialogOpen(true)}
          >
            Create your first purchase order
          </Button>
        </div>
      )}

      <PurchaseOrderForm
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open)
          if (!open) {
            setEditingPO(null)
          }
        }}
        editingPO={editingPO}
        vendors={vendors}
        onSubmit={handleSubmit}
      />
    </div>
  )
}
