# Invoice Export Feature

## Overview

The invoice export feature allows users to export their invoice data in multiple formats with various filtering options. This feature is designed to help users analyze their sales data, create reports, and integrate with external systems.

## Recent Updates

### Account Selection Field Fix
- **Fixed**: Missing account selection field in navbar quick action invoice modal
- **Improvement**: Both main invoice form and navbar quick action now have consistent account selection behavior
- **User Preference**: Account selection at form level (next to Add Line button) rather than per line item

## Features

### Export Formats

1. **CSV (Comma Separated Values)**
   - Compatible with Excel, Google Sheets, and other spreadsheet applications
   - Two modes: Summary view and detailed view with line items
   - Properly escaped fields to handle commas and quotes in data

2. **JSON (JavaScript Object Notation)**
   - Machine-readable format for API integrations
   - Includes formatted currency values
   - Contains metadata about the export

### Export Options

#### Include Line Items
- **Summary Mode**: Exports only invoice-level data (default)
- **Detailed Mode**: Includes all line items with account codes and descriptions

#### Filtering Options

1. **Date Range Filter**
   - Filter invoices by issue date
   - Specify start and end dates
   - Useful for monthly/quarterly reports

2. **Status Filter**
   - Filter by invoice status: Draft, Sent, Paid, Overdue, Cancelled
   - Multiple statuses can be selected
   - Helps focus on specific invoice states

3. **Customer Filter**
   - Export invoices for a specific customer
   - Useful for customer statements and analysis

## Usage

### From the Invoices Page

1. Navigate to the Invoices page
2. Click the "Export" button in the top-right corner
3. Configure your export options in the dialog
4. Click "Export" to download the file

### Export Dialog Options

1. **Select Format**: Choose between CSV or JSON
2. **Include Line Items**: Toggle to include detailed line item data
3. **Date Range**: Optionally filter by date range
4. **Status Filter**: Optionally filter by invoice status
5. **Customer Filter**: Optionally filter by specific customer

## File Naming Convention

Exported files follow this naming pattern:
```
invoices-export-YYYY-MM-DD[-additional-filters].format
```

Examples:
- `invoices-export-2025-06-29.csv`
- `invoices-export-2025-06-29-paid-sent.csv`
- `invoices-export-2025-06-29-2025-01-01-to-2025-06-29.json`

## CSV Format Details

### Summary Mode Columns
- Invoice Number
- Customer Name
- Customer Email
- Date Issued
- Due Date
- Status
- Subtotal
- Tax Amount
- Total Amount
- Notes
- Created At

### Detailed Mode Columns
- Invoice Number
- Customer Name
- Customer Email
- Date Issued
- Due Date
- Status
- Line Item Description
- Account Code
- Account Name
- Quantity
- Unit Price
- Line Total
- Tax Rate %
- Tax Amount
- Invoice Total
- Notes

## JSON Format Structure

```json
{
  "exportDate": "2025-06-29T10:30:00.000Z",
  "totalInvoices": 25,
  "totalAmount": 150000,
  "invoices": [
    {
      "id": "uuid",
      "invoice_number": "INV-001",
      "customer_id": "uuid",
      "date_issued": "2025-06-29",
      "due_date": "2025-07-29",
      "total_amount": 6000,
      "tax_amount": 1000,
      "status": "sent",
      "formattedTotal": "UGX 6,000",
      "formattedTax": "UGX 1,000",
      "formattedSubtotal": "UGX 5,000",
      "customers": {
        "name": "Customer Name",
        "email": "<EMAIL>"
      },
      "invoice_lines": [...]
    }
  ]
}
```

## Technical Implementation

### Files
- `src/lib/invoiceExport.ts` - Core export logic
- `src/components/invoices/InvoiceExportDialog.tsx` - Export dialog UI
- `src/hooks/useInvoiceExport.ts` - React hook for export functionality

### Key Functions
- `exportInvoices()` - Main export function
- `fetchInvoicesForExport()` - Data fetching with filters
- `exportInvoicesToCSV()` - CSV generation
- `exportInvoicesToJSON()` - JSON generation
- `downloadExportFile()` - File download utility

## Error Handling

The export feature includes comprehensive error handling:
- Validation of export options
- Database query error handling
- File generation error handling
- User-friendly error messages via toast notifications

## Performance Considerations

- Large exports are handled efficiently with streaming
- Database queries are optimized with proper indexing
- Memory usage is minimized for large datasets
- Export operations are performed asynchronously

## Security

- Exports are limited to the user's organization data
- Row Level Security (RLS) policies are enforced
- No sensitive data is exposed in exports
- File downloads are handled securely in the browser

## Future Enhancements

Potential future improvements:
- PDF export format
- Email delivery of exports
- Scheduled exports
- Custom field selection
- Export templates
- Bulk export operations
