import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Package, 
  Edit, 
  MapPin, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Barcode,
  Weight,
  Ruler,
  DollarSign,
  Activity
} from 'lucide-react'
import { 
  useProduct, 
  useStockLevelsByProduct, 
  useInventoryTransactionsByProduct 
} from '@/hooks/queries'
import type { ProductWithStock } from '@/types/inventory'

interface ProductDetailViewProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  productId: string | null
  onEdit?: (product: ProductWithStock) => void
}

export function ProductDetailView({ 
  open, 
  onOpenChange, 
  productId, 
  onEdit 
}: ProductDetailViewProps) {
  const { data: product, isLoading } = useProduct(productId || undefined)
  const { data: stockLevels = [] } = useStockLevelsByProduct(productId || undefined)
  const { data: recentTransactions = [] } = useInventoryTransactionsByProduct(productId || undefined)

  if (!productId) return null

  const getStockStatusBadge = (product: ProductWithStock) => {
    if (!product.track_inventory) {
      return <Badge variant="secondary">Not Tracked</Badge>
    }

    const available = product.total_quantity_available || 0

    if (available <= 0) {
      return <Badge variant="destructive">Out of Stock</Badge>
    }

    if (product.is_low_stock) {
      return <Badge variant="outline" className="border-orange-500 text-orange-600">Low Stock</Badge>
    }

    return <Badge variant="default" className="bg-green-100 text-green-800">In Stock</Badge>
  }

  if (!open) return null

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-y-auto w-full">
        <div className="p-6">
          <div className="flex items-center gap-2 mb-2">
            <Package className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Product Details</h2>
          </div>
          <p className="text-gray-600 mb-6">View detailed information about this product</p>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">Loading product details...</div>
          </div>
        ) : !product ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center text-red-600">Product not found</div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Header with Edit Button */}
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-2xl font-bold">{product.name}</h2>
                <p className="text-gray-600 mt-1">{product.description}</p>
                <div className="flex items-center gap-2 mt-2">
                  {getStockStatusBadge(product)}
                  <Badge variant={product.is_active ? "default" : "secondary"}>
                    {product.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                  {product.is_sellable && <Badge variant="outline">Sellable</Badge>}
                  {product.is_purchasable && <Badge variant="outline">Purchasable</Badge>}
                </div>
              </div>
              {onEdit && (
                <Button onClick={() => onEdit(product)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Product
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">SKU:</span>
                    <span className="font-mono">{product.sku}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category:</span>
                    <span>{product.category?.name || 'Uncategorized'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Unit of Measure:</span>
                    <span>{product.unit_of_measure}</span>
                  </div>
                  {product.barcode && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 flex items-center gap-1">
                        <Barcode className="h-4 w-4" />
                        Barcode:
                      </span>
                      <span className="font-mono">{product.barcode}</span>
                    </div>
                  )}
                  {product.weight && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 flex items-center gap-1">
                        <Weight className="h-4 w-4" />
                        Weight:
                      </span>
                      <span>{product.weight} kg</span>
                    </div>
                  )}
                  {product.dimensions && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 flex items-center gap-1">
                        <Ruler className="h-4 w-4" />
                        Dimensions:
                      </span>
                      <span>{product.dimensions}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Pricing Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Pricing
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Cost Price:</span>
                    <span className="font-semibold">${product.cost_price?.toFixed(2) || '0.00'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Selling Price:</span>
                    <span className="font-semibold">${product.selling_price?.toFixed(2) || '0.00'}</span>
                  </div>
                  {product.cost_price && product.selling_price && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Margin:</span>
                      <span className="font-semibold text-green-600">
                        {(((product.selling_price - product.cost_price) / product.selling_price) * 100).toFixed(1)}%
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Inventory Information */}
            {product.track_inventory && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Inventory Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {product.total_quantity_on_hand || 0}
                      </div>
                      <div className="text-sm text-gray-600">On Hand</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {product.total_quantity_available || 0}
                      </div>
                      <div className="text-sm text-gray-600">Available</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {product.reorder_level || 0}
                      </div>
                      <div className="text-sm text-gray-600">Reorder Level</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {product.reorder_quantity || 0}
                      </div>
                      <div className="text-sm text-gray-600">Reorder Qty</div>
                    </div>
                  </div>

                  {/* Stock by Location */}
                  {stockLevels.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        Stock by Location
                      </h4>
                      <div className="border rounded-lg">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Location</TableHead>
                              <TableHead>On Hand</TableHead>
                              <TableHead>Reserved</TableHead>
                              <TableHead>Available</TableHead>
                              <TableHead>Avg Cost</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {stockLevels.map((stock) => (
                              <TableRow key={stock.id}>
                                <TableCell>{stock.location?.name}</TableCell>
                                <TableCell className="font-mono">
                                  {stock.quantity_on_hand || 0}
                                </TableCell>
                                <TableCell className="font-mono">
                                  {stock.quantity_reserved || 0}
                                </TableCell>
                                <TableCell className="font-mono">
                                  {stock.quantity_available || 0}
                                </TableCell>
                                <TableCell className="font-mono">
                                  ${stock.average_cost?.toFixed(4) || '0.0000'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Recent Transactions */}
            {recentTransactions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Recent Transactions
                  </CardTitle>
                  <CardDescription>
                    Last 10 inventory movements for this product
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Location</TableHead>
                          <TableHead>Quantity</TableHead>
                          <TableHead>Reference</TableHead>
                          <TableHead>Notes</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {recentTransactions.slice(0, 10).map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>
                              {new Date(transaction.transaction_date || '').toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {transaction.transaction_type}
                              </Badge>
                            </TableCell>
                            <TableCell>{transaction.location?.name}</TableCell>
                            <TableCell className={`font-mono ${
                              (transaction.quantity || 0) > 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {transaction.quantity > 0 ? '+' : ''}{transaction.quantity}
                            </TableCell>
                            <TableCell>
                              {transaction.reference_number || '-'}
                            </TableCell>
                            <TableCell className="max-w-[200px] truncate">
                              {transaction.notes || '-'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
        </div>
      </div>
    </div>
  )
}
