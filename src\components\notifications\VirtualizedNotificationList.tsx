/**
 * Virtualized Notification List Component
 * Handles large notification lists with virtual scrolling and infinite loading
 */

import React, { useRef, useEffect, useState } from 'react'
import { FixedSizeList as List } from 'react-window'
import InfiniteLoader from 'react-window-infinite-loader'
import { NotificationItem } from './NotificationItem'
import { EmptyNotifications } from './EmptyNotifications'
import { LoadingSpinner } from '@/components/ui/loading'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { RefreshCw, AlertCircle } from 'lucide-react'
import { useNotificationPagination, useInfiniteScroll } from '@/hooks/useNotificationPagination'
import type { NotificationFilters, NotificationWithMeta } from '@/types/notifications'

interface VirtualizedNotificationListProps {
  filters?: NotificationFilters
  notifications?: NotificationWithMeta[]
  onNotificationClick?: () => void
  height?: number
  itemHeight?: number
  className?: string
  enableVirtualScrolling?: boolean
  selectedNotifications?: Set<string>
  onSelectNotification?: (notificationId: string, checked: boolean) => void
}

interface NotificationRowProps {
  index: number
  style: React.CSSProperties
  data: {
    notifications: NotificationWithMeta[]
    onNotificationClick?: () => void
    isItemLoaded: (index: number) => boolean
    loadMoreItems: () => Promise<void>
    selectedNotifications?: Set<string>
    onSelectNotification?: (notificationId: string, checked: boolean) => void
  }
}

// Individual notification row component for virtualization
const NotificationRow: React.FC<NotificationRowProps> = ({ index, style, data }) => {
  const {
    notifications,
    onNotificationClick,
    isItemLoaded,
    selectedNotifications,
    onSelectNotification
  } = data
  const notification = notifications[index]

  // Show loading placeholder if item is not loaded
  if (!isItemLoaded(index)) {
    return (
      <div style={style} className="flex items-center justify-center p-4">
        <LoadingSpinner size="sm" />
      </div>
    )
  }

  // Show notification item with selection support
  return (
    <div style={style} className="flex items-center gap-3 p-3 border-b">
      {selectedNotifications && onSelectNotification && (
        <Checkbox
          checked={selectedNotifications.has(notification.id)}
          onCheckedChange={(checked) =>
            onSelectNotification(notification.id, checked as boolean)
          }
        />
      )}
      <div className="flex-1">
        <NotificationItem
          notification={notification}
          onClick={onNotificationClick}
        />
      </div>
    </div>
  )
}

export function VirtualizedNotificationList({
  filters,
  notifications: propNotifications,
  onNotificationClick,
  height = 400,
  itemHeight = 80,
  className = '',
  enableVirtualScrolling = true,
  selectedNotifications,
  onSelectNotification
}: VirtualizedNotificationListProps) {
  const listRef = useRef<List>(null)
  const [containerHeight, setContainerHeight] = useState(height)

  // Use provided notifications or fetch them
  const paginationResult = useNotificationPagination(
    propNotifications ? undefined : filters,
    {
      pageSize: 20,
      enableVirtualScrolling,
      preloadPages: 2
    }
  )

  const {
    notifications: fetchedNotifications,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    isEmpty,
    isFirstLoad,
    loadMore,
    refresh,
    totalItems
  } = paginationResult

  // Use provided notifications or fetched ones
  const notifications = propNotifications || fetchedNotifications

  // Setup infinite scroll for non-virtualized mode
  const { loadingRef } = useInfiniteScroll(
    loadMore,
    hasMore,
    isLoadingMore,
    200
  )

  // Check if item is loaded for infinite loader
  const isItemLoaded = (index: number) => {
    return index < notifications.length
  }

  // Load more items for infinite loader
  const loadMoreItems = async () => {
    if (hasMore && !isLoadingMore) {
      await loadMore()
    }
  }

  // Handle container resize
  useEffect(() => {
    const handleResize = () => {
      setContainerHeight(height)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [height])

  // Error state
  if (error && !isLoading) {
    return (
      <div className={`flex flex-col items-center justify-center p-8 ${className}`}>
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium text-foreground mb-2">Failed to load notifications</h3>
        <p className="text-sm text-muted-foreground mb-4 text-center">{error}</p>
        <Button onClick={refresh} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    )
  }

  // Empty state
  if (isEmpty && !isFirstLoad) {
    return (
      <div className={className}>
        <EmptyNotifications type="all" isLoading={false} />
      </div>
    )
  }

  // Loading state for first load
  if (isFirstLoad) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <LoadingSpinner size="md" text="Loading notifications..." />
      </div>
    )
  }

  // Virtualized list for large datasets
  if (enableVirtualScrolling && totalItems > 50) {
    return (
      <div className={`${className}`} style={{ height: containerHeight }}>
        <InfiniteLoader
          isItemLoaded={isItemLoaded}
          itemCount={hasMore ? notifications.length + 1 : notifications.length}
          loadMoreItems={loadMoreItems}
          threshold={5}
        >
          {({ onItemsRendered, ref }) => (
            <List
              ref={(list) => {
                listRef.current = list
                ref(list)
              }}
              height={containerHeight}
              itemCount={hasMore ? notifications.length + 1 : notifications.length}
              itemSize={itemHeight}
              onItemsRendered={onItemsRendered}
              itemData={{
                notifications,
                onNotificationClick,
                isItemLoaded,
                loadMoreItems,
                selectedNotifications,
                onSelectNotification
              }}
              overscanCount={5}
              className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
            >
              {NotificationRow}
            </List>
          )}
        </InfiniteLoader>

        {/* Loading indicator for more items */}
        {isLoadingMore && (
          <div className="flex items-center justify-center p-4 border-t">
            <LoadingSpinner size="sm" text="Loading more..." />
          </div>
        )}
      </div>
    )
  }

  // Regular scrollable list for smaller datasets
  return (
    <div className={`${className}`} style={{ maxHeight: containerHeight, overflowY: 'auto' }}>
      <div className="divide-y">
        {notifications.map((notification) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onClick={onNotificationClick}
          />
        ))}
      </div>

      {/* Load more trigger for infinite scroll */}
      {hasMore && (
        <div ref={loadingRef} className="flex items-center justify-center p-4">
          {isLoadingMore ? (
            <LoadingSpinner size="sm" text="Loading more..." />
          ) : (
            <Button
              onClick={loadMore}
              variant="ghost"
              size="sm"
              className="text-muted-foreground"
            >
              Load more notifications
            </Button>
          )}
        </div>
      )}

      {/* End of list indicator */}
      {!hasMore && notifications.length > 0 && (
        <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
          You've reached the end of your notifications
        </div>
      )}
    </div>
  )
}

/**
 * Compact virtualized notification list for dropdowns
 */
export function CompactNotificationList({
  filters,
  onNotificationClick,
  maxHeight = 300,
  className = ''
}: {
  filters?: NotificationFilters
  onNotificationClick?: () => void
  maxHeight?: number
  className?: string
}) {
  return (
    <VirtualizedNotificationList
      filters={filters}
      onNotificationClick={onNotificationClick}
      height={maxHeight}
      itemHeight={60} // Smaller item height for compact view
      className={className}
      enableVirtualScrolling={false} // Disable virtualization for dropdown
    />
  )
}

/**
 * Full-page notification list with virtualization
 */
export function FullPageNotificationList({
  filters,
  className = ''
}: {
  filters?: NotificationFilters
  className?: string
}) {
  return (
    <div className={`h-full ${className}`}>
      <VirtualizedNotificationList
        filters={filters}
        height={600} // Full height
        itemHeight={80}
        enableVirtualScrolling={true}
        className="h-full"
      />
    </div>
  )
}
