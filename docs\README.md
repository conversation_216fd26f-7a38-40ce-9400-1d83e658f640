# KAYA Finance

**Cloud-based Financial Management Software for Ugandan Businesses**

KAYA Finance is a comprehensive financial management solution developed by Tom's Cyber Lab (U) Ltd, designed specifically for businesses operating in Uganda. The platform combines modern cloud technology with offline capabilities to ensure your business operations continue seamlessly regardless of internet connectivity.

## 🚀 Key Features

### 📊 **Complete Accounting Suite**
- **Chart of Accounts**: Pre-configured for Ugandan businesses with customization options
- **Journal Entries**: Double-entry bookkeeping with automated calculations
- **Recurring Journals**: Automate repetitive transactions
- **Financial Reporting**: Trial Balance, P&L, Balance Sheet, Cash Flow statements

### 💼 **Business Management**
- **Customer Management**: Complete customer database with contact information
- **Vendor Management**: Supplier tracking and payment terms
- **Invoice Management**: Professional invoicing with customizable templates
- **Bill Management**: Track and manage supplier bills
- **Payment Processing**: Comprehensive payment tracking and reconciliation

### 🏦 **Uganda-Specific Integrations**
- **URA Integration**: Direct integration with Uganda Revenue Authority for tax compliance
- **Mobile Money Support**: Native MTN Mobile Money and Airtel Money integration
- **Bank Integration**: Connect with major Ugandan banks for automatic transaction import
- **Tax Management**: VAT, PAYE, and withholding tax calculations

### 📱 **Modern Technology Stack**
- **Cloud-First**: Secure cloud infrastructure with real-time synchronization
- **Offline Capability**: Continue working even without internet connectivity
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Real-time Collaboration**: Multi-user support with role-based permissions

### 🔒 **Security & Compliance**
- **Data Protection**: Compliant with Uganda Data Protection and Privacy Act 2019
- **Audit Trail**: Complete activity logging for compliance and security
- **Role-Based Access**: Granular permissions for different user types
- **Backup & Recovery**: Automated backups with comprehensive data recovery capabilities

## 🛠️ Technology Stack

This project is built with modern, industry-standard technologies:

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Framework**: shadcn/ui components with Tailwind CSS
- **Backend**: Supabase (PostgreSQL) for database and authentication
- **State Management**: TanStack Query for server state management
- **Form Handling**: React Hook Form with Zod validation
- **Charts & Analytics**: Recharts for data visualization

## 🏗️ Development Setup

### Prerequisites
- Node.js 18+ and npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- Git for version control

### Getting Started

```sh
# Clone the repository
git clone <repository-url>
cd kaya-financial-scribe

# Install dependencies
npm install

# Start the development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Development Commands

```sh
# Run linting
npm run lint

# Build for development (with source maps)
npm run build:dev
```

## 💾 Backup & Restoration System

KAYA Finance includes a comprehensive backup and restoration system designed to protect your financial data and ensure business continuity.

### 🔄 **Backup Process Flow**

```mermaid
graph TD
    A[Scheduled Backup Trigger] --> B[Initialize Backup Job]
    B --> C[Validate Organization Settings]
    C --> D{Auto Backup Enabled?}
    D -->|No| E[Skip Backup]
    D -->|Yes| F[Determine Backup Type]
    F --> G{Backup Type}
    G -->|Full| H[Export All Tables]
    G -->|Incremental| I[Export Changed Data Since Last Backup]
    G -->|Differential| J[Export Changed Data Since Last Full Backup]
    H --> K[Compress & Encrypt Data]
    I --> K
    J --> K
    K --> L[Upload to Storage]
    L --> M[Verify Upload & Checksum]
    M --> N{Upload Successful?}
    N -->|No| O[Retry Upload]
    N -->|Yes| P[Update Backup Metadata]
    P --> Q[Send Notification]
    Q --> R[Schedule Next Backup]
    O --> S{Max Retries Reached?}
    S -->|No| L
    S -->|Yes| T[Mark as Failed & Alert]
```

### 🔧 **Backup Configuration**

#### **Backup Types**
- **Full Backup**: Complete database export (recommended weekly)
- **Incremental Backup**: Only changes since last backup (recommended daily)
- **Differential Backup**: Changes since last full backup (recommended for large datasets)

#### **Backup Settings**
```sql
-- Organization backup configuration
{
  "auto_backup_enabled": true,
  "backup_frequency": "daily",
  "backup_time": "03:00:00",
  "backup_days": [1, 2, 3, 4, 5], -- Monday to Friday
  "retention_days": 90,
  "storage_provider": "supabase",
  "notification_email": "<EMAIL>",
  "notify_on_success": false,
  "notify_on_failure": true
}
```

#### **Automated Scheduling**
- **Daily Backups**: Incremental backups at 3:00 AM
- **Weekly Backups**: Full backups every Sunday
- **Custom Schedules**: Configurable per organization
- **Retention Policy**: Automatic cleanup of expired backups

### 🔄 **Restoration Process Flow**

```mermaid
graph TD
    A[User Requests Restoration] --> B[Select Backup & Options]
    B --> C[Validate User Permissions]
    C --> D{Admin User?}
    D -->|No| E[Request Admin Approval]
    D -->|Yes| F[Create Restoration Job]
    E --> G[Admin Reviews Request]
    G --> H{Approved?}
    H -->|No| I[Notify User - Denied]
    H -->|Yes| F
    F --> J[Validate Backup Integrity]
    J --> K{Backup Valid?}
    K -->|No| L[Report Validation Error]
    K -->|Yes| M[Create Pre-Restore Snapshot]
    M --> N[Download Backup File]
    N --> O[Verify Checksum]
    O --> P{Checksum Valid?}
    P -->|No| Q[Report Corruption Error]
    P -->|Yes| R[Begin Restoration]
    R --> S[Restore Tables Sequentially]
    S --> T[Update Progress]
    T --> U{More Tables?}
    U -->|Yes| S
    U -->|No| V[Verify Restored Data]
    V --> W[Complete Restoration]
    W --> X[Send Success Notification]
    L --> Y[Log Error & Notify]
    Q --> Y
```

### 🎯 **Restoration Types**

#### **1. Full Restoration**
- **Purpose**: Complete database recovery
- **Use Case**: System failure, data corruption, disaster recovery
- **Process**: Replaces all data with backup version
- **Downtime**: Requires system maintenance window

```sql
-- Example: Full restoration
SELECT create_restoration_job(
    backup_id := 'backup-uuid-here',
    restore_type := 'full',
    restore_mode := 'replace',
    restore_notes := 'Emergency restore due to system failure'
);
```

#### **2. Partial Restoration**
- **Purpose**: Selective data recovery
- **Use Case**: Accidental deletion, specific table corruption
- **Process**: Restores only selected tables/data
- **Downtime**: Minimal, table-specific

```sql
-- Example: Restore specific tables
SELECT create_restoration_job(
    backup_id := 'backup-uuid-here',
    restore_type := 'partial',
    restore_mode := 'merge',
    selected_tables := ARRAY['customers', 'invoices', 'payments']
);
```

#### **3. Point-in-Time Restoration**
- **Purpose**: Restore to specific timestamp
- **Use Case**: Undo changes made after specific time
- **Process**: Combines full + incremental backups
- **Downtime**: Moderate, depends on data volume

```sql
-- Example: Restore to specific point in time
SELECT create_restoration_job(
    backup_id := 'base-backup-uuid',
    restore_type := 'point_in_time',
    restore_point := '2024-01-15 14:30:00'
);
```

### 📊 **Backup Monitoring & Statistics**

#### **Real-Time Monitoring**
```sql
-- Get backup statistics for organization
SELECT get_backup_statistics('org-uuid-here');

-- Returns:
{
  "total_backups": 45,
  "successful_backups": 43,
  "failed_backups": 2,
  "total_size_gb": 12.5,
  "average_size_mb": 285,
  "last_backup": "2024-01-15T03:00:00Z",
  "next_scheduled": "2024-01-16T03:00:00Z",
  "recent_backups": [...]
}
```

#### **Progress Tracking**
- **Backup Progress**: Real-time table and record counts
- **Restoration Progress**: Percentage completion with ETA
- **Error Logging**: Detailed logs for troubleshooting
- **Notifications**: Email alerts for success/failure

### 🔒 **Security & Compliance**

#### **Data Protection**
- **Encryption**: AES-256 encryption for all backup files
- **Access Control**: Role-based permissions for backup operations
- **Audit Trail**: Complete logging of all backup/restore activities
- **Compliance**: Meets Uganda Data Protection Act requirements

#### **Storage Security**
- **Multiple Providers**: Support for Supabase, AWS S3, Google Cloud, Azure
- **Redundancy**: Cross-region backup storage options
- **Retention Policies**: Configurable data retention periods
- **Secure Deletion**: Cryptographic erasure of expired backups

### 🚨 **Disaster Recovery**

#### **Recovery Time Objectives (RTO)**
- **Full Restoration**: 2-4 hours (depending on data size)
- **Partial Restoration**: 15-60 minutes
- **Point-in-Time**: 1-3 hours

#### **Recovery Point Objectives (RPO)**
- **Daily Backups**: Maximum 24 hours data loss
- **Hourly Backups**: Maximum 1 hour data loss (enterprise)
- **Real-Time Replication**: Near-zero data loss (premium)

#### **Business Continuity**
- **Automated Failover**: Automatic switching to backup systems
- **Data Validation**: Integrity checks before and after restoration
- **Rollback Capability**: Ability to revert failed restorations
- **Documentation**: Step-by-step recovery procedures

### 📋 **Best Practices**

#### **For Organizations**
1. **Regular Testing**: Test restoration procedures monthly
2. **Multiple Backup Types**: Combine full, incremental, and differential
3. **Offsite Storage**: Store backups in different geographic locations
4. **Documentation**: Maintain updated recovery procedures
5. **Training**: Ensure staff know backup/restore procedures

#### **For Administrators**
1. **Monitor Backup Health**: Check backup success rates weekly
2. **Validate Backups**: Regularly verify backup integrity
3. **Update Retention Policies**: Adjust based on business needs
4. **Security Reviews**: Regular access control audits
5. **Performance Optimization**: Monitor backup/restore performance

### 🛠️ **Manual Operations**

#### **Manual Backup**
```sql
-- Trigger immediate backup
SELECT create_manual_backup('org-uuid', 'full', 'Manual backup before system update');
```

#### **Manual Cleanup**
```sql
-- Clean up expired backups
SELECT cleanup_expired_backups();
```

#### **Backup Validation**
```sql
-- Validate specific backup
SELECT validate_backup('backup-uuid-here');
```

This comprehensive backup and restoration system ensures your financial data is always protected and recoverable, meeting enterprise-grade reliability standards while maintaining compliance with Ugandan data protection regulations.

## 🏢 About Tom's Cyber Lab (U) Ltd

**Registration Number**: 80020002602390

Tom's Cyber Lab is a comprehensive ICT solutions provider based in Uganda, offering expertise in:

- **Digital Platforms**: Custom software development and digital transformation
- **Device Procurement**: Hardware sourcing and IT infrastructure setup
- **Website Development**: Professional web development and e-commerce solutions
- **Digital Marketing**: Online presence optimization and digital marketing strategies
- **Computer Maintenance**: Hardware repair, maintenance, and technical support
- **Network Security**: Cybersecurity solutions and network infrastructure

KAYA Finance represents our specialized financial management solution, designed to improve businesses' positioning in the digital economy through comprehensive financial tools and Uganda-specific integrations.

## 📞 Support & Contact

- **Email**: <EMAIL>
- **WhatsApp**: +256 777 959 328
- **Support Hours**: Monday-Friday 8AM-6PM, Saturday 9AM-2PM (EAT)
- **Location**: Kampala, Uganda

For technical support, feature requests, or business inquiries, please contact our support team through the channels above.

## 📄 Legal & Compliance

- **Privacy Policy**: Compliant with Uganda Data Protection and Privacy Act 2019
- **Terms of Service**: Governed by Ugandan law with proper dispute resolution
- **Financial Compliance**: Designed to meet Uganda Financial Institutions Act requirements
- **URA Compliance**: Built-in features for Uganda Revenue Authority reporting

## 🔄 Version Information

Current Version: 1.0.0
- Production-ready financial management platform
- Full URA integration capabilities
- Mobile money and banking integrations
- Comprehensive audit and compliance features

---

© 2024 KAYA Finance by Tom's Cyber Lab (U) Ltd. All rights reserved.
