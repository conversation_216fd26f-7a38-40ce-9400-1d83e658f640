import React, { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  AlertTriangle, 
  Bug, 
  Activity, 
  Clock, 
  TrendingUp, 
  RefreshCw,
  Download,
  Trash2
} from 'lucide-react'
import { errorHandler } from '@/lib/errorHandler'
import { logger } from '@/lib/logger'
import type { ErrorReport } from '@/lib/errorHandler'
import type { LogEntry } from '@/lib/logger'

interface ErrorStats {
  total: number
  bySeverity: Record<string, number>
  byCategory: Record<string, number>
  recent: number
}

interface LogStats {
  total: number
  byLevel: Record<string, number>
  byComponent: Record<string, number>
  recent: number
}

export function MonitoringDashboard() {
  const [errorStats, setErrorStats] = useState<ErrorStats | null>(null)
  const [logStats, setLogStats] = useState<LogStats | null>(null)
  const [recentErrors, setRecentErrors] = useState<ErrorReport[]>([])
  const [recentLogs, setRecentLogs] = useState<LogEntry[]>([])
  const [isRefreshing, setIsRefreshing] = useState(false)

  const refreshData = async () => {
    setIsRefreshing(true)
    try {
      // Get error statistics
      const errorStatsData = errorHandler.getErrorStats()
      setErrorStats(errorStatsData)

      // Get recent errors
      const errors = errorHandler.getErrorReports({ limit: 10 })
      setRecentErrors(errors)

      // Get log statistics
      const logStatsData = logger.getLogStats()
      setLogStats(logStatsData)

      // Get recent logs
      const logs = logger.getLogs({ limit: 20 })
      setRecentLogs(logs)
    } finally {
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    refreshData()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(refreshData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'destructive'
      case 'high':
        return 'destructive'
      case 'medium':
        return 'secondary'
      case 'low':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'fatal':
      case 'error':
        return 'destructive'
      case 'warn':
        return 'secondary'
      case 'info':
        return 'outline'
      case 'debug':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const handleExportLogs = () => {
    const logsData = logger.exportLogs()
    const blob = new Blob([logsData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `app-logs-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleClearLogs = () => {
    if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
      logger.clearLogs()
      refreshData()
    }
  }

  const handleClearErrors = () => {
    if (confirm('Are you sure you want to clear all error reports? This action cannot be undone.')) {
      errorHandler.clearErrorReports()
      refreshData()
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Monitoring</h1>
          <p className="text-muted-foreground">
            Monitor application errors, logs, and performance metrics
          </p>
        </div>
        <Button 
          onClick={refreshData} 
          disabled={isRefreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Errors</CardTitle>
            <Bug className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{errorStats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              {errorStats?.recent || 0} in the last hour
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Errors</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {errorStats?.bySeverity?.critical || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Logs</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{logStats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              {logStats?.recent || 0} in the last hour
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Good</div>
            <p className="text-xs text-muted-foreground">
              No critical issues detected
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Views */}
      <Tabs defaultValue="errors" className="space-y-4">
        <TabsList>
          <TabsTrigger value="errors">Error Reports</TabsTrigger>
          <TabsTrigger value="logs">Application Logs</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="errors" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Recent Error Reports</h3>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleClearErrors}
              className="flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Clear All
            </Button>
          </div>
          
          <Card>
            <CardContent className="p-0">
              {recentErrors.length === 0 ? (
                <div className="p-6 text-center text-muted-foreground">
                  No error reports found
                </div>
              ) : (
                <div className="divide-y">
                  {recentErrors.map((error) => (
                    <div key={error.id} className="p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant={getSeverityColor(error.severity)}>
                            {error.severity}
                          </Badge>
                          <Badge variant="outline">{error.category}</Badge>
                          <span className="text-sm text-muted-foreground">
                            {error.context.component}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Clock className="w-3 h-3" />
                          {error.timestamp.toLocaleString()}
                        </div>
                      </div>
                      <p className="text-sm font-medium">{error.error.message}</p>
                      <p className="text-xs text-muted-foreground">
                        Error ID: {error.id}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Application Logs</h3>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleExportLogs}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Export
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleClearLogs}
                className="flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                Clear All
              </Button>
            </div>
          </div>
          
          <Card>
            <CardContent className="p-0">
              {recentLogs.length === 0 ? (
                <div className="p-6 text-center text-muted-foreground">
                  No log entries found
                </div>
              ) : (
                <div className="divide-y max-h-96 overflow-y-auto">
                  {recentLogs.map((log, index) => (
                    <div key={index} className="p-3 space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant={getLogLevelColor(log.level)}>
                            {log.level}
                          </Badge>
                          {log.component && (
                            <Badge variant="outline">{log.component}</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Clock className="w-3 h-3" />
                          {log.timestamp.toLocaleString()}
                        </div>
                      </div>
                      <p className="text-sm">{log.message}</p>
                      {log.action && (
                        <p className="text-xs text-muted-foreground">
                          Action: {log.action}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Error Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Error Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">By Severity</h4>
                  <div className="space-y-2">
                    {errorStats?.bySeverity && Object.entries(errorStats.bySeverity).map(([severity, count]) => (
                      <div key={severity} className="flex justify-between">
                        <Badge variant={getSeverityColor(severity)}>{severity}</Badge>
                        <span className="font-mono">{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">By Category</h4>
                  <div className="space-y-2">
                    {errorStats?.byCategory && Object.entries(errorStats.byCategory).map(([category, count]) => (
                      <div key={category} className="flex justify-between">
                        <Badge variant="outline">{category}</Badge>
                        <span className="font-mono">{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Log Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Log Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">By Level</h4>
                  <div className="space-y-2">
                    {logStats?.byLevel && Object.entries(logStats.byLevel).map(([level, count]) => (
                      <div key={level} className="flex justify-between">
                        <Badge variant={getLogLevelColor(level)}>{level}</Badge>
                        <span className="font-mono">{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">By Component</h4>
                  <div className="space-y-2">
                    {logStats?.byComponent && Object.entries(logStats.byComponent).slice(0, 5).map(([component, count]) => (
                      <div key={component} className="flex justify-between">
                        <Badge variant="outline">{component}</Badge>
                        <span className="font-mono">{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
