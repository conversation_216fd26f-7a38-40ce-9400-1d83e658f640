-- =====================================================
-- FIX JOURNAL ENTRY FUNCTIONS - COLUMN NAMES
-- =====================================================
-- This migration fixes the column name issues in the journal entry functions
-- Date: 2024-12-31
-- Purpose: Update functions to use correct column names (date_issued, total_amount)

-- =====================================================
-- STEP 1: DROP AND RECREATE GENERATE MISSING JOURNAL ENTRIES FUNCTION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Fixing generate_missing_journal_entries function with correct column names...';
END $$;

-- Drop the existing function
DROP FUNCTION IF EXISTS generate_missing_journal_entries();

-- Recreate with correct column names
CREATE OR REPLACE FUNCTION generate_missing_journal_entries()
RETURNS TABLE (
    entity_type TEXT,
    entity_id UUID,
    journal_entry_id UUID,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    user_org_id UUID;
    invoice_record RECORD;
    bill_record RECORD;
    new_journal_id UUID;
    ar_account_id UUID;
    ap_account_id UUID;
    revenue_account_id UUID;
    expense_account_id UUID;
    vat_account_id UUID;
    total_generated INTEGER := 0;
    total_errors INTEGER := 0;
BEGIN
    -- Get current user and organization
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN QUERY SELECT 'error'::TEXT, NULL::UUID, NULL::UUID, 'User not authenticated'::TEXT;
        RETURN;
    END IF;
    
    -- Get user's organization
    SELECT org_id INTO user_org_id FROM profiles WHERE id = current_user_id;
    
    IF user_org_id IS NULL THEN
        RETURN QUERY SELECT 'error'::TEXT, NULL::UUID, NULL::UUID, 'User organization not found'::TEXT;
        RETURN;
    END IF;
    
    -- Get default account mappings
    SELECT account_id INTO ar_account_id 
    FROM account_mappings 
    WHERE org_id = user_org_id AND mapping_type = 'accounts_receivable' AND is_default = true
    LIMIT 1;
    
    SELECT account_id INTO ap_account_id 
    FROM account_mappings 
    WHERE org_id = user_org_id AND mapping_type = 'accounts_payable' AND is_default = true
    LIMIT 1;
    
    SELECT account_id INTO revenue_account_id 
    FROM account_mappings 
    WHERE org_id = user_org_id AND mapping_type = 'sales_revenue' AND is_default = true
    LIMIT 1;
    
    SELECT account_id INTO expense_account_id 
    FROM account_mappings 
    WHERE org_id = user_org_id AND mapping_type = 'general_expense' AND is_default = true
    LIMIT 1;
    
    SELECT account_id INTO vat_account_id 
    FROM account_mappings 
    WHERE org_id = user_org_id AND mapping_type = 'vat_payable' AND is_default = true
    LIMIT 1;
    
    -- Process invoices without journal entries
    FOR invoice_record IN
        SELECT i.id, i.invoice_number, i.date_issued, (i.total_amount - i.tax_amount) as subtotal, i.tax_amount, i.total_amount
        FROM invoices i
        LEFT JOIN journal_entries je ON je.source_id = i.id AND je.source_type = 'invoice'
        WHERE i.org_id = user_org_id 
        AND je.id IS NULL
        AND i.status IN ('sent', 'paid')
    LOOP
        BEGIN
            -- Create journal entry for invoice
            INSERT INTO journal_entries (
                org_id, date, description, reference, 
                source_id, source_type, created_by, is_posted
            )
            VALUES (
                user_org_id, 
                invoice_record.date_issued, 
                'Invoice: ' || invoice_record.invoice_number,
                invoice_record.invoice_number,
                invoice_record.id,
                'invoice',
                current_user_id,
                false
            )
            RETURNING id INTO new_journal_id;
            
            -- Create transaction lines for invoice (DR AR, CR Revenue, CR VAT)
            IF ar_account_id IS NOT NULL THEN
                INSERT INTO transaction_lines (org_id, journal_entry_id, account_id, debit, credit, description)
                VALUES (user_org_id, new_journal_id, ar_account_id, invoice_record.total_amount, 0, 'Accounts Receivable');
            END IF;
            
            IF revenue_account_id IS NOT NULL THEN
                INSERT INTO transaction_lines (org_id, journal_entry_id, account_id, debit, credit, description)
                VALUES (user_org_id, new_journal_id, revenue_account_id, 0, invoice_record.subtotal, 'Sales Revenue');
            END IF;
            
            IF vat_account_id IS NOT NULL AND invoice_record.tax_amount > 0 THEN
                INSERT INTO transaction_lines (org_id, journal_entry_id, account_id, debit, credit, description)
                VALUES (user_org_id, new_journal_id, vat_account_id, 0, invoice_record.tax_amount, 'VAT Payable');
            END IF;
            
            total_generated := total_generated + 1;
            RETURN QUERY SELECT 'invoice'::TEXT, invoice_record.id, new_journal_id, 'success'::TEXT;
            
        EXCEPTION WHEN OTHERS THEN
            total_errors := total_errors + 1;
            RETURN QUERY SELECT 'invoice'::TEXT, invoice_record.id, NULL::UUID, ('error: ' || SQLERRM)::TEXT;
        END;
    END LOOP;
    
    -- Process bills without journal entries
    FOR bill_record IN
        SELECT b.id, b.bill_number, b.date_issued, (b.total_amount - b.tax_amount) as subtotal, b.tax_amount, b.total_amount
        FROM bills b
        LEFT JOIN journal_entries je ON je.source_id = b.id AND je.source_type = 'bill'
        WHERE b.org_id = user_org_id 
        AND je.id IS NULL
        AND b.status IN ('approved', 'paid')
    LOOP
        BEGIN
            -- Create journal entry for bill
            INSERT INTO journal_entries (
                org_id, date, description, reference, 
                source_id, source_type, created_by, is_posted
            )
            VALUES (
                user_org_id, 
                bill_record.date_issued, 
                'Bill: ' || bill_record.bill_number,
                bill_record.bill_number,
                bill_record.id,
                'bill',
                current_user_id,
                false
            )
            RETURNING id INTO new_journal_id;
            
            -- Create transaction lines for bill (DR Expense, DR VAT, CR AP)
            IF expense_account_id IS NOT NULL THEN
                INSERT INTO transaction_lines (org_id, journal_entry_id, account_id, debit, credit, description)
                VALUES (user_org_id, new_journal_id, expense_account_id, bill_record.subtotal, 0, 'Expense');
            END IF;
            
            IF vat_account_id IS NOT NULL AND bill_record.tax_amount > 0 THEN
                INSERT INTO transaction_lines (org_id, journal_entry_id, account_id, debit, credit, description)
                VALUES (user_org_id, new_journal_id, vat_account_id, bill_record.tax_amount, 0, 'VAT Receivable');
            END IF;
            
            IF ap_account_id IS NOT NULL THEN
                INSERT INTO transaction_lines (org_id, journal_entry_id, account_id, debit, credit, description)
                VALUES (user_org_id, new_journal_id, ap_account_id, 0, bill_record.total_amount, 'Accounts Payable');
            END IF;
            
            total_generated := total_generated + 1;
            RETURN QUERY SELECT 'bill'::TEXT, bill_record.id, new_journal_id, 'success'::TEXT;
            
        EXCEPTION WHEN OTHERS THEN
            total_errors := total_errors + 1;
            RETURN QUERY SELECT 'bill'::TEXT, bill_record.id, NULL::UUID, ('error: ' || SQLERRM)::TEXT;
        END;
    END LOOP;
    
    -- Return summary
    RETURN QUERY SELECT 'summary'::TEXT, NULL::UUID, NULL::UUID, 
        ('Generated: ' || total_generated || ', Errors: ' || total_errors)::TEXT;
END;
$$;

-- =====================================================
-- STEP 2: DROP AND RECREATE AUDIT MISSING JOURNAL ENTRIES FUNCTION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Fixing audit_missing_journal_entries function with correct column names...';
END $$;

-- Drop the existing function
DROP FUNCTION IF EXISTS audit_missing_journal_entries(UUID);

-- Recreate with correct column names
CREATE OR REPLACE FUNCTION audit_missing_journal_entries(org_id_param UUID)
RETURNS TABLE (
    entity_type TEXT,
    entity_id UUID,
    entity_reference TEXT,
    entity_date DATE,
    amount DECIMAL,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check invoices without journal entries
    RETURN QUERY
    SELECT 
        'invoice'::TEXT,
        i.id,
        i.invoice_number,
        i.date_issued,
        i.total_amount,
        i.status::TEXT
    FROM invoices i
    LEFT JOIN journal_entries je ON je.source_id = i.id AND je.source_type = 'invoice'
    WHERE i.org_id = org_id_param 
    AND je.id IS NULL
    AND i.status IN ('sent', 'paid');
    
    -- Check bills without journal entries
    RETURN QUERY
    SELECT 
        'bill'::TEXT,
        b.id,
        b.bill_number,
        b.date_issued,
        b.total_amount,
        b.status::TEXT
    FROM bills b
    LEFT JOIN journal_entries je ON je.source_id = b.id AND je.source_type = 'bill'
    WHERE b.org_id = org_id_param 
    AND je.id IS NULL
    AND b.status IN ('approved', 'paid');
END;
$$;

-- =====================================================
-- STEP 3: VERIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ JOURNAL ENTRY FUNCTIONS FIXED SUCCESSFULLY!';
    RAISE NOTICE '===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 CORRECTED COLUMN NAMES:';
    RAISE NOTICE '  • date → date_issued';
    RAISE NOTICE '  • total → total_amount';
    RAISE NOTICE '  • subtotal → (total_amount - tax_amount)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Functions ready to use with correct schema!';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================
