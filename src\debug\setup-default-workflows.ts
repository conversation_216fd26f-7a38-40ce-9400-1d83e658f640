import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'

/**
 * Setup default workflow templates for testing
 */
export class WorkflowSetup {
  
  /**
   * Create default workflow templates for all document types
   */
  static async createDefaultWorkflows(orgId: string, userId: string) {
    console.log('🔧 Creating default workflow templates...')
    
    try {
      // Check if workflows already exist
      const { data: existingTemplates, error: checkError } = await supabase
        .from('workflow_templates')
        .select('*')
        .eq('org_id', orgId)
      
      if (checkError) {
        console.error('❌ Error checking existing templates:', checkError)
        return false
      }
      
      if (existingTemplates && existingTemplates.length > 0) {
        console.log('ℹ️ Workflow templates already exist:', existingTemplates.length)
        return true
      }
      
      // Create invoice workflow template
      const { data: invoiceTemplate, error: invoiceError } = await supabase
        .from('workflow_templates')
        .insert({
          name: 'Standard Invoice Approval',
          description: 'Default approval workflow for invoices',
          document_type: 'invoice',
          is_default: true,
          is_active: true,
          org_id: orgId,
          created_by: userId
        })
        .select()
        .single()
      
      if (invoiceError) {
        console.error('❌ Error creating invoice template:', invoiceError)
        return false
      }
      
      // Create invoice approval steps
      await supabase
        .from('approval_steps')
        .insert([
          {
            workflow_template_id: invoiceTemplate.id,
            step_order: 1,
            name: 'Manager Review',
            required_role: ['admin', 'owner'],
            required_approvers: 1,
            allow_self_approval: false,
            org_id: orgId
          }
        ])
      
      // Create bill workflow template
      const { data: billTemplate, error: billError } = await supabase
        .from('workflow_templates')
        .insert({
          name: 'Standard Bill Approval',
          description: 'Default approval workflow for bills',
          document_type: 'bill',
          is_default: true,
          is_active: true,
          org_id: orgId,
          created_by: userId
        })
        .select()
        .single()
      
      if (billError) {
        console.error('❌ Error creating bill template:', billError)
        return false
      }
      
      // Create bill approval steps
      await supabase
        .from('approval_steps')
        .insert([
          {
            workflow_template_id: billTemplate.id,
            step_order: 1,
            name: 'Manager Review',
            required_role: ['admin', 'owner'],
            required_approvers: 1,
            allow_self_approval: false,
            org_id: orgId
          }
        ])
      
      // Create budget workflow template
      const { data: budgetTemplate, error: budgetError } = await supabase
        .from('workflow_templates')
        .insert({
          name: 'Standard Budget Approval',
          description: 'Default approval workflow for budgets',
          document_type: 'budget',
          is_default: true,
          is_active: true,
          org_id: orgId,
          created_by: userId
        })
        .select()
        .single()
      
      if (budgetError) {
        console.error('❌ Error creating budget template:', budgetError)
        return false
      }
      
      // Create budget approval steps
      await supabase
        .from('approval_steps')
        .insert([
          {
            workflow_template_id: budgetTemplate.id,
            step_order: 1,
            name: 'Executive Approval',
            required_role: ['owner'],
            required_approvers: 1,
            allow_self_approval: false,
            org_id: orgId
          }
        ])
      
      console.log('✅ Default workflow templates created successfully')
      return true
      
    } catch (error) {
      console.error('❌ Error creating default workflows:', error)
      return false
    }
  }
  
  /**
   * Create a test approval instance for testing
   */
  static async createTestApprovalInstance(orgId: string, userId: string) {
    console.log('🔧 Creating test approval instance...')
    
    try {
      // First, check if there's a test document we can use
      const { data: testBill, error: billError } = await supabase
        .from('bills')
        .select('*')
        .eq('org_id', orgId)
        .limit(1)
        .single()
      
      if (billError || !testBill) {
        console.log('ℹ️ No test bills found, creating one...')
        
        // Get a vendor for the test bill
        const { data: vendor, error: vendorError } = await supabase
          .from('vendors')
          .select('*')
          .eq('org_id', orgId)
          .limit(1)
          .single()
        
        if (vendorError || !vendor) {
          console.log('❌ No vendors found, cannot create test bill')
          return false
        }
        
        // Get an account for the test bill
        const { data: account, error: accountError } = await supabase
          .from('accounts')
          .select('*')
          .eq('org_id', orgId)
          .limit(1)
          .single()
        
        if (accountError || !account) {
          console.log('❌ No accounts found, cannot create test bill')
          return false
        }
        
        // Create test bill
        const { data: newBill, error: createBillError } = await supabase
          .from('bills')
          .insert({
            vendor_id: vendor.id,
            bill_number: 'TEST-BILL-001',
            date_issued: new Date().toISOString().split('T')[0],
            due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            total_amount: 100000,
            tax_amount: 18000,
            withholding_amount: 0,
            status: 'draft',
            notes: 'Test bill for approval workflow testing',
            org_id: orgId,
            created_by: userId
          })
          .select()
          .single()
        
        if (createBillError) {
          console.error('❌ Error creating test bill:', createBillError)
          return false
        }
        
        // Create test bill lines
        await supabase
          .from('bill_lines')
          .insert([
            {
              bill_id: newBill.id,
              account_id: account.id,
              description: 'Test service - consulting',
              quantity: 1,
              unit_price: 100000,
              tax_rate_pct: 18,
              org_id: orgId
            }
          ])
        
        console.log('✅ Test bill created:', newBill.id)
        
        // Now submit it for approval
        const { ApprovalEngine } = await import('@/lib/approval-engine/core')
        
        const approvalInstance = await ApprovalEngine.submitForApproval(
          'bill',
          newBill.id,
          newBill.total_amount,
          'UGX',
          userId,
          orgId,
          {
            vendor_id: vendor.id,
            bill_number: newBill.bill_number
          }
        )
        
        console.log('✅ Test approval instance created:', approvalInstance.id)
        return approvalInstance
      }
      
      return false
    } catch (error) {
      console.error('❌ Error creating test approval instance:', error)
      return false
    }
  }
}

// Export for browser console
if (typeof window !== 'undefined') {
  (window as { WorkflowSetup?: typeof WorkflowSetup }).WorkflowSetup = WorkflowSetup
}
