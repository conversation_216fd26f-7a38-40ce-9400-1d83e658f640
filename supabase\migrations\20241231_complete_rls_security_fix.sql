-- =====================================================
-- COMPLETE RLS SECURITY FIX - CORRECTED VERSION
-- =====================================================
-- Migration: 20241231_complete_rls_security_fix.sql
-- Description: Enables RLS and creates comprehensive policies for all tables
-- Fixes: Infinite recursion in profiles policies and missing INSERT policies
-- Author: Kaya Finance Team
-- Date: 2024-12-31

-- =====================================================
-- STEP 1: ENABLE RLS ON ALL MISSING TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔒 KAYA FINANCE - COMPLETE RLS SECURITY FIX (CORRECTED)';
    RAISE NOTICE '';
    RAISE NOTICE '📋 STEP 1: Enabling RLS on all missing tables...';
    RAISE NOTICE '';
END $$;

-- Enable RLS on all tables from the linter report
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoice_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE bills ENABLE ROW LEVEL SECURITY;
ALTER TABLE bill_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE bank_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE mobile_money_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE bank_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE tax_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_journals ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE ura_tax_filings ENABLE ROW LEVEL SECURITY;
ALTER TABLE attachments ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 2: DROP EXISTING POLICIES (IF ANY)
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 STEP 2: Cleaning up existing policies...';
    RAISE NOTICE '';
END $$;

-- Drop existing organization policies
DROP POLICY IF EXISTS "Users can view their organization" ON organizations;
DROP POLICY IF EXISTS "Owners and admins can update their organization" ON organizations;
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON organizations;

-- Drop existing profile policies (to fix recursion)
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Users can view org profiles" ON profiles;

-- Drop existing business data policies
DROP POLICY IF EXISTS "Users can view customers in their organization" ON customers;
DROP POLICY IF EXISTS "Users can manage customers in their organization" ON customers;
DROP POLICY IF EXISTS "Users can view vendors in their organization" ON vendors;
DROP POLICY IF EXISTS "Users can manage vendors in their organization" ON vendors;
DROP POLICY IF EXISTS "Users can view invoices in their organization" ON invoices;
DROP POLICY IF EXISTS "Users can manage invoices in their organization" ON invoices;
DROP POLICY IF EXISTS "Users can view bills in their organization" ON bills;
DROP POLICY IF EXISTS "Users can manage bills in their organization" ON bills;
DROP POLICY IF EXISTS "Users can view bank accounts in their organization" ON bank_accounts;
DROP POLICY IF EXISTS "Admins and above can manage bank accounts" ON bank_accounts;
DROP POLICY IF EXISTS "Users can view mobile money accounts in their organization" ON mobile_money_accounts;
DROP POLICY IF EXISTS "Admins and above can manage mobile money accounts" ON mobile_money_accounts;
DROP POLICY IF EXISTS "Users can view bank transactions in their organization" ON bank_transactions;
DROP POLICY IF EXISTS "Accountants and above can manage bank transactions" ON bank_transactions;
DROP POLICY IF EXISTS "Users can view tax rates in their organization" ON tax_rates;
DROP POLICY IF EXISTS "Admins and above can manage tax rates" ON tax_rates;
DROP POLICY IF EXISTS "Users can view budgets in their organization" ON budgets;
DROP POLICY IF EXISTS "Accountants and above can manage budgets" ON budgets;
DROP POLICY IF EXISTS "Users can view recurring journals in their organization" ON recurring_journals;
DROP POLICY IF EXISTS "Accountants and above can manage recurring journals" ON recurring_journals;
DROP POLICY IF EXISTS "Users can view URA tax filings in their organization" ON ura_tax_filings;
DROP POLICY IF EXISTS "Accountants and above can manage URA tax filings" ON ura_tax_filings;
DROP POLICY IF EXISTS "Users can view attachments in their organization" ON attachments;
DROP POLICY IF EXISTS "Users can manage attachments in their organization" ON attachments;

-- Drop existing relationship-based policies
DROP POLICY IF EXISTS "Users can view invoice lines in their organization" ON invoice_lines;
DROP POLICY IF EXISTS "Users can manage invoice lines in their organization" ON invoice_lines;
DROP POLICY IF EXISTS "Users can view bill lines in their organization" ON bill_lines;
DROP POLICY IF EXISTS "Users can manage bill lines in their organization" ON bill_lines;
DROP POLICY IF EXISTS "Users can view payment applications in their organization" ON payment_applications;
DROP POLICY IF EXISTS "Accountants and above can manage payment applications" ON payment_applications;
DROP POLICY IF EXISTS "Users can view budget lines in their organization" ON budget_lines;
DROP POLICY IF EXISTS "Accountants and above can manage budget lines" ON budget_lines;
DROP POLICY IF EXISTS "Users can view budget approvals in their organization" ON budget_approvals;
DROP POLICY IF EXISTS "Admins and above can manage budget approvals" ON budget_approvals;
DROP POLICY IF EXISTS "Users can view recurring lines in their organization" ON recurring_lines;
DROP POLICY IF EXISTS "Accountants and above can manage recurring lines" ON recurring_lines;

-- =====================================================
-- STEP 3: CREATE CORE POLICIES (ORGANIZATIONS & PROFILES)
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 STEP 3: Creating core policies for organizations and profiles...';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- ORGANIZATIONS POLICIES (COMPLETE WITH INSERT)
-- =====================================================

-- Allow authenticated users to view their organization
CREATE POLICY "Users can view their organization" ON organizations
    FOR SELECT USING (
        id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
    );

-- Allow authenticated users to create organizations (for onboarding)
CREATE POLICY "Authenticated users can create organizations" ON organizations
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Allow owners and admins to update their organization
CREATE POLICY "Owners and admins can update their organization" ON organizations
    FOR UPDATE USING (
        id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND role IN ('owner', 'admin')
        )
    );

-- =====================================================
-- PROFILES POLICIES (NO RECURSION)
-- =====================================================

-- Allow users to view their own profile (no recursion)
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (id = auth.uid());

-- Allow authenticated users to create their profile (for onboarding)
CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (id = auth.uid());

-- Allow users to update their own profile
CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (id = auth.uid());

-- Note: Org-level profile viewing is handled in application code to avoid recursion

-- =====================================================
-- STEP 4: CREATE BUSINESS DATA POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 STEP 4: Creating business data policies...';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- TABLES WITH DIRECT ORG_ID COLUMNS
-- =====================================================

-- Customers policies
CREATE POLICY "Users can view customers in their organization" ON customers
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can manage customers in their organization" ON customers
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- Vendors policies
CREATE POLICY "Users can view vendors in their organization" ON vendors
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can manage vendors in their organization" ON vendors
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- Invoices policies
CREATE POLICY "Users can view invoices in their organization" ON invoices
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can manage invoices in their organization" ON invoices
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- Bills policies
CREATE POLICY "Users can view bills in their organization" ON bills
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can manage bills in their organization" ON bills
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- Bank accounts policies
CREATE POLICY "Users can view bank accounts in their organization" ON bank_accounts
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Admins and above can manage bank accounts" ON bank_accounts
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Mobile money accounts policies
CREATE POLICY "Users can view mobile money accounts in their organization" ON mobile_money_accounts
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Admins and above can manage mobile money accounts" ON mobile_money_accounts
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Bank transactions policies
CREATE POLICY "Users can view bank transactions in their organization" ON bank_transactions
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Accountants and above can manage bank transactions" ON bank_transactions
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- Tax rates policies
CREATE POLICY "Users can view tax rates in their organization" ON tax_rates
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Admins and above can manage tax rates" ON tax_rates
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Budgets policies
CREATE POLICY "Users can view budgets in their organization" ON budgets
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Accountants and above can manage budgets" ON budgets
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- Recurring journals policies
CREATE POLICY "Users can view recurring journals in their organization" ON recurring_journals
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Accountants and above can manage recurring journals" ON recurring_journals
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- URA tax filings policies
CREATE POLICY "Users can view URA tax filings in their organization" ON ura_tax_filings
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Accountants and above can manage URA tax filings" ON ura_tax_filings
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- Attachments policies
CREATE POLICY "Users can view attachments in their organization" ON attachments
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can manage attachments in their organization" ON attachments
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
    ));

-- =====================================================
-- STEP 5: CREATE RELATIONSHIP-BASED POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 STEP 5: Creating relationship-based policies...';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- TABLES WITHOUT DIRECT ORG_ID (RELATIONSHIP-BASED)
-- =====================================================

-- Invoice lines policies (via invoice.org_id)
CREATE POLICY "Users can view invoice lines in their organization" ON invoice_lines
    FOR SELECT USING (
        invoice_id IN (
            SELECT id FROM invoices
            WHERE org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
        )
    );

CREATE POLICY "Users can manage invoice lines in their organization" ON invoice_lines
    FOR ALL USING (
        invoice_id IN (
            SELECT id FROM invoices
            WHERE org_id IN (
                SELECT org_id FROM profiles
                WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
            )
        )
    );

-- Bill lines policies (via bill.org_id)
CREATE POLICY "Users can view bill lines in their organization" ON bill_lines
    FOR SELECT USING (
        bill_id IN (
            SELECT id FROM bills
            WHERE org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
        )
    );

CREATE POLICY "Users can manage bill lines in their organization" ON bill_lines
    FOR ALL USING (
        bill_id IN (
            SELECT id FROM bills
            WHERE org_id IN (
                SELECT org_id FROM profiles
                WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
            )
        )
    );

-- Payment applications policies (via payment.org_id)
CREATE POLICY "Users can view payment applications in their organization" ON payment_applications
    FOR SELECT USING (
        payment_id IN (
            SELECT id FROM payments
            WHERE org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
        )
    );

CREATE POLICY "Accountants and above can manage payment applications" ON payment_applications
    FOR ALL USING (
        payment_id IN (
            SELECT id FROM payments
            WHERE org_id IN (
                SELECT org_id FROM profiles
                WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
            )
        )
    );

-- Budget lines policies (via budget.org_id)
CREATE POLICY "Users can view budget lines in their organization" ON budget_lines
    FOR SELECT USING (
        budget_id IN (
            SELECT id FROM budgets
            WHERE org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
        )
    );

CREATE POLICY "Accountants and above can manage budget lines" ON budget_lines
    FOR ALL USING (
        budget_id IN (
            SELECT id FROM budgets
            WHERE org_id IN (
                SELECT org_id FROM profiles
                WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
            )
        )
    );

-- Budget approvals policies (via budget.org_id)
CREATE POLICY "Users can view budget approvals in their organization" ON budget_approvals
    FOR SELECT USING (
        budget_id IN (
            SELECT id FROM budgets
            WHERE org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
        )
    );

CREATE POLICY "Admins and above can manage budget approvals" ON budget_approvals
    FOR ALL USING (
        budget_id IN (
            SELECT id FROM budgets
            WHERE org_id IN (
                SELECT org_id FROM profiles
                WHERE id = auth.uid() AND role IN ('owner', 'admin')
            )
        )
    );

-- Recurring lines policies (via recurring_journal.org_id)
CREATE POLICY "Users can view recurring lines in their organization" ON recurring_lines
    FOR SELECT USING (
        recurring_journal_id IN (
            SELECT id FROM recurring_journals
            WHERE org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
        )
    );

CREATE POLICY "Accountants and above can manage recurring lines" ON recurring_lines
    FOR ALL USING (
        recurring_journal_id IN (
            SELECT id FROM recurring_journals
            WHERE org_id IN (
                SELECT org_id FROM profiles
                WHERE id = auth.uid() AND role IN ('owner', 'admin', 'accountant')
            )
        )
    );

-- =====================================================
-- STEP 6: VERIFICATION AND COMPLETION
-- =====================================================

DO $$
DECLARE
    table_record RECORD;
    rls_enabled_count INTEGER := 0;
    total_tables INTEGER := 0;
    total_policies INTEGER := 0;
BEGIN
    RAISE NOTICE '📋 STEP 6: Verification and completion...';
    RAISE NOTICE '';

    -- Check RLS status
    FOR table_record IN
        SELECT tablename, rowsecurity
        FROM pg_tables
        WHERE schemaname = 'public'
        AND tablename IN (
            'organizations', 'profiles', 'customers', 'vendors', 'invoices', 'invoice_lines',
            'bills', 'bill_lines', 'bank_accounts', 'mobile_money_accounts',
            'payment_applications', 'bank_transactions', 'tax_rates',
            'budgets', 'budget_lines', 'budget_approvals',
            'recurring_journals', 'recurring_lines', 'ura_tax_filings', 'attachments'
        )
        ORDER BY tablename
    LOOP
        total_tables := total_tables + 1;
        IF table_record.rowsecurity THEN
            rls_enabled_count := rls_enabled_count + 1;
        END IF;
    END LOOP;

    -- Count policies
    SELECT COUNT(*) INTO total_policies
    FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename IN (
        'organizations', 'profiles', 'customers', 'vendors', 'invoices', 'invoice_lines',
        'bills', 'bill_lines', 'bank_accounts', 'mobile_money_accounts',
        'payment_applications', 'bank_transactions', 'tax_rates',
        'budgets', 'budget_lines', 'budget_approvals',
        'recurring_journals', 'recurring_lines', 'ura_tax_filings', 'attachments'
    );

    RAISE NOTICE '✅ RLS SECURITY FIX COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '📊 RESULTS:';
    RAISE NOTICE '  ✅ Tables with RLS enabled: % of %', rls_enabled_count, total_tables;
    RAISE NOTICE '  ✅ Total RLS policies created: %', total_policies;
    RAISE NOTICE '';
    RAISE NOTICE '🔒 Security Features:';
    RAISE NOTICE '  ✅ Organization-based data isolation';
    RAISE NOTICE '  ✅ Role-based access control (owner/admin/accountant)';
    RAISE NOTICE '  ✅ Read access for all org members';
    RAISE NOTICE '  ✅ Write access restricted by role';
    RAISE NOTICE '  ✅ Relationship-based policies for child tables';
    RAISE NOTICE '  ✅ Onboarding flow supported (INSERT policies)';
    RAISE NOTICE '  ✅ No infinite recursion in policies';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 ALL RLS SECURITY ISSUES RESOLVED!';
    RAISE NOTICE '';
    RAISE NOTICE 'Next Steps:';
    RAISE NOTICE '1. Run Supabase database linter again to verify fixes';
    RAISE NOTICE '2. Test application functionality thoroughly';
    RAISE NOTICE '3. Monitor logs for any RLS-related access issues';
    RAISE NOTICE '4. Regenerate TypeScript types if needed';
    RAISE NOTICE '';
    RAISE NOTICE '📋 Table Organization Mapping:';
    RAISE NOTICE '  Direct org_id: organizations, customers, vendors, invoices, bills,';
    RAISE NOTICE '                 bank_accounts, mobile_money_accounts, bank_transactions,';
    RAISE NOTICE '                 tax_rates, budgets, recurring_journals, ura_tax_filings, attachments';
    RAISE NOTICE '  Via relationships: invoice_lines (→invoices), bill_lines (→bills),';
    RAISE NOTICE '                     payment_applications (→payments), budget_lines (→budgets),';
    RAISE NOTICE '                     budget_approvals (→budgets), recurring_lines (→recurring_journals)';
    RAISE NOTICE '  Special handling: profiles (no recursion, app-level org access)';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  IMPORTANT NOTES:';
    RAISE NOTICE '  • Profiles table uses simplified policies to avoid recursion';
    RAISE NOTICE '  • Organization-level profile viewing handled in application code';
    RAISE NOTICE '  • All policies tested for onboarding flow compatibility';
    RAISE NOTICE '  • Performance indexes may be needed for relationship-based policies';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- OPTIONAL PERFORMANCE INDEXES
-- =====================================================
-- Uncomment these if you experience performance issues with relationship-based policies

-- CREATE INDEX IF NOT EXISTS idx_invoice_lines_invoice_id ON invoice_lines(invoice_id);
-- CREATE INDEX IF NOT EXISTS idx_bill_lines_bill_id ON bill_lines(bill_id);
-- CREATE INDEX IF NOT EXISTS idx_payment_applications_payment_id ON payment_applications(payment_id);
-- CREATE INDEX IF NOT EXISTS idx_budget_lines_budget_id ON budget_lines(budget_id);
-- CREATE INDEX IF NOT EXISTS idx_budget_approvals_budget_id ON budget_approvals(budget_id);
-- CREATE INDEX IF NOT EXISTS idx_recurring_lines_recurring_journal_id ON recurring_lines(recurring_journal_id);

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================
