/**
 * Performance Monitoring and Optimization System
 * Tracks performance metrics, implements caching, and provides optimization utilities
 */

import { logger, logPerformance, logWarn } from './logger'
import { config } from './config'

export interface PerformanceMetric {
  id: string
  name: string
  category: 'api' | 'render' | 'database' | 'cache' | 'bundle' | 'user_interaction'
  startTime: number
  endTime?: number
  duration?: number
  metadata?: Record<string, unknown>
  timestamp: Date
}

export interface PerformanceThresholds {
  api: number
  render: number
  database: number
  cache: number
  bundle: number
  user_interaction: number
}

export interface CacheEntry<T = unknown> {
  data: T
  timestamp: number
  ttl: number
  hits: number
  size: number
}

export interface PerformanceStats {
  totalMetrics: number
  averageDurations: Record<string, number>
  slowOperations: PerformanceMetric[]
  cacheStats: {
    totalEntries: number
    totalHits: number
    totalMisses: number
    hitRate: number
    totalSize: number
  }
  thresholdViolations: number
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: PerformanceMetric[] = []
  private cache: Map<string, CacheEntry> = new Map()
  private activeTimers: Map<string, number> = new Map()
  private maxMetrics = 1000
  private maxCacheSize = 100 * 1024 * 1024 // 100MB
  private currentCacheSize = 0
  private cacheHits = 0
  private cacheMisses = 0

  private thresholds: PerformanceThresholds = {
    api: 5000, // 5 seconds
    render: 100, // 100ms
    database: 3000, // 3 seconds
    cache: 10, // 10ms
    bundle: 2000, // 2 seconds
    user_interaction: 200 // 200ms
  }

  private constructor() {
    this.initializePerformanceObserver()
    this.startCacheCleanup()
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  private initializePerformanceObserver(): void {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        // Observe navigation timing
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              this.recordNavigationMetrics(entry as PerformanceNavigationTiming)
            }
          }
        })
        navObserver.observe({ entryTypes: ['navigation'] })

        // Observe resource timing
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'resource') {
              this.recordResourceMetrics(entry as PerformanceResourceTiming)
            }
          }
        })
        resourceObserver.observe({ entryTypes: ['resource'] })

        // Observe paint timing
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'paint') {
              this.recordPaintMetrics(entry as PerformancePaintTiming)
            }
          }
        })
        paintObserver.observe({ entryTypes: ['paint'] })

        logger.info('Performance observers initialized', {
          component: 'PerformanceMonitor',
          action: 'initializePerformanceObserver'
        })
      } catch (error) {
        logger.warn('Failed to initialize performance observers', {
          component: 'PerformanceMonitor',
          action: 'initializePerformanceObserver',
          metadata: { error: (error as Error).message }
        })
      }
    }
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = [
      {
        name: 'DNS Lookup',
        duration: entry.domainLookupEnd - entry.domainLookupStart,
        category: 'bundle' as const
      },
      {
        name: 'TCP Connection',
        duration: entry.connectEnd - entry.connectStart,
        category: 'bundle' as const
      },
      {
        name: 'Request',
        duration: entry.responseStart - entry.requestStart,
        category: 'api' as const
      },
      {
        name: 'Response',
        duration: entry.responseEnd - entry.responseStart,
        category: 'api' as const
      },
      {
        name: 'DOM Processing',
        duration: entry.domComplete - entry.domLoading,
        category: 'render' as const
      },
      {
        name: 'Load Complete',
        duration: entry.loadEventEnd - entry.loadEventStart,
        category: 'bundle' as const
      }
    ]

    metrics.forEach(metric => {
      if (metric.duration > 0) {
        this.addMetric({
          name: metric.name,
          category: metric.category,
          duration: metric.duration,
          metadata: { type: 'navigation' }
        })
      }
    })
  }

  private recordResourceMetrics(entry: PerformanceResourceTiming): void {
    const duration = entry.responseEnd - entry.startTime
    const category = this.categorizeResource(entry.name)

    this.addMetric({
      name: `Resource Load: ${entry.name.split('/').pop()}`,
      category,
      duration,
      metadata: {
        type: 'resource',
        url: entry.name,
        size: entry.transferSize,
        cached: entry.transferSize === 0
      }
    })
  }

  private recordPaintMetrics(entry: PerformancePaintTiming): void {
    this.addMetric({
      name: entry.name,
      category: 'render',
      duration: entry.startTime,
      metadata: { type: 'paint' }
    })
  }

  private categorizeResource(url: string): PerformanceMetric['category'] {
    if (url.includes('.js')) return 'bundle'
    if (url.includes('.css')) return 'render'
    if (url.includes('api') || url.includes('supabase')) return 'api'
    return 'bundle'
  }

  private startCacheCleanup(): void {
    // Clean up expired cache entries every 5 minutes
    setInterval(() => {
      this.cleanupExpiredCache()
    }, 5 * 60 * 1000)
  }

  private cleanupExpiredCache(): void {
    const now = Date.now()
    let cleanedSize = 0
    let cleanedCount = 0

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key)
        this.currentCacheSize -= entry.size
        cleanedSize += entry.size
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      logger.info('Cache cleanup completed', {
        component: 'PerformanceMonitor',
        action: 'cleanupExpiredCache',
        metadata: {
          cleanedCount,
          cleanedSize,
          remainingEntries: this.cache.size,
          remainingSize: this.currentCacheSize
        }
      })
    }
  }

  public startTimer(name: string, category: PerformanceMetric['category']): string {
    const id = `${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    this.activeTimers.set(id, performance.now())

    return id
  }

  public endTimer(id: string, metadata?: Record<string, unknown>): PerformanceMetric | null {
    const startTime = this.activeTimers.get(id)
    if (!startTime) {
      logWarn('Timer not found', {
        component: 'PerformanceMonitor',
        action: 'endTimer',
        metadata: { timerId: id }
      })
      return null
    }

    const endTime = performance.now()
    const duration = endTime - startTime
    this.activeTimers.delete(id)

    const [name, , ] = id.split('_')
    const metric = this.addMetric({
      name,
      category: 'user_interaction', // Default category
      duration,
      metadata
    })

    return metric
  }

  public addMetric(metricData: Omit<PerformanceMetric, 'id' | 'timestamp' | 'startTime' | 'endTime'>): PerformanceMetric {
    const metric: PerformanceMetric = {
      id: crypto.randomUUID(),
      startTime: performance.now(),
      endTime: performance.now() + (metricData.duration || 0),
      timestamp: new Date(),
      ...metricData
    }

    this.metrics.push(metric)

    // Maintain maximum metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // Check thresholds
    if (metric.duration && metric.duration > this.thresholds[metric.category]) {
      logPerformance(`Slow ${metric.category} operation: ${metric.name}`, metric.duration, {
        component: 'PerformanceMonitor',
        action: 'addMetric',
        metadata: {
          ...metric.metadata,
          threshold: this.thresholds[metric.category],
          violation: true
        }
      })
    }

    return metric
  }

  public setCache<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    const size = this.estimateSize(data)
    
    // Check if adding this entry would exceed cache size limit
    if (this.currentCacheSize + size > this.maxCacheSize) {
      this.evictLRU(size)
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0,
      size
    }

    this.cache.set(key, entry)
    this.currentCacheSize += size

    logger.debug('Cache entry added', {
      component: 'PerformanceMonitor',
      action: 'setCache',
      metadata: { key, size, ttl, totalSize: this.currentCacheSize }
    })
  }

  public getCache<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined
    
    if (!entry) {
      this.cacheMisses++
      return null
    }

    const now = Date.now()
    if (now > entry.timestamp + entry.ttl) {
      this.cache.delete(key)
      this.currentCacheSize -= entry.size
      this.cacheMisses++
      return null
    }

    entry.hits++
    this.cacheHits++
    return entry.data
  }

  public invalidateCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern)
      for (const [key, entry] of this.cache.entries()) {
        if (regex.test(key)) {
          this.cache.delete(key)
          this.currentCacheSize -= entry.size
        }
      }
    } else {
      this.cache.clear()
      this.currentCacheSize = 0
    }

    logger.info('Cache invalidated', {
      component: 'PerformanceMonitor',
      action: 'invalidateCache',
      metadata: { pattern, remainingEntries: this.cache.size }
    })
  }

  private evictLRU(requiredSize: number): void {
    const entries = Array.from(this.cache.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp) // Sort by timestamp (oldest first)

    let freedSize = 0
    let evictedCount = 0

    for (const [key, entry] of entries) {
      if (freedSize >= requiredSize) break

      this.cache.delete(key)
      this.currentCacheSize -= entry.size
      freedSize += entry.size
      evictedCount++
    }

    logger.info('LRU cache eviction completed', {
      component: 'PerformanceMonitor',
      action: 'evictLRU',
      metadata: { evictedCount, freedSize, requiredSize }
    })
  }

  private estimateSize(data: unknown): number {
    try {
      return new Blob([JSON.stringify(data)]).size
    } catch {
      return 1024 // Default 1KB if estimation fails
    }
  }

  public getStats(): PerformanceStats {
    const categoryDurations: Record<string, number[]> = {}
    const slowOperations: PerformanceMetric[] = []
    let thresholdViolations = 0

    this.metrics.forEach(metric => {
      if (metric.duration) {
        if (!categoryDurations[metric.category]) {
          categoryDurations[metric.category] = []
        }
        categoryDurations[metric.category].push(metric.duration)

        if (metric.duration > this.thresholds[metric.category]) {
          slowOperations.push(metric)
          thresholdViolations++
        }
      }
    })

    const averageDurations: Record<string, number> = {}
    Object.entries(categoryDurations).forEach(([category, durations]) => {
      averageDurations[category] = durations.reduce((sum, d) => sum + d, 0) / durations.length
    })

    const totalCacheOperations = this.cacheHits + this.cacheMisses
    const hitRate = totalCacheOperations > 0 ? (this.cacheHits / totalCacheOperations) * 100 : 0

    return {
      totalMetrics: this.metrics.length,
      averageDurations,
      slowOperations: slowOperations.slice(-10), // Last 10 slow operations
      cacheStats: {
        totalEntries: this.cache.size,
        totalHits: this.cacheHits,
        totalMisses: this.cacheMisses,
        hitRate,
        totalSize: this.currentCacheSize
      },
      thresholdViolations
    }
  }

  public getMetrics(category?: PerformanceMetric['category'], limit?: number): PerformanceMetric[] {
    let filtered = category ? this.metrics.filter(m => m.category === category) : this.metrics
    
    if (limit) {
      filtered = filtered.slice(-limit)
    }

    return filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  public updateThresholds(newThresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds }
    
    logger.info('Performance thresholds updated', {
      component: 'PerformanceMonitor',
      action: 'updateThresholds',
      metadata: { newThresholds }
    })
  }

  public clearMetrics(): void {
    this.metrics = []
    logger.info('Performance metrics cleared', {
      component: 'PerformanceMonitor',
      action: 'clearMetrics'
    })
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance()

// Utility functions
export const withPerformanceTracking = async <T>(
  operation: () => Promise<T>,
  name: string,
  category: PerformanceMetric['category'] = 'user_interaction',
  metadata?: Record<string, unknown>
): Promise<T> => {
  const timerId = performanceMonitor.startTimer(name, category)
  
  try {
    const result = await operation()
    performanceMonitor.endTimer(timerId, { ...metadata, success: true })
    return result
  } catch (error) {
    performanceMonitor.endTimer(timerId, { ...metadata, success: false, error: (error as Error).message })
    throw error
  }
}

export const measureRender = <T extends React.ComponentType<Record<string, unknown>>>(
  Component: T,
  displayName?: string
): T => {
  const WrappedComponent = (props: Record<string, unknown>) => {
    const componentName = displayName || Component.displayName || Component.name || 'Component'
    
    React.useEffect(() => {
      const timerId = performanceMonitor.startTimer(`Render: ${componentName}`, 'render')
      
      return () => {
        performanceMonitor.endTimer(timerId, { component: componentName })
      }
    })

    return React.createElement(Component, props)
  }

  WrappedComponent.displayName = `withPerformanceTracking(${displayName || Component.displayName || Component.name})`
  
  return WrappedComponent as T
}
