
import { useState } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { useCreateAccount, useUpdateAccount } from '@/hooks/queries'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import type { Account, AccountType } from '@/types/database'

interface AccountFormProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  editingAccount: Account | null
  accounts: Account[]
  onSuccess: () => void
}

export const AccountForm = ({ isOpen, onOpenChange, editingAccount, accounts, onSuccess }: AccountFormProps) => {
  const { profile } = useAuth()
  const { toast } = useToast()
  const createAccount = useCreateAccount()
  const updateAccount = useUpdateAccount()
  const [formData, setFormData] = useState({
    name: editingAccount?.name || '',
    code: editingAccount?.code || '',
    type: (editingAccount?.type || 'asset') as AccountType,
    parent_id: editingAccount?.parent_id || null as string | null,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    try {
      const accountData = {
        ...formData,
        // Convert the special "NO_PARENT" value back to null
        parent_id: formData.parent_id === 'NO_PARENT' ? null : formData.parent_id,
        org_id: profile.org_id,
        created_by: profile.id,
      }

      if (editingAccount) {
        await updateAccount.mutateAsync({
          accountId: editingAccount.id,
          accountData: {
            name: formData.name,
            code: formData.code,
            type: formData.type,
            parent_id: formData.parent_id === 'NO_PARENT' ? null : formData.parent_id,
            is_active: true,
          }
        })
      } else {
        await createAccount.mutateAsync({
          name: formData.name,
          code: formData.code,
          type: formData.type,
          parent_id: formData.parent_id === 'NO_PARENT' ? null : formData.parent_id,
          is_active: true,
        })
      }

      onOpenChange(false)
      resetForm()
      onSuccess()
    } catch (error) {
      console.error('Error saving account:', error)
      // Error handling is done by the React Query hooks
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      type: 'asset',
      parent_id: null,
    })
  }

  const accountTypes: AccountType[] = ['asset', 'liability', 'equity', 'income', 'expense', 'tax']

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {editingAccount ? 'Edit Account' : 'Create New Account'}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Account Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>
          <div>
            <Label htmlFor="code">Account Code</Label>
            <Input
              id="code"
              value={formData.code}
              onChange={(e) => setFormData({ ...formData, code: e.target.value })}
              pattern="^\d{4}$"
              placeholder="e.g. 1000"
              required
            />
          </div>
          <div>
            <Label htmlFor="type">Account Type</Label>
            <Select
              value={formData.type}
              onValueChange={(value: AccountType) => setFormData({ ...formData, type: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {accountTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="parent">Parent Account (Optional)</Label>
            <Select
              value={formData.parent_id || 'NO_PARENT'}
              onValueChange={(value) => setFormData({ ...formData, parent_id: value === 'NO_PARENT' ? null : value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select parent account" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="NO_PARENT">No parent</SelectItem>
                {accounts
                  .filter(acc => acc.id !== editingAccount?.id)
                  .map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.code} - {account.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2 pt-4">
            <Button type="submit" className="flex-1">
              {editingAccount ? 'Update' : 'Create'} Account
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
