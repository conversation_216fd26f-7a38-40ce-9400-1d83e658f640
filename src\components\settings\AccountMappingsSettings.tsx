import React, { useState, useMemo, useCallback, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, AlertTriangle, Settings, Play, Save } from 'lucide-react'
import { useAccountMappings } from '@/hooks/useAccountMappings'
import { useActiveAccounts } from '@/hooks/queries/useAccounts'
import { toast } from 'sonner'
import { GenerateMissingEntriesDebug } from '@/components/debug/GenerateMissingEntriesDebug'

const MAPPING_TYPES = [
  {
    key: 'accounts_receivable',
    label: 'Accounts Receivable',
    description: 'Account for tracking customer invoices',
    accountType: 'asset'
  },
  {
    key: 'accounts_payable',
    label: 'Accounts Payable',
    description: 'Account for tracking vendor bills',
    accountType: 'liability'
  },
  {
    key: 'sales_revenue',
    label: 'Sales Revenue',
    description: 'Default account for invoice revenue',
    accountType: 'income'
  },
  {
    key: 'vat_payable',
    label: 'VAT Payable',
    description: 'Account for output VAT on sales',
    accountType: 'liability'
  },
  {
    key: 'vat_input',
    label: 'VAT Input',
    description: 'Account for input VAT on purchases',
    accountType: 'asset'
  },
  {
    key: 'inventory_asset',
    label: 'Inventory Asset',
    description: 'Account for tracking inventory value',
    accountType: 'asset'
  },
  {
    key: 'cost_of_goods_sold',
    label: 'Cost of Goods Sold',
    description: 'Account for COGS when inventory is sold',
    accountType: 'expense'
  },
  {
    key: 'inventory_adjustment',
    label: 'Inventory Adjustment',
    description: 'Account for inventory adjustments and variances',
    accountType: 'expense'
  }
]

export const AccountMappingsSettings = React.memo(function AccountMappingsSettings() {
  const [selectedMappings, setSelectedMappings] = useState<Record<string, string>>({})
  const [savingMappings, setSavingMappings] = useState<Set<string>>(new Set())

  const {
    accountMappings,
    isLoading: mappingsLoading,
    validateMappings,
    isValidating,
    validationResult,
    updateMapping,
    isUpdating,
    generateMissingEntries,
    isGenerating
  } = useAccountMappings()

  const { data: accounts = [], isLoading: accountsLoading, error: accountsError } = useActiveAccounts()

  // Memoize accounts by type for performance
  const accountsByType = useMemo(() => {
    if (!accounts || !Array.isArray(accounts)) {
      return {}
    }

    return accounts.reduce((acc, account) => {
      if (!acc[account.type]) {
        acc[account.type] = []
      }
      acc[account.type].push(account)
      return acc
    }, {} as Record<string, typeof accounts>)
  }, [accounts])

  // Initialize selected mappings from existing data
  useEffect(() => {
    if (accountMappings.length > 0) {
      const mappings: Record<string, string> = {}
      accountMappings.forEach(mapping => {
        if (mapping.is_default) {
          mappings[mapping.mapping_type] = mapping.account_id
        }
      })
      setSelectedMappings(mappings)
    }
  }, [accountMappings])

  // Handle mapping changes
  const handleMappingChange = useCallback((mappingType: string, accountId: string) => {
    setSelectedMappings(prev => ({
      ...prev,
      [mappingType]: accountId
    }))
  }, [])

  // Handle save with notifications
  const handleSaveMapping = useCallback(async (mappingType: string) => {
    const accountId = selectedMappings[mappingType]
    if (!accountId) {
      toast.error('Please select an account before saving')
      return
    }

    setSavingMappings(prev => new Set(prev).add(mappingType))

    try {
      toast.loading('Saving account mapping...', { id: `save-${mappingType}` })
      await updateMapping({ mapping_type: mappingType, account_id: accountId })
      toast.success('Account mapping saved successfully', { id: `save-${mappingType}` })
    } catch (error) {
      toast.error('Failed to save account mapping', { id: `save-${mappingType}` })
      console.error('Save mapping error:', error)
    } finally {
      setSavingMappings(prev => {
        const newSet = new Set(prev)
        newSet.delete(mappingType)
        return newSet
      })
    }
  }, [selectedMappings, updateMapping])

  // Get accounts by type with memoization
  const getAccountsByType = useCallback((accountType: string) => {
    return accountsByType[accountType] || []
  }, [accountsByType])

  // Get current mapping with memoization
  const getCurrentMapping = useCallback((mappingType: string) => {
    return accountMappings.find(mapping =>
      mapping.mapping_type === mappingType && mapping.is_default
    )
  }, [accountMappings])

  // Get validation status with memoization
  const getValidationStatus = useCallback((mappingType: string) => {
    if (!validationResult) return null
    return validationResult.find(result => result.mapping_type === mappingType)
  }, [validationResult])

  // Handle validation with notifications
  const handleValidateMappings = useCallback(async () => {
    try {
      toast.loading('Validating account mappings...', { id: 'validate' })
      await validateMappings()
      toast.success('Account mappings validated successfully', { id: 'validate' })
    } catch (error) {
      toast.error('Failed to validate account mappings', { id: 'validate' })
      console.error('Validation error:', error)
    }
  }, [validateMappings])

  // Handle generate missing entries with notifications
  const handleGenerateMissingEntries = useCallback(async () => {
    try {
      toast.loading('Generating missing journal entries...', { id: 'generate' })

      // Call the mutation and let it handle its own success/error logic
      generateMissingEntries()

      // Remove the success toast here since the mutation handles it
      toast.dismiss('generate')
    } catch (error) {
      toast.error('Failed to generate missing journal entries', { id: 'generate' })
      console.error('Generate entries error:', error)
    }
  }, [generateMissingEntries])

  // Show loading state
  if (mappingsLoading || accountsLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading account mappings...</span>
        </CardContent>
      </Card>
    )
  }

  // Show error state
  if (accountsError) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <AlertTriangle className="h-6 w-6 text-destructive" />
          <span className="ml-2">Failed to load accounts. Please try again.</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Debug Component - Remove in production */}
      <GenerateMissingEntriesDebug />

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Account Mappings for Automatic Journal Entries
          </CardTitle>
          <CardDescription>
            Configure which accounts to use for automatic double-entry bookkeeping when invoices and bills are created.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex gap-2">
            <Button
              onClick={handleValidateMappings}
              disabled={isValidating}
              variant="outline"
            >
              {isValidating ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              Validate Mappings
            </Button>

            <Button
              onClick={handleGenerateMissingEntries}
              disabled={isGenerating}
              variant="outline"
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              Generate Missing Entries
            </Button>
          </div>

          {validationResult && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-medium">Validation Results:</p>
                  {validationResult.map(result => (
                    <div key={result.mapping_type} className="flex items-center gap-2">
                      <Badge variant={result.status === 'configured' ? 'default' : 'destructive'}>
                        {result.mapping_type}
                      </Badge>
                      <span className="text-sm">
                        {result.status === 'configured' 
                          ? `${result.account_name} (${result.account_code})`
                          : 'Not configured'
                        }
                      </span>
                    </div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="grid gap-6">
            {MAPPING_TYPES.map(mappingType => {
              const currentMapping = getCurrentMapping(mappingType.key)
              const validationStatus = getValidationStatus(mappingType.key)
              const availableAccounts = getAccountsByType(mappingType.accountType)
              const hasUnsavedChanges = selectedMappings[mappingType.key] !== currentMapping?.account_id
              const isSaving = savingMappings.has(mappingType.key)

              return (
                <div key={mappingType.key} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-medium">{mappingType.label}</h3>
                      <p className="text-sm text-muted-foreground">{mappingType.description}</p>
                    </div>
                    <Badge 
                      variant={validationStatus?.status === 'configured' ? 'default' : 'destructive'}
                    >
                      {validationStatus?.status || 'unknown'}
                    </Badge>
                  </div>

                  <div className="flex items-center gap-3">
                    <Select
                      value={selectedMappings[mappingType.key] || ''}
                      onValueChange={(value) => handleMappingChange(mappingType.key, value)}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Select account..." />
                      </SelectTrigger>
                      <SelectContent>
                        {availableAccounts.length > 0 ? (
                          availableAccounts.map(account => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.code} - {account.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="none" disabled>
                            No {mappingType.accountType} accounts available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>

                    <Button
                      onClick={() => handleSaveMapping(mappingType.key)}
                      disabled={!hasUnsavedChanges || isSaving || availableAccounts.length === 0}
                      size="sm"
                      variant={hasUnsavedChanges ? "default" : "outline"}
                    >
                      {isSaving ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : (
                        <Save className="h-4 w-4 mr-1" />
                      )}
                      {isSaving ? 'Saving...' : 'Save'}
                    </Button>
                  </div>

                  {currentMapping && (
                    <div className="text-sm text-muted-foreground">
                      Current: {currentMapping.account?.code} - {currentMapping.account?.name}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>How Automatic Journal Entries Work</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">When an Invoice is Created:</h4>
            <div className="text-sm space-y-1 ml-4">
              <div>• <strong>Debit:</strong> Accounts Receivable (full amount including VAT)</div>
              <div>• <strong>Credit:</strong> Sales Revenue (net amount)</div>
              <div>• <strong>Credit:</strong> VAT Payable (VAT amount, if applicable)</div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">When a Bill is Created:</h4>
            <div className="text-sm space-y-1 ml-4">
              <div>• <strong>Debit:</strong> Expense Account (net amount)</div>
              <div>• <strong>Debit:</strong> VAT Input (VAT amount, if applicable)</div>
              <div>• <strong>Credit:</strong> Accounts Payable (full amount including VAT)</div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">When Documents are Cancelled:</h4>
            <div className="text-sm space-y-1 ml-4">
              <div>• Automatic reversal entries are created</div>
              <div>• Original entries are reversed with opposite debits/credits</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
})
