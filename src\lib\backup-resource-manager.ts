// Backup Resource Management and Throttling System
// Provides resource limits, rate limiting, and concurrent operation management

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'

export interface ResourceLimits {
  maxConcurrentBackups: number
  maxConcurrentRestores: number
  maxMemoryUsageMB: number
  maxDiskUsageGB: number
  maxBandwidthMBps: number
  maxOperationsPerHour: number
  maxOperationsPerDay: number
}

export interface ResourceUsage {
  activeBackups: number
  activeRestores: number
  memoryUsageMB: number
  diskUsageGB: number
  bandwidthUsageMBps: number
  operationsLastHour: number
  operationsLastDay: number
  lastUpdated: string
}

export interface ThrottleConfig {
  enabled: boolean
  backupInterval: number // milliseconds between backup operations
  restoreInterval: number // milliseconds between restore operations
  maxRetries: number
  backoffMultiplier: number
  maxBackoffMs: number
}

export interface RateLimitResult {
  allowed: boolean
  reason?: string
  retryAfter?: number // seconds
  currentUsage: ResourceUsage
}

/**
 * Backup Resource Manager
 * Handles resource limits, throttling, and concurrent operation management
 */
export class BackupResourceManager {
  private static readonly DEFAULT_LIMITS: ResourceLimits = {
    maxConcurrentBackups: 2,
    maxConcurrentRestores: 1,
    maxMemoryUsageMB: 1024,
    maxDiskUsageGB: 10,
    maxBandwidthMBps: 50,
    maxOperationsPerHour: 10,
    maxOperationsPerDay: 50
  }

  private static readonly DEFAULT_THROTTLE: ThrottleConfig = {
    enabled: true,
    backupInterval: 5000, // 5 seconds between backups
    restoreInterval: 10000, // 10 seconds between restores
    maxRetries: 3,
    backoffMultiplier: 2,
    maxBackoffMs: 30000 // 30 seconds max backoff
  }

  private static resourceUsage: Map<string, ResourceUsage> = new Map()
  private static operationQueue: Map<string, Date[]> = new Map()

  /**
   * Check if operation is allowed based on resource limits
   */
  static async checkResourceLimits(
    orgId: string,
    operation: 'backup' | 'restore',
    estimatedResourceUsage?: Partial<ResourceUsage>
  ): Promise<RateLimitResult> {
    try {
      // Get organization-specific limits
      const limits = await this.getResourceLimits(orgId)
      
      // Get current resource usage
      const currentUsage = await this.getCurrentResourceUsage(orgId)
      
      // Check concurrent operation limits
      if (operation === 'backup' && currentUsage.activeBackups >= limits.maxConcurrentBackups) {
        return {
          allowed: false,
          reason: `Maximum concurrent backups (${limits.maxConcurrentBackups}) exceeded`,
          retryAfter: 60,
          currentUsage
        }
      }
      
      if (operation === 'restore' && currentUsage.activeRestores >= limits.maxConcurrentRestores) {
        return {
          allowed: false,
          reason: `Maximum concurrent restores (${limits.maxConcurrentRestores}) exceeded`,
          retryAfter: 120,
          currentUsage
        }
      }
      
      // Check memory limits
      const estimatedMemory = estimatedResourceUsage?.memoryUsageMB || 0
      if (currentUsage.memoryUsageMB + estimatedMemory > limits.maxMemoryUsageMB) {
        return {
          allowed: false,
          reason: `Memory limit (${limits.maxMemoryUsageMB}MB) would be exceeded`,
          retryAfter: 30,
          currentUsage
        }
      }
      
      // Check disk usage limits
      const estimatedDisk = estimatedResourceUsage?.diskUsageGB || 0
      if (currentUsage.diskUsageGB + estimatedDisk > limits.maxDiskUsageGB) {
        return {
          allowed: false,
          reason: `Disk usage limit (${limits.maxDiskUsageGB}GB) would be exceeded`,
          retryAfter: 300,
          currentUsage
        }
      }
      
      // Check rate limits
      const rateLimitCheck = await this.checkRateLimits(orgId, operation, limits)
      if (!rateLimitCheck.allowed) {
        return rateLimitCheck
      }
      
      return {
        allowed: true,
        currentUsage
      }
      
    } catch (error) {
      console.error('Error checking resource limits:', error)
      return {
        allowed: false,
        reason: 'Resource limit check failed',
        currentUsage: await this.getCurrentResourceUsage(orgId)
      }
    }
  }

  /**
   * Check rate limits for operations
   */
  private static async checkRateLimits(
    orgId: string,
    operation: 'backup' | 'restore',
    limits: ResourceLimits
  ): Promise<RateLimitResult> {
    const currentUsage = await this.getCurrentResourceUsage(orgId)
    
    // Check hourly limits
    if (currentUsage.operationsLastHour >= limits.maxOperationsPerHour) {
      return {
        allowed: false,
        reason: `Hourly operation limit (${limits.maxOperationsPerHour}) exceeded`,
        retryAfter: 3600, // 1 hour
        currentUsage
      }
    }
    
    // Check daily limits
    if (currentUsage.operationsLastDay >= limits.maxOperationsPerDay) {
      return {
        allowed: false,
        reason: `Daily operation limit (${limits.maxOperationsPerDay}) exceeded`,
        retryAfter: 86400, // 24 hours
        currentUsage
      }
    }
    
    return {
      allowed: true,
      currentUsage
    }
  }

  /**
   * Reserve resources for an operation
   */
  static async reserveResources(
    orgId: string,
    operation: 'backup' | 'restore',
    estimatedUsage: Partial<ResourceUsage>
  ): Promise<{ success: boolean; reservationId?: string; error?: string }> {
    try {
      const reservationId = `${operation}_${orgId}_${Date.now()}`
      
      // Update resource usage
      const currentUsage = await this.getCurrentResourceUsage(orgId)
      const newUsage: ResourceUsage = {
        ...currentUsage,
        activeBackups: operation === 'backup' 
          ? currentUsage.activeBackups + 1 
          : currentUsage.activeBackups,
        activeRestores: operation === 'restore' 
          ? currentUsage.activeRestores + 1 
          : currentUsage.activeRestores,
        memoryUsageMB: currentUsage.memoryUsageMB + (estimatedUsage.memoryUsageMB || 0),
        diskUsageGB: currentUsage.diskUsageGB + (estimatedUsage.diskUsageGB || 0),
        lastUpdated: new Date().toISOString()
      }
      
      // Store updated usage
      this.resourceUsage.set(orgId, newUsage)
      
      // Record operation for rate limiting
      await this.recordOperation(orgId, operation)
      
      // Log resource reservation
      await auditLogger.logActivity({
        entity_type: 'backup_resource',
        entity_id: reservationId,
        action: 'resource_reserved',
        description: `Resources reserved for ${operation} operation`,
        severity: 'info',
        category: 'system',
        metadata: {
          operation,
          estimated_usage: estimatedUsage,
          reservation_id: reservationId
        }
      })
      
      return { success: true, reservationId }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Resource reservation failed'
      }
    }
  }

  /**
   * Release reserved resources
   */
  static async releaseResources(
    orgId: string,
    reservationId: string,
    actualUsage: Partial<ResourceUsage>
  ): Promise<void> {
    try {
      const currentUsage = await this.getCurrentResourceUsage(orgId)
      const operation = reservationId.split('_')[0] as 'backup' | 'restore'
      
      const newUsage: ResourceUsage = {
        ...currentUsage,
        activeBackups: operation === 'backup' 
          ? Math.max(0, currentUsage.activeBackups - 1)
          : currentUsage.activeBackups,
        activeRestores: operation === 'restore' 
          ? Math.max(0, currentUsage.activeRestores - 1)
          : currentUsage.activeRestores,
        memoryUsageMB: Math.max(0, currentUsage.memoryUsageMB - (actualUsage.memoryUsageMB || 0)),
        diskUsageGB: currentUsage.diskUsageGB + (actualUsage.diskUsageGB || 0), // Disk usage accumulates
        lastUpdated: new Date().toISOString()
      }
      
      this.resourceUsage.set(orgId, newUsage)
      
      // Log resource release
      await auditLogger.logActivity({
        entity_type: 'backup_resource',
        entity_id: reservationId,
        action: 'resource_released',
        description: `Resources released for ${operation} operation`,
        severity: 'info',
        category: 'system',
        metadata: {
          operation,
          actual_usage: actualUsage,
          reservation_id: reservationId
        }
      })
      
    } catch (error) {
      console.error('Error releasing resources:', error)
    }
  }

  /**
   * Apply throttling to operation
   */
  static async applyThrottling(
    orgId: string,
    operation: 'backup' | 'restore'
  ): Promise<{ shouldWait: boolean; waitTime: number }> {
    try {
      const throttleConfig = await this.getThrottleConfig(orgId)
      
      if (!throttleConfig.enabled) {
        return { shouldWait: false, waitTime: 0 }
      }
      
      const lastOperations = this.operationQueue.get(orgId) || []
      const now = new Date()
      
      // Filter recent operations
      const recentOperations = lastOperations.filter(
        opTime => now.getTime() - opTime.getTime() < 60000 // Last minute
      )
      
      if (recentOperations.length === 0) {
        return { shouldWait: false, waitTime: 0 }
      }
      
      const lastOperation = recentOperations[recentOperations.length - 1]
      const timeSinceLastOp = now.getTime() - lastOperation.getTime()
      
      const requiredInterval = operation === 'backup' 
        ? throttleConfig.backupInterval 
        : throttleConfig.restoreInterval
      
      if (timeSinceLastOp < requiredInterval) {
        const waitTime = requiredInterval - timeSinceLastOp
        return { shouldWait: true, waitTime }
      }
      
      return { shouldWait: false, waitTime: 0 }
      
    } catch (error) {
      console.error('Error applying throttling:', error)
      return { shouldWait: false, waitTime: 0 }
    }
  }

  /**
   * Get resource limits for organization
   */
  private static async getResourceLimits(orgId: string): Promise<ResourceLimits> {
    try {
      const { data: settings, error } = await supabase
        .from('backup_settings')
        .select('resource_limits')
        .eq('org_id', orgId)
        .single()
      
      if (error || !settings?.resource_limits) {
        return this.DEFAULT_LIMITS
      }
      
      return { ...this.DEFAULT_LIMITS, ...settings.resource_limits }
      
    } catch (error) {
      console.error('Error getting resource limits:', error)
      return this.DEFAULT_LIMITS
    }
  }

  /**
   * Get throttle configuration for organization
   */
  private static async getThrottleConfig(orgId: string): Promise<ThrottleConfig> {
    try {
      const { data: settings, error } = await supabase
        .from('backup_settings')
        .select('throttle_config')
        .eq('org_id', orgId)
        .single()
      
      if (error || !settings?.throttle_config) {
        return this.DEFAULT_THROTTLE
      }
      
      return { ...this.DEFAULT_THROTTLE, ...settings.throttle_config }
      
    } catch (error) {
      console.error('Error getting throttle config:', error)
      return this.DEFAULT_THROTTLE
    }
  }

  /**
   * Get current resource usage for organization
   */
  private static async getCurrentResourceUsage(orgId: string): Promise<ResourceUsage> {
    // Check in-memory cache first
    const cached = this.resourceUsage.get(orgId)
    if (cached && Date.now() - new Date(cached.lastUpdated).getTime() < 30000) {
      return cached
    }
    
    try {
      // Get active operations from database
      const { data: activeBackups } = await supabase
        .from('backup_metadata')
        .select('id')
        .eq('org_id', orgId)
        .in('status', ['pending', 'in_progress'])
      
      const { data: activeRestores } = await supabase
        .from('backup_restorations')
        .select('id')
        .eq('org_id', orgId)
        .in('status', ['pending', 'in_progress'])
      
      // Get operation counts for rate limiting
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      
      const { data: hourlyOps } = await supabase
        .from('backup_metadata')
        .select('id')
        .eq('org_id', orgId)
        .gte('created_at', oneHourAgo.toISOString())
      
      const { data: dailyOps } = await supabase
        .from('backup_metadata')
        .select('id')
        .eq('org_id', orgId)
        .gte('created_at', oneDayAgo.toISOString())
      
      const usage: ResourceUsage = {
        activeBackups: activeBackups?.length || 0,
        activeRestores: activeRestores?.length || 0,
        memoryUsageMB: 0, // Would need system monitoring integration
        diskUsageGB: 0, // Would need storage usage calculation
        bandwidthUsageMBps: 0, // Would need network monitoring
        operationsLastHour: hourlyOps?.length || 0,
        operationsLastDay: dailyOps?.length || 0,
        lastUpdated: new Date().toISOString()
      }
      
      // Cache the result
      this.resourceUsage.set(orgId, usage)
      
      return usage
      
    } catch (error) {
      console.error('Error getting resource usage:', error)
      
      // Return default usage on error
      return {
        activeBackups: 0,
        activeRestores: 0,
        memoryUsageMB: 0,
        diskUsageGB: 0,
        bandwidthUsageMBps: 0,
        operationsLastHour: 0,
        operationsLastDay: 0,
        lastUpdated: new Date().toISOString()
      }
    }
  }

  /**
   * Record operation for rate limiting
   */
  private static async recordOperation(orgId: string, operation: 'backup' | 'restore'): Promise<void> {
    const operations = this.operationQueue.get(orgId) || []
    operations.push(new Date())
    
    // Keep only last 100 operations
    if (operations.length > 100) {
      operations.splice(0, operations.length - 100)
    }
    
    this.operationQueue.set(orgId, operations)
  }

  /**
   * Get resource usage statistics
   */
  static async getResourceStatistics(orgId: string): Promise<{
    current: ResourceUsage
    limits: ResourceLimits
    utilizationPercentage: {
      concurrentBackups: number
      concurrentRestores: number
      memory: number
      disk: number
      hourlyOperations: number
      dailyOperations: number
    }
  }> {
    const current = await this.getCurrentResourceUsage(orgId)
    const limits = await this.getResourceLimits(orgId)
    
    const utilizationPercentage = {
      concurrentBackups: (current.activeBackups / limits.maxConcurrentBackups) * 100,
      concurrentRestores: (current.activeRestores / limits.maxConcurrentRestores) * 100,
      memory: (current.memoryUsageMB / limits.maxMemoryUsageMB) * 100,
      disk: (current.diskUsageGB / limits.maxDiskUsageGB) * 100,
      hourlyOperations: (current.operationsLastHour / limits.maxOperationsPerHour) * 100,
      dailyOperations: (current.operationsLastDay / limits.maxOperationsPerDay) * 100
    }
    
    return {
      current,
      limits,
      utilizationPercentage
    }
  }
}
