import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { LoadingSpinner } from '@/components/ui/loading'
import { Calendar, FileText } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface TaxSummary {
  taxRateName: string
  taxableAmount: number
  taxAmount: number
}

interface WithholdingTaxSummary {
  rateName: string
  grossAmount: number
  withholdingAmount: number
}

export const TaxReports = () => {
  const { profile } = useAuth()
  const [vatSummary, setVatSummary] = useState<TaxSummary[]>([])
  const [withholdingSummary, setWithholdingSummary] = useState<WithholdingTaxSummary[]>([])
  const [loading, setLoading] = useState(false)
  const [startDate, setStartDate] = useState(() => {
    const date = new Date()
    date.setMonth(0, 1) // January 1st of current year
    return date.toISOString().split('T')[0]
  })
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0])

  const fetchTaxReports = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      
      // Fetch VAT data from transaction lines with tax rates
      const { data: vatTransactions, error: vatError } = await supabase
        .from('transaction_lines')
        .select(`
          tax_amount,
          debit,
          credit,
          tax_rate_id,
          withholding_tax_rates!tax_rate_id(name, rate_pct)
        `)
        .eq('org_id', profile.org_id)
        .not('tax_rate_id', 'is', null)
        .gte('created_at', `${startDate}T00:00:00`)
        .lte('created_at', `${endDate}T23:59:59`)

      if (vatError) throw vatError

      // Fetch tax rates for reference
      const { data: taxRates } = await supabase
        .from('tax_rates')
        .select('id, name, rate_pct')
        .eq('org_id', profile.org_id)

      // Group VAT by tax rate
      const vatMap = new Map<string, { taxableAmount: number; taxAmount: number }>()
      
      vatTransactions?.forEach(transaction => {
        // Try to find the tax rate name from either withholding_tax_rates or tax_rates
        let rateName = 'Unknown'
        if (transaction.withholding_tax_rates) {
          rateName = transaction.withholding_tax_rates.name
        } else if (taxRates) {
          const taxRate = taxRates.find(tr => tr.id === transaction.tax_rate_id)
          if (taxRate) {
            rateName = taxRate.name
          }
        }

        const lineAmount = (transaction.debit || 0) + (transaction.credit || 0)
        const taxAmount = transaction.tax_amount || 0
        
        if (!vatMap.has(rateName)) {
          vatMap.set(rateName, { taxableAmount: 0, taxAmount: 0 })
        }
        
        const existing = vatMap.get(rateName)!
        existing.taxableAmount += lineAmount
        existing.taxAmount += taxAmount
      })

      const vatSummaryData: TaxSummary[] = Array.from(vatMap.entries()).map(([rateName, data]) => ({
        taxRateName: rateName,
        taxableAmount: data.taxableAmount,
        taxAmount: data.taxAmount
      }))

      // Fetch withholding tax data from invoices and bills
      const { data: invoiceWithholding } = await supabase
        .from('invoices')
        .select(`
          total_amount,
          withholding_amount,
          withholding_tax_rates!withholding_tax_rate_id(name, rate_pct)
        `)
        .eq('org_id', profile.org_id)
        .not('withholding_tax_rate_id', 'is', null)
        .gte('date_issued', startDate)
        .lte('date_issued', endDate)

      const { data: billWithholding } = await supabase
        .from('bills')
        .select(`
          total_amount,
          withholding_amount,
          withholding_tax_rates!withholding_tax_rate_id(name, rate_pct)
        `)
        .eq('org_id', profile.org_id)
        .not('withholding_tax_rate_id', 'is', null)
        .gte('date_issued', startDate)
        .lte('date_issued', endDate)

      // Group withholding tax
      const withholdingMap = new Map<string, { grossAmount: number; withholdingAmount: number }>()
      
      const allWithholding = [...(invoiceWithholding || []), ...(billWithholding || [])]
      allWithholding.forEach(item => {
        const rateName = item.withholding_tax_rates?.name || 'Unknown'
        
        if (!withholdingMap.has(rateName)) {
          withholdingMap.set(rateName, { grossAmount: 0, withholdingAmount: 0 })
        }
        
        const existing = withholdingMap.get(rateName)!
        existing.grossAmount += item.total_amount || 0
        existing.withholdingAmount += item.withholding_amount || 0
      })

      const withholdingSummaryData: WithholdingTaxSummary[] = Array.from(withholdingMap.entries()).map(([rateName, data]) => ({
        rateName,
        grossAmount: data.grossAmount,
        withholdingAmount: data.withholdingAmount
      }))

      setVatSummary(vatSummaryData)
      setWithholdingSummary(withholdingSummaryData)
    } catch (error) {
      console.error('Error fetching tax reports:', error)
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, startDate, endDate])

  useEffect(() => {
    if (profile?.org_id) {
      fetchTaxReports()
    }
  }, [profile?.org_id, startDate, endDate, fetchTaxReports])

  if (loading) {
    return (
      <div className="p-8">
        <LoadingSpinner text="Loading tax reports..." showText />
      </div>
    )
  }

  const totalVatAmount = vatSummary.reduce((sum, item) => sum + item.taxAmount, 0)
  const totalWithholdingAmount = withholdingSummary.reduce((sum, item) => sum + item.withholdingAmount, 0)

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Label htmlFor="startDate">From:</Label>
          <Input
            id="startDate"
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="w-auto"
          />
        </div>
        <div className="flex items-center gap-2">
          <Label htmlFor="endDate">To:</Label>
          <Input
            id="endDate"
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            className="w-auto"
          />
        </div>
        <Button onClick={fetchTaxReports} size="sm">
          <Calendar className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="vat" className="space-y-4">
        <TabsList>
          <TabsTrigger value="vat">VAT Summary</TabsTrigger>
          <TabsTrigger value="withholding">Withholding Tax</TabsTrigger>
          <TabsTrigger value="filings">Tax Filings</TabsTrigger>
        </TabsList>

        <TabsContent value="vat">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                VAT Summary Report
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tax Rate</TableHead>
                      <TableHead className="text-right">Taxable Amount</TableHead>
                      <TableHead className="text-right">Tax Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {vatSummary.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.taxRateName}</TableCell>
                        <TableCell className="text-right font-mono">
                          {formatCurrency(item.taxableAmount)}
                        </TableCell>
                        <TableCell className="text-right font-mono">
                          {formatCurrency(item.taxAmount)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-bold border-t-2 bg-primary/10">
                      <TableCell>Total</TableCell>
                      <TableCell className="text-right font-mono">
                        {formatCurrency(vatSummary.reduce((sum, item) => sum + item.taxableAmount, 0))}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {formatCurrency(totalVatAmount)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="withholding">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Withholding Tax Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tax Rate</TableHead>
                      <TableHead className="text-right">Gross Amount</TableHead>
                      <TableHead className="text-right">Withholding Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {withholdingSummary.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.rateName}</TableCell>
                        <TableCell className="text-right font-mono">
                          {formatCurrency(item.grossAmount)}
                        </TableCell>
                        <TableCell className="text-right font-mono">
                          {formatCurrency(item.withholdingAmount)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-bold border-t-2 bg-primary/10">
                      <TableCell>Total</TableCell>
                      <TableCell className="text-right font-mono">
                        {formatCurrency(withholdingSummary.reduce((sum, item) => sum + item.grossAmount, 0))}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {formatCurrency(totalWithholdingAmount)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="filings">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Tax Filing Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">VAT Payable</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{formatCurrency(totalVatAmount)}</p>
                    <p className="text-sm text-muted-foreground">Current period</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Withholding Tax</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{formatCurrency(totalWithholdingAmount)}</p>
                    <p className="text-sm text-muted-foreground">To be remitted</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Net Tax Position</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">
                      {formatCurrency(totalVatAmount - totalWithholdingAmount)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {totalVatAmount > totalWithholdingAmount ? 'Payable' : 'Refundable'}
                    </p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
