import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys, type QueryFilters } from '@/lib/queryKeys'
import { toast } from '@/hooks/use-toast'
import type { InvoiceWithCustomer, InvoiceFormData } from '@/types/invoices'

/**
 * Hook to fetch all invoices for the organization
 */
export function useInvoices(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: filters 
      ? queryKeys.invoices.filtered(profile?.org_id || '', filters)
      : queryKeys.invoices.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('invoices')
        .select(`
          *,
          customers(*)
        `)
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`invoice_number.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }
      
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      // Apply ordering
      query = query.order('created_at', { ascending: false })

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit)
      }
      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

/**
 * Hook to fetch invoices by customer
 */
export function useInvoicesByCustomer(customerId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.invoices.byCustomer(profile?.org_id || '', customerId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !customerId) return []

      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers(*)
        `)
        .eq('org_id', profile.org_id)
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id && !!customerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch invoices by status
 */
export function useInvoicesByStatus(status: string) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.invoices.byStatus(profile?.org_id || '', status),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers(*)
        `)
        .eq('org_id', profile.org_id)
        .eq('status', status)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id && !!status,
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

/**
 * Hook to fetch a single invoice by ID
 */
export function useInvoice(invoiceId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.invoices.detail(profile?.org_id || '', invoiceId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !invoiceId) return null

      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers(*),
          invoice_lines(*)
        `)
        .eq('id', invoiceId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id && !!invoiceId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to create a new invoice
 */
export function useCreateInvoice() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (invoiceData: InvoiceFormData) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { lines, ...invoiceFields } = invoiceData

      // Create the invoice (exclude account_id which belongs to invoice_lines)
      const { account_id, ...validInvoiceFields } = invoiceFields
      const { data: invoice, error: invoiceError } = await supabase
        .from('invoices')
        .insert({
          ...validInvoiceFields,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (invoiceError) throw invoiceError

      // Create invoice lines if provided
      if (lines && lines.length > 0) {
        const { error: linesError } = await supabase
          .from('invoice_lines')
          .insert(
            lines.map(line => ({
              ...line,
              invoice_id: invoice.id,
              org_id: profile.org_id,
            }))
          )

        if (linesError) throw linesError
      }

      return invoice
    },
    onSuccess: (newInvoice) => {
      // Invalidate and refetch invoices list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.invoices.all(profile?.org_id || '') 
      })
      
      // Invalidate customer-specific invoices
      if (newInvoice.customer_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.invoices.byCustomer(profile?.org_id || '', newInvoice.customer_id) 
        })
      }

      // Invalidate status-specific invoices
      if (newInvoice.status) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.invoices.byStatus(profile?.org_id || '', newInvoice.status) 
        })
      }

      toast({
        title: 'Success',
        description: 'Invoice created successfully',
      })
    },
    onError: (error) => {
      console.error('Error creating invoice:', error)
      toast({
        title: 'Error',
        description: 'Failed to create invoice',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update an existing invoice
 */
export function useUpdateInvoice() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      invoiceId, 
      invoiceData 
    }: { 
      invoiceId: string
      invoiceData: Partial<InvoiceFormData>
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { lines, ...invoiceFields } = invoiceData

      // Update the invoice (exclude account_id which belongs to invoice_lines)
      const { account_id, ...validInvoiceFields } = invoiceFields
      const { data, error } = await supabase
        .from('invoices')
        .update({
          ...validInvoiceFields,
          updated_at: new Date().toISOString(),
        })
        .eq('id', invoiceId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error

      // Update invoice lines if provided
      if (lines) {
        // Delete existing lines
        await supabase
          .from('invoice_lines')
          .delete()
          .eq('invoice_id', invoiceId)

        // Insert new lines
        if (lines.length > 0) {
          await supabase
            .from('invoice_lines')
            .insert(
              lines.map(line => ({
                ...line,
                invoice_id: invoiceId,
                org_id: profile.org_id,
              }))
            )
        }
      }

      return data
    },
    onSuccess: (updatedInvoice) => {
      // Update the invoice in the cache
      queryClient.setQueryData(
        queryKeys.invoices.detail(profile?.org_id || '', updatedInvoice.id),
        updatedInvoice
      )

      // Invalidate invoices list to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.invoices.all(profile?.org_id || '') 
      })

      // Invalidate customer-specific invoices
      if (updatedInvoice.customer_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.invoices.byCustomer(profile?.org_id || '', updatedInvoice.customer_id) 
        })
      }

      // Invalidate status-specific invoices
      if (updatedInvoice.status) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.invoices.byStatus(profile?.org_id || '', updatedInvoice.status) 
        })
      }

      toast({
        title: 'Success',
        description: 'Invoice updated successfully',
      })
    },
    onError: (error) => {
      console.error('Error updating invoice:', error)
      toast({
        title: 'Error',
        description: 'Failed to update invoice',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete an invoice
 */
export function useDeleteInvoice() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (invoiceId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Delete invoice lines first
      await supabase
        .from('invoice_lines')
        .delete()
        .eq('invoice_id', invoiceId)

      // Delete the invoice
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', invoiceId)
        .eq('org_id', profile.org_id)

      if (error) throw error
      return invoiceId
    },
    onSuccess: (deletedInvoiceId) => {
      // Remove the invoice from the cache
      queryClient.removeQueries({ 
        queryKey: queryKeys.invoices.detail(profile?.org_id || '', deletedInvoiceId) 
      })

      // Invalidate invoices list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.invoices.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: 'Invoice deleted successfully',
      })
    },
    onError: (error) => {
      console.error('Error deleting invoice:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete invoice',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update invoice status
 */
export function useUpdateInvoiceStatus() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ invoiceId, status }: { invoiceId: string, status: string }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('invoices')
        .update({ 
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', invoiceId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (updatedInvoice) => {
      // Update the invoice in the cache
      queryClient.setQueryData(
        queryKeys.invoices.detail(profile?.org_id || '', updatedInvoice.id),
        updatedInvoice
      )

      // Invalidate all invoice lists to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.invoices.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: `Invoice status updated to ${updatedInvoice.status}`,
      })
    },
    onError: (error) => {
      console.error('Error updating invoice status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update invoice status',
        variant: 'destructive',
      })
    },
  })
}
