import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { 
  CheckCircle, 
  XCircle, 
  MessageSquare, 
  Clock,
  User,
  Calendar,
  DollarSign,
  FileText
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { useApprovalAction, useApprovalActions } from '@/hooks/queries/useApprovalWorkflow'
import { LineItemsDisplay } from './LineItemsDisplay'
import type { ApprovalInstanceWithDetails } from '@/types/approval-workflow'

const approvalSchema = z.object({
  comments: z.string().optional(),
  rejection_reason: z.string().optional(),
})

interface ApprovalActionDialogProps {
  instance: ApprovalInstanceWithDetails
  open: boolean
  onClose: () => void
}

export function ApprovalActionDialog({ 
  instance, 
  open, 
  onClose 
}: ApprovalActionDialogProps) {
  const [action, setAction] = useState<'approve' | 'reject' | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<z.infer<typeof approvalSchema>>({
    resolver: zodResolver(approvalSchema),
    defaultValues: {
      comments: '',
      rejection_reason: '',
    },
  })

  const approvalActionMutation = useApprovalAction()
  const { data: actions } = useApprovalActions(instance.id)

  // Reset state when modal opens
  useEffect(() => {
    if (open) {
      console.log('🔍 ApprovalActionDialog: Opening with instance:', {
        id: instance.id,
        document_type: instance.document_type,
        document_id: instance.document_id,
        status: instance.status,
        document_details: instance.document_details
      })
      setAction(null)
      form.reset()
    }
  }, [open, form, instance])

  const onSubmit = async (values: z.infer<typeof approvalSchema>) => {
    if (!action) return

    setIsSubmitting(true)

    try {
      console.log('🔍 ApprovalActionDialog: Submitting action:', {
        action,
        instanceId: instance.id,
        comments: values.comments,
        rejection_reason: action === 'reject' ? values.rejection_reason : undefined
      })

      const result = await approvalActionMutation.mutateAsync({
        approvalInstanceId: instance.id,
        actionRequest: {
          action,
          comments: values.comments,
          rejection_reason: action === 'reject' ? values.rejection_reason : undefined
        }
      })

      console.log('✅ ApprovalActionDialog: Action result:', result)

      // Reset form and action state
      form.reset()
      setAction(null)
      // Close the modal
      onClose()
    } catch (error) {
      console.error('Error processing approval action:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getStatusIcon(instance.status)}
            Approval Request - {instance.document_details?.title}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-1">
          {/* Document Details */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Document Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Document Number:</span>
                  <span className="text-sm">{instance.document_details?.number}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Type:</span>
                  <Badge variant="outline">
                    {instance.document_type.charAt(0).toUpperCase() + instance.document_type.slice(1)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Amount:</span>
                  <span className="text-sm font-semibold">
                    {formatCurrency(instance.document_amount || 0, instance.currency_code)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Date:</span>
                  <span className="text-sm">{instance.document_details?.date}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant={instance.status === 'pending' ? 'default' : 
                                 instance.status === 'approved' ? 'success' : 'destructive'}>
                    {instance.status.charAt(0).toUpperCase() + instance.status.slice(1)}
                  </Badge>
                </div>
                {instance.document_details?.description && (
                  <div>
                    <span className="text-sm font-medium">Description:</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      {instance.document_details.description}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Workflow Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Submitted By:</span>
                  <span className="text-sm">{instance.submitted_by_profile?.full_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Submitted At:</span>
                  <span className="text-sm flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(instance.submitted_at).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Current Step:</span>
                  <span className="text-sm">
                    {instance.current_step_order} of {instance.total_steps}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Workflow:</span>
                  <span className="text-sm">{instance.workflow_template?.name}</span>
                </div>
                {instance.current_step && (
                  <div>
                    <span className="text-sm font-medium">Current Step:</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      {instance.current_step.name}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Line Items */}
            <LineItemsDisplay
              documentType={instance.document_type}
              documentId={instance.document_id}
              currencyCode={instance.currency_code}
            />
          </div>

          {/* Actions and History */}
          <div className="space-y-4">
            {/* Action Buttons */}
            {instance.status === 'pending' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Take Action</CardTitle>
                </CardHeader>
                <CardContent>
                  {!action ? (
                    <div className="flex gap-2">
                      <Button
                        onClick={() => setAction('approve')}
                        className="flex-1"
                        variant="default"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                      <Button
                        onClick={() => setAction('reject')}
                        className="flex-1"
                        variant="destructive"
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                    </div>
                  ) : (
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                          {action === 'approve' && <CheckCircle className="h-4 w-4 text-green-500" />}
                          {action === 'reject' && <XCircle className="h-4 w-4 text-red-500" />}
                          <span className="font-medium">
                            {action.charAt(0).toUpperCase() + action.slice(1)} this request
                          </span>
                        </div>

                        <FormField
                          control={form.control}
                          name="comments"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Comments</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Add your comments..."
                                  {...field}
                                  rows={3}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {action === 'reject' && (
                          <FormField
                            control={form.control}
                            name="rejection_reason"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Rejection Reason *</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Please provide a reason for rejection..."
                                    {...field}
                                    rows={2}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}

                        <div className="flex gap-2">
                          <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="flex-1"
                            variant={action === 'approve' ? 'default' : 
                                   action === 'reject' ? 'destructive' : 'outline'}
                          >
                            {isSubmitting ? 'Processing...' : `Confirm ${action.charAt(0).toUpperCase() + action.slice(1)}`}
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                              setAction(null)
                              form.reset()
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </form>
                    </Form>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Approval History */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Approval History</CardTitle>
              </CardHeader>
              <CardContent>
                {actions && actions.length > 0 ? (
                  <div className="space-y-3">
                    {actions.map((action, index) => (
                      <div key={action.id} className="flex gap-3 p-3 border rounded-lg">
                        <div className="flex-shrink-0">
                          {getStatusIcon(action.action)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">
                              {action.approver_profile?.full_name}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(action.action_taken_at).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {action.action.charAt(0).toUpperCase() + action.action.slice(1)}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              Step {action.step_order}
                            </span>
                          </div>
                          {action.comments && (
                            <p className="text-sm text-muted-foreground mt-2">
                              {action.comments}
                            </p>
                          )}
                          {action.rejection_reason && (
                            <p className="text-sm text-red-600 mt-2">
                              <strong>Reason:</strong> {action.rejection_reason}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No actions taken yet
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
