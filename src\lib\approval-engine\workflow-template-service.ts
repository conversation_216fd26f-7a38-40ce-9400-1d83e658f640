import { supabase } from '@/lib/supabase'
import type { 
  WorkflowTemplate, 
  ApprovalStep, 
  ApprovalRule,
  DocumentType,
  UserRole
} from '@/types/database'
import type {
  WorkflowTemplateWithSteps,
  CreateWorkflowTemplatePayload,
  CreateApprovalStepPayload,
  CreateApprovalRulePayload
} from '@/types/approval-workflow'

/**
 * Workflow Template Service
 * Manages workflow templates including CRUD operations and validation
 */
export class WorkflowTemplateService {

  /**
   * Get all workflow templates for an organization
   */
  static async getWorkflowTemplates(orgId: string): Promise<WorkflowTemplateWithSteps[]> {
    try {
      const { data, error } = await supabase
        .from('workflow_templates')
        .select(`
          *,
          approval_steps(*),
          approval_rules(*)
        `)
        .eq('org_id', orgId)
        .order('document_type')
        .order('name')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching workflow templates:', error)
      throw error
    }
  }

  /**
   * Get a specific workflow template with steps and rules
   */
  static async getWorkflowTemplate(templateId: string): Promise<WorkflowTemplateWithSteps | null> {
    try {
      const { data, error } = await supabase
        .from('workflow_templates')
        .select(`
          *,
          approval_steps(*),
          approval_rules(*)
        `)
        .eq('id', templateId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching workflow template:', error)
      return null
    }
  }

  /**
   * Create a new workflow template
   */
  static async createWorkflowTemplate(
    payload: CreateWorkflowTemplatePayload,
    orgId: string,
    createdBy: string
  ): Promise<WorkflowTemplateWithSteps> {
    try {
      // Validate payload
      this.validateWorkflowTemplate(payload)

      // Start transaction
      const { data: template, error: templateError } = await supabase
        .from('workflow_templates')
        .insert({
          name: payload.name,
          description: payload.description,
          document_type: payload.document_type,
          is_active: payload.is_active ?? true,
          is_default: payload.is_default ?? false,
          org_id: orgId,
          created_by: createdBy
        })
        .select()
        .single()

      if (templateError) throw templateError

      // Create approval steps
      const stepsToInsert = payload.steps.map(step => ({
        ...step,
        workflow_template_id: template.id,
        org_id: orgId
      }))

      const { data: steps, error: stepsError } = await supabase
        .from('approval_steps')
        .insert(stepsToInsert)
        .select()

      if (stepsError) throw stepsError

      // Create approval rules if provided
      let rules: ApprovalRule[] = []
      if (payload.rules && payload.rules.length > 0) {
        const rulesToInsert = payload.rules.map(rule => ({
          ...rule,
          workflow_template_id: template.id,
          org_id: orgId
        }))

        const { data: rulesData, error: rulesError } = await supabase
          .from('approval_rules')
          .insert(rulesToInsert)
          .select()

        if (rulesError) throw rulesError
        rules = rulesData || []
      }

      return {
        ...template,
        approval_steps: steps || [],
        approval_rules: rules
      }
    } catch (error) {
      console.error('Error creating workflow template:', error)
      throw error
    }
  }

  /**
   * Update an existing workflow template
   */
  static async updateWorkflowTemplate(
    templateId: string,
    payload: Partial<CreateWorkflowTemplatePayload>
  ): Promise<WorkflowTemplateWithSteps> {
    try {
      // Update template
      const { data: template, error: templateError } = await supabase
        .from('workflow_templates')
        .update({
          name: payload.name,
          description: payload.description,
          is_active: payload.is_active,
          is_default: payload.is_default,
          updated_at: new Date().toISOString()
        })
        .eq('id', templateId)
        .select()
        .single()

      if (templateError) throw templateError

      // Update steps if provided
      if (payload.steps) {
        // Delete existing steps
        await supabase
          .from('approval_steps')
          .delete()
          .eq('workflow_template_id', templateId)

        // Insert new steps
        const stepsToInsert = payload.steps.map(step => ({
          ...step,
          workflow_template_id: templateId,
          org_id: template.org_id
        }))

        await supabase
          .from('approval_steps')
          .insert(stepsToInsert)
      }

      // Update rules if provided
      if (payload.rules) {
        // Delete existing rules
        await supabase
          .from('approval_rules')
          .delete()
          .eq('workflow_template_id', templateId)

        // Insert new rules
        if (payload.rules.length > 0) {
          const rulesToInsert = payload.rules.map(rule => ({
            ...rule,
            workflow_template_id: templateId,
            org_id: template.org_id
          }))

          await supabase
            .from('approval_rules')
            .insert(rulesToInsert)
        }
      }

      // Return updated template
      return await this.getWorkflowTemplate(templateId) as WorkflowTemplateWithSteps
    } catch (error) {
      console.error('Error updating workflow template:', error)
      throw error
    }
  }

  /**
   * Delete a workflow template
   */
  static async deleteWorkflowTemplate(templateId: string): Promise<void> {
    try {
      // Check if template is in use
      const { data: instances, error: checkError } = await supabase
        .from('approval_instances')
        .select('id')
        .eq('workflow_template_id', templateId)
        .limit(1)

      if (checkError) throw checkError

      if (instances && instances.length > 0) {
        throw new Error('Cannot delete workflow template that is in use')
      }

      // Delete template (cascading deletes will handle steps and rules)
      const { error } = await supabase
        .from('workflow_templates')
        .delete()
        .eq('id', templateId)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting workflow template:', error)
      throw error
    }
  }

  /**
   * Create default workflow templates for an organization
   */
  static async createDefaultTemplates(orgId: string, createdBy: string): Promise<void> {
    try {
      // Use the database function to create default templates
      const { error } = await supabase.rpc('create_default_workflow_templates', {
        p_org_id: orgId,
        p_created_by: createdBy
      })

      if (error) throw error
    } catch (error) {
      console.error('Error creating default templates:', error)
      throw error
    }
  }

  /**
   * Get default template for a document type
   */
  static async getDefaultTemplate(
    orgId: string, 
    documentType: DocumentType
  ): Promise<WorkflowTemplateWithSteps | null> {
    try {
      const { data, error } = await supabase
        .from('workflow_templates')
        .select(`
          *,
          approval_steps(*),
          approval_rules(*)
        `)
        .eq('org_id', orgId)
        .eq('document_type', documentType)
        .eq('is_default', true)
        .eq('is_active', true)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching default template:', error)
      return null
    }
  }

  /**
   * Set template as default for its document type
   */
  static async setAsDefault(templateId: string): Promise<void> {
    try {
      // Get template info
      const { data: template, error: templateError } = await supabase
        .from('workflow_templates')
        .select('org_id, document_type')
        .eq('id', templateId)
        .single()

      if (templateError) throw templateError

      // Unset current default
      await supabase
        .from('workflow_templates')
        .update({ is_default: false })
        .eq('org_id', template.org_id)
        .eq('document_type', template.document_type)

      // Set new default
      const { error } = await supabase
        .from('workflow_templates')
        .update({ is_default: true })
        .eq('id', templateId)

      if (error) throw error
    } catch (error) {
      console.error('Error setting default template:', error)
      throw error
    }
  }

  /**
   * Validate workflow template payload
   */
  private static validateWorkflowTemplate(payload: CreateWorkflowTemplatePayload): void {
    if (!payload.name || payload.name.trim().length === 0) {
      throw new Error('Template name is required')
    }

    if (!payload.document_type) {
      throw new Error('Document type is required')
    }

    if (!payload.steps || payload.steps.length === 0) {
      throw new Error('At least one approval step is required')
    }

    // Validate steps
    const stepOrders = new Set<number>()
    for (const step of payload.steps) {
      if (stepOrders.has(step.step_order)) {
        throw new Error(`Duplicate step order: ${step.step_order}`)
      }
      stepOrders.add(step.step_order)

      if (!step.name || step.name.trim().length === 0) {
        throw new Error('Step name is required')
      }

      if (!step.required_role || step.required_role.length === 0) {
        throw new Error('At least one required role must be specified for each step')
      }

      if (step.required_approvers && step.required_approvers < 1) {
        throw new Error('Required approvers must be at least 1')
      }
    }

    // Validate step orders are sequential starting from 1
    const sortedOrders = Array.from(stepOrders).sort((a, b) => a - b)
    for (let i = 0; i < sortedOrders.length; i++) {
      if (sortedOrders[i] !== i + 1) {
        throw new Error('Step orders must be sequential starting from 1')
      }
    }
  }

  /**
   * Clone an existing workflow template
   */
  static async cloneWorkflowTemplate(
    templateId: string,
    newName: string,
    orgId: string,
    createdBy: string
  ): Promise<WorkflowTemplateWithSteps> {
    try {
      const originalTemplate = await this.getWorkflowTemplate(templateId)
      if (!originalTemplate) {
        throw new Error('Template not found')
      }

      const clonePayload: CreateWorkflowTemplatePayload = {
        name: newName,
        description: `Cloned from: ${originalTemplate.name}`,
        document_type: originalTemplate.document_type,
        is_active: true,
        is_default: false,
        steps: originalTemplate.approval_steps.map(step => ({
          step_order: step.step_order,
          name: step.name,
          description: step.description,
          step_type: step.step_type,
          required_role: step.required_role,
          required_approvers: step.required_approvers,
          allow_self_approval: step.allow_self_approval,
          escalation_enabled: step.escalation_enabled,
          escalation_timeout_hours: step.escalation_timeout_hours,
          escalation_type: step.escalation_type,
          escalation_to_role: step.escalation_to_role,
          allow_delegation: step.allow_delegation
        })),
        rules: originalTemplate.approval_rules.map(rule => ({
          name: rule.name,
          description: rule.description,
          priority: rule.priority,
          is_active: rule.is_active,
          conditions: rule.conditions as Record<string, unknown>
        }))
      }

      return await this.createWorkflowTemplate(clonePayload, orgId, createdBy)
    } catch (error) {
      console.error('Error cloning workflow template:', error)
      throw error
    }
  }
}
