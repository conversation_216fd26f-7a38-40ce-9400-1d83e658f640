import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys, type QueryFilters } from '@/lib/queryKeys'
import { toast } from '@/hooks/use-toast'

export interface JournalEntryWithLines {
  id: string
  reference: string
  description: string
  date: string
  status: string
  total_debit: number
  total_credit: number
  org_id: string
  created_at: string
  updated_at: string
  transaction_lines: Array<{
    id: string
    account_id: string
    debit: number
    credit: number
    description: string
    created_at: string
    journal_entry_id: string
    org_id: string
    tax_amount?: number
    tax_rate_id?: string
    accounts: {
      code: string
      name: string
    }
  }>
}

/**
 * Hook to fetch all journal entries for the organization
 */
export function useJournalEntries(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: filters 
      ? queryKeys.journalEntries.filtered(profile?.org_id || '', filters)
      : queryKeys.journalEntries.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('journal_entries')
        .select(`
          *,
          transaction_lines (
            id,
            account_id,
            debit,
            credit,
            description,
            created_at,
            journal_entry_id,
            org_id,
            tax_amount,
            tax_rate_id,
            accounts (
              code,
              name
            )
          )
        `)
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`reference.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }
      
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      // Apply ordering
      query = query.order('date', { ascending: false })

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit)
      }
      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

/**
 * Hook to fetch journal entries by account
 */
export function useJournalEntriesByAccount(accountId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.journalEntries.byAccount(profile?.org_id || '', accountId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !accountId) return []

      const { data, error } = await supabase
        .from('journal_entries')
        .select(`
          *,
          transaction_lines!inner (
            id,
            account_id,
            debit,
            credit,
            description,
            created_at,
            journal_entry_id,
            org_id,
            tax_amount,
            tax_rate_id,
            accounts (
              code,
              name
            )
          )
        `)
        .eq('org_id', profile.org_id)
        .eq('transaction_lines.account_id', accountId)
        .order('date', { ascending: false })

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id && !!accountId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch a single journal entry by ID
 */
export function useJournalEntry(entryId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.journalEntries.detail(profile?.org_id || '', entryId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !entryId) return null

      const { data, error } = await supabase
        .from('journal_entries')
        .select(`
          *,
          transaction_lines (
            id,
            account_id,
            debit,
            credit,
            description,
            created_at,
            journal_entry_id,
            org_id,
            tax_amount,
            tax_rate_id,
            accounts (
              code,
              name
            )
          )
        `)
        .eq('id', entryId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id && !!entryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to create a new journal entry
 */
export function useCreateJournalEntry() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (entryData: {
      reference: string
      description: string
      date: string
      lines: Array<{
        account_id: string
        debit: number
        credit: number
        description: string
        tax_amount?: number
        tax_rate_id?: string
      }>
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { lines, ...entryFields } = entryData

      // Calculate totals
      const total_debit = lines.reduce((sum, line) => sum + line.debit, 0)
      const total_credit = lines.reduce((sum, line) => sum + line.credit, 0)

      // Create the journal entry
      const { data: entry, error: entryError } = await supabase
        .from('journal_entries')
        .insert({
          ...entryFields,
          total_debit,
          total_credit,
          status: 'draft',
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (entryError) throw entryError

      // Create transaction lines
      if (lines && lines.length > 0) {
        const { error: linesError } = await supabase
          .from('transaction_lines')
          .insert(
            lines.map(line => ({
              ...line,
              journal_entry_id: entry.id,
              org_id: profile.org_id,
            }))
          )

        if (linesError) throw linesError
      }

      return entry
    },
    onSuccess: (newEntry) => {
      // Invalidate and refetch journal entries list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.journalEntries.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: 'Journal entry created successfully',
      })
    },
    onError: (error) => {
      console.error('Error creating journal entry:', error)
      toast({
        title: 'Error',
        description: 'Failed to create journal entry',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update an existing journal entry
 */
export function useUpdateJournalEntry() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      entryId, 
      entryData 
    }: { 
      entryId: string
      entryData: {
        reference?: string
        description?: string
        date?: string
        lines?: Array<{
          account_id: string
          debit: number
          credit: number
          description: string
          tax_amount?: number
          tax_rate_id?: string
        }>
      }
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { lines, ...entryFields } = entryData

      // Calculate totals if lines are provided
      const updateFields = { ...entryFields }
      if (lines) {
        updateFields.total_debit = lines.reduce((sum, line) => sum + line.debit, 0)
        updateFields.total_credit = lines.reduce((sum, line) => sum + line.credit, 0)
      }

      // Update the journal entry
      const { data, error } = await supabase
        .from('journal_entries')
        .update({
          ...updateFields,
          updated_at: new Date().toISOString(),
        })
        .eq('id', entryId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error

      // Update transaction lines if provided
      if (lines) {
        // Delete existing lines
        await supabase
          .from('transaction_lines')
          .delete()
          .eq('journal_entry_id', entryId)

        // Insert new lines
        if (lines.length > 0) {
          await supabase
            .from('transaction_lines')
            .insert(
              lines.map(line => ({
                ...line,
                journal_entry_id: entryId,
                org_id: profile.org_id,
              }))
            )
        }
      }

      return data
    },
    onSuccess: (updatedEntry) => {
      // Update the entry in the cache
      queryClient.setQueryData(
        queryKeys.journalEntries.detail(profile?.org_id || '', updatedEntry.id),
        updatedEntry
      )

      // Invalidate journal entries list to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.journalEntries.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: 'Journal entry updated successfully',
      })
    },
    onError: (error) => {
      console.error('Error updating journal entry:', error)
      toast({
        title: 'Error',
        description: 'Failed to update journal entry',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a journal entry
 */
export function useDeleteJournalEntry() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (entryId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Delete transaction lines first
      await supabase
        .from('transaction_lines')
        .delete()
        .eq('journal_entry_id', entryId)

      // Delete the journal entry
      const { error } = await supabase
        .from('journal_entries')
        .delete()
        .eq('id', entryId)
        .eq('org_id', profile.org_id)

      if (error) throw error
      return entryId
    },
    onSuccess: (deletedEntryId) => {
      // Remove the entry from the cache
      queryClient.removeQueries({ 
        queryKey: queryKeys.journalEntries.detail(profile?.org_id || '', deletedEntryId) 
      })

      // Invalidate journal entries list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.journalEntries.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: 'Journal entry deleted successfully',
      })
    },
    onError: (error) => {
      console.error('Error deleting journal entry:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete journal entry',
        variant: 'destructive',
      })
    },
  })
}
