
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { TrialBalanceReport } from '@/components/reports/TrialBalanceReport'
import { ProfitLossReport } from '@/components/reports/ProfitLossReport'
import { BalanceSheetReport } from '@/components/reports/BalanceSheetReport'
import { CashFlowReport } from '@/components/reports/CashFlowReport'
import { TaxReports } from '@/components/reports/TaxReports'
import { BudgetReports } from '@/components/reports/BudgetReports'
import { StatementReports } from '@/components/reports/StatementReports'
import { FileText, Download, Printer } from 'lucide-react'

export const Reports = () => {
  const [activeTab, setActiveTab] = useState('trial-balance')

  const reportTabs = [
    {
      id: 'trial-balance',
      label: 'Trial Balance',
      component: <TrialBalanceReport />
    },
    {
      id: 'profit-loss',
      label: 'Profit & Loss',
      component: <ProfitLossReport />
    },
    {
      id: 'balance-sheet',
      label: 'Balance Sheet',
      component: <BalanceSheetReport />
    },
    {
      id: 'cash-flow',
      label: 'Cash Flow',
      component: <CashFlowReport />
    },
    {
      id: 'tax-reports',
      label: 'Tax Reports',
      component: <TaxReports />
    },
    {
      id: 'budget-reports',
      label: 'Budget Reports',
      component: <BudgetReports />
    },
    {
      id: 'statements',
      label: 'Statements',
      component: <StatementReports />
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Reports & Analytics</h1>
          <p className="text-muted-foreground">
            Generate comprehensive financial reports and analytics
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-7">
          {reportTabs.map((tab) => (
            <TabsTrigger key={tab.id} value={tab.id} className="text-xs">
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {reportTabs.map((tab) => (
          <TabsContent key={tab.id} value={tab.id}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {tab.label}
                </CardTitle>
                <CardDescription>
                  View and analyze your {tab.label.toLowerCase()} data
                </CardDescription>
              </CardHeader>
              <CardContent>
                {tab.component}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
