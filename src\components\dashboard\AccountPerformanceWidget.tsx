import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { formatCurrency } from '@/lib/utils'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts'

import { Calendar, TrendingUp, FileText } from 'lucide-react'
import { AccountTransactionsModal } from '@/components/transactions/AccountTransactionsModal'
import type { Account } from '@/types/database'

interface AccountPerformance {
  account: Account
  transactionCount: number
  totalDebits: number
  totalCredits: number
  netAmount: number
  chartData: Array<{
    month: string
    debits: number
    credits: number
  }>
}



export function AccountPerformanceWidget() {
  const { profile } = useAuth()
  const [accounts, setAccounts] = useState<Account[]>([])
  const [selectedAccountId, setSelectedAccountId] = useState<string>('')
  const [startDate, setStartDate] = useState<string>(
    new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]
  )
  const [endDate, setEndDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  )
  const [performance, setPerformance] = useState<AccountPerformance | null>(null)
  const [loading, setLoading] = useState(false)
  const [showTransactionsModal, setShowTransactionsModal] = useState(false)

  const fetchAccounts = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      const { data, error } = await supabase
        .from('accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('code')

      if (error) throw error
      setAccounts(data || [])
      
      // Auto-select first account if none selected
      if (data && data.length > 0 && !selectedAccountId) {
        setSelectedAccountId(data[0].id)
      }
    } catch (error) {
      console.error('Error fetching accounts:', error)
    }
  }, [profile?.org_id, selectedAccountId])

  const fetchAccountPerformance = useCallback(async () => {
    if (!profile?.org_id || !selectedAccountId) return

    setLoading(true)
    try {
      const account = accounts.find(a => a.id === selectedAccountId)
      if (!account) return

      // Fetch transaction lines for the account in the date range
      const { data: transactionLines, error } = await supabase
        .from('transaction_lines')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('account_id', selectedAccountId)
        .gte('created_at', `${startDate}T00:00:00`)
        .lte('created_at', `${endDate}T23:59:59`)
        .order('created_at')

      if (error) throw error

      const totalDebits = transactionLines?.reduce((sum, line) => sum + (line.debit || 0), 0) || 0
      const totalCredits = transactionLines?.reduce((sum, line) => sum + (line.credit || 0), 0) || 0
      const netAmount = totalDebits - totalCredits
      const transactionCount = transactionLines?.length || 0

      // Generate chart data by month
      const chartData = generateChartData(transactionLines || [], startDate, endDate)

      setPerformance({
        account,
        transactionCount,
        totalDebits,
        totalCredits,
        netAmount,
        chartData
      })
    } catch (error) {
      console.error('Error fetching account performance:', error)
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, selectedAccountId, accounts, startDate, endDate])

  useEffect(() => {
    if (profile?.org_id) {
      fetchAccounts()
    }
  }, [profile?.org_id, fetchAccounts])

  useEffect(() => {
    if (selectedAccountId && startDate && endDate) {
      fetchAccountPerformance()
    }
  }, [selectedAccountId, startDate, endDate, fetchAccountPerformance])

  const generateChartData = (transactions: Array<{ created_at: string; debit?: number; credit?: number }>, start: string, end: string) => {
    const startMonth = new Date(start)
    const endMonth = new Date(end)
    const months: Array<{ month: string; debits: number; credits: number }> = []

    const current = new Date(startMonth.getFullYear(), startMonth.getMonth(), 1)
    while (current <= endMonth) {
      const monthKey = current.toISOString().slice(0, 7) // YYYY-MM format
      const monthName = current.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
      
      const monthTransactions = transactions.filter(t => 
        t.created_at.startsWith(monthKey)
      )
      
      const debits = monthTransactions.reduce((sum, t) => sum + (t.debit || 0), 0)
      const credits = monthTransactions.reduce((sum, t) => sum + (t.credit || 0), 0)
      
      months.push({
        month: monthName,
        debits,
        credits
      })
      
      current.setMonth(current.getMonth() + 1)
    }

    return months
  }

  const handleViewTransactions = () => {
    setShowTransactionsModal(true)
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Account Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Account Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Account Selector */}
          <div className="space-y-2">
            <Label htmlFor="account">Account</Label>
            <Select value={selectedAccountId} onValueChange={setSelectedAccountId}>
              <SelectTrigger>
                <SelectValue placeholder="Select account" />
              </SelectTrigger>
              <SelectContent>
                {accounts.map((account) => (
                  <SelectItem key={account.id} value={account.id}>
                    {account.code} - {account.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-1">
              <Label htmlFor="startDate" className="text-xs">From</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="text-xs"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="endDate" className="text-xs">To</Label>
              <Input
                id="endDate"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="text-xs"
              />
            </div>
          </div>

          {/* Performance Metrics */}
          {performance && (
            <>
              <div className="space-y-3">
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary">
                    {performance.transactionCount}
                  </p>
                  <p className="text-xs text-muted-foreground">Transactions</p>
                  <Button
                    variant="link"
                    size="sm"
                    onClick={handleViewTransactions}
                    className="h-auto p-0 text-xs"
                  >
                    <FileText className="h-3 w-3 mr-1" />
                    View Details
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="text-center p-2 bg-green-50 rounded">
                    <p className="font-semibold text-green-700">
                      {formatCurrency(performance.totalDebits)}
                    </p>
                    <p className="text-green-600">Debits</p>
                  </div>
                  <div className="text-center p-2 bg-red-50 rounded">
                    <p className="font-semibold text-red-700">
                      {formatCurrency(performance.totalCredits)}
                    </p>
                    <p className="text-red-600">Credits</p>
                  </div>
                </div>

                <div className="text-center p-2 bg-blue-50 rounded">
                  <p className="font-semibold text-blue-700">
                    {formatCurrency(performance.netAmount)}
                  </p>
                  <p className="text-xs text-blue-600">Net Amount</p>
                </div>
              </div>

              {/* Chart */}
              {performance.chartData.length > 0 && (
                <div className="h-32 flex justify-center">
                  <BarChart width={300} height={120} data={performance.chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: 10 }}
                      interval={0}
                      angle={-45}
                      textAnchor="end"
                      height={40}
                    />
                    <YAxis tick={{ fontSize: 10 }} />
                    <Tooltip
                      formatter={(value: number) => [formatCurrency(value), '']}
                      labelStyle={{ fontSize: '12px' }}
                      contentStyle={{ fontSize: '12px' }}
                    />
                    <Bar dataKey="debits" fill="#22c55e" />
                    <Bar dataKey="credits" fill="#ef4444" />
                  </BarChart>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Account Transactions Modal */}
      {performance && (
        <AccountTransactionsModal
          open={showTransactionsModal}
          onOpenChange={setShowTransactionsModal}
          accountId={selectedAccountId}
          accountName={performance.account.name}
          startDate={startDate}
          endDate={endDate}
        />
      )}
    </>
  )
}
