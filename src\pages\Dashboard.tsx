
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '@/hooks/useAuthHook'
import { useRecentPayments } from '@/hooks/queries'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { formatCurrency } from '@/lib/utils'
import { ArrowUpRight, ArrowDownRight, DollarSign, Users, Eye } from 'lucide-react'
import { TransactionDetailsModal } from '@/components/transactions/TransactionDetailsModal'
import { AccountPerformanceWidget } from '@/components/dashboard/AccountPerformanceWidget'
import { BudgetAlertBanner } from '@/components/budgets/BudgetAlertBanner'

export default function Dashboard() {
  const { profile } = useAuth()
  const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const { data: stats } = useQuery({
    queryKey: ['dashboard-stats', profile?.org_id],
    queryFn: async () => {
      const { data: customers } = await supabase
        .from('customers')
        .select('id')
        .eq('org_id', profile?.org_id)

      const { data: vendors } = await supabase
        .from('vendors')
        .select('id')
        .eq('org_id', profile?.org_id)

      // Fix: Get invoices with status 'paid' and sum their total_amount (not amount_due)
      const { data: invoices } = await supabase
        .from('invoices')
        .select('total_amount')
        .eq('org_id', profile?.org_id)
        .eq('status', 'paid')

      // Fix: Get bills with status 'paid' and sum their total_amount (not amount_due)
      const { data: bills } = await supabase
        .from('bills')
        .select('total_amount')
        .eq('org_id', profile?.org_id)
        .eq('status', 'paid')

      return {
        customers: customers?.length || 0,
        vendors: vendors?.length || 0,
        revenue: invoices?.reduce((sum, inv) => sum + (inv.total_amount || 0), 0) || 0,
        expenses: bills?.reduce((sum, bill) => sum + (bill.total_amount || 0), 0) || 0,
      }
    },
    enabled: !!profile?.org_id,
  })

  const { data: recentTransactions } = useQuery({
    queryKey: ['recent-transactions', profile?.org_id],
    queryFn: async () => {
      // Fetch payments
      const { data: payments } = await supabase
        .from('payments')
        .select('*')
        .eq('org_id', profile?.org_id)
        .order('payment_date', { ascending: false })
        .limit(5)

      // Fetch customers and vendors
      const { data: customers } = await supabase
        .from('customers')
        .select('id, name')
        .eq('org_id', profile?.org_id)

      const { data: vendors } = await supabase
        .from('vendors')
        .select('id, name')
        .eq('org_id', profile?.org_id)

      // Enhance payments with payee names
      return payments?.map(payment => {
        let payee_name = 'Unknown'
        
        if (payment.payee_type === 'customer') {
          const customer = customers?.find(c => c.id === payment.payee_id)
          payee_name = customer?.name || 'Customer'
        } else if (payment.payee_type === 'vendor') {
          const vendor = vendors?.find(v => v.id === payment.payee_id)
          payee_name = vendor?.name || 'Vendor'
        }

        return {
          ...payment,
          payee_name
        }
      }) || []
    },
    enabled: !!profile?.org_id,
  })

  const handleViewTransaction = (transactionId: string) => {
    setSelectedTransactionId(transactionId)
    setIsModalOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Budget Alert Banner */}
      <BudgetAlertBanner showDismiss={true} />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats?.revenue || 0)}</div>
            <p className="text-xs text-muted-foreground">
              From paid invoices
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats?.expenses || 0)}</div>
            <p className="text-xs text-muted-foreground">
              From paid bills
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.customers || 0}</div>
            <p className="text-xs text-muted-foreground">
              Active customers
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Vendors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.vendors || 0}</div>
            <p className="text-xs text-muted-foreground">
              Active vendors
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Recent Transactions - 3/4 width */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Payee</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Channel</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentTransactions && recentTransactions.length > 0 ? (
                    recentTransactions.map((transaction) => (
                      <TableRow
                        key={transaction.id}
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleViewTransaction(transaction.id)}
                      >
                        <TableCell>
                          {new Date(transaction.payment_date).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          {transaction.payee_type === 'customer' ? (
                            <span className="flex items-center text-green-600">
                              <ArrowUpRight className="mr-1 h-4 w-4" />
                              Payment Received
                            </span>
                          ) : (
                            <span className="flex items-center text-red-600">
                              <ArrowDownRight className="mr-1 h-4 w-4" />
                              Payment Sent
                            </span>
                          )}
                        </TableCell>
                        <TableCell>{transaction.payee_name}</TableCell>
                        <TableCell>{formatCurrency(transaction.amount)}</TableCell>
                        <TableCell>{transaction.channel}</TableCell>
                        <TableCell>{transaction.transaction_id}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleViewTransaction(transaction.id)
                            }}
                            className="h-8 w-8 p-0"
                            title="View transaction details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        No recent transactions found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>

        {/* Account Performance Widget - 1/4 width */}
        <div className="lg:col-span-1">
          <AccountPerformanceWidget />
        </div>
      </div>

      {/* Transaction Details Modal */}
      {selectedTransactionId && (
        <TransactionDetailsModal
          open={isModalOpen}
          onOpenChange={setIsModalOpen}
          transactionId={selectedTransactionId}
        />
      )}
    </div>
  )
}
