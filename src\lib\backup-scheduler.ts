/**
 * Automated Backup Scheduler and Monitoring System
 * Handles scheduled backups, monitoring, and alerting
 */

import { BackupService } from './backup-service'
import { logger, logInfo, logError, logWarn } from './logger'
import { errorHandler, handleCriticalError } from './errorHandler'
import { config } from './config'

export interface BackupScheduleConfig {
  enabled: boolean
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly'
  time?: string // HH:MM format for daily/weekly/monthly
  dayOfWeek?: number // 0-6 for weekly (0 = Sunday)
  dayOfMonth?: number // 1-31 for monthly
  retentionDays: number
  maxBackups: number
  autoCleanup: boolean
  notifyOnFailure: boolean
  notifyOnSuccess: boolean
}

export interface BackupStatus {
  lastBackup?: Date
  lastSuccess?: Date
  lastFailure?: Date
  consecutiveFailures: number
  totalBackups: number
  totalFailures: number
  isRunning: boolean
  nextScheduled?: Date
}

export interface BackupAlert {
  id: string
  type: 'success' | 'failure' | 'warning' | 'info'
  message: string
  timestamp: Date
  backupId?: string
  error?: string
}

class BackupScheduler {
  private static instance: BackupScheduler
  private config: BackupScheduleConfig
  private status: BackupStatus
  private alerts: BackupAlert[] = []
  private schedulerInterval?: NodeJS.Timeout
  private backupService: BackupService
  private maxAlerts = 100

  private constructor() {
    this.config = this.getDefaultConfig()
    this.status = this.getInitialStatus()
    this.backupService = new BackupService()
    this.loadPersistedData()
    this.initializeScheduler()
  }

  public static getInstance(): BackupScheduler {
    if (!BackupScheduler.instance) {
      BackupScheduler.instance = new BackupScheduler()
    }
    return BackupScheduler.instance
  }

  private getDefaultConfig(): BackupScheduleConfig {
    return {
      enabled: config.app.environment === 'production',
      frequency: 'daily',
      time: '02:00', // 2 AM
      retentionDays: 30,
      maxBackups: 50,
      autoCleanup: true,
      notifyOnFailure: true,
      notifyOnSuccess: false
    }
  }

  private getInitialStatus(): BackupStatus {
    return {
      consecutiveFailures: 0,
      totalBackups: 0,
      totalFailures: 0,
      isRunning: false
    }
  }

  private loadPersistedData(): void {
    try {
      const savedConfig = localStorage.getItem('backup_scheduler_config')
      if (savedConfig) {
        this.config = { ...this.config, ...JSON.parse(savedConfig) }
      }

      const savedStatus = localStorage.getItem('backup_scheduler_status')
      if (savedStatus) {
        const parsed = JSON.parse(savedStatus)
        this.status = {
          ...this.status,
          ...parsed,
          lastBackup: parsed.lastBackup ? new Date(parsed.lastBackup) : undefined,
          lastSuccess: parsed.lastSuccess ? new Date(parsed.lastSuccess) : undefined,
          lastFailure: parsed.lastFailure ? new Date(parsed.lastFailure) : undefined,
          nextScheduled: parsed.nextScheduled ? new Date(parsed.nextScheduled) : undefined,
          isRunning: false // Always reset running status on load
        }
      }

      const savedAlerts = localStorage.getItem('backup_scheduler_alerts')
      if (savedAlerts) {
        this.alerts = JSON.parse(savedAlerts).map((alert: { timestamp: string; [key: string]: unknown }) => ({
          ...alert,
          timestamp: new Date(alert.timestamp)
        }))
      }
    } catch (error) {
      logError('Failed to load persisted backup scheduler data', {
        component: 'BackupScheduler',
        action: 'loadPersistedData',
        metadata: { error: (error as Error).message }
      })
    }
  }

  private persistData(): void {
    try {
      localStorage.setItem('backup_scheduler_config', JSON.stringify(this.config))
      localStorage.setItem('backup_scheduler_status', JSON.stringify(this.status))
      localStorage.setItem('backup_scheduler_alerts', JSON.stringify(this.alerts))
    } catch (error) {
      logError('Failed to persist backup scheduler data', {
        component: 'BackupScheduler',
        action: 'persistData',
        metadata: { error: (error as Error).message }
      })
    }
  }

  private initializeScheduler(): void {
    if (this.config.enabled) {
      this.scheduleNextBackup()
      this.startSchedulerLoop()
    }

    logInfo('Backup scheduler initialized', {
      component: 'BackupScheduler',
      action: 'initialize',
      metadata: {
        enabled: this.config.enabled,
        frequency: this.config.frequency,
        nextScheduled: this.status.nextScheduled?.toISOString()
      }
    })
  }

  private startSchedulerLoop(): void {
    // Check every minute if a backup should run
    this.schedulerInterval = setInterval(() => {
      this.checkAndRunScheduledBackup()
    }, 60000)
  }

  private scheduleNextBackup(): void {
    const now = new Date()
    let nextBackup: Date

    switch (this.config.frequency) {
      case 'hourly':
        nextBackup = new Date(now.getTime() + 60 * 60 * 1000)
        break

      case 'daily':
        nextBackup = this.getNextDailyBackup(now)
        break

      case 'weekly':
        nextBackup = this.getNextWeeklyBackup(now)
        break

      case 'monthly':
        nextBackup = this.getNextMonthlyBackup(now)
        break

      default:
        nextBackup = this.getNextDailyBackup(now)
    }

    this.status.nextScheduled = nextBackup
    this.persistData()

    logInfo('Next backup scheduled', {
      component: 'BackupScheduler',
      action: 'scheduleNextBackup',
      metadata: {
        frequency: this.config.frequency,
        nextScheduled: nextBackup.toISOString()
      }
    })
  }

  private getNextDailyBackup(now: Date): Date {
    const [hours, minutes] = (this.config.time || '02:00').split(':').map(Number)
    const nextBackup = new Date(now)
    nextBackup.setHours(hours, minutes, 0, 0)

    if (nextBackup <= now) {
      nextBackup.setDate(nextBackup.getDate() + 1)
    }

    return nextBackup
  }

  private getNextWeeklyBackup(now: Date): Date {
    const targetDay = this.config.dayOfWeek || 0 // Sunday
    const [hours, minutes] = (this.config.time || '02:00').split(':').map(Number)
    
    const nextBackup = new Date(now)
    nextBackup.setHours(hours, minutes, 0, 0)
    
    const daysUntilTarget = (targetDay - now.getDay() + 7) % 7
    if (daysUntilTarget === 0 && nextBackup <= now) {
      nextBackup.setDate(nextBackup.getDate() + 7)
    } else {
      nextBackup.setDate(nextBackup.getDate() + daysUntilTarget)
    }

    return nextBackup
  }

  private getNextMonthlyBackup(now: Date): Date {
    const targetDay = this.config.dayOfMonth || 1
    const [hours, minutes] = (this.config.time || '02:00').split(':').map(Number)
    
    const nextBackup = new Date(now)
    nextBackup.setDate(targetDay)
    nextBackup.setHours(hours, minutes, 0, 0)

    if (nextBackup <= now) {
      nextBackup.setMonth(nextBackup.getMonth() + 1)
    }

    return nextBackup
  }

  private async checkAndRunScheduledBackup(): Promise<void> {
    if (!this.config.enabled || this.status.isRunning || !this.status.nextScheduled) {
      return
    }

    const now = new Date()
    if (now >= this.status.nextScheduled) {
      await this.runScheduledBackup()
    }
  }

  private async runScheduledBackup(): Promise<void> {
    if (this.status.isRunning) {
      logWarn('Backup already running, skipping scheduled backup', {
        component: 'BackupScheduler',
        action: 'runScheduledBackup'
      })
      return
    }

    this.status.isRunning = true
    this.status.lastBackup = new Date()
    this.persistData()

    logInfo('Starting scheduled backup', {
      component: 'BackupScheduler',
      action: 'runScheduledBackup',
      metadata: {
        frequency: this.config.frequency,
        scheduledTime: this.status.nextScheduled?.toISOString()
      }
    })

    try {
      const result = await this.backupService.createBackup('scheduled')
      
      if (result.success) {
        this.handleBackupSuccess(result.backup_id!)
      } else {
        this.handleBackupFailure(result.error || 'Unknown error')
      }
    } catch (error) {
      this.handleBackupFailure((error as Error).message)
    } finally {
      this.status.isRunning = false
      this.scheduleNextBackup()
      this.persistData()

      // Auto cleanup if enabled
      if (this.config.autoCleanup) {
        await this.performAutoCleanup()
      }
    }
  }

  private handleBackupSuccess(backupId: string): void {
    this.status.lastSuccess = new Date()
    this.status.consecutiveFailures = 0
    this.status.totalBackups++

    this.addAlert({
      type: 'success',
      message: `Scheduled backup completed successfully`,
      backupId
    })

    if (this.config.notifyOnSuccess) {
      this.sendNotification('success', `Backup ${backupId} completed successfully`)
    }

    logInfo('Scheduled backup completed successfully', {
      component: 'BackupScheduler',
      action: 'handleBackupSuccess',
      metadata: { backupId }
    })
  }

  private handleBackupFailure(error: string): void {
    this.status.lastFailure = new Date()
    this.status.consecutiveFailures++
    this.status.totalFailures++

    this.addAlert({
      type: 'failure',
      message: `Scheduled backup failed: ${error}`,
      error
    })

    if (this.config.notifyOnFailure) {
      this.sendNotification('failure', `Backup failed: ${error}`)
    }

    // Log as critical if multiple consecutive failures
    if (this.status.consecutiveFailures >= 3) {
      handleCriticalError(new Error(`Backup system failing: ${this.status.consecutiveFailures} consecutive failures`), {
        component: 'BackupScheduler',
        action: 'handleBackupFailure',
        metadata: {
          consecutiveFailures: this.status.consecutiveFailures,
          lastError: error
        }
      })
    } else {
      logError('Scheduled backup failed', {
        component: 'BackupScheduler',
        action: 'handleBackupFailure',
        metadata: {
          error,
          consecutiveFailures: this.status.consecutiveFailures
        }
      })
    }
  }

  private async performAutoCleanup(): Promise<void> {
    try {
      const result = await this.backupService.cleanupOldBackups(this.config.retentionDays)
      
      if (result.success && result.deleted_count! > 0) {
        this.addAlert({
          type: 'info',
          message: `Auto cleanup removed ${result.deleted_count} old backups`
        })

        logInfo('Auto cleanup completed', {
          component: 'BackupScheduler',
          action: 'performAutoCleanup',
          metadata: { deletedCount: result.deleted_count }
        })
      }
    } catch (error) {
      logError('Auto cleanup failed', {
        component: 'BackupScheduler',
        action: 'performAutoCleanup',
        metadata: { error: (error as Error).message }
      })
    }
  }

  private addAlert(alert: Omit<BackupAlert, 'id' | 'timestamp'>): void {
    const newAlert: BackupAlert = {
      ...alert,
      id: crypto.randomUUID(),
      timestamp: new Date()
    }

    this.alerts.unshift(newAlert)

    // Maintain maximum alerts
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(0, this.maxAlerts)
    }

    this.persistData()
  }

  private sendNotification(type: 'success' | 'failure', message: string): void {
    // In a production environment, this would send notifications via:
    // - Email
    // - Slack
    // - SMS
    // - Push notifications
    // - Webhook

    logInfo('Backup notification sent', {
      component: 'BackupScheduler',
      action: 'sendNotification',
      metadata: { type, message }
    })

    // For now, just log to console in development
    if (config.app.environment === 'development') {
      console.log(`[BACKUP NOTIFICATION] ${type.toUpperCase()}: ${message}`)
    }
  }

  // Public API methods
  public updateConfig(newConfig: Partial<BackupScheduleConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.persistData()

    if (this.config.enabled) {
      this.scheduleNextBackup()
      if (!this.schedulerInterval) {
        this.startSchedulerLoop()
      }
    } else {
      this.stopScheduler()
    }

    logInfo('Backup scheduler configuration updated', {
      component: 'BackupScheduler',
      action: 'updateConfig',
      metadata: { newConfig }
    })
  }

  public getConfig(): BackupScheduleConfig {
    return { ...this.config }
  }

  public getStatus(): BackupStatus {
    return { ...this.status }
  }

  public getAlerts(limit?: number): BackupAlert[] {
    return limit ? this.alerts.slice(0, limit) : [...this.alerts]
  }

  public clearAlerts(): void {
    this.alerts = []
    this.persistData()
  }

  public async runManualBackup(): Promise<{ success: boolean; backupId?: string; error?: string }> {
    if (this.status.isRunning) {
      return { success: false, error: 'Backup already in progress' }
    }

    try {
      this.status.isRunning = true
      this.persistData()

      const result = await this.backupService.createBackup('manual')
      
      if (result.success) {
        this.addAlert({
          type: 'success',
          message: 'Manual backup completed successfully',
          backupId: result.backup_id
        })
      } else {
        this.addAlert({
          type: 'failure',
          message: `Manual backup failed: ${result.error}`,
          error: result.error
        })
      }

      return result
    } finally {
      this.status.isRunning = false
      this.persistData()
    }
  }

  public stopScheduler(): void {
    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval)
      this.schedulerInterval = undefined
    }

    logInfo('Backup scheduler stopped', {
      component: 'BackupScheduler',
      action: 'stopScheduler'
    })
  }

  public startScheduler(): void {
    if (!this.schedulerInterval && this.config.enabled) {
      this.startSchedulerLoop()
      this.scheduleNextBackup()

      logInfo('Backup scheduler started', {
        component: 'BackupScheduler',
        action: 'startScheduler'
      })
    }
  }
}

// Export singleton instance
export const backupScheduler = BackupScheduler.getInstance()
