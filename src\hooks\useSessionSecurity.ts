import { useEffect, useRef, useCallback, useMemo } from 'react'
import { useAuth } from './useAuthHook'
import { toast } from 'sonner'

// Generate UUID using cryptographically secure method
function generateUUID(): string {
  // Use crypto.randomUUID() if available (modern browsers)
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID()
  }

  // Fallback for older browsers - still use crypto.getRandomValues for security
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const array = new Uint8Array(1)
      crypto.getRandomValues(array)
      const r = array[0] % 16
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  // Last resort fallback (should not happen in modern environments)
  console.warn('crypto API not available, using less secure random generation')
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

interface SessionSecurityConfig {
  timeoutMinutes: number
  warningMinutes: number
  checkIntervalSeconds: number
  persistentSession?: boolean
  heartbeatEndpoint?: string
}

const DEFAULT_CONFIG: SessionSecurityConfig = {
  timeoutMinutes: 30, // 30 minutes session timeout
  warningMinutes: 5,  // Warn 5 minutes before timeout
  checkIntervalSeconds: 60, // Check every minute
  persistentSession: false,
}

export function useSessionSecurity(config: Partial<SessionSecurityConfig> = {}) {
  const { user, signOut } = useAuth()
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config])
  
  // Use sessionStorage for persistence across page refreshes
  const lastActivityRef = useRef<number>(
    (() => {
      const saved = finalConfig.persistentSession
        ? parseInt(sessionStorage.getItem('lastActivity') || '0', 10)
        : 0
      return saved || Date.now()
    })()
  )

  const timeoutWarningShownRef = useRef<boolean>(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Detect visibility change event type
  const getVisibilityChangeEvent = (): string => {
    if (typeof document.hidden !== 'undefined') return 'visibilitychange'
    if (typeof (document as Document & { webkitHidden?: boolean }).webkitHidden !== 'undefined') return 'webkitvisibilitychange'
    return 'msvisibilitychange'
  }

  // Update last activity timestamp with persistence
  const updateActivity = useCallback(() => {
    const now = Date.now()
    lastActivityRef.current = now
    if (finalConfig.persistentSession) {
      sessionStorage.setItem('lastActivity', now.toString())
    }
    timeoutWarningShownRef.current = false

    // Optional: Send heartbeat to server if endpoint provided
    if (finalConfig.heartbeatEndpoint) {
      navigator.sendBeacon(finalConfig.heartbeatEndpoint)
    }
  }, [finalConfig.persistentSession, finalConfig.heartbeatEndpoint])

  // Enhanced timeout check with tab visibility awareness
  const checkTimeout = useCallback(() => {
    if (!user) return

    const now = Date.now()
    const timeSinceActivity = now - lastActivityRef.current
    const timeoutMs = finalConfig.timeoutMinutes * 60 * 1000
    const warningMs = finalConfig.warningMinutes * 60 * 1000

    // Pause timeout when tab is not visible
    if (document.hidden) return

    // Check if session should timeout
    if (timeSinceActivity >= timeoutMs) {
      toast.error('Session expired due to inactivity')
      signOut()
      return
    }

    // Show warning if approaching timeout
    if (timeSinceActivity >= (timeoutMs - warningMs) && !timeoutWarningShownRef.current) {
      timeoutWarningShownRef.current = true
      const remainingMinutes = Math.ceil((timeoutMs - timeSinceActivity) / (60 * 1000))
      toast.warning(`Session will expire in ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''} due to inactivity`, {
        action: {
          label: 'Extend',
          onClick: () => updateActivity()
        }
      })
    }
  }, [user, signOut, finalConfig, updateActivity])

  // Set up activity listeners with passive events for performance
  useEffect(() => {
    if (!user) return

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    const options = { passive: true, capture: true }
    const visibilityEvent = getVisibilityChangeEvent()

    // Create event handler that doesn't expect parameters
    const handleActivity = () => updateActivity()

    events.forEach(event => {
      document.addEventListener(event, handleActivity, options)
    })

    // Handle tab visibility changes
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        updateActivity()
      }
    }

    document.addEventListener(visibilityEvent, handleVisibilityChange)

    // Set up timeout checking interval
    intervalRef.current = setInterval(checkTimeout, finalConfig.checkIntervalSeconds * 1000)

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, options)
      })
      document.removeEventListener(visibilityEvent, handleVisibilityChange)

      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [user, updateActivity, checkTimeout, finalConfig.checkIntervalSeconds])

  // Reset activity on user change
  useEffect(() => {
    if (user) {
      updateActivity()
    }
  }, [user, updateActivity])

  return {
    updateActivity,
    getRemainingTime: useCallback(() => {
      const timeSinceActivity = Date.now() - lastActivityRef.current
      const timeoutMs = finalConfig.timeoutMinutes * 60 * 1000
      return Math.max(0, timeoutMs - timeSinceActivity)
    }, [finalConfig.timeoutMinutes]),
    extendSession: updateActivity,
    isSessionActive: useCallback(() => {
      const timeSinceActivity = Date.now() - lastActivityRef.current
      const timeoutMs = finalConfig.timeoutMinutes * 60 * 1000
      return timeSinceActivity < timeoutMs
    }, [finalConfig.timeoutMinutes])
  }
}

// Enhanced security headers utility
export function addSecurityHeaders() {
  // Only add if we're in the browser and headers haven't been added yet
  if (typeof document === 'undefined') return

  // Add CSP meta tag if not already present
  if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
    const cspMeta = document.createElement('meta')
    cspMeta.httpEquiv = "Content-Security-Policy"
    cspMeta.content = [
      "default-src 'self'",
      `script-src 'self' ${process.env.NODE_ENV === 'development' ? "'unsafe-inline' 'unsafe-eval'" : "'strict-dynamic'"}`,
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      `connect-src 'self' ${process.env.NODE_ENV === 'development' ? 'ws://localhost:* ' : ''}https://kmejequnwwngmzwkszqs.supabase.co wss://kmejequnwwngmzwkszqs.supabase.co`,
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "upgrade-insecure-requests"
    ].join('; ')
    document.head.appendChild(cspMeta)
  }

  // Security headers as meta tags (fallback when HTTP headers aren't available)
  const securityHeaders = [
    { httpEquiv: 'X-Frame-Options', content: 'DENY' },
    { httpEquiv: 'X-Content-Type-Options', content: 'nosniff' },
    { httpEquiv: 'Strict-Transport-Security', content: 'max-age=63072000; includeSubDomains; preload' },
    { name: 'Referrer-Policy', content: 'strict-origin-when-cross-origin' },
    { name: 'Permissions-Policy', content: 'geolocation=(), microphone=(), camera=(), payment=()' },
    { name: 'X-XSS-Protection', content: '1; mode=block' }
  ]

  securityHeaders.forEach(header => {
    const attr = 'httpEquiv' in header ? 'http-equiv' : 'name'
    const selector = `meta[${attr}="${header[attr === 'http-equiv' ? 'httpEquiv' : 'name']}"]`
    
    if (!document.querySelector(selector)) {
      const meta = document.createElement('meta')
      if ('httpEquiv' in header) meta.httpEquiv = header.httpEquiv
      if ('name' in header) meta.name = header.name
      meta.content = header.content
      document.head.appendChild(meta)
    }
  })
}

// Enhanced CSRF token management with automatic request header injection
export class CSRFTokenManager {
  private static instance: CSRFTokenManager
  private token: string | null = null
  private observer: MutationObserver | null = null

  private constructor() {
    this.setupFormObserver()
  }

  static getInstance(): CSRFTokenManager {
    if (!CSRFTokenManager.instance) {
      CSRFTokenManager.instance = new CSRFTokenManager()
    }
    return CSRFTokenManager.instance
  }

  private setupFormObserver() {
    if (typeof window === 'undefined') return

    // Watch for dynamically added forms
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node instanceof HTMLElement) {
            const forms = node.querySelectorAll('form')
            forms.forEach(form => this.addCSRFToken(form))
          }
        })
      })
    })

    this.observer.observe(document.body, {
      subtree: true,
      childList: true
    })
  }

  generateToken(): string {
    this.token = generateUUID()
    sessionStorage.setItem('csrf_token', this.token)
    return this.token
  }

  getToken(): string | null {
    if (!this.token) {
      this.token = sessionStorage.getItem('csrf_token')
    }
    return this.token
  }

  validateToken(token: string): boolean {
    const currentToken = this.getToken()
    return currentToken !== null && currentToken === token
  }

  clearToken(): void {
    this.token = null
    sessionStorage.removeItem('csrf_token')
  }

  addCSRFToken(form: HTMLFormElement) {
    if (!form.querySelector('input[name="csrf_token"]')) {
      const csrfInput = document.createElement('input')
      csrfInput.type = 'hidden'
      csrfInput.name = 'csrf_token'
      csrfInput.value = this.getToken() || ''
      form.appendChild(csrfInput)
    }
  }

  // Add CSRF token to all fetch requests
  setupFetchInterceptor() {
    if (typeof window === 'undefined') return

    const originalFetch = window.fetch
    window.fetch = async (input, init = {}) => {
      const headers = new Headers(init.headers)
      
      // Only add to same-origin requests
      if (typeof input === 'string' && new URL(input, window.location.href).origin === window.location.origin) {
        if (!headers.has('X-CSRF-Token')) {
          headers.set('X-CSRF-Token', this.getToken() || '')
        }
      }

      return originalFetch(input, { ...init, headers })
    }
  }
}

// Enhanced security initialization
export function initializeSecurity() {
  if (typeof window === 'undefined') return

  addSecurityHeaders()
  
  const csrfManager = CSRFTokenManager.getInstance()
  csrfManager.generateToken()
  csrfManager.setupFetchInterceptor()

  // Add CSRF token to all existing forms
  document.querySelectorAll('form').forEach(form => {
    csrfManager.addCSRFToken(form)
  })

  // Protect against prototype pollution
  if (typeof Object.freeze === 'function') {
    Object.freeze(Object.prototype)
    Object.freeze(Array.prototype)
  }
}
