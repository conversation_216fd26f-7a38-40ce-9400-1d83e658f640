import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  DollarSign, 
  TrendingUp,
  Calendar,
  User
} from 'lucide-react'
import { useBudgetApprovalDetails, useBudgetApprovalAction } from '@/hooks/queries/useBudgetApprovalWorkflow'

interface BudgetApprovalInterfaceProps {
  approvalInstanceId: string
  onApprovalComplete?: () => void
  className?: string
}

export function BudgetApprovalInterface({ 
  approvalInstanceId, 
  onApprovalComplete,
  className = ""
}: BudgetApprovalInterfaceProps) {
  const { data: budgetApproval, isLoading } = useBudgetApprovalDetails(approvalInstanceId)
  const budgetApprovalAction = useBudgetApprovalAction()
  
  const [action, setAction] = useState<'approve' | 'reject' | null>(null)
  const [comments, setComments] = useState('')
  const [budgetAdjustment, setBudgetAdjustment] = useState<number>(0)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const getEscalationBadge = (level: string) => {
    switch (level) {
      case 'critical_override':
        return <Badge variant="destructive">Critical Override</Badge>
      case 'budget_override':
        return <Badge variant="secondary">Budget Override</Badge>
      default:
        return <Badge variant="default">Normal</Badge>
    }
  }

  const getEscalationIcon = (level: string) => {
    switch (level) {
      case 'critical_override':
        return <AlertTriangle className="h-5 w-5 text-red-600" />
      case 'budget_override':
        return <TrendingUp className="h-5 w-5 text-orange-600" />
      default:
        return <DollarSign className="h-5 w-5 text-blue-600" />
    }
  }

  const handleSubmit = async () => {
    if (!action) return

    setIsSubmitting(true)
    try {
      await budgetApprovalAction.mutateAsync({
        approvalInstanceId,
        action,
        comments: comments.trim() || undefined,
        budgetAdjustment: action === 'approve' && budgetAdjustment > 0 ? budgetAdjustment : undefined
      })
      
      onApprovalComplete?.()
    } catch (error) {
      console.error('Budget approval action failed:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!budgetApproval) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert>
            <AlertDescription>
              No budget approval information found for this request.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  const isAlreadyProcessed = budgetApproval.status !== 'pending'

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {getEscalationIcon(budgetApproval.escalation_level)}
            Budget Override Approval
          </CardTitle>
          {getEscalationBadge(budgetApproval.escalation_level)}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Budget Impact Summary */}
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="font-medium">Budget Exceedance Alert</div>
              <div className="text-sm">
                This {budgetApproval.document_type} exceeds the approved budget by{' '}
                <span className="font-medium">{formatCurrency(budgetApproval.exceedance_amount)}</span>
              </div>
            </div>
          </AlertDescription>
        </Alert>

        {/* Account Information */}
        <div className="space-y-3">
          <div className="text-sm font-medium">Account Information:</div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Account:</span>
              <div className="font-medium">{budgetApproval.accounts?.name}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Account Code:</span>
              <div className="font-medium">{budgetApproval.accounts?.code}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Document Amount:</span>
              <div className="font-medium">{formatCurrency(budgetApproval.approval_instances?.document_amount || 0)}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Exceedance:</span>
              <div className="font-medium text-red-600">{formatCurrency(budgetApproval.exceedance_amount)}</div>
            </div>
          </div>
        </div>

        {/* Justification */}
        {budgetApproval.justification && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Justification:</div>
            <div className="p-3 bg-gray-50 rounded-lg text-sm">
              {budgetApproval.justification}
            </div>
          </div>
        )}

        {/* Request Details */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Request Details:</div>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span>Requested by: {budgetApproval.created_by_name || budgetApproval.created_by_email}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>On: {new Date(budgetApproval.created_at).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        <Separator />

        {/* Approval Status */}
        {isAlreadyProcessed ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              {budgetApproval.status === 'approved' ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              <span className="font-medium">
                {budgetApproval.status === 'approved' ? 'Approved' : 'Rejected'}
              </span>
              {budgetApproval.approved_by_name && (
                <span className="text-sm text-muted-foreground">
                  by {budgetApproval.approved_by_name}
                </span>
              )}
            </div>
            
            {budgetApproval.approved_at && (
              <div className="text-sm text-muted-foreground">
                On {new Date(budgetApproval.approved_at).toLocaleString()}
              </div>
            )}

            {budgetApproval.budget_adjustment && budgetApproval.budget_adjustment > 0 && (
              <Alert className="border-blue-200 bg-blue-50">
                <DollarSign className="h-4 w-4" />
                <AlertDescription>
                  Budget adjustment approved: {formatCurrency(budgetApproval.budget_adjustment)}
                </AlertDescription>
              </Alert>
            )}

            {budgetApproval.rejection_reason && (
              <Alert className="border-red-200 bg-red-50">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium">Rejection Reason:</div>
                  <div className="text-sm mt-1">{budgetApproval.rejection_reason}</div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        ) : (
          /* Approval Actions */
          <div className="space-y-4">
            <div className="text-sm font-medium">Approval Decision:</div>
            
            {/* Action Selection */}
            <div className="flex gap-2">
              <Button
                variant={action === 'approve' ? 'default' : 'outline'}
                onClick={() => setAction('approve')}
                className="flex-1"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Approve
              </Button>
              <Button
                variant={action === 'reject' ? 'destructive' : 'outline'}
                onClick={() => setAction('reject')}
                className="flex-1"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject
              </Button>
            </div>

            {/* Budget Adjustment (only for approval) */}
            {action === 'approve' && (
              <div className="space-y-2">
                <Label htmlFor="budget-adjustment">
                  Budget Adjustment (Optional)
                </Label>
                <Input
                  id="budget-adjustment"
                  type="number"
                  value={budgetAdjustment}
                  onChange={(e) => setBudgetAdjustment(Number(e.target.value))}
                  placeholder="Enter additional budget allocation"
                />
                <div className="text-xs text-muted-foreground">
                  Increase the budget by this amount to accommodate future similar expenses
                </div>
              </div>
            )}

            {/* Comments */}
            <div className="space-y-2">
              <Label htmlFor="comments">
                Comments {action === 'reject' && <span className="text-red-500">*</span>}
              </Label>
              <Textarea
                id="comments"
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                placeholder={
                  action === 'approve' 
                    ? "Optional comments about the approval..."
                    : "Please provide a reason for rejection..."
                }
                rows={3}
              />
            </div>

            {/* Submit Button */}
            <Button
              onClick={handleSubmit}
              disabled={!action || isSubmitting || (action === 'reject' && !comments.trim())}
              className="w-full"
            >
              {isSubmitting ? 'Processing...' : `${action === 'approve' ? 'Approve' : 'Reject'} Budget Override`}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
