
import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash } from 'lucide-react'
import { toast } from 'sonner'
import type { WithholdingTaxRate } from '@/types/database'

interface WithholdingTaxRatesTableProps {
  withholdingTaxRates: WithholdingTaxRate[]
  onEdit: (withholdingTaxRate: WithholdingTaxRate) => void
  onRefresh: () => void
}

export function WithholdingTaxRatesTable({ withholdingTaxRates, onEdit, onRefresh }: WithholdingTaxRatesTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (id: string) => {
    setDeletingId(id)
    try {
      const { error } = await supabase
        .from('withholding_tax_rates')
        .delete()
        .eq('id', id)

      if (error) throw error
      
      toast.success('Withholding tax rate deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting withholding tax rate:', error)
      toast.error('Failed to delete withholding tax rate')
    } finally {
      setDeletingId(null)
    }
  }

  const formatPercentage = (rate: number) => {
    return `${rate}%`
  }

  if (withholdingTaxRates.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No withholding tax rates found. Create your first withholding tax rate to get started.
      </div>
    )
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Rate</TableHead>
          <TableHead>Description</TableHead>
          <TableHead>URA Code</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Created</TableHead>
          <TableHead className="w-[50px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {withholdingTaxRates.map((withholdingTaxRate) => (
          <TableRow key={withholdingTaxRate.id}>
            <TableCell className="font-medium">{withholdingTaxRate.name}</TableCell>
            <TableCell>{formatPercentage(withholdingTaxRate.rate_pct)}</TableCell>
            <TableCell className="max-w-xs truncate">
              {withholdingTaxRate.description || '-'}
            </TableCell>
            <TableCell>{withholdingTaxRate.ura_code || '-'}</TableCell>
            <TableCell>
              <Badge variant={withholdingTaxRate.is_active ? 'default' : 'secondary'}>
                {withholdingTaxRate.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </TableCell>
            <TableCell>
              {new Date(withholdingTaxRate.created_at).toLocaleDateString()}
            </TableCell>
            <TableCell>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(withholdingTaxRate)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDelete(withholdingTaxRate.id)}
                    disabled={deletingId === withholdingTaxRate.id}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
