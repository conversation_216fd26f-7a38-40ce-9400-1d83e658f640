import React from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { AlertTriangle, TrendingUp, DollarSign } from 'lucide-react'
import { useHasCriticalBudgetAlerts, useBudgetAlerts } from '@/hooks/queries/useBudgetAlerts'

interface BudgetAlertIndicatorProps {
  className?: string
}

export function BudgetAlertIndicator({ className = "" }: BudgetAlertIndicatorProps) {
  const { hasCritical, criticalCount, totalAlerts } = useHasCriticalBudgetAlerts()
  const { data: alertSummary } = useBudgetAlerts()

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  if (!alertSummary || totalAlerts === 0) {
    return null
  }

  const getIcon = () => {
    if (alertSummary.exceededAlerts > 0) {
      return <AlertTriangle className="h-4 w-4 text-red-600" />
    }
    if (alertSummary.criticalAlerts > 0) {
      return <TrendingUp className="h-4 w-4 text-orange-600" />
    }
    return <TrendingUp className="h-4 w-4 text-yellow-600" />
  }

  const getButtonVariant = () => {
    if (hasCritical) return "destructive"
    return "outline"
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button 
          variant={getButtonVariant()} 
          size="sm" 
          className={`relative ${className}`}
        >
          {getIcon()}
          <span className="ml-1 hidden sm:inline">Budget</span>
          {totalAlerts > 0 && (
            <Badge 
              variant={hasCritical ? "destructive" : "secondary"}
              className="ml-2 px-1 py-0 text-xs min-w-[1.25rem] h-5"
            >
              {totalAlerts}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80" align="end">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            <span className="font-medium">Budget Alerts</span>
            <Badge variant="outline">{totalAlerts} Total</Badge>
          </div>

          {/* Alert Summary */}
          <div className="grid grid-cols-3 gap-2 text-center">
            {alertSummary.exceededAlerts > 0 && (
              <div className="p-2 bg-red-50 rounded-lg border border-red-200">
                <div className="text-lg font-bold text-red-600">
                  {alertSummary.exceededAlerts}
                </div>
                <div className="text-xs text-red-600">Exceeded</div>
              </div>
            )}
            
            {alertSummary.criticalAlerts > 0 && (
              <div className="p-2 bg-orange-50 rounded-lg border border-orange-200">
                <div className="text-lg font-bold text-orange-600">
                  {alertSummary.criticalAlerts}
                </div>
                <div className="text-xs text-orange-600">Critical</div>
              </div>
            )}
            
            {alertSummary.warningAlerts > 0 && (
              <div className="p-2 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="text-lg font-bold text-yellow-600">
                  {alertSummary.warningAlerts}
                </div>
                <div className="text-xs text-yellow-600">Warning</div>
              </div>
            )}
          </div>

          {/* Top Alerts */}
          <div className="space-y-2">
            <div className="text-sm font-medium">Top Alerts:</div>
            {alertSummary.alerts.slice(0, 3).map((alert) => (
              <div 
                key={alert.id}
                className="p-2 rounded-lg border bg-gray-50"
              >
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      {alert.alertLevel === 'exceeded' && (
                        <AlertTriangle className="h-3 w-3 text-red-600" />
                      )}
                      {alert.alertLevel === 'critical' && (
                        <TrendingUp className="h-3 w-3 text-orange-600" />
                      )}
                      {alert.alertLevel === 'warning' && (
                        <TrendingUp className="h-3 w-3 text-yellow-600" />
                      )}
                      <span className="text-sm font-medium">{alert.accountName}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {alert.accountCode}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-medium ${
                      alert.alertLevel === 'exceeded' ? 'text-red-600' :
                      alert.alertLevel === 'critical' ? 'text-orange-600' :
                      'text-yellow-600'
                    }`}>
                      {alert.utilizationPercent.toFixed(1)}%
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatCurrency(alert.actualAmount)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {alertSummary.alerts.length > 3 && (
              <div className="text-xs text-muted-foreground text-center">
                +{alertSummary.alerts.length - 3} more alerts
              </div>
            )}
          </div>

          {/* Action Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full"
            onClick={() => {
              // Navigate to budget reports or alerts page
              window.location.href = '/reports?tab=budgets'
            }}
          >
            View All Budget Reports
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  )
}
