
import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { LoadingSpinner } from '@/components/ui/loading'
import { Calendar } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import type { Account } from '@/types/database'

interface TrialBalanceData {
  account: Account
  debit_balance: number
  credit_balance: number
}

export const TrialBalanceReport = () => {
  const { profile } = useAuth()
  const [data, setData] = useState<TrialBalanceData[]>([])
  const [loading, setLoading] = useState(false)
  const [asOfDate, setAsOfDate] = useState(new Date().toISOString().split('T')[0])

  const fetchTrialBalance = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      
      // Fetch accounts with their balances
      const { data: accounts, error: accountsError } = await supabase
        .from('accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('code')

      if (accountsError) throw accountsError

      const trialBalanceData: TrialBalanceData[] = []

      for (const account of accounts || []) {
        // Calculate debit and credit totals for each account
        const { data: transactions, error: transError } = await supabase
          .from('transaction_lines')
          .select('debit, credit')
          .eq('org_id', profile.org_id)
          .eq('account_id', account.id)
          .lte('created_at', `${asOfDate}T23:59:59`)

        if (transError) throw transError

        const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
        const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0

        // Determine which side the balance falls on based on account type
        let debitBalance = 0
        let creditBalance = 0
        const netBalance = debitTotal - creditTotal

        if (['asset', 'expense'].includes(account.type)) {
          // Normal debit balance accounts
          debitBalance = netBalance > 0 ? netBalance : 0
          creditBalance = netBalance < 0 ? Math.abs(netBalance) : 0
        } else {
          // Normal credit balance accounts (liability, equity, revenue)
          creditBalance = netBalance < 0 ? Math.abs(netBalance) : 0
          debitBalance = netBalance > 0 ? netBalance : 0
        }

        if (debitBalance > 0 || creditBalance > 0) {
          trialBalanceData.push({
            account,
            debit_balance: debitBalance,
            credit_balance: creditBalance
          })
        }
      }

      setData(trialBalanceData)
    } catch (error) {
      console.error('Error fetching trial balance:', error)
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, asOfDate])

  useEffect(() => {
    if (profile?.org_id) {
      fetchTrialBalance()
    }
  }, [profile?.org_id, asOfDate, fetchTrialBalance])

  const totalDebits = data.reduce((sum, item) => sum + item.debit_balance, 0)
  const totalCredits = data.reduce((sum, item) => sum + item.credit_balance, 0)

  if (loading) {
    return (
      <div className="p-8">
        <LoadingSpinner text="Loading trial balance..." showText />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Label htmlFor="asOfDate">As of Date:</Label>
          <Input
            id="asOfDate"
            type="date"
            value={asOfDate}
            onChange={(e) => setAsOfDate(e.target.value)}
            className="w-auto"
          />
        </div>
        <Button onClick={fetchTrialBalance} size="sm">
          <Calendar className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Account Code</TableHead>
              <TableHead>Account Name</TableHead>
              <TableHead>Account Type</TableHead>
              <TableHead className="text-right">Debit Balance</TableHead>
              <TableHead className="text-right">Credit Balance</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((item) => (
              <TableRow key={item.account.id}>
                <TableCell className="font-mono">{item.account.code}</TableCell>
                <TableCell>{item.account.name}</TableCell>
                <TableCell className="capitalize">{item.account.type}</TableCell>
                <TableCell className="text-right font-mono">
                  {item.debit_balance > 0 ? formatCurrency(item.debit_balance) : '-'}
                </TableCell>
                <TableCell className="text-right font-mono">
                  {item.credit_balance > 0 ? formatCurrency(item.credit_balance) : '-'}
                </TableCell>
              </TableRow>
            ))}
            <TableRow className="font-bold bg-muted/50">
              <TableCell colSpan={3}>Total</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(totalDebits)}
              </TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(totalCredits)}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>

      {totalDebits !== totalCredits && (
        <Card className="border-destructive bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive">Trial Balance Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-destructive">
              The trial balance does not balance. Debits and credits should be equal.
              Difference: {formatCurrency(Math.abs(totalDebits - totalCredits))}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
