-- Manual Budget Migration Script
-- Run this in the Supabase SQL Editor to add account_id to budgets table

-- Step 1: Add account_id column to budgets table
ALTER TABLE budgets 
ADD COLUMN IF NOT EXISTS account_id UUID;

-- Step 2: Add foreign key constraint
ALTER TABLE budgets 
ADD CONSTRAINT budgets_account_id_fkey 
FOREIGN KEY (account_id) REFERENCES accounts(id);

-- Step 3: Add index for performance
CREATE INDEX IF NOT EXISTS idx_budgets_account_id ON budgets(account_id);

-- Step 4: Migrate existing data from budget_lines to budgets
-- This sets the budget's account_id to the first account found in its budget lines
UPDATE budgets 
SET account_id = (
    SELECT account_id 
    FROM budget_lines 
    WHERE budget_lines.budget_id = budgets.id 
    LIMIT 1
)
WHERE account_id IS NULL 
AND EXISTS (
    SELECT 1 
    FROM budget_lines 
    WHERE budget_lines.budget_id = budgets.id
);

-- Step 5: Add comment to document the change
COMMENT ON COLUMN budgets.account_id IS 'Account associated with this budget. All budget lines inherit this account.';

-- Verification query - run this to check the migration
-- SELECT 
--     b.name as budget_name,
--     a.code as account_code,
--     a.name as account_name,
--     COUNT(bl.id) as line_count
-- FROM budgets b
-- LEFT JOIN accounts a ON b.account_id = a.id
-- LEFT JOIN budget_lines bl ON bl.budget_id = b.id
-- GROUP BY b.id, b.name, a.code, a.name
-- ORDER BY b.created_at DESC;
