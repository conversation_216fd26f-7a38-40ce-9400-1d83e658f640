#!/bin/bash

# Kaya Finance Deployment Script
# Usage: ./scripts/deploy.sh [staging|production] [--skip-tests] [--force]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT=${1:-staging}
SKIP_TESTS=${2:-false}
FORCE_DEPLOY=${3:-false}

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    echo -e "${RED}Error: Environment must be 'staging' or 'production'${NC}"
    exit 1
fi

# Function to print status messages
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18 or later."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or later is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check if pnpm is installed
    if ! command_exists pnpm; then
        print_error "pnpm is not installed. Please install pnpm."
        exit 1
    fi
    
    # Check if git is installed and we're in a git repository
    if ! command_exists git; then
        print_error "Git is not installed."
        exit 1
    fi
    
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "Not in a git repository."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to check git status
check_git_status() {
    print_status "Checking git status..."
    
    # Check if there are uncommitted changes
    if ! git diff-index --quiet HEAD --; then
        if [[ "$FORCE_DEPLOY" != "true" ]]; then
            print_error "There are uncommitted changes. Please commit or stash them before deploying."
            print_warning "Use --force to deploy anyway (not recommended)."
            exit 1
        else
            print_warning "Deploying with uncommitted changes (forced)."
        fi
    fi
    
    # Check current branch
    CURRENT_BRANCH=$(git branch --show-current)
    if [[ "$ENVIRONMENT" == "production" && "$CURRENT_BRANCH" != "main" ]]; then
        if [[ "$FORCE_DEPLOY" != "true" ]]; then
            print_error "Production deployments must be from 'main' branch. Current branch: $CURRENT_BRANCH"
            print_warning "Use --force to deploy anyway (not recommended)."
            exit 1
        else
            print_warning "Deploying to production from non-main branch (forced)."
        fi
    fi
    
    print_success "Git status check passed"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    cd "$PROJECT_ROOT"
    
    if ! pnpm install --frozen-lockfile; then
        print_error "Failed to install dependencies"
        exit 1
    fi
    
    print_success "Dependencies installed"
}

# Function to run tests
run_tests() {
    if [[ "$SKIP_TESTS" == true ]]; then
        print_warning "Skipping tests as requested"
        return
    fi
    
    print_status "Running test suite..."
    
    # Run linting
    print_status "Running linting..."
    if ! pnpm lint; then
        print_error "Linting failed"
        exit 1
    fi
    
    # Run type checking
    print_status "Running type checking..."
    if ! pnpm type-check; then
        print_error "Type checking failed"
        exit 1
    fi
    
    # Run unit tests
    print_status "Running unit tests..."
    if ! pnpm test:unit; then
        print_error "Unit tests failed"
        exit 1
    fi
    
    # Run integration tests for production
    if [[ "$ENVIRONMENT" == "production" ]]; then
        print_status "Running integration tests..."
        if ! pnpm test:integration; then
            print_error "Integration tests failed"
            exit 1
        fi
    fi
    
    print_success "All tests passed"
}

# Function to build application
build_application() {
    print_status "Building application for $ENVIRONMENT..."
    
    # Set environment variables based on deployment target
    if [[ "$ENVIRONMENT" == "production" ]]; then
        export VITE_ENVIRONMENT=production
        print_status "Building for production environment"
    else
        export VITE_ENVIRONMENT=staging
        print_status "Building for staging environment"
    fi
    
    if ! pnpm build; then
        print_error "Build failed"
        exit 1
    fi
    
    print_success "Build completed"
}

# Function to run security checks
run_security_checks() {
    print_status "Running security checks..."
    
    # Run npm audit
    print_status "Running npm audit..."
    if ! pnpm audit --audit-level moderate; then
        print_warning "Security vulnerabilities found. Please review and fix them."
        if [[ "$FORCE_DEPLOY" != "true" ]]; then
            print_error "Deployment blocked due to security issues. Use --force to deploy anyway."
            exit 1
        fi
    fi
    
    print_success "Security checks completed"
}

# Function to deploy to environment
deploy_to_environment() {
    print_status "Deploying to $ENVIRONMENT..."
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        # Production deployment
        print_status "Deploying to production..."
        
        # Create a git tag for the release
        TAG="v$(date +%Y%m%d-%H%M%S)"
        git tag "$TAG"
        git push origin "$TAG"
        print_status "Created release tag: $TAG"
        
        # Deploy using your preferred method (Vercel, Netlify, etc.)
        # Example for Vercel:
        # vercel --prod --token $VERCEL_TOKEN
        
        print_success "Deployed to production"
        print_status "Production URL: https://app.kaya-finance.com"
        
    else
        # Staging deployment
        print_status "Deploying to staging..."
        
        # Deploy using your preferred method
        # Example for Vercel:
        # vercel --token $VERCEL_TOKEN
        
        print_success "Deployed to staging"
        print_status "Staging URL: https://staging.kaya-finance.com"
    fi
}

# Function to run post-deployment tests
run_post_deployment_tests() {
    print_status "Running post-deployment tests..."
    
    # Health check
    if [[ "$ENVIRONMENT" == "production" ]]; then
        URL="https://app.kaya-finance.com"
    else
        URL="https://staging.kaya-finance.com"
    fi
    
    print_status "Running health check against $URL..."
    
    # Wait for deployment to be ready
    sleep 30
    
    # Simple health check
    if command_exists curl; then
        if curl -f -s "$URL" > /dev/null; then
            print_success "Health check passed"
        else
            print_error "Health check failed - application may not be responding"
            exit 1
        fi
    else
        print_warning "curl not available, skipping health check"
    fi
    
    # Run smoke tests if available
    if [[ -f "$PROJECT_ROOT/package.json" ]] && grep -q "test:smoke" "$PROJECT_ROOT/package.json"; then
        print_status "Running smoke tests..."
        if ! pnpm test:smoke --url="$URL"; then
            print_error "Smoke tests failed"
            exit 1
        fi
        print_success "Smoke tests passed"
    fi
}

# Function to send notifications
send_notifications() {
    print_status "Sending deployment notifications..."
    
    # You can add notification logic here (Slack, email, etc.)
    # Example:
    # curl -X POST -H 'Content-type: application/json' \
    #   --data '{"text":"🚀 Kaya Finance deployed to '$ENVIRONMENT'"}' \
    #   $SLACK_WEBHOOK_URL
    
    print_success "Notifications sent"
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up..."
    # Add any cleanup logic here
    print_success "Cleanup completed"
}

# Main deployment function
main() {
    print_status "Starting deployment to $ENVIRONMENT..."
    print_status "Timestamp: $(date)"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --force)
                FORCE_DEPLOY=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    # Run deployment steps
    check_prerequisites
    check_git_status
    install_dependencies
    run_security_checks
    run_tests
    build_application
    deploy_to_environment
    run_post_deployment_tests
    send_notifications
    cleanup
    
    print_success "Deployment to $ENVIRONMENT completed successfully!"
    print_status "Deployment finished at: $(date)"
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; cleanup; exit 1' INT TERM

# Run main function
main "$@"
