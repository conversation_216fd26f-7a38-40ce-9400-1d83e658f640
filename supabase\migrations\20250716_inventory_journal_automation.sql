-- =====================================================
-- INVENTORY JOURNAL AUTOMATION MIGRATION
-- =====================================================
-- This migration adds automated journal entries for inventory transactions
-- including COGS calculations, inventory adjustments, and stock movements

-- Add inventory-specific account mapping types
INSERT INTO account_mappings (org_id, mapping_type, account_id, is_default, created_at, updated_at)
SELECT 
    org_id,
    'inventory_asset' as mapping_type,
    account_id,
    false as is_default,
    NOW() as created_at,
    NOW() as updated_at
FROM account_mappings 
WHERE mapping_type = 'accounts_receivable' 
AND NOT EXISTS (
    SELECT 1 FROM account_mappings am2 
    WHERE am2.org_id = account_mappings.org_id 
    AND am2.mapping_type = 'inventory_asset'
)
ON CONFLICT (org_id, mapping_type, account_id) DO NOTHING;

-- Add COGS account mapping
INSERT INTO account_mappings (org_id, mapping_type, account_id, is_default, created_at, updated_at)
SELECT 
    org_id,
    'cost_of_goods_sold' as mapping_type,
    account_id,
    false as is_default,
    NOW() as created_at,
    NOW() as updated_at
FROM account_mappings 
WHERE mapping_type = 'accounts_receivable' 
AND NOT EXISTS (
    SELECT 1 FROM account_mappings am2 
    WHERE am2.org_id = account_mappings.org_id 
    AND am2.mapping_type = 'cost_of_goods_sold'
)
ON CONFLICT (org_id, mapping_type, account_id) DO NOTHING;

-- Add inventory adjustment account mapping
INSERT INTO account_mappings (org_id, mapping_type, account_id, is_default, created_at, updated_at)
SELECT 
    org_id,
    'inventory_adjustment' as mapping_type,
    account_id,
    false as is_default,
    NOW() as created_at,
    NOW() as updated_at
FROM account_mappings 
WHERE mapping_type = 'accounts_receivable' 
AND NOT EXISTS (
    SELECT 1 FROM account_mappings am2 
    WHERE am2.org_id = account_mappings.org_id 
    AND am2.mapping_type = 'inventory_adjustment'
)
ON CONFLICT (org_id, mapping_type, account_id) DO NOTHING;

-- Function to create journal entries for inventory transactions
CREATE OR REPLACE FUNCTION create_inventory_journal_entry()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_journal_id UUID;
    inventory_account_id UUID;
    cogs_account_id UUID;
    adjustment_account_id UUID;
    validation_result RECORD;
    required_mappings TEXT[];
    automation_enabled BOOLEAN := true;
    product_info RECORD;
    cost_amount DECIMAL(15,2);
BEGIN
    -- Check if automation is enabled for this organization
    SELECT enabled INTO automation_enabled
    FROM automation_settings
    WHERE org_id = NEW.org_id 
    AND setting_key = 'inventory_journal_automation';
    
    -- Default to enabled if no setting exists
    IF automation_enabled IS NULL THEN
        automation_enabled := true;
    END IF;
    
    IF NOT automation_enabled THEN
        RETURN NEW;
    END IF;

    -- Only process specific transaction types
    IF NEW.transaction_type NOT IN ('sale', 'purchase', 'adjustment', 'transfer') THEN
        RETURN NEW;
    END IF;

    -- Get product information
    SELECT name, cost_price INTO product_info
    FROM products 
    WHERE id = NEW.product_id AND org_id = NEW.org_id;
    
    IF NOT FOUND THEN
        RETURN NEW;
    END IF;

    -- Calculate cost amount
    cost_amount := COALESCE(NEW.unit_cost, product_info.cost_price, 0) * ABS(NEW.quantity);

    BEGIN
        -- Determine required mappings based on transaction type
        CASE NEW.transaction_type
            WHEN 'sale' THEN
                required_mappings := ARRAY['inventory_asset', 'cost_of_goods_sold'];
            WHEN 'purchase' THEN
                required_mappings := ARRAY['inventory_asset'];
            WHEN 'adjustment' THEN
                required_mappings := ARRAY['inventory_asset', 'inventory_adjustment'];
            WHEN 'transfer' THEN
                required_mappings := ARRAY['inventory_asset'];
            ELSE
                RETURN NEW;
        END CASE;

        -- Validate required account mappings
        FOR validation_result IN
            SELECT * FROM validate_account_mappings(NEW.org_id, required_mappings)
        LOOP
            IF NOT validation_result.is_valid THEN
                PERFORM handle_journal_entry_error(
                    NEW.org_id,
                    'inventory_transaction',
                    NEW.id,
                    'missing_account_mapping',
                    validation_result.error_message,
                    jsonb_build_object('mapping_type', validation_result.mapping_type)
                );
                RETURN NEW;
            END IF;

            -- Store account IDs based on mapping type
            CASE validation_result.mapping_type
                WHEN 'inventory_asset' THEN inventory_account_id := validation_result.account_id;
                WHEN 'cost_of_goods_sold' THEN cogs_account_id := validation_result.account_id;
                WHEN 'inventory_adjustment' THEN adjustment_account_id := validation_result.account_id;
            END CASE;
        END LOOP;

        -- Create journal entry
        INSERT INTO journal_entries (
            org_id, date, description, reference,
            source_id, source_type, created_by, is_posted
        )
        VALUES (
            NEW.org_id,
            NEW.transaction_date,
            'Inventory ' || NEW.transaction_type || ': ' || product_info.name,
            COALESCE(NEW.reference_number, 'INV-' || NEW.id::text),
            NEW.id,
            'inventory_transaction',
            NEW.created_by,
            false
        )
        RETURNING id INTO new_journal_id;

        -- Create transaction lines based on transaction type
        CASE NEW.transaction_type
            WHEN 'sale' THEN
                -- For sales: DR COGS, CR Inventory Asset
                INSERT INTO transaction_lines (
                    org_id, journal_entry_id, account_id,
                    debit, credit, description
                )
                VALUES 
                (
                    NEW.org_id, new_journal_id, cogs_account_id,
                    cost_amount, 0, 'COGS - ' || product_info.name
                ),
                (
                    NEW.org_id, new_journal_id, inventory_account_id,
                    0, cost_amount, 'Inventory reduction - ' || product_info.name
                );

            WHEN 'purchase' THEN
                -- For purchases: DR Inventory Asset (handled by bill automation)
                -- This is typically handled by the bill journal entry
                -- We only create an entry if this is a direct purchase adjustment
                IF NEW.reference_type != 'bill' THEN
                    INSERT INTO transaction_lines (
                        org_id, journal_entry_id, account_id,
                        debit, credit, description
                    )
                    VALUES (
                        NEW.org_id, new_journal_id, inventory_account_id,
                        cost_amount, 0, 'Inventory purchase - ' || product_info.name
                    );
                END IF;

            WHEN 'adjustment' THEN
                -- For adjustments: DR/CR Inventory Asset, CR/DR Adjustment Account
                IF NEW.quantity > 0 THEN
                    -- Positive adjustment (increase inventory)
                    INSERT INTO transaction_lines (
                        org_id, journal_entry_id, account_id,
                        debit, credit, description
                    )
                    VALUES 
                    (
                        NEW.org_id, new_journal_id, inventory_account_id,
                        cost_amount, 0, 'Inventory adjustment increase - ' || product_info.name
                    ),
                    (
                        NEW.org_id, new_journal_id, adjustment_account_id,
                        0, cost_amount, 'Inventory adjustment - ' || product_info.name
                    );
                ELSE
                    -- Negative adjustment (decrease inventory)
                    INSERT INTO transaction_lines (
                        org_id, journal_entry_id, account_id,
                        debit, credit, description
                    )
                    VALUES 
                    (
                        NEW.org_id, new_journal_id, adjustment_account_id,
                        cost_amount, 0, 'Inventory adjustment - ' || product_info.name
                    ),
                    (
                        NEW.org_id, new_journal_id, inventory_account_id,
                        0, cost_amount, 'Inventory adjustment decrease - ' || product_info.name
                    );
                END IF;

            WHEN 'transfer' THEN
                -- For transfers: No journal entry needed as it's between locations
                -- Delete the journal entry we just created
                DELETE FROM journal_entries WHERE id = new_journal_id;
        END CASE;

    EXCEPTION
        WHEN OTHERS THEN
            -- Log the error
            PERFORM handle_journal_entry_error(
                NEW.org_id,
                'inventory_transaction',
                NEW.id,
                'journal_creation_failed',
                SQLERRM,
                jsonb_build_object(
                    'transaction_type', NEW.transaction_type,
                    'product_id', NEW.product_id,
                    'quantity', NEW.quantity,
                    'unit_cost', NEW.unit_cost
                )
            );
    END;

    RETURN NEW;
END;
$$;

-- Create trigger for inventory journal automation
DROP TRIGGER IF EXISTS inventory_journal_entry_trigger ON inventory_transactions;
CREATE TRIGGER inventory_journal_entry_trigger
    AFTER INSERT ON inventory_transactions
    FOR EACH ROW
    EXECUTE FUNCTION create_inventory_journal_entry();

-- Initialize automation settings for existing organizations
INSERT INTO automation_settings (org_id, setting_key, enabled, setting_value, created_at, updated_at)
SELECT 
    id as org_id,
    'inventory_journal_automation' as setting_key,
    true as enabled,
    jsonb_build_object(
        'auto_post', false,
        'create_cogs_entries', true,
        'create_adjustment_entries', true
    ) as setting_value,
    NOW() as created_at,
    NOW() as updated_at
FROM organizations
WHERE NOT EXISTS (
    SELECT 1 FROM automation_settings 
    WHERE org_id = organizations.id 
    AND setting_key = 'inventory_journal_automation'
);

-- Add comment for documentation
COMMENT ON FUNCTION create_inventory_journal_entry() IS 
'Automatically creates journal entries for inventory transactions including COGS calculations and inventory adjustments';
