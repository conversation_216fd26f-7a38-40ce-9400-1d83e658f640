import { supabase } from '@/lib/supabase'

/**
 * Database verification utilities for approval workflow
 */
export class DatabaseVerification {
  
  /**
   * Check if required database functions exist
   */
  static async checkDatabaseFunctions() {
    console.log('🔍 Checking database functions...')
    
    try {
      // Test advance_approval_workflow function
      const { error } = await supabase.rpc('advance_approval_workflow', {
        p_approval_instance_id: '00000000-0000-0000-0000-000000000000',
        p_action: 'approved',
        p_approver_id: '00000000-0000-0000-0000-000000000000'
      })
      
      if (error) {
        if (error.message.includes('function') && error.message.includes('does not exist')) {
          console.error('❌ Database function advance_approval_workflow does not exist')
          return false
        } else {
          console.log('✅ Database function advance_approval_workflow exists (expected error for dummy data)')
          return true
        }
      }
      
      return true
    } catch (error) {
      console.error('❌ Database function check failed:', error)
      return false
    }
  }
  
  /**
   * Check if approval workflow tables exist and have data
   */
  static async checkApprovalTables() {
    console.log('🔍 Checking approval workflow tables...')
    
    const tables = [
      'workflow_templates',
      'approval_steps', 
      'approval_instances',
      'approval_actions'
    ]
    
    const results: Record<string, number> = {}
    
    for (const table of tables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true })
        
        if (error) {
          console.error(`❌ Error checking table ${table}:`, error)
          results[table] = -1
        } else {
          results[table] = count || 0
          console.log(`✅ Table ${table}: ${count || 0} records`)
        }
      } catch (error) {
        console.error(`❌ Failed to check table ${table}:`, error)
        results[table] = -1
      }
    }
    
    return results
  }
  
  /**
   * Check if there are any approval instances with document details
   */
  static async checkApprovalInstancesWithDocuments() {
    console.log('🔍 Checking approval instances with document details...')
    
    try {
      const { data: instances, error } = await supabase
        .from('approval_instances')
        .select('*')
        .limit(5)
      
      if (error) {
        console.error('❌ Error fetching approval instances:', error)
        return false
      }
      
      console.log(`✅ Found ${instances?.length || 0} approval instances`)
      
      if (instances && instances.length > 0) {
        for (const instance of instances) {
          console.log(`📋 Instance ${instance.id}:`)
          console.log(`   - Document Type: ${instance.document_type}`)
          console.log(`   - Document ID: ${instance.document_id}`)
          console.log(`   - Status: ${instance.status}`)
          
          // Check if the referenced document exists
          let tableName: string
          switch (instance.document_type) {
            case 'invoice':
              tableName = 'invoices'
              break
            case 'bill':
              tableName = 'bills'
              break
            case 'budget':
              tableName = 'budgets'
              break
            case 'payment':
              tableName = 'payments'
              break
            default:
              continue
          }
          
          const { data: document, error: docError } = await supabase
            .from(tableName)
            .select('*')
            .eq('id', instance.document_id)
            .single()
          
          if (docError) {
            console.log(`   ❌ Referenced ${instance.document_type} not found: ${docError.message}`)
          } else {
            console.log(`   ✅ Referenced ${instance.document_type} exists`)
            
            // Check for line items
            let lineTableName: string
            let foreignKey: string
            
            switch (instance.document_type) {
              case 'invoice':
                lineTableName = 'invoice_lines'
                foreignKey = 'invoice_id'
                break
              case 'bill':
                lineTableName = 'bill_lines'
                foreignKey = 'bill_id'
                break
              case 'budget':
                lineTableName = 'budget_lines'
                foreignKey = 'budget_id'
                break
              default:
                continue
            }
            
            const { count: lineCount, error: lineError } = await supabase
              .from(lineTableName)
              .select('*', { count: 'exact', head: true })
              .eq(foreignKey, instance.document_id)
            
            if (lineError) {
              console.log(`   ❌ Error checking line items: ${lineError.message}`)
            } else {
              console.log(`   📝 Line items: ${lineCount || 0}`)
            }
          }
        }
      }
      
      return instances
    } catch (error) {
      console.error('❌ Failed to check approval instances:', error)
      return false
    }
  }
  
  /**
   * Run all database verification checks
   */
  static async runAllChecks() {
    console.log('🚀 Starting database verification...')
    console.log('=' .repeat(50))
    
    const results = {
      functions: false,
      tables: {},
      instances: false
    }
    
    // Check functions
    results.functions = await this.checkDatabaseFunctions()
    console.log('-'.repeat(50))
    
    // Check tables
    results.tables = await this.checkApprovalTables()
    console.log('-'.repeat(50))
    
    // Check instances
    results.instances = await this.checkApprovalInstancesWithDocuments()
    
    console.log('=' .repeat(50))
    console.log('📊 Database Verification Summary:')
    console.log('- Functions:', results.functions ? '✅ PASS' : '❌ FAIL')
    console.log('- Tables:', Object.values(results.tables).every(count => count >= 0) ? '✅ PASS' : '❌ FAIL')
    console.log('- Instances:', results.instances ? '✅ PASS' : '❌ FAIL')
    
    return results
  }
}

// Export for browser console
if (typeof window !== 'undefined') {
  (window as { DatabaseVerification?: typeof DatabaseVerification }).DatabaseVerification = DatabaseVerification
}
