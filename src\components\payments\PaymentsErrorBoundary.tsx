import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Home } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface Props {
  children: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

class PaymentsErrorBoundaryClass extends Component<Props, State> {
  public state: State = {
    hasError: false
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('PaymentsErrorBoundary caught an error:', error, errorInfo)
    
    // Log to monitoring service if available
    if (typeof window !== 'undefined' && (window as { Sentry?: { captureException: (error: Error, options?: Record<string, unknown>) => void } }).Sentry) {
      (window as { Sentry: { captureException: (error: Error, options?: Record<string, unknown>) => void } }).Sentry.captureException(error, {
        tags: {
          component: 'payments',
          boundary: 'PaymentsErrorBoundary'
        },
        contexts: {
          react: {
            componentStack: errorInfo.componentStack,
          },
        },
      })
    }
    
    this.setState({ errorInfo })
    this.props.onError?.(error, errorInfo)
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className="p-6">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <AlertTriangle className="w-5 h-5" />
                Payments System Error
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                An error occurred while loading the payments system. This could be due to:
              </p>
              <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
                <li>Network connectivity issues</li>
                <li>Database connection problems</li>
                <li>Temporary server issues</li>
                <li>Data corruption or invalid payment records</li>
              </ul>
              
              {this.state.error && (
                <details className="text-sm">
                  <summary className="cursor-pointer text-muted-foreground font-medium">
                    Technical Details
                  </summary>
                  <div className="mt-2 p-3 bg-muted rounded text-xs">
                    <div className="font-medium">Error:</div>
                    <pre className="whitespace-pre-wrap">{this.state.error.message}</pre>
                    {this.state.error.stack && (
                      <>
                        <div className="font-medium mt-2">Stack Trace:</div>
                        <pre className="whitespace-pre-wrap text-xs">{this.state.error.stack}</pre>
                      </>
                    )}
                  </div>
                </details>
              )}
              
              <div className="flex gap-2 pt-2">
                <Button onClick={this.handleRetry} className="flex items-center gap-2">
                  <RefreshCw className="w-4 h-4" />
                  Try Again
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
                <PaymentsErrorBoundaryNavigateButton />
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// Separate component to use hooks
function PaymentsErrorBoundaryNavigateButton() {
  const navigate = useNavigate()
  
  return (
    <Button 
      variant="secondary" 
      onClick={() => navigate('/dashboard')}
      className="flex items-center gap-2"
    >
      <Home className="w-4 h-4" />
      Go to Dashboard
    </Button>
  )
}

export function PaymentsErrorBoundary({ children, onError }: Props) {
  return (
    <PaymentsErrorBoundaryClass onError={onError}>
      {children}
    </PaymentsErrorBoundaryClass>
  )
}
