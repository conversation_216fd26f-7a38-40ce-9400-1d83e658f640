import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { LoadingButton } from '@/components/ui/loading'

interface BankAccount {
  id?: string
  name: string
  bank_name: string
  account_no: string
  branch_name?: string
  currency: string
  opening_balance: number
  opening_date: string
  is_active: boolean
}

interface BankAccountFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingAccount?: BankAccount | null
  onSuccess: () => void
}

export function BankAccountForm({
  open,
  onOpenChange,
  editingAccount,
  onSuccess
}: BankAccountFormProps) {
  const { profile } = useAuth()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<BankAccount>({
    name: '',
    bank_name: '',
    account_no: '',
    branch_name: '',
    currency: 'UGX',
    opening_balance: 0,
    opening_date: new Date().toISOString().split('T')[0],
    is_active: true
  })

  useEffect(() => {
    if (editingAccount) {
      setFormData({
        ...editingAccount,
        opening_date: editingAccount.opening_date.split('T')[0] // Convert to date input format
      })
    } else {
      setFormData({
        name: '',
        bank_name: '',
        account_no: '',
        branch_name: '',
        currency: 'UGX',
        opening_balance: 0,
        opening_date: new Date().toISOString().split('T')[0],
        is_active: true
      })
    }
  }, [editingAccount, open])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    setLoading(true)
    try {
      const bankAccountData = {
        name: formData.name,
        bank_name: formData.bank_name,
        account_no: formData.account_no,
        branch_name: formData.branch_name || null,
        currency: formData.currency,
        opening_balance: formData.opening_balance,
        opening_date: formData.opening_date,
        is_active: formData.is_active,
        org_id: profile.org_id,
        created_by: profile.id
      }

      if (editingAccount?.id) {
        const { error } = await supabase
          .from('bank_accounts')
          .update(bankAccountData)
          .eq('id', editingAccount.id)

        if (error) throw error
        toast({
          title: 'Success',
          description: 'Bank account updated successfully'
        })
      } else {
        const { error } = await supabase
          .from('bank_accounts')
          .insert([bankAccountData])

        if (error) throw error
        toast({
          title: 'Success',
          description: 'Bank account created successfully'
        })
      }

      onSuccess()
      onOpenChange(false)
    } catch (error) {
      console.error('Error saving bank account:', error)
      toast({
        title: 'Error',
        description: 'Failed to save bank account',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {editingAccount ? 'Edit Bank Account' : 'Add Bank Account'}
          </DialogTitle>
          <DialogDescription>
            {editingAccount
              ? 'Update the bank account details below.'
              : 'Add a new bank account for payment processing.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Account Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., Main Business Account"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="bank_name">Bank Name *</Label>
            <Input
              id="bank_name"
              value={formData.bank_name}
              onChange={(e) => setFormData({ ...formData, bank_name: e.target.value })}
              placeholder="e.g., Stanbic Bank Uganda"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="account_no">Account Number *</Label>
            <Input
              id="account_no"
              value={formData.account_no}
              onChange={(e) => setFormData({ ...formData, account_no: e.target.value })}
              placeholder="e.g., *************"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="branch_name">Branch Name</Label>
            <Input
              id="branch_name"
              value={formData.branch_name}
              onChange={(e) => setFormData({ ...formData, branch_name: e.target.value })}
              placeholder="e.g., Kampala Main Branch"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Input
                id="currency"
                value={formData.currency}
                onChange={(e) => setFormData({ ...formData, currency: e.target.value })}
                placeholder="UGX"
                maxLength={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="opening_balance">Opening Balance</Label>
              <Input
                id="opening_balance"
                type="number"
                step="0.01"
                value={formData.opening_balance}
                onChange={(e) => setFormData({ ...formData, opening_balance: Number(e.target.value) })}
                placeholder="0.00"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="opening_date">Opening Date</Label>
            <Input
              id="opening_date"
              type="date"
              value={formData.opening_date}
              onChange={(e) => setFormData({ ...formData, opening_date: e.target.value })}
              required
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
            />
            <Label htmlFor="is_active">Active Account</Label>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <LoadingButton
              type="submit"
              loading={loading}
              loadingText={editingAccount ? 'Updating...' : 'Creating...'}
            >
              {editingAccount ? 'Update' : 'Create'} Account
            </LoadingButton>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
