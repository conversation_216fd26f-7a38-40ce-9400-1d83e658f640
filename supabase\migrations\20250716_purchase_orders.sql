-- =====================================================
-- PURCHASE ORDERS MIGRATION
-- =====================================================
-- This migration creates the purchase order system with vendor integration
-- and automatic bill creation upon receipt

-- Purchase Orders Table
CREATE TABLE IF NOT EXISTS purchase_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    
    -- PO Details
    po_number VARCHAR(100) NOT NULL,
    date_issued DATE NOT NULL DEFAULT CURRENT_DATE,
    expected_delivery_date DATE,
    
    -- Financial
    subtotal DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    
    -- Status and workflow
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'confirmed', 'partially_received', 'received', 'cancelled')),
    
    -- Additional info
    notes TEXT,
    terms_and_conditions TEXT,
    delivery_address TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    UNIQUE(org_id, po_number)
);

-- Purchase Order Lines Table
CREATE TABLE IF NOT EXISTS purchase_order_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    purchase_order_id UUID NOT NULL REFERENCES purchase_orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    
    -- Line details
    item VARCHAR(255) NOT NULL,
    description TEXT,
    quantity DECIMAL(15,3) NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(15,2) NOT NULL CHECK (unit_price >= 0),
    tax_rate_pct DECIMAL(5,2) DEFAULT 0 CHECK (tax_rate_pct >= 0 AND tax_rate_pct <= 100),
    line_total DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    
    -- Receiving tracking
    quantity_received DECIMAL(15,3) DEFAULT 0 CHECK (quantity_received >= 0),
    quantity_remaining DECIMAL(15,3) GENERATED ALWAYS AS (quantity - quantity_received) STORED,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Purchase Order Receipts Table (for tracking partial receipts)
CREATE TABLE IF NOT EXISTS purchase_order_receipts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    purchase_order_id UUID NOT NULL REFERENCES purchase_orders(id) ON DELETE CASCADE,
    
    -- Receipt details
    receipt_number VARCHAR(100) NOT NULL,
    receipt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    received_by UUID REFERENCES auth.users(id),
    
    -- Optional bill reference (if bill is created from this receipt)
    bill_id UUID REFERENCES bills(id) ON DELETE SET NULL,
    
    -- Notes
    notes TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(org_id, receipt_number)
);

-- Purchase Order Receipt Lines Table
CREATE TABLE IF NOT EXISTS purchase_order_receipt_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    receipt_id UUID NOT NULL REFERENCES purchase_order_receipts(id) ON DELETE CASCADE,
    po_line_id UUID NOT NULL REFERENCES purchase_order_lines(id) ON DELETE CASCADE,
    
    -- Received quantities
    quantity_received DECIMAL(15,3) NOT NULL CHECK (quantity_received > 0),
    unit_cost DECIMAL(15,2), -- Actual cost received (may differ from PO price)
    
    -- Quality control
    quality_status VARCHAR(20) DEFAULT 'accepted' CHECK (quality_status IN ('accepted', 'rejected', 'pending')),
    quality_notes TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_purchase_orders_org_id ON purchase_orders(org_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_vendor_id ON purchase_orders(vendor_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_date_issued ON purchase_orders(date_issued);

CREATE INDEX IF NOT EXISTS idx_po_lines_purchase_order_id ON purchase_order_lines(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_po_lines_product_id ON purchase_order_lines(product_id);

CREATE INDEX IF NOT EXISTS idx_po_receipts_purchase_order_id ON purchase_order_receipts(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_po_receipts_bill_id ON purchase_order_receipts(bill_id);

CREATE INDEX IF NOT EXISTS idx_po_receipt_lines_receipt_id ON purchase_order_receipt_lines(receipt_id);
CREATE INDEX IF NOT EXISTS idx_po_receipt_lines_po_line_id ON purchase_order_receipt_lines(po_line_id);

-- RLS Policies
ALTER TABLE purchase_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_order_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_order_receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_order_receipt_lines ENABLE ROW LEVEL SECURITY;

-- Purchase Orders policies
CREATE POLICY "Users can view purchase orders in their organization" ON purchase_orders
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert purchase orders in their organization" ON purchase_orders
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update purchase orders in their organization" ON purchase_orders
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete purchase orders in their organization" ON purchase_orders
    FOR DELETE USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

-- Purchase Order Lines policies
CREATE POLICY "Users can view PO lines in their organization" ON purchase_order_lines
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert PO lines in their organization" ON purchase_order_lines
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update PO lines in their organization" ON purchase_order_lines
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete PO lines in their organization" ON purchase_order_lines
    FOR DELETE USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

-- Purchase Order Receipts policies
CREATE POLICY "Users can view PO receipts in their organization" ON purchase_order_receipts
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert PO receipts in their organization" ON purchase_order_receipts
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update PO receipts in their organization" ON purchase_order_receipts
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

-- Purchase Order Receipt Lines policies
CREATE POLICY "Users can view PO receipt lines in their organization" ON purchase_order_receipt_lines
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert PO receipt lines in their organization" ON purchase_order_receipt_lines
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update PO receipt lines in their organization" ON purchase_order_receipt_lines
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

-- Function to update PO status based on receipt status
CREATE OR REPLACE FUNCTION update_purchase_order_status()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    po_id UUID;
    total_quantity DECIMAL(15,3);
    total_received DECIMAL(15,3);
    new_status VARCHAR(20);
BEGIN
    -- Get the purchase order ID
    IF TG_TABLE_NAME = 'purchase_order_receipt_lines' THEN
        SELECT pol.purchase_order_id INTO po_id
        FROM purchase_order_lines pol
        JOIN purchase_order_receipt_lines porl ON pol.id = porl.po_line_id
        WHERE porl.id = NEW.id;
    ELSE
        po_id := NEW.purchase_order_id;
    END IF;

    -- Calculate total quantities
    SELECT 
        SUM(quantity),
        SUM(quantity_received)
    INTO total_quantity, total_received
    FROM purchase_order_lines
    WHERE purchase_order_id = po_id;

    -- Determine new status
    IF total_received = 0 THEN
        new_status := 'confirmed'; -- No receipts yet
    ELSIF total_received >= total_quantity THEN
        new_status := 'received'; -- Fully received
    ELSE
        new_status := 'partially_received'; -- Partially received
    END IF;

    -- Update the purchase order status
    UPDATE purchase_orders
    SET 
        status = new_status,
        updated_at = NOW()
    WHERE id = po_id;

    RETURN NEW;
END;
$$;

-- Triggers for automatic status updates
CREATE TRIGGER update_po_status_on_receipt_line
    AFTER INSERT OR UPDATE OR DELETE ON purchase_order_receipt_lines
    FOR EACH ROW
    EXECUTE FUNCTION update_purchase_order_status();

-- Function to update quantity_received in purchase_order_lines
CREATE OR REPLACE FUNCTION update_po_line_received_quantity()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Update the quantity_received in purchase_order_lines
    UPDATE purchase_order_lines
    SET quantity_received = (
        SELECT COALESCE(SUM(quantity_received), 0)
        FROM purchase_order_receipt_lines
        WHERE po_line_id = NEW.po_line_id
    )
    WHERE id = NEW.po_line_id;

    RETURN NEW;
END;
$$;

-- Trigger to update received quantities
CREATE TRIGGER update_po_line_received_quantity_trigger
    AFTER INSERT OR UPDATE OR DELETE ON purchase_order_receipt_lines
    FOR EACH ROW
    EXECUTE FUNCTION update_po_line_received_quantity();

-- Comments for documentation
COMMENT ON TABLE purchase_orders IS 'Purchase orders for vendor procurement with approval workflow';
COMMENT ON TABLE purchase_order_lines IS 'Line items for purchase orders with product references';
COMMENT ON TABLE purchase_order_receipts IS 'Receipt records for tracking deliveries against purchase orders';
COMMENT ON TABLE purchase_order_receipt_lines IS 'Individual line items received against purchase order lines';
