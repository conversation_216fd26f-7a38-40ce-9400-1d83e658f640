#!/bin/bash

# =====================================================
# KAYA FINANCE PRODUCTION BUILD SCRIPT
# Comprehensive production build with security checks
# =====================================================

set -e  # Exit on any error

echo "🚀 Starting Kaya Finance Production Build..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_warning ".env.production not found. Creating from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env.production
        print_warning "Please update .env.production with your production values before building."
        exit 1
    else
        print_error ".env.example not found. Cannot create production environment file."
        exit 1
    fi
fi

# Validate Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    print_error "Node.js version $REQUIRED_VERSION or higher is required. Current version: $NODE_VERSION"
    exit 1
fi

print_success "Node.js version check passed: $NODE_VERSION"

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf dist/
rm -rf node_modules/.vite/

# Install dependencies
print_status "Installing dependencies..."
npm ci --production=false

# Run security audit
print_status "Running security audit..."
if npm audit --audit-level=high; then
    print_success "Security audit passed"
else
    print_warning "Security vulnerabilities found. Please review and fix before deploying."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Run linting
print_status "Running ESLint..."
if npm run lint; then
    print_success "Linting passed"
else
    print_error "Linting failed. Please fix errors before building."
    exit 1
fi

# Run type checking
print_status "Running TypeScript type checking..."
if npx tsc --noEmit; then
    print_success "Type checking passed"
else
    print_error "Type checking failed. Please fix type errors before building."
    exit 1
fi

# Set production environment
export NODE_ENV=production
export VITE_APP_ENV=production

# Build the application
print_status "Building application for production..."
if npm run build; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

# Verify build output
print_status "Verifying build output..."
if [ ! -d "dist" ]; then
    print_error "Build directory 'dist' not found"
    exit 1
fi

if [ ! -f "dist/index.html" ]; then
    print_error "index.html not found in build output"
    exit 1
fi

# Check bundle sizes
print_status "Analyzing bundle sizes..."
MAIN_JS_SIZE=$(find dist/assets -name "index-*.js" -exec ls -lh {} \; | awk '{print $5}')
MAIN_CSS_SIZE=$(find dist/assets -name "index-*.css" -exec ls -lh {} \; | awk '{print $5}')

print_status "Main JS bundle size: $MAIN_JS_SIZE"
print_status "Main CSS bundle size: $MAIN_CSS_SIZE"

# Security headers check
print_status "Adding security headers to build..."
cat > dist/_headers << EOF
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains
  Content-Security-Policy: default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.ipify.org https://ipapi.co; frame-ancestors 'none'; base-uri 'self'; form-action 'self'

/assets/*
  Cache-Control: public, max-age=31536000, immutable

/*.html
  Cache-Control: public, max-age=0, must-revalidate

/manifest.json
  Cache-Control: public, max-age=86400
EOF

# Create deployment info
print_status "Creating deployment info..."
cat > dist/deployment-info.json << EOF
{
  "version": "$(node -p "require('./package.json').version")",
  "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "nodeVersion": "$NODE_VERSION",
  "environment": "production"
}
EOF

# Generate build report
print_status "Generating build report..."
TOTAL_SIZE=$(du -sh dist/ | cut -f1)
FILE_COUNT=$(find dist -type f | wc -l)

cat > build-report.txt << EOF
Kaya Finance Production Build Report
====================================
Build Date: $(date)
Total Size: $TOTAL_SIZE
File Count: $FILE_COUNT
Main JS: $MAIN_JS_SIZE
Main CSS: $MAIN_CSS_SIZE
Node Version: $NODE_VERSION
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo 'unknown')

Build completed successfully!
EOF

print_success "Production build completed successfully!"
print_status "Build output: ./dist/"
print_status "Build report: ./build-report.txt"
print_status "Total build size: $TOTAL_SIZE"

# Optional: Preview the build
read -p "Would you like to preview the production build? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Starting preview server..."
    npm run preview
fi

print_success "🎉 Production build ready for deployment!"
