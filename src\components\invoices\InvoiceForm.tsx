import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Plus } from 'lucide-react'
import { InvoiceLineItem } from './InvoiceLineItem'
import { supabase } from '@/lib/supabase'
import type { InvoiceFormProps, InvoiceFormData, InvoiceLineData } from '@/types/invoices'

const initialFormData: InvoiceFormData = {
  customer_id: '',
  account_id: '',
  invoice_number: '',
  date_issued: new Date().toISOString().split('T')[0],
  due_date: '',
  notes: '',
  status: 'draft',
}

const initialLineData: InvoiceLineData = {
  item: '',
  description: '',
  quantity: 1,
  unit_price: 0,
  tax_rate_pct: 0,
}

export function InvoiceForm({ 
  open, 
  onOpenChange, 
  editingInvoice, 
  customers, 
  accounts,
  onSubmit 
}: InvoiceFormProps) {
  const [formData, setFormData] = useState<InvoiceFormData>(
    editingInvoice ? {
      customer_id: editingInvoice.customer_id,
      account_id: '', // Will be populated from existing invoice lines
      invoice_number: editingInvoice.invoice_number,
      date_issued: editingInvoice.date_issued,
      due_date: editingInvoice.due_date,
      notes: editingInvoice.notes || '',
      status: editingInvoice.status,
    } : initialFormData
  )
  const [invoiceLines, setInvoiceLines] = useState<InvoiceLineData[]>(
    editingInvoice ? [] : [{ ...initialLineData }]
  )
  const [submitting, setSubmitting] = useState(false)

  // Load existing invoice lines when editing
  useEffect(() => {
    const loadInvoiceLines = async () => {
      if (!editingInvoice) return

      try {
        const { data: lines, error } = await supabase
          .from('invoice_lines')
          .select('*')
          .eq('invoice_id', editingInvoice.id)
          .order('created_at')

        if (error) throw error

        if (lines && lines.length > 0) {
          // Set the account_id from the first line (since all lines should have the same account)
          const firstLine = lines[0]
          if (firstLine.account_id) {
            setFormData(prev => ({ ...prev, account_id: firstLine.account_id }))
          }

          // Convert database lines to form format
          const formLines: InvoiceLineData[] = lines.map(line => {
            // Split the description to extract item and description
            const description = line.description || ''
            const parts = description.split(' - ')
            const item = parts[0] || ''
            const itemDescription = parts.length > 1 ? parts.slice(1).join(' - ') : ''

            return {
              item,
              description: itemDescription,
              quantity: line.quantity,
              unit_price: line.unit_price,
              tax_rate_pct: line.tax_rate_pct
            }
          })

          setInvoiceLines(formLines)
        } else {
          setInvoiceLines([{ ...initialLineData }])
        }
      } catch (error) {
        console.error('Error loading invoice lines:', error)
        setInvoiceLines([{ ...initialLineData }])
      }
    }

    loadInvoiceLines()
  }, [editingInvoice])

  const addInvoiceLine = () => {
    setInvoiceLines([...invoiceLines, { ...initialLineData }])
  }

  const updateInvoiceLine = (index: number, field: keyof InvoiceLineData, value: string | number) => {
    const updatedLines = [...invoiceLines]
    updatedLines[index] = { ...updatedLines[index], [field]: value }
    setInvoiceLines(updatedLines)
  }

  const removeInvoiceLine = (index: number) => {
    setInvoiceLines(invoiceLines.filter((_, i) => i !== index))
  }

  const calculateLineTotals = () => {
    let subtotal = 0
    let taxAmount = 0

    invoiceLines.forEach(line => {
      const lineTotal = line.quantity * line.unit_price
      subtotal += lineTotal
      taxAmount += lineTotal * (line.tax_rate_pct / 100)
    })

    return {
      subtotal,
      taxAmount,
      totalAmount: subtotal + taxAmount
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    try {
      await onSubmit(formData, invoiceLines)
      setFormData(initialFormData)
      setInvoiceLines([{ ...initialLineData }])
    } finally {
      setSubmitting(false)
    }
  }

  const totals = calculateLineTotals()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingInvoice ? 'Edit Invoice' : 'Create New Invoice'}
          </DialogTitle>
          <DialogDescription>
            {editingInvoice ? 'Update the invoice details' : 'Create a new invoice for a customer'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customer_id">Customer *</Label>
              <Select value={formData.customer_id || ''} onValueChange={(value) => setFormData({ ...formData, customer_id: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>



            <div>
              <Label htmlFor="invoice_number">Invoice Number</Label>
              <Input
                id="invoice_number"
                value={formData.invoice_number}
                onChange={(e) => setFormData({ ...formData, invoice_number: e.target.value })}
                placeholder="Auto-generated if empty"
              />
            </div>

            <div>
              <Label htmlFor="date_issued">Date Issued *</Label>
              <Input
                id="date_issued"
                type="date"
                value={formData.date_issued}
                onChange={(e) => setFormData({ ...formData, date_issued: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="due_date">Due Date *</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => setFormData({ ...formData, due_date: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value as InvoiceFormData['status'] })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Additional notes for the invoice"
              rows={3}
            />
          </div>

          <div>
            <div className="flex items-center justify-between mb-4">
              <Label>Invoice Lines</Label>
              <div className="flex items-center gap-4">
                <div className="min-w-[200px]">
                  <Label htmlFor="account_id">Account *</Label>
                  <Select value={formData.account_id || ''} onValueChange={(value) => setFormData({ ...formData, account_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select account" />
                    </SelectTrigger>
                    <SelectContent>
                      {accounts.map((account) => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.name} ({account.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Button type="button" variant="outline" onClick={addInvoiceLine} className="mt-6">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Line
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              {invoiceLines.map((line, index) => (
                <InvoiceLineItem
                  key={index}
                  line={line}
                  index={index}
                  onUpdate={updateInvoiceLine}
                  onRemove={removeInvoiceLine}
                  isLast={invoiceLines.length === 1}
                />
              ))}
            </div>

            {invoiceLines.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>UGX {totals.subtotal.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>UGX {totals.taxAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-bold border-t pt-2">
                    <span>Total:</span>
                    <span>UGX {totals.totalAmount.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={submitting}>
              {submitting ? 'Saving...' : editingInvoice ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
} 
