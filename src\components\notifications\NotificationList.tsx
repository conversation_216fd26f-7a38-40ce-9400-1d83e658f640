import { NotificationItem } from './NotificationItem'
import type { NotificationWithMeta } from '@/types/notifications'

interface NotificationListProps {
  notifications: NotificationWithMeta[]
  onNotificationClick?: () => void
}

export function NotificationList({ 
  notifications, 
  onNotificationClick 
}: NotificationListProps) {
  return (
    <div className="divide-y">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onClick={onNotificationClick}
        />
      ))}
    </div>
  )
}
