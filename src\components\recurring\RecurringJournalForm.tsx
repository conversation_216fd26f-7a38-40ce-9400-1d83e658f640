
import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { Plus, Trash2 } from 'lucide-react'

const recurringJournalSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'yearly']),
  start_date: z.string().min(1, 'Start date is required'),
  end_date: z.string().optional()
})

const recurringLineSchema = z.object({
  account_id: z.string().min(1, 'Account is required'),
  debit: z.number().min(0, 'Debit must be positive'),
  credit: z.number().min(0, 'Credit must be positive'),
  description: z.string().optional()
})

type RecurringJournalFormData = z.infer<typeof recurringJournalSchema>
type RecurringLineFormData = z.infer<typeof recurringLineSchema>

interface RecurringJournalFormProps {
  onClose: () => void
  onSuccess: () => void
}

export const RecurringJournalForm = ({ onClose, onSuccess }: RecurringJournalFormProps) => {
  const { profile } = useAuth()
  const [recurringLines, setRecurringLines] = useState<RecurringLineFormData[]>([
    { account_id: '', debit: 0, credit: 0, description: '' },
    { account_id: '', debit: 0, credit: 0, description: '' }
  ])

  const form = useForm<RecurringJournalFormData>({
    resolver: zodResolver(recurringJournalSchema),
    defaultValues: {
      name: '',
      frequency: 'monthly',
      start_date: new Date().toISOString().split('T')[0],
      end_date: ''
    }
  })

  // Fetch accounts for recurring lines
  const { data: accounts = [] } = useQuery({
    queryKey: ['accounts', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('accounts')
        .select('id, name, code')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('code')

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id
  })

  const addRecurringLine = () => {
    setRecurringLines([...recurringLines, { account_id: '', debit: 0, credit: 0, description: '' }])
  }

  const removeRecurringLine = (index: number) => {
    if (recurringLines.length > 2) {
      setRecurringLines(recurringLines.filter((_, i) => i !== index))
    }
  }

  const updateRecurringLine = (index: number, field: keyof RecurringLineFormData, value: string | number) => {
    const updated = [...recurringLines]
    updated[index] = { ...updated[index], [field]: value }
    setRecurringLines(updated)
  }

  const calculateTotals = () => {
    const totalDebits = recurringLines.reduce((sum, line) => sum + (line.debit || 0), 0)
    const totalCredits = recurringLines.reduce((sum, line) => sum + (line.credit || 0), 0)
    return { totalDebits, totalCredits }
  }

  const calculateNextDate = (startDate: string, frequency: string) => {
    const date = new Date(startDate)
    switch (frequency.toLowerCase()) {
      case 'daily':
        date.setDate(date.getDate() + 1)
        break
      case 'weekly':
        date.setDate(date.getDate() + 7)
        break
      case 'monthly':
        date.setMonth(date.getMonth() + 1)
        break
      case 'quarterly':
        date.setMonth(date.getMonth() + 3)
        break
      case 'yearly':
        date.setFullYear(date.getFullYear() + 1)
        break
    }
    return date.toISOString().split('T')[0]
  }

  const onSubmit = async (data: RecurringJournalFormData) => {
    if (!profile?.org_id) {
      toast.error('Organization not found')
      return
    }

    const { totalDebits, totalCredits } = calculateTotals()
    
    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      toast.error('Debits and credits must be equal')
      return
    }

    const validLines = recurringLines.filter(line => 
      line.account_id && (line.debit > 0 || line.credit > 0)
    )

    if (validLines.length < 2) {
      toast.error('At least two transaction lines are required')
      return
    }

    try {
      const nextDate = calculateNextDate(data.start_date, data.frequency)
      
      const recurringJournalData = {
        name: data.name,
        frequency: data.frequency.toUpperCase(),
        start_date: data.start_date,
        end_date: data.end_date || null,
        next_date: nextDate,
        org_id: profile.org_id,
        created_by: profile.id,
        is_active: true
      }

      const { data: recurringJournal, error: journalError } = await supabase
        .from('recurring_journals')
        .insert(recurringJournalData)
        .select()
        .single()

      if (journalError) throw journalError

      // Create recurring lines
      const lines = validLines.map(line => ({
        recurring_journal_id: recurringJournal.id,
        account_id: line.account_id,
        debit: line.debit || 0,
        credit: line.credit || 0,
        description: line.description || null
      }))

      const { error: linesError } = await supabase
        .from('recurring_lines')
        .insert(lines)

      if (linesError) throw linesError

      toast.success('Recurring journal created successfully')
      onSuccess()
      onClose()
    } catch (error) {
      console.error('Error creating recurring journal:', error)
      toast.error('Failed to create recurring journal')
    }
  }

  const { totalDebits, totalCredits } = calculateTotals()
  const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            {...form.register('name')}
            placeholder="Enter recurring journal name"
          />
          {form.formState.errors.name && (
            <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="frequency">Frequency</Label>
          <Select value={form.watch('frequency')} onValueChange={(value) => form.setValue('frequency', value as 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly')}>
            <SelectTrigger>
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="start_date">Start Date</Label>
          <Input
            id="start_date"
            type="date"
            {...form.register('start_date')}
          />
          {form.formState.errors.start_date && (
            <p className="text-sm text-red-600">{form.formState.errors.start_date.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="end_date">End Date (Optional)</Label>
          <Input
            id="end_date"
            type="date"
            {...form.register('end_date')}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Transaction Lines</CardTitle>
            <div className="flex items-center gap-4">
              <div className={`text-sm px-2 py-1 rounded ${isBalanced ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {isBalanced ? 'Balanced' : 'Out of Balance'}
              </div>
              <Button type="button" onClick={addRecurringLine} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Line
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Account</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Debit</TableHead>
                <TableHead>Credit</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recurringLines.map((line, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Select
                      value={line.account_id}
                      onValueChange={(value) => updateRecurringLine(index, 'account_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select account" />
                      </SelectTrigger>
                      <SelectContent>
                        {accounts.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            {account.code} - {account.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Input
                      value={line.description || ''}
                      onChange={(e) => updateRecurringLine(index, 'description', e.target.value)}
                      placeholder="Line description"
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      step="0.01"
                      value={line.debit || ''}
                      onChange={(e) => updateRecurringLine(index, 'debit', parseFloat(e.target.value) || 0)}
                      disabled={line.credit > 0}
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      step="0.01"
                      value={line.credit || ''}
                      onChange={(e) => updateRecurringLine(index, 'credit', parseFloat(e.target.value) || 0)}
                      disabled={line.debit > 0}
                    />
                  </TableCell>
                  <TableCell>
                    {recurringLines.length > 2 && (
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        onClick={() => removeRecurringLine(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
              <TableRow className="font-bold">
                <TableCell colSpan={2}>Totals</TableCell>
                <TableCell>{totalDebits.toFixed(2)}</TableCell>
                <TableCell>{totalCredits.toFixed(2)}</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <div className="flex gap-2 pt-4">
        <Button type="submit" className="flex-1" disabled={!isBalanced}>
          Create Recurring Journal
        </Button>
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
      </div>
    </form>
  )
}
