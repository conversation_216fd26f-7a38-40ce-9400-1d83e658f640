// =====================================================
// PRODUCTION ENVIRONMENT CONFIGURATION
// Centralized configuration management for all environments
// =====================================================

export interface AppConfig {
  app: {
    name: string
    version: string
    environment: 'development' | 'staging' | 'production'
    enableDevtools: boolean
  }
  supabase: {
    url: string
    anonKey: string
  }
  security: {
    sessionTimeoutMinutes: number
    csrfProtection: boolean
    enableAuditLogging: boolean
  }
  api: {
    timeout: number
    maxFileSize: number
  }
  features: {
    offlineMode: boolean
    notifications: boolean
    ipDetection: boolean
  }
  company: {
    name: string
    registrationNumber: string
    supportWhatsapp: string
    supportEmail: string
  }
  legal: {
    privacyPolicyUrl: string
    termsOfServiceUrl: string
    dataRetentionYears: number
  }
}

// Environment detection
const getEnvironment = (): 'development' | 'staging' | 'production' => {
  const mode = import.meta.env.MODE
  const env = import.meta.env.VITE_APP_ENV
  
  if (env === 'production' || mode === 'production') return 'production'
  if (env === 'staging' || mode === 'staging') return 'staging'
  return 'development'
}

// Supabase configuration based on environment
const getSupabaseConfig = () => {
  const environment = getEnvironment()
  
  if (environment === 'production') {
    return {
      url: import.meta.env.VITE_SUPABASE_URL_PROD || import.meta.env.VITE_SUPABASE_URL || 'https://kmejequnwwngmzwkszqs.supabase.co',
      anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY_PROD || import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImttZWplcXVud3duZ216d2tzenFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDk3ODQsImV4cCI6MjA2Mzc4NTc4NH0.ROGVGPdlDh_o1TJJJijM1BTubWUhUXUh5oZWxOKDdjw'
    }
  }
  
  return {
    url: import.meta.env.VITE_SUPABASE_URL || 'https://kmejequnwwngmzwkszqs.supabase.co',
    anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImttZWplcXVud3duZ216d2tzenFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDk3ODQsImV4cCI6MjA2Mzc4NTc4NH0.ROGVGPdlDh_o1TJJJijM1BTubWUhUXUh5oZWxOKDdjw'
  }
}

// Main configuration object
export const config: AppConfig = {
  app: {
    name: import.meta.env.VITE_APP_NAME || 'Kaya Finance',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    environment: getEnvironment(),
    enableDevtools: import.meta.env.VITE_ENABLE_DEVTOOLS === 'true' && getEnvironment() !== 'production'
  },
  supabase: getSupabaseConfig(),
  security: {
    sessionTimeoutMinutes: Number(import.meta.env.VITE_SESSION_TIMEOUT_MINUTES) || 30,
    csrfProtection: import.meta.env.VITE_CSRF_PROTECTION !== 'false',
    enableAuditLogging: import.meta.env.VITE_ENABLE_AUDIT_LOGGING !== 'false'
  },
  api: {
    timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 30000,
    maxFileSize: Number(import.meta.env.VITE_MAX_FILE_SIZE) || 10485760 // 10MB
  },
  features: {
    offlineMode: import.meta.env.VITE_ENABLE_OFFLINE_MODE !== 'false',
    notifications: import.meta.env.VITE_ENABLE_NOTIFICATIONS !== 'false',
    ipDetection: import.meta.env.VITE_ENABLE_IP_DETECTION !== 'false'
  },
  company: {
    name: import.meta.env.VITE_COMPANY_NAME || "Tom's Cyber Lab (U) Ltd",
    registrationNumber: import.meta.env.VITE_COMPANY_REG_NUMBER || '80020002602390',
    supportWhatsapp: import.meta.env.VITE_SUPPORT_WHATSAPP || '256777959328',
    supportEmail: import.meta.env.VITE_SUPPORT_EMAIL || '<EMAIL>'
  },
  legal: {
    privacyPolicyUrl: import.meta.env.VITE_PRIVACY_POLICY_URL || '/privacy-policy',
    termsOfServiceUrl: import.meta.env.VITE_TERMS_OF_SERVICE_URL || '/terms-of-service',
    dataRetentionYears: Number(import.meta.env.VITE_DATA_RETENTION_YEARS) || 7
  }
}

// Configuration validation
export function validateConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Validate required Supabase configuration
  if (!config.supabase.url || !config.supabase.url.startsWith('https://')) {
    errors.push('Invalid Supabase URL')
  }
  
  if (!config.supabase.anonKey || config.supabase.anonKey.length < 100) {
    errors.push('Invalid Supabase anonymous key')
  }
  
  // Validate security configuration
  if (config.security.sessionTimeoutMinutes < 5 || config.security.sessionTimeoutMinutes > 480) {
    errors.push('Session timeout must be between 5 and 480 minutes')
  }
  
  // Validate API configuration
  if (config.api.timeout < 1000 || config.api.timeout > 120000) {
    errors.push('API timeout must be between 1 and 120 seconds')
  }
  
  if (config.api.maxFileSize < 1048576 || config.api.maxFileSize > 104857600) {
    errors.push('Max file size must be between 1MB and 100MB')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Environment-specific feature flags
export const isProduction = () => config.app.environment === 'production'
export const isDevelopment = () => config.app.environment === 'development'
export const isStaging = () => config.app.environment === 'staging'

// Security helpers
export const getCSPDirectives = () => {
  const baseDirectives = [
    "default-src 'self'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    `connect-src 'self' ${config.supabase.url} ${config.supabase.url.replace('https://', 'wss://')}`,
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ]
  
  if (isDevelopment()) {
    baseDirectives.push("script-src 'self' 'unsafe-inline' 'unsafe-eval'")
    baseDirectives.push("connect-src 'self' ws://localhost:* http://localhost:* https://api.ipify.org https://ipapi.co")
  } else {
    baseDirectives.push("script-src 'self'")
    baseDirectives.push("connect-src 'self' https://api.ipify.org https://ipapi.co")
  }
  
  return baseDirectives.join('; ')
}

// Export configuration for use throughout the app
export default config

// Log configuration in development
if (isDevelopment()) {
  console.log('🔧 App Configuration:', {
    environment: config.app.environment,
    version: config.app.version,
    supabaseUrl: config.supabase.url,
    features: config.features
  })
  
  const validation = validateConfig()
  if (!validation.isValid) {
    console.warn('⚠️ Configuration validation errors:', validation.errors)
  }
}
