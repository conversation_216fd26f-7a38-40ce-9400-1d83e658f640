import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Trash2 } from 'lucide-react'

export interface BudgetLineData {
  item: string
  amount: number
  notes: string
}

interface BudgetLineItemProps {
  line: BudgetLineData
  index: number
  onUpdate: (index: number, field: keyof BudgetLineData, value: string | number) => void
  onRemove: (index: number) => void
  isLast: boolean
}

export function BudgetLineItem({
  line,
  index,
  onUpdate,
  onRemove,
  isLast
}: BudgetLineItemProps) {
  return (
    <div className="grid grid-cols-12 gap-4 items-end">
      <div className="col-span-4">
        <Label>Item Name</Label>
        <Input
          value={line.item}
          onChange={(e) => onUpdate(index, 'item', e.target.value)}
          placeholder="Budget item name"
        />
      </div>

      <div className="col-span-3">
        <Label>Amount</Label>
        <Input
          type="number"
          min="0"
          step="0.01"
          value={line.amount}
          onChange={(e) => onUpdate(index, 'amount', parseFloat(e.target.value) || 0)}
          placeholder="0.00"
        />
      </div>

      <div className="col-span-4">
        <Label>Notes</Label>
        <Input
          value={line.notes}
          onChange={(e) => onUpdate(index, 'notes', e.target.value)}
          placeholder="Optional notes"
        />
      </div>

      <div className="col-span-1">
        {!isLast && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => onRemove(index)}
            className="h-10"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}
