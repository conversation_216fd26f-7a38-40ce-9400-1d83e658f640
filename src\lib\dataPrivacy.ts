import React, { useCallback } from 'react'
import { supabase } from './supabase'
import { config } from './config'
import { monitoring } from './monitoring'

// Data privacy types
export interface DataSubject {
  id: string
  email: string
  name: string
  phone?: string
  organization: string
  dataCategories: DataCategory[]
  consentStatus: ConsentStatus
  retentionPeriod: number // in years
  createdAt: string
  lastAccessed?: string
}

export interface DataCategory {
  category: 'personal' | 'financial' | 'transactional' | 'technical' | 'marketing'
  description: string
  legalBasis: 'consent' | 'contract' | 'legal_obligation' | 'legitimate_interest'
  retentionPeriod: number
  sensitive: boolean
}

export interface ConsentStatus {
  marketing: boolean
  analytics: boolean
  functional: boolean
  necessary: boolean // Always true, cannot be revoked
  lastUpdated: string
  ipAddress?: string
  userAgent?: string
}

export interface DataExportRequest {
  id: string
  subjectId: string
  requestDate: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  completedDate?: string
  downloadUrl?: string
  expiresAt?: string
}

export interface DataDeletionRequest {
  id: string
  subjectId: string
  requestDate: string
  reason: string
  status: 'pending' | 'approved' | 'processing' | 'completed' | 'rejected'
  approvedBy?: string
  completedDate?: string
  retainedData?: string[] // Data that cannot be deleted due to legal requirements
}

class DataPrivacyService {
  private static instance: DataPrivacyService

  static getInstance(): DataPrivacyService {
    if (!DataPrivacyService.instance) {
      DataPrivacyService.instance = new DataPrivacyService()
    }
    return DataPrivacyService.instance
  }

  // Record user consent
  async recordConsent(
    userId: string,
    consent: Partial<ConsentStatus>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      const consentRecord = {
        user_id: userId,
        marketing: consent.marketing || false,
        analytics: consent.analytics || false,
        functional: consent.functional || false,
        necessary: true, // Always required
        ip_address: ipAddress,
        user_agent: userAgent,
        timestamp: new Date().toISOString()
      }

      const { error } = await supabase
        .from('user_consent')
        .upsert(consentRecord)

      if (error) throw error

      // Log consent change
      monitoring.trackUserAction('consent_updated', {
        userId,
        consent: consentRecord
      })

      console.log('User consent recorded:', userId)
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'record_consent'
        },
        extra: { userId, consent }
      })
      throw error
    }
  }

  // Get user consent status
  async getConsentStatus(userId: string): Promise<ConsentStatus | null> {
    try {
      const { data, error } = await supabase
        .from('user_consent')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') throw error

      if (!data) return null

      return {
        marketing: data.marketing,
        analytics: data.analytics,
        functional: data.functional,
        necessary: data.necessary,
        lastUpdated: data.timestamp,
        ipAddress: data.ip_address,
        userAgent: data.user_agent
      }
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'get_consent_status'
        },
        extra: { userId }
      })
      throw error
    }
  }

  // Request data export (GDPR Article 20 - Right to data portability)
  async requestDataExport(userId: string, email: string): Promise<string> {
    try {
      const exportRequest = {
        id: crypto.randomUUID(),
        subject_id: userId,
        email,
        request_date: new Date().toISOString(),
        status: 'pending'
      }

      const { error } = await supabase
        .from('data_export_requests')
        .insert(exportRequest)

      if (error) throw error

      // Log export request
      monitoring.trackUserAction('data_export_requested', {
        userId,
        requestId: exportRequest.id
      })

      // Trigger background processing
      this.processDataExport(exportRequest.id)

      return exportRequest.id
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'request_data_export'
        },
        extra: { userId }
      })
      throw error
    }
  }

  // Process data export request
  private async processDataExport(requestId: string): Promise<void> {
    try {
      // Update status to processing
      await supabase
        .from('data_export_requests')
        .update({ status: 'processing' })
        .eq('id', requestId)

      // Get request details
      const { data: request, error: requestError } = await supabase
        .from('data_export_requests')
        .select('*')
        .eq('id', requestId)
        .single()

      if (requestError) throw requestError

      // Collect all user data
      const userData = await this.collectUserData(request.subject_id)

      // Generate export file
      const exportData = {
        exportDate: new Date().toISOString(),
        dataSubject: request.subject_id,
        data: userData,
        metadata: {
          version: config.app.version,
          format: 'JSON',
          encryption: 'AES-256-GCM'
        }
      }

      // In a real implementation, you would:
      // 1. Encrypt the data
      // 2. Upload to secure storage
      // 3. Generate time-limited download URL
      // 4. Send notification email

      const downloadUrl = await this.createSecureDownloadUrl(exportData)
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

      // Update request with download URL
      await supabase
        .from('data_export_requests')
        .update({
          status: 'completed',
          completed_date: new Date().toISOString(),
          download_url: downloadUrl,
          expires_at: expiresAt.toISOString()
        })
        .eq('id', requestId)

      console.log('Data export completed:', requestId)
    } catch (error) {
      // Update status to failed
      await supabase
        .from('data_export_requests')
        .update({ status: 'failed' })
        .eq('id', requestId)

      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'process_data_export'
        },
        extra: { requestId }
      })
    }
  }

  // Collect all user data for export
  private async collectUserData(userId: string): Promise<Record<string, unknown>> {
    const userData: Record<string, unknown> = {}

    try {
      // Profile data
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      userData.profile = profile

      // Organization data (if user is admin)
      if (profile?.role === 'admin') {
        const { data: organization } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', profile.org_id)
          .single()

        userData.organization = organization
      }

      // Audit logs
      const { data: auditLogs } = await supabase
        .from('audit_logs')
        .select('*')
        .eq('profile_id', userId)
        .order('created_at', { ascending: false })
        .limit(1000) // Limit to recent logs

      userData.auditLogs = auditLogs

      // Consent history
      const { data: consentHistory } = await supabase
        .from('user_consent')
        .select('*')
        .eq('user_id', userId)

      userData.consentHistory = consentHistory

      return userData
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'collect_user_data'
        },
        extra: { userId }
      })
      throw error
    }
  }

  // Create secure download URL (placeholder implementation)
  private async createSecureDownloadUrl(data: Record<string, unknown>): Promise<string> {
    // In a real implementation, this would:
    // 1. Encrypt the data
    // 2. Upload to secure storage (Supabase Storage with RLS)
    // 3. Generate signed URL with expiration
    
    // For now, return a placeholder URL
    return `https://secure.kayafinance.net/exports/${crypto.randomUUID()}`
  }

  // Request data deletion (GDPR Article 17 - Right to erasure)
  async requestDataDeletion(
    userId: string,
    reason: string,
    email: string
  ): Promise<string> {
    try {
      const deletionRequest = {
        id: crypto.randomUUID(),
        subject_id: userId,
        email,
        reason,
        request_date: new Date().toISOString(),
        status: 'pending'
      }

      const { error } = await supabase
        .from('data_deletion_requests')
        .insert(deletionRequest)

      if (error) throw error

      // Log deletion request
      monitoring.trackUserAction('data_deletion_requested', {
        userId,
        requestId: deletionRequest.id,
        reason
      })

      return deletionRequest.id
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'request_data_deletion'
        },
        extra: { userId, reason }
      })
      throw error
    }
  }

  // Check data retention compliance
  async checkRetentionCompliance(): Promise<{
    expiredData: Array<{
      table: string
      recordId: string
      expiryDate: string
      category: string
    }>
    totalExpired: number
  }> {
    try {
      const retentionPeriodYears = config.legal.dataRetentionYears
      const cutoffDate = new Date()
      cutoffDate.setFullYear(cutoffDate.getFullYear() - retentionPeriodYears)

      // Check various tables for expired data
      const expiredData: Array<{
        table: string;
        recordId: string;
        expiryDate: string;
        category: string;
      }> = []

      // Check audit logs
      const { data: expiredAuditLogs } = await supabase
        .from('audit_logs')
        .select('id, created_at')
        .lt('created_at', cutoffDate.toISOString())

      expiredAuditLogs?.forEach(log => {
        expiredData.push({
          table: 'audit_logs',
          recordId: log.id,
          expiryDate: log.created_at,
          category: 'audit'
        })
      })

      // Check other tables as needed...

      return {
        expiredData,
        totalExpired: expiredData.length
      }
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'check_retention_compliance'
        }
      })
      throw error
    }
  }

  // Anonymize expired data
  async anonymizeExpiredData(recordIds: string[], table: string): Promise<void> {
    try {
      // Implementation depends on the table structure
      // This is a placeholder for the anonymization logic
      
      monitoring.trackUserAction('data_anonymized', {
        table,
        recordCount: recordIds.length
      })

      console.log(`Anonymized ${recordIds.length} records from ${table}`)
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'anonymize_expired_data'
        },
        extra: { table, recordCount: recordIds.length }
      })
      throw error
    }
  }

  // Get privacy dashboard data
  async getPrivacyDashboard(userId: string): Promise<{
    consentStatus: ConsentStatus | null
    dataCategories: DataCategory[]
    exportRequests: DataExportRequest[]
    deletionRequests: DataDeletionRequest[]
    lastDataAccess?: string
  }> {
    try {
      const [
        consentStatus,
        exportRequests,
        deletionRequests
      ] = await Promise.all([
        this.getConsentStatus(userId),
        this.getExportRequests(userId),
        this.getDeletionRequests(userId)
      ])

      const dataCategories: DataCategory[] = [
        {
          category: 'personal',
          description: 'Name, email, phone number',
          legalBasis: 'contract',
          retentionPeriod: 7,
          sensitive: false
        },
        {
          category: 'financial',
          description: 'Transaction data, invoices, payments',
          legalBasis: 'legal_obligation',
          retentionPeriod: 7,
          sensitive: true
        },
        {
          category: 'technical',
          description: 'IP address, browser information, usage logs',
          legalBasis: 'legitimate_interest',
          retentionPeriod: 2,
          sensitive: false
        }
      ]

      return {
        consentStatus,
        dataCategories,
        exportRequests,
        deletionRequests
      }
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'data_privacy',
          operation: 'get_privacy_dashboard'
        },
        extra: { userId }
      })
      throw error
    }
  }

  // Get export requests for user
  private async getExportRequests(userId: string): Promise<DataExportRequest[]> {
    const { data, error } = await supabase
      .from('data_export_requests')
      .select('*')
      .eq('subject_id', userId)
      .order('request_date', { ascending: false })

    if (error) throw error

    return data?.map(req => ({
      id: req.id,
      subjectId: req.subject_id,
      requestDate: req.request_date,
      status: req.status,
      completedDate: req.completed_date,
      downloadUrl: req.download_url,
      expiresAt: req.expires_at
    })) || []
  }

  // Get deletion requests for user
  private async getDeletionRequests(userId: string): Promise<DataDeletionRequest[]> {
    const { data, error } = await supabase
      .from('data_deletion_requests')
      .select('*')
      .eq('subject_id', userId)
      .order('request_date', { ascending: false })

    if (error) throw error

    return data?.map(req => ({
      id: req.id,
      subjectId: req.subject_id,
      requestDate: req.request_date,
      reason: req.reason,
      status: req.status,
      approvedBy: req.approved_by,
      completedDate: req.completed_date,
      retainedData: req.retained_data
    })) || []
  }
}

// Export singleton instance
export const dataPrivacy = DataPrivacyService.getInstance()

// React hook for data privacy
export function useDataPrivacy(userId?: string) {
  const [dashboard, setDashboard] = React.useState<{
    consentStatus: ConsentStatus | null;
    dataCategories: DataCategory[];
    exportRequests: DataExportRequest[];
    deletionRequests: DataDeletionRequest[];
    lastDataAccess?: string;
  } | null>(null)
  const [loading, setLoading] = React.useState(false)

  const loadDashboard = useCallback(async () => {
    if (!userId) return

    setLoading(true)
    try {
      const data = await dataPrivacy.getPrivacyDashboard(userId)
      setDashboard(data)
    } catch (error) {
      console.error('Failed to load privacy dashboard:', error)
    } finally {
      setLoading(false)
    }
  }, [userId])

  React.useEffect(() => {
    if (userId) {
      loadDashboard()
    }
  }, [userId, loadDashboard])

  const updateConsent = async (consent: Partial<ConsentStatus>) => {
    if (!userId) return
    await dataPrivacy.recordConsent(userId, consent)
    await loadDashboard() // Refresh dashboard
  }

  const requestExport = async () => {
    if (!userId || !dashboard?.consentStatus) return
    return await dataPrivacy.requestDataExport(userId, '<EMAIL>')
  }

  const requestDeletion = async (reason: string) => {
    if (!userId) return
    return await dataPrivacy.requestDataDeletion(userId, reason, '<EMAIL>')
  }

  return {
    dashboard,
    loading,
    updateConsent,
    requestExport,
    requestDeletion,
    refresh: loadDashboard
  }
}
