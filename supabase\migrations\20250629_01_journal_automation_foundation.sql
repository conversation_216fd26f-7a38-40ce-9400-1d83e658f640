-- =====================================================
-- JOURNAL ENTRY AUTOMATION - SEGMENT 1: FOUNDATION
-- =====================================================
-- This migration creates the core infrastructure for automated journal entries
-- Date: 2025-06-29
-- Purpose: Create supporting tables, validation functions, and error handling
-- Dependencies: None (foundational)

-- =====================================================
-- STEP 1: CREATE SUPPORTING TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating journal entry automation foundation tables...';
END $$;

-- Account mappings table for organization-specific account configurations
CREATE TABLE IF NOT EXISTS account_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    mapping_type VARCHAR(50) NOT NULL, -- 'accounts_receivable', 'accounts_payable', 'revenue', 'expense', 'vat_payable', 'vat_receivable', 'cash'
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(org_id, mapping_type, is_default),
    CONSTRAINT valid_mapping_type CHECK (mapping_type IN (
        'accounts_receivable', 'accounts_payable', 'revenue', 'expense',
        'vat_payable', 'vat_receivable', 'cash', 'bank'
    ))
);

-- Journal entry errors table for error logging and tracking
CREATE TABLE IF NOT EXISTS journal_entry_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    source_type VARCHAR(50) NOT NULL, -- 'invoice', 'payment', 'payment_application', 'reconciliation'
    source_id UUID NOT NULL,
    error_type VARCHAR(100) NOT NULL, -- 'missing_account_mapping', 'unbalanced_entry', 'duplicate_entry', 'database_error'
    error_message TEXT NOT NULL,
    error_details JSONB DEFAULT '{}',
    retry_count INTEGER DEFAULT 0,
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automation settings table for feature flags and configuration
CREATE TABLE IF NOT EXISTS automation_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL,
    setting_value JSONB NOT NULL DEFAULT '{}',
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(org_id, setting_key)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_account_mappings_org_type ON account_mappings(org_id, mapping_type);
CREATE INDEX IF NOT EXISTS idx_journal_entry_errors_org_source ON journal_entry_errors(org_id, source_type, source_id);
CREATE INDEX IF NOT EXISTS idx_journal_entry_errors_unresolved ON journal_entry_errors(org_id, resolved) WHERE resolved = false;
CREATE INDEX IF NOT EXISTS idx_automation_settings_org_key ON automation_settings(org_id, setting_key);

-- =====================================================
-- STEP 2: CREATE VALIDATION FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating validation functions...';
END $$;

-- Function to validate required account mappings exist
CREATE OR REPLACE FUNCTION validate_account_mappings(
    org_id_param UUID,
    required_mappings TEXT[]
)
RETURNS TABLE (
    mapping_type TEXT,
    account_id UUID,
    is_valid BOOLEAN,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    mapping_type_item TEXT;
    account_record RECORD;
BEGIN
    -- Check each required mapping type
    FOREACH mapping_type_item IN ARRAY required_mappings
    LOOP
        -- Get the default account mapping for this type
        SELECT am.account_id, a.name, a.code
        INTO account_record
        FROM account_mappings am
        JOIN accounts a ON am.account_id = a.id
        WHERE am.org_id = org_id_param
        AND am.mapping_type = mapping_type_item
        AND am.is_default = true;

        IF account_record.account_id IS NOT NULL THEN
            RETURN QUERY SELECT
                mapping_type_item,
                account_record.account_id,
                true,
                NULL::TEXT;
        ELSE
            RETURN QUERY SELECT
                mapping_type_item,
                NULL::UUID,
                false,
                ('Missing account mapping for: ' || mapping_type_item)::TEXT;
        END IF;
    END LOOP;
END;
$$;

-- Function to validate journal entry balance
CREATE OR REPLACE FUNCTION validate_journal_entry_balance(
    journal_entry_id_param UUID
)
RETURNS TABLE (
    is_balanced BOOLEAN,
    total_debits DECIMAL(15,2),
    total_credits DECIMAL(15,2),
    difference DECIMAL(15,2)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    debits_sum DECIMAL(15,2) := 0;
    credits_sum DECIMAL(15,2) := 0;
    diff DECIMAL(15,2);
BEGIN
    -- Calculate total debits and credits
    SELECT
        COALESCE(SUM(debit), 0),
        COALESCE(SUM(credit), 0)
    INTO debits_sum, credits_sum
    FROM transaction_lines
    WHERE journal_entry_id = journal_entry_id_param;

    diff := debits_sum - credits_sum;

    RETURN QUERY SELECT
        (ABS(diff) < 0.01), -- Allow for minor rounding differences
        debits_sum,
        credits_sum,
        diff;
END;
$$;

-- Function to handle journal entry errors
CREATE OR REPLACE FUNCTION handle_journal_entry_error(
    org_id_param UUID,
    source_type_param VARCHAR(50),
    source_id_param UUID,
    error_type_param VARCHAR(100),
    error_message_param TEXT,
    error_details_param JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    error_id UUID;
BEGIN
    -- Insert error record
    INSERT INTO journal_entry_errors (
        org_id, source_type, source_id, error_type,
        error_message, error_details
    )
    VALUES (
        org_id_param, source_type_param, source_id_param,
        error_type_param, error_message_param, error_details_param
    )
    RETURNING id INTO error_id;

    -- Send notification to accounting staff (integrate with existing notification system)
    PERFORM create_notification_from_template(
        'journal_entry_error',
        org_id_param,
        NULL, -- Organization-wide notification
        jsonb_build_object(
            'source_type', source_type_param,
            'source_id', source_id_param,
            'error_type', error_type_param,
            'error_message', error_message_param
        ),
        source_type_param,
        source_id_param
    );

    RETURN error_id;
END;
$$;

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ JOURNAL ENTRY AUTOMATION FOUNDATION COMPLETE!';
    RAISE NOTICE '================================================';
    RAISE NOTICE '';
    RAISE NOTICE '📋 CREATED:';
    RAISE NOTICE '  • account_mappings table';
    RAISE NOTICE '  • journal_entry_errors table';
    RAISE NOTICE '  • automation_settings table';
    RAISE NOTICE '  • validate_account_mappings() function';
    RAISE NOTICE '  • validate_journal_entry_balance() function';
    RAISE NOTICE '  • handle_journal_entry_error() function';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 NEXT: Run segment 2 for invoice automation';
    RAISE NOTICE '';
END $$;