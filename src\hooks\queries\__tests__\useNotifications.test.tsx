/**
 * @jest-environment jsdom
 */

import React from 'react'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { 
  useNotifications, 
  useNotificationCount, 
  useMarkNotificationAsRead,
  useArchiveNotification,
  useDeleteNotification,
  useBulkUpdateNotificationPreferences
} from '../useNotifications'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/integrations/supabase/client'

// Mock dependencies
jest.mock('@/hooks/useAuthHook')
jest.mock('@/integrations/supabase/client')
jest.mock('@/components/ui/toast-utils')

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockSupabase = supabase as jest.Mocked<typeof supabase>

// Mock profile
const mockProfile = {
  id: 'user-1',
  org_id: 'org-1',
  email: '<EMAIL>',
  full_name: 'Test User'
}

// Mock notifications
const mockNotifications = [
  {
    id: '1',
    type: 'payment_pending_approval',
    category: 'financial',
    priority: 'high',
    title: 'Payment Approval Required',
    message: 'Payment requires approval',
    is_read: false,
    is_archived: false,
    created_at: '2025-06-28T10:00:00Z',
    updated_at: '2025-06-28T10:00:00Z',
    user_id: 'user-1',
    org_id: 'org-1',
    entity_type: 'payment',
    entity_id: 'payment-1',
    read_at: null,
    data: {}
  }
]

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useNotifications', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseAuth.mockReturnValue({
      profile: mockProfile,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    // Mock Supabase query builder
    const mockSelect = jest.fn().mockReturnThis()
    const mockEq = jest.fn().mockReturnThis()
    const mockOr = jest.fn().mockReturnThis()
    const mockOrder = jest.fn().mockReturnThis()
    const mockLimit = jest.fn().mockReturnThis()
    const mockRange = jest.fn().mockReturnThis()

    mockSupabase.from = jest.fn().mockReturnValue({
      select: mockSelect,
      eq: mockEq,
      or: mockOr,
      order: mockOrder,
      limit: mockLimit,
      range: mockRange
    })

    mockSelect.mockResolvedValue({
      data: mockNotifications,
      error: null,
      count: 1
    })
  })

  it('fetches notifications successfully', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useNotifications(), { wrapper })

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(result.current.data).toEqual(mockNotifications)
    expect(mockSupabase.from).toHaveBeenCalledWith('notifications')
  })

  it('handles loading state', () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useNotifications(), { wrapper })

    expect(result.current.isLoading).toBe(true)
  })

  it('handles error state', async () => {
    const mockError = new Error('Database error')
    const mockSelect = jest.fn().mockResolvedValue({
      data: null,
      error: mockError
    })

    mockSupabase.from = jest.fn().mockReturnValue({
      select: mockSelect,
      eq: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis()
    })

    const wrapper = createWrapper()
    const { result } = renderHook(() => useNotifications(), { wrapper })

    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })

    expect(result.current.error).toBeTruthy()
  })

  it('applies filters correctly', async () => {
    const filters = { is_read: false, category: 'financial' }
    const wrapper = createWrapper()
    
    renderHook(() => useNotifications(filters), { wrapper })

    await waitFor(() => {
      expect(mockSupabase.from).toHaveBeenCalledWith('notifications')
    })
  })

  it('handles unauthenticated user', () => {
    mockUseAuth.mockReturnValue({
      profile: null,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    const wrapper = createWrapper()
    const { result } = renderHook(() => useNotifications(), { wrapper })

    expect(result.current.data).toEqual([])
  })
})

describe('useNotificationCount', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseAuth.mockReturnValue({
      profile: mockProfile,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    const mockSelect = jest.fn().mockResolvedValue({
      data: [{ count: 5 }],
      error: null
    })

    mockSupabase.from = jest.fn().mockReturnValue({
      select: mockSelect,
      eq: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis()
    })
  })

  it('fetches notification count successfully', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useNotificationCount(), { wrapper })

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(result.current.data).toBe(5)
  })

  it('returns 0 for unauthenticated user', () => {
    mockUseAuth.mockReturnValue({
      profile: null,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    const wrapper = createWrapper()
    const { result } = renderHook(() => useNotificationCount(), { wrapper })

    expect(result.current.data).toBe(0)
  })
})

describe('useMarkNotificationAsRead', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseAuth.mockReturnValue({
      profile: mockProfile,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    const mockUpdate = jest.fn().mockResolvedValue({
      data: { ...mockNotifications[0], is_read: true },
      error: null
    })

    mockSupabase.from = jest.fn().mockReturnValue({
      update: mockUpdate,
      eq: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis()
    })
  })

  it('marks notification as read successfully', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useMarkNotificationAsRead(), { wrapper })

    await waitFor(() => {
      expect(result.current.mutate).toBeDefined()
    })

    result.current.mutate('1')

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(mockSupabase.from).toHaveBeenCalledWith('notifications')
  })

  it('handles error when marking as read', async () => {
    const mockError = new Error('Update failed')
    const mockUpdate = jest.fn().mockResolvedValue({
      data: null,
      error: mockError
    })

    mockSupabase.from = jest.fn().mockReturnValue({
      update: mockUpdate,
      eq: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis()
    })

    const wrapper = createWrapper()
    const { result } = renderHook(() => useMarkNotificationAsRead(), { wrapper })

    result.current.mutate('1')

    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })
  })
})

describe('useArchiveNotification', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseAuth.mockReturnValue({
      profile: mockProfile,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    const mockUpdate = jest.fn().mockResolvedValue({
      data: { ...mockNotifications[0], is_archived: true },
      error: null
    })

    mockSupabase.from = jest.fn().mockReturnValue({
      update: mockUpdate,
      eq: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis()
    })
  })

  it('archives notification successfully', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useArchiveNotification(), { wrapper })

    result.current.mutate('1')

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(mockSupabase.from).toHaveBeenCalledWith('notifications')
  })
})

describe('useDeleteNotification', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseAuth.mockReturnValue({
      profile: mockProfile,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    const mockDelete = jest.fn().mockResolvedValue({
      data: null,
      error: null
    })

    mockSupabase.from = jest.fn().mockReturnValue({
      delete: mockDelete,
      eq: jest.fn().mockReturnThis()
    })
  })

  it('deletes notification successfully', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useDeleteNotification(), { wrapper })

    result.current.mutate('1')

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(mockSupabase.from).toHaveBeenCalledWith('notifications')
  })
})

describe('useBulkUpdateNotificationPreferences', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseAuth.mockReturnValue({
      profile: mockProfile,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    const mockUpsert = jest.fn().mockResolvedValue({
      data: [],
      error: null
    })

    mockSupabase.from = jest.fn().mockReturnValue({
      upsert: mockUpsert,
      select: jest.fn().mockReturnThis()
    })
  })

  it('updates preferences in bulk successfully', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useBulkUpdateNotificationPreferences(), { wrapper })

    const preferences = [
      {
        notification_type: 'payment_pending_approval',
        enabled: true,
        email_enabled: true,
        in_app_enabled: true,
        push_enabled: true
      }
    ]

    result.current.mutate(preferences)

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(mockSupabase.from).toHaveBeenCalledWith('notification_preferences')
  })

  it('handles bulk update errors', async () => {
    const mockError = new Error('Bulk update failed')
    const mockUpsert = jest.fn().mockResolvedValue({
      data: null,
      error: mockError
    })

    mockSupabase.from = jest.fn().mockReturnValue({
      upsert: mockUpsert,
      select: jest.fn().mockReturnThis()
    })

    const wrapper = createWrapper()
    const { result } = renderHook(() => useBulkUpdateNotificationPreferences(), { wrapper })

    const preferences = [
      {
        notification_type: 'payment_pending_approval',
        enabled: true,
        email_enabled: true,
        in_app_enabled: true,
        push_enabled: true
      }
    ]

    result.current.mutate(preferences)

    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })
  })
})
