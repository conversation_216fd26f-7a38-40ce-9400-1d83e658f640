# GitHub Repository Setup Guide for Kaya Finance

This guide will help you create and set up a GitHub repository for the Kaya Finance project.

## Prerequisites

1. **GitHub Account**: Make sure you have a GitHub account
2. **Git Installed**: Ensure Git is installed on your system
3. **GitHub CLI (Optional)**: Install GitHub CLI for easier repository creation

## Step 1: Initialize Git Repository

First, initialize a Git repository in your project directory:

```bash
# Navigate to your project directory
cd /c/Users/<USER>/Projects/kaya-finance-UG

# Initialize Git repository
git init

# Add all files to staging
git add .

# Create initial commit
git commit -m "Initial commit: Kaya Finance application"
```

## Step 2: Create GitHub Repository

### Option A: Using GitHub CLI (Recommended)

If you have GitHub CLI installed:

```bash
# Create repository on GitHub and set up remote
gh repo create kaya-finance --public --description "Modern Financial Management for Small & Medium Enterprises"

# Push to GitHub
git push -u origin main
```

### Option B: Using GitHub Web Interface

1. **Go to GitHub**: Visit [github.com](https://github.com)
2. **Create New Repository**:
   - Click the "+" icon in the top right
   - Select "New repository"
3. **Repository Settings**:
   - **Repository name**: `kaya-finance`
   - **Description**: `Modern Financial Management for Small & Medium Enterprises`
   - **Visibility**: Public (or Private if preferred)
   - **Initialize**: Don't initialize with README, .gitignore, or license (we already have these)
4. **Create Repository**: Click "Create repository"

### Option C: Manual Setup

After creating the repository on GitHub:

```bash
# Add GitHub remote
git remote add origin https://github.com/YOUR_USERNAME/kaya-finance.git

# Rename default branch to main (if needed)
git branch -M main

# Push to GitHub
git push -u origin main
```

## Step 3: Configure Repository Settings

### Branch Protection Rules

1. Go to your repository on GitHub
2. Navigate to **Settings** → **Branches**
3. Add rule for `main` branch:
   - ✅ Require pull request reviews before merging
   - ✅ Require status checks to pass before merging
   - ✅ Require branches to be up to date before merging
   - ✅ Include administrators

### Repository Topics

Add relevant topics to help others discover your repository:
- `finance`
- `accounting`
- `sme`
- `uganda`
- `africa`
- `react`
- `typescript`
- `supabase`
- `financial-management`

### Repository Description

Set the description to: "Modern Financial Management for Small & Medium Enterprises"

## Step 4: Set Up GitHub Actions (CI/CD)

Create `.github/workflows/ci.yml`:

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run type check
      run: npm run type-check
    
    - name: Run linting
      run: npm run lint
    
    - name: Run tests
      run: npm run test:coverage
    
    - name: Build application
      run: npm run build
```

## Step 5: Create Development Workflow

### Branch Strategy

1. **Main Branch**: Production-ready code
2. **Develop Branch**: Integration branch for features
3. **Feature Branches**: Individual features (`feature/feature-name`)
4. **Hotfix Branches**: Critical fixes (`hotfix/fix-name`)

```bash
# Create and switch to develop branch
git checkout -b develop
git push -u origin develop

# Set develop as default branch for new PRs (optional)
```

### Commit Convention

Use conventional commits:
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Test additions/changes
- `chore:` - Maintenance tasks

## Step 6: Set Up Issue Templates

Create `.github/ISSUE_TEMPLATE/` directory with templates:

1. **Bug Report** (`.github/ISSUE_TEMPLATE/bug_report.md`)
2. **Feature Request** (`.github/ISSUE_TEMPLATE/feature_request.md`)
3. **Documentation** (`.github/ISSUE_TEMPLATE/documentation.md`)

## Step 7: Configure Secrets

Add the following secrets in GitHub repository settings:

1. **SUPABASE_URL**: Your Supabase project URL
2. **SUPABASE_ANON_KEY**: Your Supabase anonymous key
3. **SENTRY_DSN**: Your Sentry DSN (if using)

## Step 8: Update README

Update the README.md file to replace placeholder URLs:

```bash
# Replace YOUR_USERNAME with your actual GitHub username
sed -i 's/your-username/YOUR_ACTUAL_USERNAME/g' README.md
```

## Step 9: Create Release

After setting up everything:

```bash
# Tag the initial release
git tag -a v1.0.0 -m "Initial release of Kaya Finance"
git push origin v1.0.0
```

## Next Steps

1. **Invite Collaborators**: Add team members to the repository
2. **Set Up Deployment**: Configure deployment to staging/production
3. **Documentation**: Ensure all documentation is up to date
4. **Security**: Set up security scanning and dependency updates
5. **Monitoring**: Configure error tracking and performance monitoring

## Useful Commands

```bash
# Check repository status
git status

# View commit history
git log --oneline

# Create and switch to new branch
git checkout -b feature/new-feature

# Push new branch to GitHub
git push -u origin feature/new-feature

# Sync with remote changes
git pull origin main

# View remote repositories
git remote -v
```

## Troubleshooting

### Authentication Issues

If you encounter authentication issues:

```bash
# Configure Git credentials
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Use personal access token for HTTPS
# Or set up SSH keys for SSH authentication
```

### Large File Issues

If you have large files:

```bash
# Install Git LFS
git lfs install

# Track large files
git lfs track "*.pdf"
git lfs track "*.zip"

# Add .gitattributes
git add .gitattributes
```

---

**Note**: Replace `YOUR_USERNAME` with your actual GitHub username throughout this process.
