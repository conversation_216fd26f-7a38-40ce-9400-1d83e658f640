# Journal Entry Automation Architecture

## Overview

This document outlines the comprehensive architecture for automated journal entry creation in the Kaya Finance application. The system will ensure real-time, accurate double-entry bookkeeping for all financial transactions.

## Current State Analysis

### Existing Infrastructure
- **Manual Functions**: `generate_missing_journal_entries()` and `audit_missing_journal_entries()`
- **Batch Processing**: Functions process invoices/bills retroactively
- **Notification Triggers**: Payment approval and invoice payment notifications exist
- **No Real-time Automation**: Journal entries are not created automatically when transactions occur

### Critical Gaps Identified
1. **No automatic journal entry creation** when invoices are issued
2. **No payment journal entries** when payments are applied to invoices
3. **Missing payment reconciliation** journal entries
4. **No validation** for account mappings before journal creation
5. **No error handling** for failed automated entries

## Proposed Architecture

### 1. Trigger Points for Automation

#### Invoice Lifecycle Triggers
```sql
-- Trigger when invoice status changes to 'sent' or 'paid'
CREATE TRIGGER invoice_journal_entry_trigger
    AFTER INSERT OR UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION create_invoice_journal_entry();
```

#### Payment Application Triggers
```sql
-- Trigger when payment is applied to invoice/bill
CREATE TRIGGER payment_application_journal_trigger
    AFTER INSERT ON payment_applications
    FOR EACH ROW
    EXECUTE FUNCTION create_payment_application_journal_entry();
```

#### Payment Reconciliation Triggers
```sql
-- Trigger when payment reconciliation status changes
CREATE TRIGGER payment_reconciliation_trigger
    AFTER UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION create_payment_reconciliation_journal_entry();
```

### 2. Account Mapping System

#### Required Account Mappings
- **Accounts Receivable (AR)**: Customer invoices
- **Accounts Payable (AP)**: Vendor bills
- **Revenue Accounts**: Sales income
- **Expense Accounts**: Operating expenses
- **VAT Payable**: Tax collections
- **VAT Receivable**: Tax payments
- **Cash/Bank Accounts**: Payment processing

#### Validation Logic
```sql
-- Function to validate required account mappings exist
CREATE FUNCTION validate_account_mappings(org_id UUID, transaction_type TEXT)
RETURNS BOOLEAN;
```

### 3. Journal Entry Creation Functions

#### Invoice Journal Entries
**When Invoice Status = 'sent':**
- **Debit**: Accounts Receivable (total_amount)
- **Credit**: Revenue Account (subtotal)
- **Credit**: VAT Payable (tax_amount)

#### Payment Application Journal Entries
**Customer Payment Applied to Invoice:**
- **Debit**: Cash/Bank Account (payment_amount)
- **Credit**: Accounts Receivable (payment_amount)

**Vendor Payment Applied to Bill:**
- **Debit**: Accounts Payable (payment_amount)
- **Credit**: Cash/Bank Account (payment_amount)

### 4. Error Handling Strategy

#### Error Types
1. **Missing Account Mappings**: Required accounts not configured
2. **Unbalanced Entries**: Debits ≠ Credits
3. **Duplicate Entries**: Journal entry already exists for transaction
4. **Database Constraints**: Foreign key violations, data type errors

#### Error Recovery
- **Automatic Retry**: For transient database errors
- **Manual Intervention Queue**: For configuration issues
- **Notification System**: Alert accounting staff of failures
- **Audit Trail**: Log all failed attempts with detailed error messages

### 5. Data Integrity Measures

#### Transaction Atomicity
```sql
-- Ensure payment application and journal entry are created together
BEGIN;
    INSERT INTO payment_applications (...);
    INSERT INTO journal_entries (...);
    INSERT INTO transaction_lines (...);
COMMIT;
```

#### Validation Checks
- **Balance Validation**: Sum(debits) = Sum(credits)
- **Amount Validation**: Payment applications ≤ outstanding balance
- **Duplicate Prevention**: Check for existing journal entries
- **Account Existence**: Verify all referenced accounts exist

### 6. Rollback Mechanisms

#### Automatic Rollback Triggers
- **Failed Transaction**: Rollback entire transaction on any error
- **Validation Failure**: Prevent creation of invalid entries
- **Constraint Violation**: Maintain database integrity

#### Manual Rollback Tools
- **Journal Entry Reversal**: Create reversing entries
- **Payment Application Removal**: Safely remove incorrect applications
- **Audit Trail Maintenance**: Track all rollback operations

## Implementation Phases

### Phase 1: Core Infrastructure
1. Create account mapping validation system
2. Implement basic journal entry creation functions
3. Add error logging and notification system

### Phase 2: Invoice Automation
1. Create invoice journal entry triggers
2. Implement real-time invoice journal creation
3. Add validation for invoice-specific accounts

### Phase 3: Payment Automation
1. Create payment application triggers
2. Implement payment reconciliation automation
3. Add bank account integration

### Phase 4: Data Integrity
1. Add comprehensive validation checks
2. Implement transaction-level consistency
3. Create rollback and recovery mechanisms

### Phase 5: Testing & Deployment
1. Comprehensive testing framework
2. Historical data migration
3. Gradual rollout with feature flags

## Technical Specifications

### Database Functions Required
- `create_invoice_journal_entry()`
- `create_payment_application_journal_entry()`
- `create_payment_reconciliation_journal_entry()`
- `validate_account_mappings()`
- `validate_journal_entry_balance()`
- `handle_journal_entry_error()`

### New Tables Required
- `journal_entry_errors`: Error logging and tracking
- `account_mappings`: Organization-specific account configurations
- `automation_settings`: Feature flags and configuration

### Integration Points
- **Notification System**: Alert on automation failures
- **Audit System**: Track all automated journal entries
- **Reporting System**: Include automated entries in financial reports

## Success Metrics

### Automation Coverage
- **100%** of invoices have journal entries within 1 minute of status change
- **100%** of payment applications create journal entries immediately
- **99.9%** uptime for automation system

### Data Accuracy
- **Zero** unbalanced journal entries
- **Zero** duplicate journal entries
- **100%** of automated entries pass audit validation

### Error Handling
- **<1%** error rate for automated journal creation
- **<5 minutes** average resolution time for automation failures
- **100%** of errors logged and tracked

This architecture provides a robust foundation for automated journal entry creation while maintaining data integrity and providing comprehensive error handling.

## Implementation Status: COMPLETE ✅

The journal entry automation system has been successfully implemented and deployed to the Supabase database. All core functionality is now active and operational.

## User Guide for Accounting Staff

### Getting Started

#### 1. Initial Setup
Before using the automation system, ensure account mappings are configured:

```sql
-- Check current account mappings
SELECT * FROM account_mappings WHERE org_id = 'your-org-id';

-- Set up account mappings manually if needed
SELECT update_account_mapping('your-org-id', 'accounts_receivable', 'your-ar-account-id');
SELECT update_account_mapping('your-org-id', 'revenue', 'your-revenue-account-id');
SELECT update_account_mapping('your-org-id', 'vat_payable', 'your-vat-account-id');
SELECT update_account_mapping('your-org-id', 'cash', 'your-cash-account-id');
SELECT update_account_mapping('your-org-id', 'accounts_payable', 'your-ap-account-id');

-- Initialize automation settings
SELECT * FROM initialize_automation_settings('your-org-id');
```

#### 2. How Automation Works

**Invoice Journal Entries:**
- Automatically created when invoice status changes to 'sent'
- Creates: DR Accounts Receivable, CR Revenue, CR VAT Payable
- Appears in journal entries with source_type = 'invoice'

**Payment Journal Entries:**
- Automatically created when payments are applied to invoices/bills
- Customer payments: DR Cash, CR Accounts Receivable
- Vendor payments: DR Accounts Payable, CR Cash
- Appears in journal entries with source_type = 'payment_application'

#### 3. Monitoring and Troubleshooting

**Check for Errors:**
```sql
-- View unresolved automation errors
SELECT * FROM journal_entry_errors
WHERE org_id = 'your-org-id' AND resolved = false
ORDER BY created_at DESC;
```

**Run System Tests:**
```sql
-- Test the automation system
SELECT * FROM run_journal_automation_tests('your-org-id');

-- Validate accounting accuracy
SELECT * FROM validate_accounting_accuracy('your-org-id');
```

**Retry Failed Entries:**
```sql
-- Automatically retry failed journal entries
SELECT * FROM retry_failed_journal_entries('your-org-id');

-- Manually resolve specific errors
SELECT resolve_journal_entry_error('error-id', 'Resolution notes');
```

#### 4. Historical Data Migration

If you have existing invoices and payments without journal entries:

```sql
-- Migrate all historical data
SELECT * FROM migrate_all_historical_journal_entries('your-org-id');

-- Or migrate specific types
SELECT * FROM migrate_historical_invoice_journal_entries('your-org-id');
SELECT * FROM migrate_historical_payment_journal_entries('your-org-id');
```

### Daily Operations

#### Reviewing Automated Journal Entries
1. **Check Daily Journal Entries:**
   - Review journal_entries table for entries created today
   - Verify all automated entries are balanced
   - Check for any error notifications

2. **Validate Invoice Processing:**
   - When invoices are marked as 'sent', verify journal entries are created
   - Check that AR, Revenue, and VAT accounts are properly debited/credited

3. **Monitor Payment Processing:**
   - When payments are applied, verify corresponding journal entries
   - Ensure cash and receivables/payables are properly adjusted

#### Monthly Reconciliation
1. **Run Accuracy Validation:**
   ```sql
   SELECT * FROM validate_accounting_accuracy('your-org-id');
   ```

2. **Review Error Log:**
   ```sql
   SELECT * FROM journal_entry_errors
   WHERE org_id = 'your-org-id'
   AND created_at >= date_trunc('month', CURRENT_DATE);
   ```

3. **Check System Coverage:**
   - Verify all invoices have corresponding journal entries
   - Confirm all payment applications have journal entries

### Troubleshooting Common Issues

#### Issue: No Journal Entry Created for Invoice
**Cause:** Missing account mappings or automation disabled
**Solution:**
1. Check account mappings exist for AR, Revenue, VAT
2. Verify automation settings are enabled
3. Check journal_entry_errors table for specific error

#### Issue: Unbalanced Journal Entries
**Cause:** Data inconsistency or calculation error
**Solution:**
1. Run balance validation function
2. Check transaction_lines for the journal entry
3. Verify invoice amounts are correct

#### Issue: Payment Journal Entry Missing
**Cause:** Missing cash or AR/AP account mappings
**Solution:**
1. Verify cash account mapping exists
2. Check AR/AP account mappings
3. Ensure payment application was created properly

### Advanced Features

#### Custom Account Mappings
You can set up multiple account mappings for different scenarios:
```sql
-- Set up department-specific revenue accounts
INSERT INTO account_mappings (org_id, mapping_type, account_id, is_default)
VALUES ('your-org-id', 'revenue_consulting', 'consulting-revenue-account-id', false);
```

#### Automation Settings
Control automation behavior:
```sql
-- Disable invoice automation temporarily
UPDATE automation_settings
SET enabled = false
WHERE org_id = 'your-org-id' AND setting_key = 'invoice_journal_automation';

-- Enable auto-posting of journal entries
UPDATE automation_settings
SET setting_value = jsonb_set(setting_value, '{auto_post}', 'true')
WHERE org_id = 'your-org-id' AND setting_key = 'invoice_journal_automation';
```

## Technical Documentation

### Database Schema

#### Core Tables
- `account_mappings`: Organization-specific account configurations
- `journal_entry_errors`: Error logging and tracking
- `automation_settings`: Feature flags and configuration

#### Key Functions
- `create_invoice_journal_entry()`: Invoice automation trigger
- `create_payment_application_journal_entry()`: Payment automation trigger
- `validate_account_mappings()`: Account validation
- `handle_journal_entry_error()`: Error handling

### API Integration

The automation system works seamlessly with the existing Kaya Finance frontend. No changes are required to the user interface - journal entries are created automatically in the background when:

1. Invoice status is changed to 'sent' via the invoice form
2. Payments are applied to invoices via the payment application dialog
3. Bills are paid through the payment system

### Security and Permissions

- All automation functions use `SECURITY DEFINER` for consistent execution
- Row Level Security (RLS) policies ensure data isolation by organization
- Error logging includes audit trails for troubleshooting
- Manual intervention tools require appropriate user permissions