import { supabase } from '@/lib/supabase'
import { ApprovalEngine } from './core'
import { AuthorizationService } from './authorization-service'
import type { 
  ApprovalAction,
  ApprovalStatus,
  UserRole
} from '@/types/database'
import type {
  CreateApprovalActionPayload,
  ApprovalActionRequest,
  BulkApprovalRequest,
  BulkApprovalResult
} from '@/types/approval-workflow'

/**
 * Approval Action Handler
 * Processes approval actions with validation and state transitions
 */
export class ApprovalActionHandler {

  /**
   * Process a single approval action
   */
  static async processAction(
    approvalInstanceId: string,
    approverId: string,
    actionRequest: ApprovalActionRequest
  ): Promise<{ success: boolean; message: string; action?: ApprovalAction }> {
    try {
      // Validate the action request
      const validation = await this.validateActionRequest(
        approvalInstanceId,
        approverId,
        actionRequest
      )

      if (!validation.valid) {
        return { success: false, message: validation.message }
      }

      // Convert action request to payload
      const actionPayload: CreateApprovalActionPayload = {
        approval_instance_id: approvalInstanceId,
        action: actionRequest.action === 'approve' ? 'approved' : 
                actionRequest.action === 'reject' ? 'rejected' : 'delegated',
        comments: actionRequest.comments,
        rejection_reason: actionRequest.rejection_reason,
        delegated_to: actionRequest.delegated_to,
        delegation_reason: actionRequest.delegation_reason,
        delegation_expires_at: actionRequest.delegation_expires_at
      }

      // Process the action using the approval engine
      const result = await ApprovalEngine.processApprovalAction(
        approvalInstanceId,
        approverId,
        actionPayload
      )

      if (result.success) {
        // Get the created action record
        const { data: action, error } = await supabase
          .from('approval_actions')
          .select('*')
          .eq('approval_instance_id', approvalInstanceId)
          .eq('approver_id', approverId)
          .order('created_at', { ascending: false })
          .limit(1)
          .single()

        if (!error) {
          result.action = action
        }
      }

      return result
    } catch (error) {
      console.error('Error processing approval action:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Process bulk approval actions
   */
  static async processBulkActions(
    bulkRequest: BulkApprovalRequest,
    approverId: string
  ): Promise<BulkApprovalResult> {
    const successful: string[] = []
    const failed: Array<{ id: string; error: string }> = []

    for (const instanceId of bulkRequest.approval_instance_ids) {
      try {
        const actionRequest: ApprovalActionRequest = {
          action: bulkRequest.action,
          comments: bulkRequest.comments,
          rejection_reason: bulkRequest.rejection_reason
        }

        const result = await this.processAction(instanceId, approverId, actionRequest)

        if (result.success) {
          successful.push(instanceId)
        } else {
          failed.push({ id: instanceId, error: result.message })
        }
      } catch (error) {
        failed.push({
          id: instanceId,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return {
      successful,
      failed,
      total_processed: bulkRequest.approval_instance_ids.length
    }
  }

  /**
   * Delegate approval to another user
   */
  static async delegateApproval(
    approvalInstanceId: string,
    delegatorId: string,
    delegatedToId: string,
    delegationReason: string,
    expiresAt?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Validate delegation
      const validation = await this.validateDelegation(
        approvalInstanceId,
        delegatorId,
        delegatedToId
      )

      if (!validation.valid) {
        return { success: false, message: validation.message }
      }

      // Create delegation action
      const actionRequest: ApprovalActionRequest = {
        action: 'delegate',
        delegated_to: delegatedToId,
        delegation_reason: delegationReason,
        delegation_expires_at: expiresAt
      }

      const result = await this.processAction(approvalInstanceId, delegatorId, actionRequest)
      return result
    } catch (error) {
      console.error('Error delegating approval:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Delegation failed'
      }
    }
  }

  /**
   * Get approval actions for an instance
   */
  static async getApprovalActions(approvalInstanceId: string): Promise<ApprovalAction[]> {
    try {
      const { data, error } = await supabase
        .from('approval_actions')
        .select(`
          *,
          approver:profiles!approval_actions_approver_id_fkey(id, full_name, email, role),
          delegated_to_profile:profiles!approval_actions_delegated_to_fkey(id, full_name, email, role)
        `)
        .eq('approval_instance_id', approvalInstanceId)
        .order('action_taken_at', { ascending: true })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching approval actions:', error)
      return []
    }
  }

  /**
   * Validate action request
   */
  private static async validateActionRequest(
    approvalInstanceId: string,
    approverId: string,
    actionRequest: ApprovalActionRequest
  ): Promise<{ valid: boolean; message: string }> {
    try {
      // Get approval instance
      const { data: instance, error: instanceError } = await supabase
        .from('approval_instances')
        .select(`
          *,
          workflow_template:workflow_templates(
            *,
            approval_steps(*)
          )
        `)
        .eq('id', approvalInstanceId)
        .single()

      if (instanceError) {
        return { valid: false, message: 'Approval instance not found' }
      }

      // Check if instance is still pending
      if (instance.status !== 'pending') {
        return { valid: false, message: `Cannot process action on ${instance.status} approval` }
      }

      // Get current step
      const currentStep = instance.workflow_template.approval_steps?.find(
        step => step.step_order === instance.current_step_order
      )

      if (!currentStep) {
        return { valid: false, message: 'Current approval step not found' }
      }

      // Check if user can approve this step
      const canApprove = await AuthorizationService.canApproveStep(
        approverId,
        currentStep.required_role,
        currentStep.allow_self_approval,
        instance.submitted_by
      )

      if (!canApprove) {
        return { valid: false, message: 'Insufficient authority to approve this step' }
      }

      // Check if user has already acted on this step
      const { data: existingAction, error: actionError } = await supabase
        .from('approval_actions')
        .select('id')
        .eq('approval_instance_id', approvalInstanceId)
        .eq('step_order', instance.current_step_order)
        .eq('approver_id', approverId)
        .limit(1)

      if (actionError) {
        return { valid: false, message: 'Error checking existing actions' }
      }

      if (existingAction && existingAction.length > 0) {
        return { valid: false, message: 'You have already acted on this approval step' }
      }

      // Validate action-specific requirements
      if (actionRequest.action === 'reject' && !actionRequest.rejection_reason) {
        return { valid: false, message: 'Rejection reason is required' }
      }

      if (actionRequest.action === 'delegate') {
        if (!actionRequest.delegated_to) {
          return { valid: false, message: 'Delegation target is required' }
        }
        if (!actionRequest.delegation_reason) {
          return { valid: false, message: 'Delegation reason is required' }
        }
      }

      // Check approval limits for approve action
      if (actionRequest.action === 'approve' && instance.document_amount) {
        const authority = await ApprovalEngine.checkApprovalAuthority(
          approverId,
          approvalInstanceId,
          instance.document_amount
        )

        if (!authority.can_approve) {
          return { 
            valid: false, 
            message: authority.reasons?.join(', ') || 'Insufficient approval authority'
          }
        }
      }

      return { valid: true, message: 'Action request is valid' }
    } catch (error) {
      console.error('Error validating action request:', error)
      return { valid: false, message: 'Validation failed' }
    }
  }

  /**
   * Validate delegation
   */
  private static async validateDelegation(
    approvalInstanceId: string,
    delegatorId: string,
    delegatedToId: string
  ): Promise<{ valid: boolean; message: string }> {
    try {
      // Check if delegated user exists and is in same organization
      const { data: delegatorProfile, error: delegatorError } = await supabase
        .from('profiles')
        .select('org_id, role')
        .eq('id', delegatorId)
        .single()

      if (delegatorError) {
        return { valid: false, message: 'Delegator profile not found' }
      }

      const { data: delegatedProfile, error: delegatedError } = await supabase
        .from('profiles')
        .select('org_id, role')
        .eq('id', delegatedToId)
        .single()

      if (delegatedError) {
        return { valid: false, message: 'Delegation target not found' }
      }

      if (delegatorProfile.org_id !== delegatedProfile.org_id) {
        return { valid: false, message: 'Cannot delegate to user in different organization' }
      }

      // Check if delegated user has appropriate role for the step
      const { data: instance, error: instanceError } = await supabase
        .from('approval_instances')
        .select(`
          *,
          workflow_template:workflow_templates(
            *,
            approval_steps(*)
          )
        `)
        .eq('id', approvalInstanceId)
        .single()

      if (instanceError) {
        return { valid: false, message: 'Approval instance not found' }
      }

      const currentStep = instance.workflow_template.approval_steps?.find(
        step => step.step_order === instance.current_step_order
      )

      if (!currentStep) {
        return { valid: false, message: 'Current step not found' }
      }

      if (!currentStep.required_role.includes(delegatedProfile.role)) {
        return { 
          valid: false, 
          message: `Delegation target role ${delegatedProfile.role} is not authorized for this step`
        }
      }

      return { valid: true, message: 'Delegation is valid' }
    } catch (error) {
      console.error('Error validating delegation:', error)
      return { valid: false, message: 'Delegation validation failed' }
    }
  }

  /**
   * Get user's pending actions
   */
  static async getUserPendingActions(userId: string, orgId: string): Promise<ApprovalAction[]> {
    try {
      const { data, error } = await supabase
        .from('approval_actions')
        .select(`
          *,
          approval_instance:approval_instances!inner(
            *,
            workflow_template:workflow_templates(*)
          )
        `)
        .eq('approver_id', userId)
        .eq('org_id', orgId)
        .eq('approval_instance.status', 'pending')
        .order('action_taken_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching user pending actions:', error)
      return []
    }
  }

  /**
   * Cancel pending delegations
   */
  static async cancelDelegation(
    approvalInstanceId: string,
    delegatorId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Find active delegation
      const { data: delegation, error: findError } = await supabase
        .from('approval_actions')
        .select('*')
        .eq('approval_instance_id', approvalInstanceId)
        .eq('approver_id', delegatorId)
        .eq('action', 'delegated')
        .order('action_taken_at', { ascending: false })
        .limit(1)
        .single()

      if (findError) {
        return { valid: false, message: 'No active delegation found' }
      }

      // Create cancellation action
      const { error: cancelError } = await supabase
        .from('approval_actions')
        .insert({
          approval_instance_id: approvalInstanceId,
          approver_id: delegatorId,
          action: 'cancelled',
          comments: 'Delegation cancelled',
          action_taken_at: new Date().toISOString()
        })

      if (cancelError) throw cancelError

      return { success: true, message: 'Delegation cancelled successfully' }
    } catch (error) {
      console.error('Error cancelling delegation:', error)
      return { success: false, message: 'Failed to cancel delegation' }
    }
  }
}
