# Unified Approval Workflow System

## Overview

The Unified Approval Workflow System is a comprehensive solution for managing approval processes across all document types (invoices, bills, payments, budgets) in the Kaya Finance application. It provides centralized workflow management, role-based authorization, and multi-channel notifications.

## Architecture

### Core Components

1. **Approval Engine Core** (`src/lib/approval-engine/core.ts`)
   - Orchestrates workflow execution
   - Handles document submission for approval
   - Manages workflow state transitions

2. **Workflow Template Service** (`src/lib/approval-engine/workflow-template-service.ts`)
   - Manages workflow templates
   - CRUD operations for templates and steps
   - Default template creation

3. **Approval Rules Engine** (`src/lib/approval-engine/rules-engine.ts`)
   - Evaluates documents against approval rules
   - Determines appropriate workflow templates
   - Supports complex conditional logic

4. **Authorization Service** (`src/lib/approval-engine/authorization-service.ts`)
   - Validates user approval authority
   - Enforces role-based limits
   - Checks daily/monthly approval limits

5. **Action Handler** (`src/lib/approval-engine/action-handler.ts`)
   - Processes approval actions (approve/reject/delegate)
   - Handles bulk operations
   - Manages delegation workflows

### Database Schema

The system uses the following main tables:

- `workflow_templates` - Stores reusable workflow configurations
- `approval_rules` - Defines conditions for workflow selection
- `approval_steps` - Individual steps within workflows
- `role_approval_limits` - Role-based approval authority limits
- `approval_instances` - Active approval processes
- `approval_actions` - Audit trail of all approval decisions

## Features

### 1. Centralized Approval Engine

- **Unified Processing**: Single engine handles all document types
- **Configurable Workflows**: Flexible workflow templates with multiple steps
- **Automatic Routing**: Rules-based workflow selection
- **State Management**: Robust state tracking and transitions

### 2. Role-Based Approval Limits

- **Hierarchical Authority**: Different limits for different roles
- **Amount Thresholds**: Per-transaction, daily, and monthly limits
- **Document Type Specific**: Different limits for invoices, bills, payments
- **Time-Based Limits**: Effective date ranges for limit changes

### 3. Comprehensive Notification System

- **Multi-Channel Delivery**: Email, in-app, SMS notifications
- **Event-Driven**: Automatic notifications for all workflow events
- **Customizable Templates**: Configurable notification content
- **Reminder System**: Automated reminders for pending approvals

### 4. Advanced Workflow Features

- **Parallel Processing**: Multiple approvers for single step
- **Sequential Steps**: Multi-level approval chains
- **Escalation**: Automatic escalation for overdue approvals
- **Delegation**: Temporary delegation of approval authority
- **Bulk Operations**: Approve/reject multiple items simultaneously

## Usage

### Submitting Documents for Approval

```typescript
import { useSubmitForApproval } from '@/hooks/queries/useApprovalWorkflow'

const submitForApprovalMutation = useSubmitForApproval()

// Submit a bill for approval
await submitForApprovalMutation.mutateAsync({
  documentType: 'bill',
  documentId: 'bill-123',
  documentAmount: 1000000,
  currencyCode: 'UGX',
  metadata: {
    vendor_id: 'vendor-456',
    bill_number: 'BILL-2024-001'
  }
})
```

### Processing Approval Actions

```typescript
import { useApprovalAction } from '@/hooks/queries/useApprovalWorkflow'

const approvalActionMutation = useApprovalAction()

// Approve a document
await approvalActionMutation.mutateAsync({
  approvalInstanceId: 'instance-123',
  actionRequest: {
    action: 'approve',
    comments: 'Approved - all documentation verified'
  }
})

// Reject a document
await approvalActionMutation.mutateAsync({
  approvalInstanceId: 'instance-123',
  actionRequest: {
    action: 'reject',
    rejection_reason: 'Missing required documentation'
  }
})
```

### Viewing Approval Dashboard

The approval dashboard (`/approvals`) provides:

- **Pending Approvals**: Items requiring user action
- **Approval Statistics**: Real-time metrics and KPIs
- **Bulk Operations**: Mass approve/reject functionality
- **Filtering**: Advanced filtering by status, type, amount, date
- **Search**: Full-text search across approval instances

### Configuring Workflows

Administrators can configure workflows through the system:

1. **Create Workflow Templates**: Define reusable approval processes
2. **Set Approval Rules**: Configure conditions for workflow selection
3. **Define Role Limits**: Set approval authority for different roles
4. **Configure Notifications**: Customize notification preferences

## API Reference

### Core Functions

#### ApprovalEngine.submitForApproval()
Submits a document for approval workflow processing.

**Parameters:**
- `documentType`: Type of document (invoice, bill, payment, budget)
- `documentId`: Unique identifier of the document
- `documentAmount`: Total amount requiring approval
- `currencyCode`: Currency code (default: UGX)
- `submittedBy`: User ID of submitter
- `orgId`: Organization ID
- `metadata`: Additional document metadata

#### ApprovalEngine.processApprovalAction()
Processes an approval action (approve/reject/delegate).

**Parameters:**
- `approvalInstanceId`: ID of the approval instance
- `approverId`: User ID of the approver
- `actionPayload`: Action details and comments

### React Hooks

#### useApprovalInstances()
Fetches approval instances with filtering and pagination.

#### usePendingApprovals()
Gets pending approvals for the current user.

#### useApprovalStats()
Retrieves approval statistics for dashboard display.

#### useSubmitForApproval()
Mutation hook for submitting documents for approval.

#### useApprovalAction()
Mutation hook for processing approval actions.

## Configuration

### Default Role Limits

The system comes with default approval limits:

- **Accountant**: 1M UGX per transaction, 5M daily, 20M monthly
- **Admin**: 5M UGX per transaction, 25M daily, 100M monthly  
- **Owner**: Unlimited approval authority

### Default Workflows

Standard workflows are created for each document type:

1. **Invoice Approval**: Accountant Review → Manager Approval
2. **Bill Approval**: Accountant Review → Manager Approval
3. **Payment Approval**: Accountant Review → Manager Approval → Final Authorization
4. **Budget Approval**: Manager Review → Executive Approval

## Security

### Row Level Security (RLS)

All approval workflow tables implement RLS policies:

- Users can only access data within their organization
- Approval actions are restricted to authorized users
- Audit trails are immutable and fully logged

### Authorization Checks

Multiple layers of authorization:

1. **Role-based access**: User must have appropriate role
2. **Amount limits**: Transaction must be within user's limits
3. **Step requirements**: User must be authorized for current step
4. **Self-approval rules**: Configurable self-approval restrictions

## Monitoring and Auditing

### Audit Trail

Complete audit trail includes:

- All approval actions with timestamps
- User information and IP addresses
- Comments and rejection reasons
- Delegation records and expiration dates

### Performance Monitoring

Key metrics tracked:

- Average approval time by document type
- Approval success/rejection rates
- Escalation frequency
- User approval activity

### Notifications

Comprehensive notification system:

- Real-time approval status updates
- Escalation alerts for overdue approvals
- Daily/weekly approval summaries
- System health notifications

## Troubleshooting

### Common Issues

1. **Workflow Not Triggering**: Check approval rules and template configuration
2. **Authorization Errors**: Verify user roles and approval limits
3. **Notification Failures**: Check notification preferences and delivery settings
4. **Performance Issues**: Review database indexes and query optimization

### Support

For technical support or feature requests, contact the development team or refer to the system documentation.

## Future Enhancements

Planned improvements include:

- Advanced analytics and reporting
- Integration with external approval systems
- Mobile app support for approvals
- AI-powered approval recommendations
- Advanced workflow automation
