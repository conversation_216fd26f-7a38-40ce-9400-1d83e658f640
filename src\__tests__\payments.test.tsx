import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { PaymentForm } from '@/components/payments/PaymentForm'
import { useCreatePayment, useUpdatePayment } from '@/hooks/queries'
import type { Customer, Vendor, Invoice, Bill } from '@/types/database'

// Mock the hooks
jest.mock('@/hooks/queries', () => ({
  useCreatePayment: jest.fn(),
  useUpdatePayment: jest.fn(),
  useCustomers: jest.fn(),
  useVendors: jest.fn(),
  useInvoices: jest.fn(),
  useBills: jest.fn(),
}))

jest.mock('@/hooks/useAuthHook', () => ({
  useAuth: () => ({
    profile: { org_id: 'test-org-id' }
  })
}))

jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}))

const mockCreatePayment = useCreatePayment as jest.MockedFunction<typeof useCreatePayment>
const mockUpdatePayment = useUpdatePayment as jest.MockedFunction<typeof useUpdatePayment>

const mockCustomers: Customer[] = [
  {
    id: 'customer-1',
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '+256777123456',
    tin_number: '1234567890',
    org_id: 'test-org-id',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    address: null,
    notes: null,
    payment_terms: 30
  }
]

const mockVendors: Vendor[] = [
  {
    id: 'vendor-1',
    name: 'Test Vendor',
    email: '<EMAIL>',
    phone: '+256777123456',
    tin_number: '1234567890',
    org_id: 'test-org-id',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    address: null,
    notes: null,
    payment_terms: 30
  }
]

const mockInvoices: Invoice[] = [
  {
    id: 'invoice-1',
    customer_id: 'customer-1',
    invoice_number: 'INV-001',
    date_issued: '2024-01-01',
    due_date: '2024-01-31',
    total_amount: 1000,
    tax_amount: 180,
    status: 'sent',
    org_id: 'test-org-id',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    notes: null
  }
]

describe('Payment Management', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    mockCreatePayment.mockReturnValue({
      mutate: jest.fn(),
      isPending: false,
      error: null,
    })

    mockUpdatePayment.mockReturnValue({
      mutate: jest.fn(),
      isPending: false,
      error: null,
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    )
  }

  describe('PaymentForm', () => {
    it('renders create payment form correctly', () => {
      renderWithProviders(
        <PaymentForm
          open={true}
          onOpenChange={jest.fn()}
          payment={null}
          customers={mockCustomers}
          vendors={mockVendors}
          invoices={mockInvoices}
          bills={[]}
        />
      )

      expect(screen.getByText('Create New Payment')).toBeInTheDocument()
      expect(screen.getByText('Payment Type')).toBeInTheDocument()
      expect(screen.getByText('Amount')).toBeInTheDocument()
      expect(screen.getByText('Payment Method')).toBeInTheDocument()
      expect(screen.getByText('Payment Date')).toBeInTheDocument()
    })

    it('validates required fields', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <PaymentForm
          open={true}
          onOpenChange={jest.fn()}
          payment={null}
          customers={mockCustomers}
          vendors={mockVendors}
          invoices={mockInvoices}
          bills={[]}
        />
      )

      const submitButton = screen.getByRole('button', { name: /create payment/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/amount is required/i)).toBeInTheDocument()
      })
    })

    it('validates payment amount against invoice amount', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <PaymentForm
          open={true}
          onOpenChange={jest.fn()}
          payment={null}
          customers={mockCustomers}
          vendors={mockVendors}
          invoices={mockInvoices}
          bills={[]}
        />
      )

      // Select payment type as customer payment
      const paymentTypeSelect = screen.getByRole('combobox', { name: /payment type/i })
      await user.click(paymentTypeSelect)
      await user.click(screen.getByText('Customer Payment'))

      // Select customer
      const customerSelect = screen.getByRole('combobox', { name: /customer/i })
      await user.click(customerSelect)
      await user.click(screen.getByText('Test Customer'))

      // Select invoice
      const invoiceSelect = screen.getByRole('combobox', { name: /invoice/i })
      await user.click(invoiceSelect)
      await user.click(screen.getByText('INV-001'))

      // Enter amount greater than invoice amount
      const amountInput = screen.getByLabelText(/amount/i)
      await user.type(amountInput, '1500')

      const submitButton = screen.getByRole('button', { name: /create payment/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/payment amount cannot exceed invoice amount/i)).toBeInTheDocument()
      })
    })

    it('creates payment with valid data', async () => {
      const user = userEvent.setup()
      const mockMutate = jest.fn()

      mockCreatePayment.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
        error: null,
      })

      renderWithProviders(
        <PaymentForm
          open={true}
          onOpenChange={jest.fn()}
          payment={null}
          customers={mockCustomers}
          vendors={mockVendors}
          invoices={mockInvoices}
          bills={[]}
        />
      )

      // Fill in payment details
      const paymentTypeSelect = screen.getByRole('combobox', { name: /payment type/i })
      await user.click(paymentTypeSelect)
      await user.click(screen.getByText('Customer Payment'))

      const customerSelect = screen.getByRole('combobox', { name: /customer/i })
      await user.click(customerSelect)
      await user.click(screen.getByText('Test Customer'))

      const invoiceSelect = screen.getByRole('combobox', { name: /invoice/i })
      await user.click(invoiceSelect)
      await user.click(screen.getByText('INV-001'))

      const amountInput = screen.getByLabelText(/amount/i)
      await user.type(amountInput, '1000')

      const paymentMethodSelect = screen.getByRole('combobox', { name: /payment method/i })
      await user.click(paymentMethodSelect)
      await user.click(screen.getByText('Bank Transfer'))

      const referenceInput = screen.getByLabelText(/reference/i)
      await user.type(referenceInput, 'TXN-001')

      const submitButton = screen.getByRole('button', { name: /create payment/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockMutate).toHaveBeenCalledWith(
          expect.objectContaining({
            payee_type: 'customer',
            payee_id: 'customer-1',
            invoice_id: 'invoice-1',
            amount: 1000,
            payment_method: 'bank_transfer',
            reference_number: 'TXN-001'
          })
        )
      })
    })

    it('handles vendor payments correctly', async () => {
      const user = userEvent.setup()
      const mockMutate = jest.fn()

      mockCreatePayment.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
        error: null,
      })

      renderWithProviders(
        <PaymentForm
          open={true}
          onOpenChange={jest.fn()}
          payment={null}
          customers={mockCustomers}
          vendors={mockVendors}
          invoices={mockInvoices}
          bills={[]}
        />
      )

      // Select vendor payment
      const paymentTypeSelect = screen.getByRole('combobox', { name: /payment type/i })
      await user.click(paymentTypeSelect)
      await user.click(screen.getByText('Vendor Payment'))

      const vendorSelect = screen.getByRole('combobox', { name: /vendor/i })
      await user.click(vendorSelect)
      await user.click(screen.getByText('Test Vendor'))

      const amountInput = screen.getByLabelText(/amount/i)
      await user.type(amountInput, '500')

      const paymentMethodSelect = screen.getByRole('combobox', { name: /payment method/i })
      await user.click(paymentMethodSelect)
      await user.click(screen.getByText('Mobile Money'))

      const submitButton = screen.getByRole('button', { name: /create payment/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockMutate).toHaveBeenCalledWith(
          expect.objectContaining({
            payee_type: 'vendor',
            payee_id: 'vendor-1',
            amount: 500,
            payment_method: 'mobile_money'
          })
        )
      })
    })
  })

  describe('Payment Validation', () => {
    it('validates payment amount is positive', () => {
      const amount = -100
      expect(amount > 0).toBe(false)
    })

    it('validates payment amount is not zero', () => {
      const amount = 0
      expect(amount > 0).toBe(false)
    })

    it('validates payment amount format', () => {
      const validAmount = 1000.50
      const invalidAmount = NaN
      
      expect(Number.isFinite(validAmount)).toBe(true)
      expect(Number.isFinite(invalidAmount)).toBe(false)
    })

    it('validates payment method selection', () => {
      const validMethods = ['cash', 'bank_transfer', 'mobile_money', 'cheque']
      const testMethod = 'bank_transfer'
      
      expect(validMethods.includes(testMethod)).toBe(true)
    })

    it('validates payment date format', () => {
      const validDate = '2024-01-01'
      const invalidDate = 'invalid-date'
      
      expect(new Date(validDate).toString()).not.toBe('Invalid Date')
      expect(new Date(invalidDate).toString()).toBe('Invalid Date')
    })
  })

  describe('Payment Status Management', () => {
    it('marks invoice as paid when fully paid', () => {
      const invoiceAmount = 1000
      const paymentAmount = 1000
      const shouldMarkAsPaid = paymentAmount >= invoiceAmount
      
      expect(shouldMarkAsPaid).toBe(true)
    })

    it('keeps invoice as partial when partially paid', () => {
      const invoiceAmount = 1000
      const paymentAmount = 500
      const shouldMarkAsPaid = paymentAmount >= invoiceAmount
      
      expect(shouldMarkAsPaid).toBe(false)
    })

    it('calculates remaining balance correctly', () => {
      const invoiceAmount = 1000
      const paymentAmount = 300
      const remainingBalance = invoiceAmount - paymentAmount
      
      expect(remainingBalance).toBe(700)
    })
  })
})
