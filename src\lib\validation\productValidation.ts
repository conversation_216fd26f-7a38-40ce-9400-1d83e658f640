import { z } from 'zod'
import type { ProductFormData } from '@/types/inventory'

// Enhanced product validation schema with comprehensive rules
export const productValidationSchema = z.object({
  sku: z.string()
    .min(1, 'SKU is required')
    .max(100, 'SKU must be 100 characters or less')
    .regex(/^[A-Za-z0-9\-_]+$/, 'SKU can only contain letters, numbers, hyphens, and underscores')
    .refine(val => val.trim() === val, 'SKU cannot have leading or trailing spaces'),
  
  name: z.string()
    .min(1, 'Product name is required')
    .max(255, 'Name must be 255 characters or less')
    .refine(val => val.trim().length > 0, 'Product name cannot be empty or just spaces'),
  
  description: z.string()
    .max(1000, 'Description must be 1000 characters or less')
    .optional(),
  
  category_id: z.string().optional(),
  
  unit_of_measure: z.string()
    .min(1, 'Unit of measure is required')
    .max(50, 'Unit of measure must be 50 characters or less'),
  
  cost_price: z.number()
    .min(0, 'Cost price must be positive')
    .max(999999.99, 'Cost price cannot exceed $999,999.99')
    .refine(val => Number.isFinite(val), 'Cost price must be a valid number'),
  
  selling_price: z.number()
    .min(0, 'Selling price must be positive')
    .max(999999.99, 'Selling price cannot exceed $999,999.99')
    .refine(val => Number.isFinite(val), 'Selling price must be a valid number'),
  
  track_inventory: z.boolean().default(true),
  
  reorder_level: z.number()
    .min(0, 'Reorder level must be positive')
    .max(999999, 'Reorder level cannot exceed 999,999')
    .refine(val => Number.isInteger(val) || val === Math.floor(val * 1000) / 1000, 'Reorder level must have at most 3 decimal places'),
  
  reorder_quantity: z.number()
    .min(0, 'Reorder quantity must be positive')
    .max(999999, 'Reorder quantity cannot exceed 999,999')
    .refine(val => Number.isInteger(val) || val === Math.floor(val * 1000) / 1000, 'Reorder quantity must have at most 3 decimal places'),
  
  barcode: z.string()
    .max(255, 'Barcode must be 255 characters or less')
    .optional()
    .refine(val => !val || /^[0-9A-Za-z\-]+$/.test(val), 'Barcode can only contain letters, numbers, and hyphens'),
  
  weight: z.number()
    .min(0, 'Weight must be positive')
    .max(999999, 'Weight cannot exceed 999,999 kg')
    .optional()
    .nullable()
    .refine(val => val === null || val === undefined || Number.isFinite(val), 'Weight must be a valid number'),
  
  dimensions: z.string()
    .max(100, 'Dimensions must be 100 characters or less')
    .optional(),
  
  is_active: z.boolean().default(true),
  is_sellable: z.boolean().default(true),
  is_purchasable: z.boolean().default(true),
}).refine((data) => {
  // Business rule: Selling price should not be less than cost price (warning, not error)
  // This is handled separately as a warning
  return true
}, {
  message: "Validation passed",
})

// Additional validation rules that require external data
export interface ValidationContext {
  existingProducts?: Array<{ id: string; sku: string; barcode?: string | null }>
  categories?: Array<{ id: string; name: string }>
  currentProductId?: string // For edit mode, exclude current product from uniqueness checks
}

export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string[]>
  warnings: Record<string, string[]>
}

export function validateProduct(data: ProductFormData, context: ValidationContext = {}): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: {},
    warnings: {}
  }

  // First, run Zod validation
  const zodResult = productValidationSchema.safeParse(data)
  if (!zodResult.success) {
    zodResult.error.errors.forEach(error => {
      const field = error.path[0] as string
      if (!result.errors[field]) {
        result.errors[field] = []
      }
      result.errors[field].push(error.message)
    })
    result.isValid = false
  }

  // SKU uniqueness validation
  if (data.sku && context.existingProducts) {
    const duplicateSku = context.existingProducts.find(p => 
      p.sku.toLowerCase() === data.sku.toLowerCase() && 
      p.id !== context.currentProductId
    )
    if (duplicateSku) {
      if (!result.errors.sku) result.errors.sku = []
      result.errors.sku.push('SKU already exists. Please choose a unique SKU.')
      result.isValid = false
    }
  }

  // Barcode uniqueness validation
  if (data.barcode && context.existingProducts) {
    const duplicateBarcode = context.existingProducts.find(p => 
      p.barcode && 
      p.barcode.toLowerCase() === data.barcode.toLowerCase() && 
      p.id !== context.currentProductId
    )
    if (duplicateBarcode) {
      if (!result.errors.barcode) result.errors.barcode = []
      result.errors.barcode.push('Barcode already exists. Please choose a unique barcode.')
      result.isValid = false
    }
  }

  // Category validation
  if (data.category_id && data.category_id !== 'none' && context.categories) {
    const categoryExists = context.categories.find(c => c.id === data.category_id)
    if (!categoryExists) {
      if (!result.errors.category_id) result.errors.category_id = []
      result.errors.category_id.push('Selected category does not exist.')
      result.isValid = false
    }
  }

  // Business rule validations (warnings)
  
  // Pricing warnings
  if (data.cost_price > 0 && data.selling_price > 0) {
    if (data.selling_price < data.cost_price) {
      if (!result.warnings.selling_price) result.warnings.selling_price = []
      result.warnings.selling_price.push('Selling price is below cost price. You will lose money on each sale.')
    } else {
      const margin = ((data.selling_price - data.cost_price) / data.selling_price) * 100
      if (margin < 10) {
        if (!result.warnings.selling_price) result.warnings.selling_price = []
        result.warnings.selling_price.push('Very low profit margin. Consider increasing the selling price.')
      } else if (margin < 20) {
        if (!result.warnings.selling_price) result.warnings.selling_price = []
        result.warnings.selling_price.push('Low profit margin. Consider reviewing your pricing strategy.')
      }
    }
  }

  // Inventory tracking warnings
  if (data.track_inventory) {
    if (data.reorder_level > 0 && data.reorder_quantity === 0) {
      if (!result.warnings.reorder_quantity) result.warnings.reorder_quantity = []
      result.warnings.reorder_quantity.push('Reorder level is set but reorder quantity is zero.')
    }
    
    if (data.reorder_level === 0 && data.reorder_quantity > 0) {
      if (!result.warnings.reorder_level) result.warnings.reorder_level = []
      result.warnings.reorder_level.push('Reorder quantity is set but reorder level is zero.')
    }
  }

  // SKU format recommendations
  if (data.sku) {
    if (data.sku.length < 3) {
      if (!result.warnings.sku) result.warnings.sku = []
      result.warnings.sku.push('SKU is very short. Consider using a more descriptive SKU.')
    }
    
    if (!/[A-Z]/.test(data.sku) && !/[a-z]/.test(data.sku)) {
      if (!result.warnings.sku) result.warnings.sku = []
      result.warnings.sku.push('SKU contains only numbers. Consider adding letters for better readability.')
    }
  }

  // Product status warnings
  if (!data.is_active && data.is_sellable) {
    if (!result.warnings.is_sellable) result.warnings.is_sellable = []
    result.warnings.is_sellable.push('Product is inactive but marked as sellable. Consider making it non-sellable.')
  }

  if (data.is_sellable && data.selling_price === 0) {
    if (!result.warnings.selling_price) result.warnings.selling_price = []
    result.warnings.selling_price.push('Product is sellable but has no selling price set.')
  }

  if (data.is_purchasable && data.cost_price === 0) {
    if (!result.warnings.cost_price) result.warnings.cost_price = []
    result.warnings.cost_price.push('Product is purchasable but has no cost price set.')
  }

  return result
}

// Utility function to format validation errors for display
export function formatValidationErrors(errors: Record<string, string[]>): string[] {
  const formatted: string[] = []
  Object.entries(errors).forEach(([field, fieldErrors]) => {
    fieldErrors.forEach(error => {
      formatted.push(`${field}: ${error}`)
    })
  })
  return formatted
}

// Utility function to check if a field has errors
export function hasFieldError(errors: Record<string, string[]>, field: string): boolean {
  return !!(errors[field] && errors[field].length > 0)
}

// Utility function to get field errors
export function getFieldErrors(errors: Record<string, string[]>, field: string): string[] {
  return errors[field] || []
}

// Utility function to check if a field has warnings
export function hasFieldWarning(warnings: Record<string, string[]>, field: string): boolean {
  return !!(warnings[field] && warnings[field].length > 0)
}

// Utility function to get field warnings
export function getFieldWarnings(warnings: Record<string, string[]>, field: string): string[] {
  return warnings[field] || []
}

// Real-time validation for specific fields
export function validateField(field: string, value: any, allData: Partial<ProductFormData>, context: ValidationContext = {}): ValidationResult {
  const fullData = { ...allData, [field]: value } as ProductFormData
  const result = validateProduct(fullData, context)
  
  // Filter to only include errors/warnings for the specific field
  return {
    isValid: !hasFieldError(result.errors, field),
    errors: { [field]: getFieldErrors(result.errors, field) },
    warnings: { [field]: getFieldWarnings(result.warnings, field) }
  }
}
