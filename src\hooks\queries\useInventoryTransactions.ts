import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import type { 
  InventoryTransaction, 
  InventoryTransactionWithDetails, 
  InventoryTransactionFormData,
  InventoryTransactionFilters 
} from '@/types/inventory'

/**
 * Hook to fetch all inventory transactions for an organization
 */
export function useInventoryTransactions(filters?: InventoryTransactionFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('inventory_transactions')
        .select(`
          *,
          product:products(*),
          location:inventory_locations(*)
        `)
        .eq('org_id', profile.org_id)
        .order('transaction_date', { ascending: false })

      // Apply filters
      if (filters?.product_id) {
        query = query.eq('product_id', filters.product_id)
      }
      if (filters?.location_id) {
        query = query.eq('location_id', filters.location_id)
      }
      if (filters?.transaction_type) {
        query = query.eq('transaction_type', filters.transaction_type)
      }
      if (filters?.reference_type) {
        query = query.eq('reference_type', filters.reference_type)
      }
      if (filters?.date_from) {
        query = query.gte('transaction_date', filters.date_from)
      }
      if (filters?.date_to) {
        query = query.lte('transaction_date', filters.date_to)
      }

      const { data, error } = await query

      if (error) throw error
      return data as InventoryTransactionWithDetails[]
    },
    enabled: !!profile?.org_id,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

/**
 * Hook to fetch inventory transactions for a specific product
 */
export function useInventoryTransactionsByProduct(productId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryTransactions.byProduct(profile?.org_id || '', productId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !productId) return []

      const { data, error } = await supabase
        .from('inventory_transactions')
        .select(`
          *,
          product:products(*),
          location:inventory_locations(*)
        `)
        .eq('org_id', profile.org_id)
        .eq('product_id', productId)
        .order('transaction_date', { ascending: false })
        .limit(100) // Limit to recent transactions

      if (error) throw error
      return data as InventoryTransactionWithDetails[]
    },
    enabled: !!profile?.org_id && !!productId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to fetch inventory transactions for a specific location
 */
export function useInventoryTransactionsByLocation(locationId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryTransactions.byLocation(profile?.org_id || '', locationId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !locationId) return []

      const { data, error } = await supabase
        .from('inventory_transactions')
        .select(`
          *,
          product:products(*),
          location:inventory_locations(*)
        `)
        .eq('org_id', profile.org_id)
        .eq('location_id', locationId)
        .order('transaction_date', { ascending: false })
        .limit(100) // Limit to recent transactions

      if (error) throw error
      return data as InventoryTransactionWithDetails[]
    },
    enabled: !!profile?.org_id && !!locationId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to fetch recent inventory transactions
 */
export function useRecentInventoryTransactions(limit: number = 20) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryTransactions.recent(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('inventory_transactions')
        .select(`
          *,
          product:products(id, name, sku),
          location:inventory_locations(id, name, code)
        `)
        .eq('org_id', profile.org_id)
        .order('transaction_date', { ascending: false })
        .limit(limit)

      if (error) throw error
      return data as InventoryTransactionWithDetails[]
    },
    enabled: !!profile?.org_id,
    staleTime: 30 * 1000, // 30 seconds
  })
}

/**
 * Hook to create a new inventory transaction
 */
export function useCreateInventoryTransaction() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (transactionData: InventoryTransactionFormData) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('inventory_transactions')
        .insert({
          ...transactionData,
          org_id: profile.org_id,
          created_by: profile.id,
          total_cost: transactionData.unit_cost 
            ? transactionData.quantity * transactionData.unit_cost 
            : null,
        })
        .select(`
          *,
          product:products(*),
          location:inventory_locations(*)
        `)
        .single()

      if (error) throw error
      return data as InventoryTransactionWithDetails
    },
    onSuccess: (data) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.byProduct(profile?.org_id || '', data.product_id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.byLocation(profile?.org_id || '', data.location_id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.recent(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.byProduct(profile?.org_id || '', data.product_id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.byLocation(profile?.org_id || '', data.location_id) })
    },
  })
}

/**
 * Hook to get inventory movement summary for a date range
 */
export function useInventoryMovementSummary(dateFrom?: string, dateTo?: string) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['inventory-movement-summary', profile?.org_id, dateFrom, dateTo],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_inventory_movement_summary', {
        org_id_param: profile.org_id,
        date_from_param: dateFrom || null,
        date_to_param: dateTo || null
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get inventory valuation
 */
export function useInventoryValuation() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['inventory-valuation', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return { total_value: 0, items: [] }

      const { data, error } = await supabase.rpc('get_inventory_valuation', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get stock movement report
 */
export function useStockMovementReport(productId?: string, locationId?: string, dateFrom?: string, dateTo?: string) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['stock-movement-report', profile?.org_id, productId, locationId, dateFrom, dateTo],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_stock_movement_report', {
        org_id_param: profile.org_id,
        product_id_param: productId || null,
        location_id_param: locationId || null,
        date_from_param: dateFrom || null,
        date_to_param: dateTo || null
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to get ABC analysis (product classification by value)
 */
export function useABCAnalysis() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['abc-analysis', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return { a_items: [], b_items: [], c_items: [] }

      const { data, error } = await supabase.rpc('get_abc_analysis', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 30 * 60 * 1000, // 30 minutes
  })
}
