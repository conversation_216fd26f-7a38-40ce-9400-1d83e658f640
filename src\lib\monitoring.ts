import * as Sentry from '@sentry/react'
import { BrowserTracing } from '@sentry/tracing'
import { config } from './config'
import { supabase } from '@/integrations/supabase/client'
import { logger, logError, logWarn, logInfo } from './logger'
import { errorHandler } from './errorHandler'

// Performance monitoring configuration
interface PerformanceMetrics {
  name: string
  value: number
  unit: string
  timestamp: number
  tags?: Record<string, string>
}

// Error tracking configuration
interface ErrorContext {
  user?: {
    id: string
    email: string
    organization: string
  }
  extra?: Record<string, unknown>
  tags?: Record<string, string>
  level?: 'error' | 'warning' | 'info' | 'debug'
}

// Health check configuration
// Define a type for metadata values
type MetadataValue = string | number | boolean | null | undefined | Date | MetadataValue[] | { [key: string]: MetadataValue }

interface HealthCheck {
  name: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  responseTime: number
  message?: string
  lastCheck: Date
  metadata?: Record<string, MetadataValue>
}

// Alert configuration
interface Alert {
  id: string
  type: 'error' | 'warning' | 'info' | 'critical'
  title: string
  message: string
  source: string
  timestamp: Date
  resolved: boolean
  resolvedAt?: Date
  metadata?: Record<string, MetadataValue>
}

class MonitoringService {
  private static instance: MonitoringService
  private initialized = false
  private performanceObserver?: PerformanceObserver
  private healthChecks: Map<string, HealthCheck> = new Map()
  private alerts: Alert[] = []
  private healthCheckInterval?: NodeJS.Timeout
  private maxAlerts = 1000

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService()
    }
    return MonitoringService.instance
  }

  // Initialize monitoring services
  initialize() {
    if (this.initialized) return

    // Initialize Sentry only in production or staging
    if (config.app.environment !== 'development') {
      this.initializeSentry()
    }

    // Initialize performance monitoring
    this.initializePerformanceMonitoring()

    // Initialize custom error handlers
    this.initializeErrorHandlers()

    this.initialized = true
    console.log('🔍 Monitoring services initialized')
  }

  // Initialize Sentry error tracking
  private initializeSentry() {
    const sentryDsn = import.meta.env.VITE_SENTRY_DSN
    
    if (!sentryDsn) {
      console.warn('Sentry DSN not configured')
      return
    }

    Sentry.init({
      dsn: sentryDsn,
      environment: config.app.environment,
      release: config.app.version,
      
      // Performance monitoring
      integrations: [
        new BrowserTracing({
          // Capture interactions like clicks, form submissions
          tracingOrigins: [
            'localhost',
            'kayafinance.net',
            'staging.kayafinance.net',
            /^\//
          ],
        }),
      ],

      // Performance monitoring sample rate
      tracesSampleRate: config.app.environment === 'production' ? 0.1 : 1.0,

      // Error filtering
      beforeSend(event, hint) {
        // Filter out development errors
        if (config.app.environment === 'development') {
          return null
        }

        // Filter out known non-critical errors
        const error = hint.originalException
        if (error instanceof Error) {
          // Skip network errors that are user-related
          if (error.message.includes('Network Error') || 
              error.message.includes('Failed to fetch')) {
            return null
          }
        }

        return event
      },

      // Additional configuration
      attachStacktrace: true,
      autoSessionTracking: true,
      sendDefaultPii: false, // Don't send personally identifiable information
    })

    console.log('🔍 Sentry error tracking initialized')
  }

  // Initialize performance monitoring
  private initializePerformanceMonitoring() {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported')
      return
    }

    // Monitor Core Web Vitals
    this.performanceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.trackPerformanceMetric({
          name: entry.name,
          value: entry.value || (entry as PerformanceEntry & { duration?: number }).duration || 0,
          unit: 'ms',
          timestamp: Date.now(),
          tags: {
            type: entry.entryType,
            environment: config.app.environment
          }
        })
      }
    })

    // Observe different types of performance entries
    try {
      this.performanceObserver.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint'] })
    } catch (error) {
      console.warn('Some performance metrics not supported:', error)
    }

    console.log('📊 Performance monitoring initialized')
  }

  // Initialize custom error handlers
  private initializeErrorHandlers() {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.captureError(event.error, {
        extra: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        },
        tags: {
          type: 'javascript_error'
        }
      })
    })

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError(event.reason, {
        extra: {
          promise: event.promise
        },
        tags: {
          type: 'unhandled_promise_rejection'
        }
      })
    })

    console.log('🛡️ Error handlers initialized')
  }

  // Capture and report errors
  captureError(error: Error | string, context?: ErrorContext) {
    const errorMessage = typeof error === 'string' ? error : error.message
    
    // Log to console in development
    if (config.app.environment === 'development') {
      console.error('Error captured:', errorMessage, context)
      return
    }

    // Set user context if available
    if (context?.user) {
      Sentry.setUser({
        id: context.user.id,
        email: context.user.email,
        organization: context.user.organization
      })
    }

    // Set additional context
    if (context?.extra) {
      Sentry.setExtra('context', context.extra)
    }

    if (context?.tags) {
      Object.entries(context.tags).forEach(([key, value]) => {
        Sentry.setTag(key, value)
      })
    }

    // Capture the error
    if (typeof error === 'string') {
      Sentry.captureMessage(error, context?.level || 'error')
    } else {
      Sentry.captureException(error)
    }
  }

  // Track performance metrics
  trackPerformanceMetric(metric: PerformanceMetrics) {
    // Log to console in development
    if (config.app.environment === 'development') {
      console.log('Performance metric:', metric)
      return
    }

    // Send to Sentry as a custom metric
    Sentry.addBreadcrumb({
      category: 'performance',
      message: `${metric.name}: ${metric.value}${metric.unit}`,
      level: 'info',
      data: metric
    })

    // You can also send to other analytics services here
    this.sendToAnalytics(metric)
  }

  // Track user actions for debugging
  trackUserAction(action: string, data?: Record<string, unknown>) {
    Sentry.addBreadcrumb({
      category: 'user_action',
      message: action,
      level: 'info',
      data
    })

    // Log in development
    if (config.app.environment === 'development') {
      console.log('User action:', action, data)
    }
  }

  // Track API calls
  trackApiCall(url: string, method: string, status: number, duration: number) {
    const isError = status >= 400

    Sentry.addBreadcrumb({
      category: 'http',
      message: `${method} ${url}`,
      level: isError ? 'error' : 'info',
      data: {
        url,
        method,
        status_code: status,
        duration
      }
    })

    // Track as performance metric
    this.trackPerformanceMetric({
      name: `api_call_${method.toLowerCase()}`,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      tags: {
        endpoint: url,
        method,
        status: status.toString(),
        success: (!isError).toString()
      }
    })
  }

  // Send metrics to analytics service
  private sendToAnalytics(metric: PerformanceMetrics) {
    // Implement analytics service integration here
    // Example: Google Analytics, Mixpanel, etc.
    
    if (config.app.environment === 'development') {
      console.log('Would send to analytics:', metric)
    }
  }

  // Set user context for error tracking
  setUserContext(user: { id: string; email: string; organization: string }) {
    Sentry.setUser({
      id: user.id,
      email: user.email,
      organization: user.organization
    })
  }

  // Clear user context (on logout)
  clearUserContext() {
    Sentry.setUser(null)
  }

  // Manually flush events (useful before page unload)
  async flush(timeout = 2000): Promise<boolean> {
    return Sentry.flush(timeout)
  }

  // Health check methods
  registerHealthCheck(
    name: string,
    checkFunction: () => Promise<Omit<HealthCheck, 'name' | 'lastCheck'>>
  ): void {
    this.healthChecks.set(name, {
      name,
      status: 'healthy',
      responseTime: 0,
      lastCheck: new Date(),
      checkFunction
    } as HealthCheck & { checkFunction: () => Promise<Partial<HealthCheck>> })

    logInfo('Health check registered', {
      component: 'MonitoringService',
      action: 'registerHealthCheck',
      metadata: { name }
    })
  }

  async runHealthCheck(name: string): Promise<HealthCheck | null> {
    const check = this.healthChecks.get(name)
    if (!check) return null

    try {
      const result = await (check as HealthCheck & { checkFunction: () => Promise<Partial<HealthCheck>> }).checkFunction()
      const updatedCheck: HealthCheck = {
        name,
        ...result,
        lastCheck: new Date()
      }

      this.healthChecks.set(name, updatedCheck)

      // Generate alerts for unhealthy services
      if (result.status === 'unhealthy') {
        this.createAlert({
          type: 'critical',
          title: `Health Check Failed: ${name}`,
          message: result.message || 'Health check failed',
          source: 'health_check',
          metadata: { healthCheck: name, ...result.metadata }
        })
      }

      return updatedCheck
    } catch (error) {
      const failedCheck: HealthCheck = {
        name,
        status: 'unhealthy',
        responseTime: 0,
        message: `Health check error: ${(error as Error).message}`,
        lastCheck: new Date()
      }

      this.healthChecks.set(name, failedCheck)
      return failedCheck
    }
  }

  createAlert(alertData: Omit<Alert, 'id' | 'timestamp' | 'resolved'>): string {
    const alert: Alert = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      resolved: false,
      ...alertData
    }

    this.alerts.unshift(alert)

    // Maintain maximum alerts
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(0, this.maxAlerts)
    }

    logInfo('Alert created', {
      component: 'MonitoringService',
      action: 'createAlert',
      metadata: {
        alertId: alert.id,
        type: alert.type,
        source: alert.source
      }
    })

    return alert.id
  }

  getHealthStatus(): {
    overall: 'healthy' | 'degraded' | 'unhealthy'
    checks: HealthCheck[]
    lastUpdate: Date
  } {
    const checks = Array.from(this.healthChecks.values())
    const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length
    const degradedCount = checks.filter(c => c.status === 'degraded').length

    let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
    if (unhealthyCount > 0) {
      overall = 'unhealthy'
    } else if (degradedCount > 0) {
      overall = 'degraded'
    }

    return {
      overall,
      checks,
      lastUpdate: new Date()
    }
  }

  getAlerts(filters?: {
    type?: Alert['type']
    resolved?: boolean
    source?: string
    limit?: number
  }): Alert[] {
    let filtered = [...this.alerts]

    if (filters) {
      if (filters.type) {
        filtered = filtered.filter(a => a.type === filters.type)
      }
      if (filters.resolved !== undefined) {
        filtered = filtered.filter(a => a.resolved === filters.resolved)
      }
      if (filters.source) {
        filtered = filtered.filter(a => a.source === filters.source)
      }
      if (filters.limit) {
        filtered = filtered.slice(0, filters.limit)
      }
    }

    return filtered
  }
}

// Export singleton instance
export const monitoring = MonitoringService.getInstance()

// React Error Boundary component
export const ErrorBoundary = Sentry.withErrorBoundary

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const trackPageLoad = (pageName: string) => {
    monitoring.trackPerformanceMetric({
      name: 'page_load',
      value: performance.now(),
      unit: 'ms',
      timestamp: Date.now(),
      tags: {
        page: pageName
      }
    })
  }

  const trackUserInteraction = (interaction: string, data?: Record<string, unknown>) => {
    monitoring.trackUserAction(interaction, data)
  }

  return {
    trackPageLoad,
    trackUserInteraction
  }
}

// API monitoring interceptor
export function createApiMonitoringInterceptor() {
  return {
    request: (config: { metadata?: { startTime: number } }) => {
      config.metadata = { startTime: Date.now() }
      return config
    },

    response: (response: { config: { metadata: { startTime: number }; url: string; method: string }; status: number }) => {
      const duration = Date.now() - response.config.metadata.startTime
      monitoring.trackApiCall(
        response.config.url,
        response.config.method.toUpperCase(),
        response.status,
        duration
      )
      return response
    },
    
    error: (error: {
      config?: {
        metadata?: { startTime: number };
        url?: string;
        method?: string;
      };
      response?: {
        status?: number;
        data?: unknown;
      };
    }) => {
      const duration = Date.now() - error.config?.metadata?.startTime || 0
      monitoring.trackApiCall(
        error.config?.url || 'unknown',
        error.config?.method?.toUpperCase() || 'unknown',
        error.response?.status || 0,
        duration
      )
      
      monitoring.captureError(error, {
        tags: {
          type: 'api_error',
          url: error.config?.url
        },
        extra: {
          response: error.response?.data,
          status: error.response?.status
        }
      })
      
      return Promise.reject(error)
    }
  }
}

/**
 * Notification-specific monitoring and alerting
 */

export interface NotificationMetric {
  name: string
  value: number
  timestamp: Date
  labels?: Record<string, string>
  unit?: string
}

export interface AlertRule {
  id: string
  name: string
  metric: string
  condition: 'gt' | 'lt' | 'eq' | 'gte' | 'lte'
  threshold: number
  duration: number // seconds
  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean
  channels: string[] // notification channels
}

export interface HealthCheck {
  name: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  message?: string
  timestamp: Date
  responseTime?: number
  details?: Record<string, MetadataValue>
}

/**
 * Notification Metrics Collector
 */
export class NotificationMetricsCollector {
  private metrics: Map<string, NotificationMetric[]> = new Map()
  private readonly maxMetricsPerType = 1000

  /**
   * Record a metric value
   */
  record(name: string, value: number, labels?: Record<string, string>, unit?: string): void {
    const metric: NotificationMetric = {
      name,
      value,
      timestamp: new Date(),
      labels,
      unit
    }

    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    const metrics = this.metrics.get(name)!
    metrics.push(metric)

    // Keep only recent metrics to prevent memory leaks
    if (metrics.length > this.maxMetricsPerType) {
      metrics.splice(0, metrics.length - this.maxMetricsPerType)
    }

    // Send to external monitoring service if configured
    this.sendToExternalService(metric)
  }

  /**
   * Get metrics for a specific name
   */
  getMetrics(name: string, since?: Date): NotificationMetric[] {
    const metrics = this.metrics.get(name) || []

    if (since) {
      return metrics.filter(m => m.timestamp >= since)
    }

    return [...metrics]
  }

  /**
   * Calculate metric statistics
   */
  getStats(name: string, since?: Date): {
    count: number
    sum: number
    avg: number
    min: number
    max: number
    latest: number | null
  } {
    const metrics = this.getMetrics(name, since)

    if (metrics.length === 0) {
      return {
        count: 0,
        sum: 0,
        avg: 0,
        min: 0,
        max: 0,
        latest: null
      }
    }

    const values = metrics.map(m => m.value)
    const sum = values.reduce((a, b) => a + b, 0)

    return {
      count: metrics.length,
      sum,
      avg: sum / metrics.length,
      min: Math.min(...values),
      max: Math.max(...values),
      latest: values[values.length - 1]
    }
  }

  /**
   * Send metric to external monitoring service
   */
  private async sendToExternalService(metric: NotificationMetric): Promise<void> {
    try {
      // DataDog integration
      if (process.env.NEXT_PUBLIC_DATADOG_API_KEY) {
        await this.sendToDataDog(metric)
      }

      // Custom webhook
      if (process.env.NEXT_PUBLIC_METRICS_WEBHOOK_URL) {
        await this.sendToWebhook(metric)
      }
    } catch (error) {
      console.error('Failed to send metric to external service:', error)
    }
  }

  /**
   * Send metric to DataDog
   */
  private async sendToDataDog(metric: NotificationMetric): Promise<void> {
    const payload = {
      series: [{
        metric: `kaya.notifications.${metric.name}`,
        points: [[Math.floor(metric.timestamp.getTime() / 1000), metric.value]],
        tags: metric.labels ? Object.entries(metric.labels).map(([k, v]) => `${k}:${v}`) : [],
        type: 'gauge'
      }]
    }

    await fetch('https://api.datadoghq.com/api/v1/series', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'DD-API-KEY': process.env.NEXT_PUBLIC_DATADOG_API_KEY!
      },
      body: JSON.stringify(payload)
    })
  }

  /**
   * Send metric to custom webhook
   */
  private async sendToWebhook(metric: NotificationMetric): Promise<void> {
    await fetch(process.env.NEXT_PUBLIC_METRICS_WEBHOOK_URL!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(metric)
    })
  }
}

/**
 * Notification Health Checker
 */
export class NotificationHealthChecker {
  private checks: Map<string, HealthCheck> = new Map()

  /**
   * Run a health check
   */
  async runCheck(name: string, checkFn: () => Promise<HealthCheck>): Promise<HealthCheck> {
    const startTime = Date.now()

    try {
      const result = await checkFn()
      result.responseTime = Date.now() - startTime
      this.checks.set(name, result)
      return result
    } catch (error) {
      const failedCheck: HealthCheck = {
        name,
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
        responseTime: Date.now() - startTime
      }

      this.checks.set(name, failedCheck)
      return failedCheck
    }
  }

  /**
   * Get all health checks
   */
  getAllChecks(): HealthCheck[] {
    return Array.from(this.checks.values())
  }

  /**
   * Get overall system health
   */
  getOverallHealth(): {
    status: 'healthy' | 'degraded' | 'unhealthy'
    checks: HealthCheck[]
    summary: {
      total: number
      healthy: number
      degraded: number
      unhealthy: number
    }
  } {
    const checks = this.getAllChecks()
    const summary = {
      total: checks.length,
      healthy: checks.filter(c => c.status === 'healthy').length,
      degraded: checks.filter(c => c.status === 'degraded').length,
      unhealthy: checks.filter(c => c.status === 'unhealthy').length
    }

    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'

    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy'
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded'
    }

    return {
      status: overallStatus,
      checks,
      summary
    }
  }
}

/**
 * Global notification monitoring instances
 */
export const notificationMetrics = new NotificationMetricsCollector()
export const notificationHealthChecker = new NotificationHealthChecker()

/**
 * Notification-specific metrics helpers
 */
export const NotificationMetrics = {
  /**
   * Record notification creation
   */
  recordCreated(type: string, orgId: string): void {
    notificationMetrics.record('notifications_created_total', 1, { type, org_id: orgId })
  },

  /**
   * Record notification delivery
   */
  recordDelivered(type: string, channel: string, success: boolean): void {
    notificationMetrics.record('notifications_delivered_total', 1, {
      type,
      channel,
      status: success ? 'success' : 'failed'
    })
  },

  /**
   * Record notification read
   */
  recordRead(type: string, timeToRead: number): void {
    notificationMetrics.record('notifications_read_total', 1, { type })
    notificationMetrics.record('notification_read_time_seconds', timeToRead, { type })
  },

  /**
   * Record real-time connection
   */
  recordConnection(connected: boolean): void {
    notificationMetrics.record('realtime_connections_active', connected ? 1 : -1)
  },

  /**
   * Record API response time
   */
  recordApiResponseTime(endpoint: string, duration: number): void {
    notificationMetrics.record('api_response_time_seconds', duration, { endpoint })
  },

  /**
   * Record database query time
   */
  recordDbQueryTime(operation: string, duration: number): void {
    notificationMetrics.record('db_query_time_seconds', duration, { operation })
  },

  /**
   * Record email delivery metrics
   */
  recordEmailDelivery(status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'failed'): void {
    notificationMetrics.record('email_delivery_total', 1, { status })
  },

  /**
   * Record push notification metrics
   */
  recordPushDelivery(status: 'sent' | 'delivered' | 'failed'): void {
    notificationMetrics.record('push_delivery_total', 1, { status })
  },

  /**
   * Record integration delivery metrics
   */
  recordIntegrationDelivery(type: string, status: 'sent' | 'failed'): void {
    notificationMetrics.record('integration_delivery_total', 1, { type, status })
  }
}

/**
 * Notification health checks
 */
export const NotificationHealthChecks = {
  /**
   * Check database connectivity
   */
  async database(): Promise<HealthCheck> {
    const startTime = Date.now()

    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('count')
        .limit(1)

      if (error) throw error

      return {
        name: 'database',
        status: 'healthy',
        message: 'Database connection successful',
        timestamp: new Date(),
        responseTime: Date.now() - startTime
      }
    } catch (error) {
      return {
        name: 'database',
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'Database connection failed',
        timestamp: new Date(),
        responseTime: Date.now() - startTime
      }
    }
  },

  /**
   * Check real-time service
   */
  async realtime(): Promise<HealthCheck> {
    const startTime = Date.now()

    try {
      // Test WebSocket connection by creating a temporary channel
      const channel = supabase.channel('health-check')

      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          channel.unsubscribe()
          resolve({
            name: 'realtime',
            status: 'unhealthy',
            message: 'Real-time connection timeout',
            timestamp: new Date(),
            responseTime: Date.now() - startTime
          })
        }, 5000)

        channel.subscribe((status) => {
          clearTimeout(timeout)
          channel.unsubscribe()

          if (status === 'SUBSCRIBED') {
            resolve({
              name: 'realtime',
              status: 'healthy',
              message: 'Real-time service operational',
              timestamp: new Date(),
              responseTime: Date.now() - startTime
            })
          } else {
            resolve({
              name: 'realtime',
              status: 'degraded',
              message: `Real-time status: ${status}`,
              timestamp: new Date(),
              responseTime: Date.now() - startTime
            })
          }
        })
      })
    } catch (error) {
      return {
        name: 'realtime',
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'Real-time service failed',
        timestamp: new Date(),
        responseTime: Date.now() - startTime
      }
    }
  },

  /**
   * Check notification creation performance
   */
  async notificationPerformance(): Promise<HealthCheck> {
    const startTime = Date.now()

    try {
      // Check recent notification creation rate
      const { data, error } = await supabase
        .from('notifications')
        .select('created_at')
        .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes
        .order('created_at', { ascending: false })

      if (error) throw error

      const count = data?.length || 0
      const rate = count / 5 // notifications per minute

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
      let message = `Notification creation rate: ${rate.toFixed(1)}/min`

      if (rate > 100) {
        status = 'degraded'
        message += ' (high load)'
      } else if (rate > 200) {
        status = 'unhealthy'
        message += ' (very high load)'
      }

      return {
        name: 'notification_performance',
        status,
        message,
        timestamp: new Date(),
        responseTime: Date.now() - startTime,
        details: { rate, count }
      }
    } catch (error) {
      return {
        name: 'notification_performance',
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'Performance check failed',
        timestamp: new Date(),
        responseTime: Date.now() - startTime
      }
    }
  }
}

/**
 * Initialize notification monitoring
 */
export function initializeNotificationMonitoring(): void {
  // Set up periodic health checks
  setInterval(async () => {
    await notificationHealthChecker.runCheck('database', NotificationHealthChecks.database)
    await notificationHealthChecker.runCheck('realtime', NotificationHealthChecks.realtime)
    await notificationHealthChecker.runCheck('performance', NotificationHealthChecks.notificationPerformance)
  }, 60000) // Every minute

  // Set up performance monitoring
  if (typeof window !== 'undefined') {
    // Monitor page load performance
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      NotificationMetrics.recordApiResponseTime('page_load', navigation.loadEventEnd - navigation.fetchStart)
    })

    // Monitor real-time connection status
    let connectionStartTime = Date.now()

    // This would be integrated with your real-time connection logic
    const monitorConnection = (connected: boolean) => {
      NotificationMetrics.recordConnection(connected)

      if (connected) {
        const connectionTime = Date.now() - connectionStartTime
        notificationMetrics.record('realtime_connection_time_ms', connectionTime)
      } else {
        connectionStartTime = Date.now()
      }
    }

    // Export for use in real-time hooks
    ;(window as Window & { monitorConnection?: () => void }).monitorConnection = monitorConnection
  }
}
