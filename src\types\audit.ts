import type { AuditLog } from './database'

export interface AuditLogWithProfile extends AuditLog {
  profiles?: {
    email: string
    full_name?: string
  }
}

export interface AuditLogFilters {
  search?: string
  entity_type?: string
  action?: string
  severity?: string
  category?: string
  user_id?: string
  date_from?: string
  date_to?: string
  ip_address?: string
}

export interface AuditLogStats {
  total_logs: number
  logs_today: number
  logs_this_week: number
  logs_this_month: number
  top_actions: Array<{ action: string; count: number }>
  top_entities: Array<{ entity_type: string; count: number }>
  top_users: Array<{ user_email: string; count: number }>
  severity_breakdown: Record<string, number>
  category_breakdown: Record<string, number>
}

export interface AuditLogAnalytics {
  activity_timeline: Array<{
    date: string
    count: number
    severity_breakdown: Record<string, number>
  }>
  user_activity: Array<{
    user_email: string
    total_actions: number
    last_activity: string
    risk_score: number
  }>
  entity_activity: Array<{
    entity_type: string
    total_modifications: number
    last_modified: string
  }>
  security_events: Array<{
    event_type: string
    count: number
    severity: string
    last_occurrence: string
  }>
}

export interface AuditLogExportOptions {
  format: 'csv' | 'json' | 'pdf'
  filters: AuditLogFilters
  include_metadata: boolean
  date_range: {
    start: string
    end: string
  }
}

export interface AuditLogRetentionPolicy {
  retention_days: number
  archive_after_days: number
  auto_purge_enabled: boolean
  compliance_requirements: string[]
}

export interface AuditRetentionStatus {
  total_logs: number
  logs_by_age: {
    current: number // < 30 days
    recent: number // 30-90 days
    old: number // 90-365 days
    very_old: number // > 365 days
  }
  logs_by_type: Record<string, {
    count: number
    oldest: string
    newest: string
    can_archive: number
    can_purge: number
  }>
}

export interface AuditLogAlert {
  id: string
  name: string
  description: string
  conditions: {
    entity_type?: string
    action?: string
    severity?: string
    user_pattern?: string
    frequency_threshold?: number
    time_window_minutes?: number
  }
  notification_channels: string[]
  is_active: boolean
  created_at: string
  last_triggered?: string
}

export interface AuditLogCompliance {
  standard: string // e.g., 'SOX', 'GDPR', 'PCI-DSS'
  requirements: string[]
  compliance_score: number
  last_assessment: string
  findings: Array<{
    requirement: string
    status: 'compliant' | 'non_compliant' | 'partial'
    details: string
  }>
}

// Audit log display configurations
export const AUDIT_ACTION_LABELS: Record<string, string> = {
  create: 'Created',
  update: 'Updated',
  delete: 'Deleted',
  view: 'Viewed',
  export: 'Exported',
  import: 'Imported',
  login: 'Logged In',
  logout: 'Logged Out',
  password_change: 'Changed Password',
  permission_change: 'Changed Permissions',
  approve: 'Approved',
  reject: 'Rejected',
  reconcile: 'Reconciled',
  send: 'Sent',
  cancel: 'Cancelled',
  backup: 'Backed Up',
  restore: 'Restored',
  archive: 'Archived',
  purge: 'Purged'
}

export const AUDIT_SEVERITY_COLORS: Record<string, string> = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800'
}

export const AUDIT_CATEGORY_LABELS: Record<string, string> = {
  authentication: 'Authentication',
  authorization: 'Authorization',
  data_modification: 'Data Modification',
  data_access: 'Data Access',
  financial_transaction: 'Financial Transaction',
  system_configuration: 'System Configuration',
  user_management: 'User Management',
  compliance: 'Compliance',
  security: 'Security',
  backup_restore: 'Backup & Restore'
}

export const ENTITY_TYPE_LABELS: Record<string, string> = {
  customers: 'Customers',
  vendors: 'Vendors',
  invoices: 'Invoices',
  bills: 'Bills',
  payments: 'Payments',
  journal_entries: 'Journal Entries',
  accounts: 'Chart of Accounts',
  budgets: 'Budgets',
  tax_rates: 'Tax Rates',
  organizations: 'Organizations',
  profiles: 'User Profiles',
  authentication: 'Authentication',
  settings: 'Settings'
}

// Risk scoring for audit events
export const RISK_SCORES: Record<string, number> = {
  // Actions
  delete: 8,
  purge: 10,
  permission_change: 9,
  password_change: 6,
  approve: 7,
  reject: 7,
  export: 5,
  update: 3,
  create: 2,
  view: 1,
  
  // Severity multipliers
  critical: 3,
  high: 2,
  medium: 1.5,
  low: 1
}

export function calculateRiskScore(action: string, severity: string, entityType: string): number {
  const actionScore = RISK_SCORES[action] || 1
  const severityMultiplier = RISK_SCORES[severity] || 1
  
  // Financial entities get higher risk scores
  const financialEntities = ['payments', 'invoices', 'bills', 'journal_entries', 'budgets']
  const entityMultiplier = financialEntities.includes(entityType) ? 1.5 : 1
  
  return Math.round(actionScore * severityMultiplier * entityMultiplier)
}

export function formatAuditLogDescription(log: AuditLogWithProfile): string {
  const action = AUDIT_ACTION_LABELS[log.action] || log.action
  const entity = ENTITY_TYPE_LABELS[log.entity_type] || log.entity_type
  const user = log.profiles?.email || 'Unknown User'
  
  return `${user} ${action.toLowerCase()} ${entity.toLowerCase()}`
}

export function getAuditLogIcon(action: string): string {
  const iconMap: Record<string, string> = {
    create: '➕',
    update: '✏️',
    delete: '🗑️',
    view: '👁️',
    export: '📤',
    import: '📥',
    login: '🔐',
    logout: '🚪',
    approve: '✅',
    reject: '❌',
    reconcile: '🔄',
    send: '📧',
    cancel: '🚫',
    backup: '💾',
    restore: '♻️'
  }
  
  return iconMap[action] || '📝'
}
