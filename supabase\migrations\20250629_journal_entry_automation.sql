-- =====================================================
-- JOURNAL ENTRY AUTOMATION SYSTEM
-- =====================================================
-- This migration implements comprehensive automated journal entry creation
-- Date: 2025-06-29
-- Purpose: Add real-time journal entry automation for invoices, payments, and reconciliation

-- =====================================================
-- STEP 1: CREATE SUPPORTING TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating journal entry automation tables...';
END $$;

-- Account mappings table for organization-specific account configurations
CREATE TABLE IF NOT EXISTS account_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    mapping_type VARCHAR(50) NOT NULL, -- 'accounts_receivable', 'accounts_payable', 'revenue', 'expense', 'vat_payable', 'vat_receivable', 'cash'
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(org_id, mapping_type, is_default),
    CONSTRAINT valid_mapping_type CHECK (mapping_type IN (
        'accounts_receivable', 'accounts_payable', 'revenue', 'expense',
        'vat_payable', 'vat_receivable', 'cash', 'bank'
    ))
);

-- Journal entry errors table for error logging and tracking
CREATE TABLE IF NOT EXISTS journal_entry_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    source_type VARCHAR(50) NOT NULL, -- 'invoice', 'payment', 'payment_application', 'reconciliation'
    source_id UUID NOT NULL,
    error_type VARCHAR(100) NOT NULL, -- 'missing_account_mapping', 'unbalanced_entry', 'duplicate_entry', 'database_error'
    error_message TEXT NOT NULL,
    error_details JSONB DEFAULT '{}',
    retry_count INTEGER DEFAULT 0,
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automation settings table for feature flags and configuration
CREATE TABLE IF NOT EXISTS automation_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL,
    setting_value JSONB NOT NULL DEFAULT '{}',
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(org_id, setting_key)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_account_mappings_org_type ON account_mappings(org_id, mapping_type);
CREATE INDEX IF NOT EXISTS idx_journal_entry_errors_org_source ON journal_entry_errors(org_id, source_type, source_id);
CREATE INDEX IF NOT EXISTS idx_journal_entry_errors_unresolved ON journal_entry_errors(org_id, resolved) WHERE resolved = false;
CREATE INDEX IF NOT EXISTS idx_automation_settings_org_key ON automation_settings(org_id, setting_key);

-- =====================================================
-- STEP 2: CREATE VALIDATION FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating validation functions...';
END $$;

-- Function to validate required account mappings exist
CREATE OR REPLACE FUNCTION validate_account_mappings(
    org_id_param UUID,
    required_mappings TEXT[]
)
RETURNS TABLE (
    mapping_type TEXT,
    account_id UUID,
    is_valid BOOLEAN,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    mapping_type_item TEXT;
    account_record RECORD;
BEGIN
    -- Check each required mapping type
    FOREACH mapping_type_item IN ARRAY required_mappings
    LOOP
        -- Get the default account mapping for this type
        SELECT am.account_id, a.name, a.code
        INTO account_record
        FROM account_mappings am
        JOIN accounts a ON am.account_id = a.id
        WHERE am.org_id = org_id_param
        AND am.mapping_type = mapping_type_item
        AND am.is_default = true;

        IF account_record.account_id IS NOT NULL THEN
            RETURN QUERY SELECT
                mapping_type_item,
                account_record.account_id,
                true,
                NULL::TEXT;
        ELSE
            RETURN QUERY SELECT
                mapping_type_item,
                NULL::UUID,
                false,
                ('Missing account mapping for: ' || mapping_type_item)::TEXT;
        END IF;
    END LOOP;
END;
$$;

-- Function to validate journal entry balance
CREATE OR REPLACE FUNCTION validate_journal_entry_balance(
    journal_entry_id_param UUID
)
RETURNS TABLE (
    is_balanced BOOLEAN,
    total_debits DECIMAL(15,2),
    total_credits DECIMAL(15,2),
    difference DECIMAL(15,2)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    debits_sum DECIMAL(15,2) := 0;
    credits_sum DECIMAL(15,2) := 0;
    diff DECIMAL(15,2);
BEGIN
    -- Calculate total debits and credits
    SELECT
        COALESCE(SUM(debit), 0),
        COALESCE(SUM(credit), 0)
    INTO debits_sum, credits_sum
    FROM transaction_lines
    WHERE journal_entry_id = journal_entry_id_param;

    diff := debits_sum - credits_sum;

    RETURN QUERY SELECT
        (ABS(diff) < 0.01), -- Allow for minor rounding differences
        debits_sum,
        credits_sum,
        diff;
END;
$$;

-- Function to handle journal entry errors
CREATE OR REPLACE FUNCTION handle_journal_entry_error(
    org_id_param UUID,
    source_type_param VARCHAR(50),
    source_id_param UUID,
    error_type_param VARCHAR(100),
    error_message_param TEXT,
    error_details_param JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    error_id UUID;
BEGIN
    -- Insert error record
    INSERT INTO journal_entry_errors (
        org_id, source_type, source_id, error_type,
        error_message, error_details
    )
    VALUES (
        org_id_param, source_type_param, source_id_param,
        error_type_param, error_message_param, error_details_param
    )
    RETURNING id INTO error_id;

    -- Send notification to accounting staff (integrate with existing notification system)
    PERFORM create_notification_from_template(
        'journal_entry_error',
        org_id_param,
        NULL, -- Organization-wide notification
        jsonb_build_object(
            'source_type', source_type_param,
            'source_id', source_id_param,
            'error_type', error_type_param,
            'error_message', error_message_param
        ),
        source_type_param,
        source_id_param
    );

    RETURN error_id;
END;
$$;

-- =====================================================
-- STEP 3: CREATE INVOICE JOURNAL ENTRY AUTOMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 Creating invoice journal entry automation...';
END $$;

-- Function to create journal entry for invoice
CREATE OR REPLACE FUNCTION create_invoice_journal_entry()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_journal_id UUID;
    ar_account_id UUID;
    revenue_account_id UUID;
    vat_account_id UUID;
    validation_result RECORD;
    required_mappings TEXT[] := ARRAY['accounts_receivable', 'revenue', 'vat_payable'];
    subtotal DECIMAL(15,2);
    automation_enabled BOOLEAN := true;
BEGIN
    -- Only process when invoice status changes to 'sent' or when new invoice is created with 'sent' status
    IF (TG_OP = 'INSERT' AND NEW.status = 'sent') OR
       (TG_OP = 'UPDATE' AND NEW.status = 'sent' AND (OLD.status IS NULL OR OLD.status != 'sent')) THEN

        -- Check if automation is enabled for this organization
        SELECT setting_value->>'enabled' = 'true'
        INTO automation_enabled
        FROM automation_settings
        WHERE org_id = NEW.org_id AND setting_key = 'invoice_journal_automation';

        -- Default to enabled if no setting exists
        IF automation_enabled IS NULL THEN
            automation_enabled := true;
        END IF;

        IF NOT automation_enabled THEN
            RETURN NEW;
        END IF;

        -- Check if journal entry already exists for this invoice
        IF EXISTS (
            SELECT 1 FROM journal_entries
            WHERE source_id = NEW.id AND source_type = 'invoice'
        ) THEN
            RETURN NEW;
        END IF;

        BEGIN
            -- Validate required account mappings
            FOR validation_result IN
                SELECT * FROM validate_account_mappings(NEW.org_id, required_mappings)
            LOOP
                IF NOT validation_result.is_valid THEN
                    PERFORM handle_journal_entry_error(
                        NEW.org_id,
                        'invoice',
                        NEW.id,
                        'missing_account_mapping',
                        validation_result.error_message,
                        jsonb_build_object('mapping_type', validation_result.mapping_type)
                    );
                    RETURN NEW;
                END IF;

                -- Store account IDs based on mapping type
                CASE validation_result.mapping_type
                    WHEN 'accounts_receivable' THEN ar_account_id := validation_result.account_id;
                    WHEN 'revenue' THEN revenue_account_id := validation_result.account_id;
                    WHEN 'vat_payable' THEN vat_account_id := validation_result.account_id;
                END CASE;
            END LOOP;

            -- Calculate subtotal (total - tax)
            subtotal := NEW.total_amount - NEW.tax_amount;

            -- Create journal entry
            INSERT INTO journal_entries (
                org_id, date, description, reference,
                source_id, source_type, created_by, is_posted
            )
            VALUES (
                NEW.org_id,
                NEW.date_issued,
                'Invoice: ' || NEW.invoice_number,
                NEW.invoice_number,
                NEW.id,
                'invoice',
                NEW.created_by,
                false
            )
            RETURNING id INTO new_journal_id;

            -- Create transaction lines for invoice (DR AR, CR Revenue, CR VAT)

            -- Debit: Accounts Receivable (total amount)
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                NEW.org_id, new_journal_id, ar_account_id,
                NEW.total_amount, 0, 'Accounts Receivable - ' || NEW.invoice_number
            );

            -- Credit: Revenue (subtotal)
            IF subtotal > 0 THEN
                INSERT INTO transaction_lines (
                    org_id, journal_entry_id, account_id,
                    debit, credit, description
                )
                VALUES (
                    NEW.org_id, new_journal_id, revenue_account_id,
                    0, subtotal, 'Sales Revenue - ' || NEW.invoice_number
                );
            END IF;

            -- Credit: VAT Payable (tax amount)
            IF NEW.tax_amount > 0 THEN
                INSERT INTO transaction_lines (
                    org_id, journal_entry_id, account_id,
                    debit, credit, description
                )
                VALUES (
                    NEW.org_id, new_journal_id, vat_account_id,
                    0, NEW.tax_amount, 'VAT Payable - ' || NEW.invoice_number
                );
            END IF;

            -- Validate the journal entry is balanced
            DECLARE
                balance_check RECORD;
            BEGIN
                SELECT * INTO balance_check
                FROM validate_journal_entry_balance(new_journal_id);

                IF NOT balance_check.is_balanced THEN
                    RAISE EXCEPTION 'Journal entry is not balanced. Debits: %, Credits: %, Difference: %',
                        balance_check.total_debits, balance_check.total_credits, balance_check.difference;
                END IF;
            END;

        EXCEPTION WHEN OTHERS THEN
            -- Log the error
            PERFORM handle_journal_entry_error(
                NEW.org_id,
                'invoice',
                NEW.id,
                'database_error',
                SQLERRM,
                jsonb_build_object(
                    'invoice_number', NEW.invoice_number,
                    'total_amount', NEW.total_amount,
                    'tax_amount', NEW.tax_amount
                )
            );
        END;
    END IF;

    RETURN NEW;
END;
$$;

-- Create the trigger for invoice journal entries
DROP TRIGGER IF EXISTS invoice_journal_entry_trigger ON invoices;
CREATE TRIGGER invoice_journal_entry_trigger
    AFTER INSERT OR UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION create_invoice_journal_entry();

-- =====================================================
-- STEP 4: ACCOUNT MAPPING SETUP AND VALIDATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating account mapping setup functions...';
END $$;

-- Function to setup default account mappings for an organization
CREATE OR REPLACE FUNCTION setup_default_account_mappings(org_id_param UUID)
RETURNS TABLE (
    mapping_type TEXT,
    account_id UUID,
    account_name TEXT,
    account_code TEXT,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    account_record RECORD;
    mapping_types TEXT[] := ARRAY[
        'accounts_receivable', 'accounts_payable', 'revenue', 'expense',
        'vat_payable', 'vat_receivable', 'cash', 'bank'
    ];
    mapping_type_item TEXT;
BEGIN
    -- Loop through each mapping type and try to find suitable accounts
    FOREACH mapping_type_item IN ARRAY mapping_types
    LOOP
        -- Skip if mapping already exists
        IF EXISTS (
            SELECT 1 FROM account_mappings
            WHERE org_id = org_id_param AND mapping_type = mapping_type_item
        ) THEN
            -- Return existing mapping
            SELECT am.mapping_type, am.account_id, a.name, a.code, 'existing'
            INTO account_record
            FROM account_mappings am
            JOIN accounts a ON am.account_id = a.id
            WHERE am.org_id = org_id_param
            AND am.mapping_type = mapping_type_item
            AND am.is_default = true;

            RETURN QUERY SELECT
                account_record.mapping_type,
                account_record.account_id,
                account_record.name,
                account_record.code,
                account_record.status;
            CONTINUE;
        END IF;

        -- Try to find suitable account based on account type and name patterns
        CASE mapping_type_item
            WHEN 'accounts_receivable' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND (a.account_type = 'asset' OR a.account_type = 'current_asset')
                AND (LOWER(a.name) LIKE '%receivable%' OR LOWER(a.name) LIKE '%debtors%')
                ORDER BY a.code
                LIMIT 1;

            WHEN 'accounts_payable' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND (a.account_type = 'liability' OR a.account_type = 'current_liability')
                AND (LOWER(a.name) LIKE '%payable%' OR LOWER(a.name) LIKE '%creditors%')
                ORDER BY a.code
                LIMIT 1;

            WHEN 'revenue' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND a.account_type = 'revenue'
                AND (LOWER(a.name) LIKE '%sales%' OR LOWER(a.name) LIKE '%revenue%' OR LOWER(a.name) LIKE '%income%')
                ORDER BY a.code
                LIMIT 1;

            WHEN 'expense' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND a.account_type = 'expense'
                ORDER BY a.code
                LIMIT 1;

            WHEN 'vat_payable' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND (a.account_type = 'liability' OR a.account_type = 'current_liability')
                AND (LOWER(a.name) LIKE '%vat%' OR LOWER(a.name) LIKE '%tax%')
                AND LOWER(a.name) LIKE '%payable%'
                ORDER BY a.code
                LIMIT 1;

            WHEN 'vat_receivable' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND (a.account_type = 'asset' OR a.account_type = 'current_asset')
                AND (LOWER(a.name) LIKE '%vat%' OR LOWER(a.name) LIKE '%tax%')
                AND (LOWER(a.name) LIKE '%receivable%' OR LOWER(a.name) LIKE '%input%')
                ORDER BY a.code
                LIMIT 1;

            WHEN 'cash' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND (a.account_type = 'asset' OR a.account_type = 'current_asset')
                AND (LOWER(a.name) LIKE '%cash%' OR LOWER(a.name) LIKE '%petty%')
                ORDER BY a.code
                LIMIT 1;

            WHEN 'bank' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND (a.account_type = 'asset' OR a.account_type = 'current_asset')
                AND (LOWER(a.name) LIKE '%bank%' OR LOWER(a.name) LIKE '%checking%' OR LOWER(a.name) LIKE '%savings%')
                ORDER BY a.code
                LIMIT 1;
        END CASE;

        -- If account found, create the mapping
        IF account_record.id IS NOT NULL THEN
            INSERT INTO account_mappings (
                org_id, mapping_type, account_id, is_default
            )
            VALUES (
                org_id_param, mapping_type_item, account_record.id, true
            );

            RETURN QUERY SELECT
                mapping_type_item,
                account_record.id,
                account_record.name,
                account_record.code,
                'created'::TEXT;
        ELSE
            RETURN QUERY SELECT
                mapping_type_item,
                NULL::UUID,
                NULL::TEXT,
                NULL::TEXT,
                'not_found'::TEXT;
        END IF;
    END LOOP;
END;
$$;

-- Function to get account mapping for a specific type
CREATE OR REPLACE FUNCTION get_account_mapping(
    org_id_param UUID,
    mapping_type_param TEXT
)
RETURNS TABLE (
    account_id UUID,
    account_name TEXT,
    account_code TEXT,
    account_type TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id,
        a.name,
        a.code,
        a.account_type
    FROM account_mappings am
    JOIN accounts a ON am.account_id = a.id
    WHERE am.org_id = org_id_param
    AND am.mapping_type = mapping_type_param
    AND am.is_default = true;
END;
$$;

-- Function to update account mapping
CREATE OR REPLACE FUNCTION update_account_mapping(
    org_id_param UUID,
    mapping_type_param TEXT,
    new_account_id_param UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    account_exists BOOLEAN := false;
BEGIN
    -- Verify the account exists and belongs to the organization
    SELECT EXISTS (
        SELECT 1 FROM accounts
        WHERE id = new_account_id_param AND org_id = org_id_param
    ) INTO account_exists;

    IF NOT account_exists THEN
        RAISE EXCEPTION 'Account does not exist or does not belong to organization';
    END IF;

    -- Update or insert the mapping
    INSERT INTO account_mappings (org_id, mapping_type, account_id, is_default)
    VALUES (org_id_param, mapping_type_param, new_account_id_param, true)
    ON CONFLICT (org_id, mapping_type, is_default)
    DO UPDATE SET
        account_id = new_account_id_param,
        updated_at = NOW();

    RETURN true;
END;
$$;

-- =====================================================
-- STEP 5: PAYMENT APPLICATION JOURNAL ENTRY AUTOMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '💰 Creating payment application journal entry automation...';
END $$;

-- Function to create journal entry for payment application
CREATE OR REPLACE FUNCTION create_payment_application_journal_entry()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_journal_id UUID;
    cash_account_id UUID;
    ar_account_id UUID;
    ap_account_id UUID;
    payment_record RECORD;
    document_record RECORD;
    validation_result RECORD;
    required_mappings TEXT[];
    automation_enabled BOOLEAN := true;
BEGIN
    -- Check if automation is enabled
    SELECT setting_value->>'enabled' = 'true'
    INTO automation_enabled
    FROM automation_settings
    WHERE org_id = (SELECT org_id FROM payments WHERE id = NEW.payment_id)
    AND setting_key = 'payment_journal_automation';

    IF automation_enabled IS NULL THEN
        automation_enabled := true;
    END IF;

    IF NOT automation_enabled THEN
        RETURN NEW;
    END IF;

    -- Get payment details
    SELECT p.*, ba.name as bank_account_name
    INTO payment_record
    FROM payments p
    LEFT JOIN bank_accounts ba ON p.bank_account_id = ba.id
    WHERE p.id = NEW.payment_id;

    -- Check if journal entry already exists for this payment application
    IF EXISTS (
        SELECT 1 FROM journal_entries
        WHERE source_id = NEW.id AND source_type = 'payment_application'
    ) THEN
        RETURN NEW;
    END IF;

    BEGIN
        -- Determine required mappings based on payment type
        IF payment_record.payee_type = 'customer' THEN
            -- Customer payment: need cash and accounts receivable
            required_mappings := ARRAY['cash', 'accounts_receivable'];
        ELSE
            -- Vendor payment: need accounts payable and cash
            required_mappings := ARRAY['accounts_payable', 'cash'];
        END IF;

        -- Validate required account mappings
        FOR validation_result IN
            SELECT * FROM validate_account_mappings(payment_record.org_id, required_mappings)
        LOOP
            IF NOT validation_result.is_valid THEN
                PERFORM handle_journal_entry_error(
                    payment_record.org_id,
                    'payment_application',
                    NEW.id,
                    'missing_account_mapping',
                    validation_result.error_message,
                    jsonb_build_object(
                        'mapping_type', validation_result.mapping_type,
                        'payment_id', NEW.payment_id,
                        'payee_type', payment_record.payee_type
                    )
                );
                RETURN NEW;
            END IF;

            -- Store account IDs based on mapping type
            CASE validation_result.mapping_type
                WHEN 'cash' THEN cash_account_id := validation_result.account_id;
                WHEN 'accounts_receivable' THEN ar_account_id := validation_result.account_id;
                WHEN 'accounts_payable' THEN ap_account_id := validation_result.account_id;
            END CASE;
        END LOOP;

        -- Get document details for reference
        IF NEW.applied_to_type = 'invoice' THEN
            SELECT invoice_number as document_number, 'Invoice' as document_type
            INTO document_record
            FROM invoices WHERE id = NEW.applied_to_id;
        ELSIF NEW.applied_to_type = 'bill' THEN
            SELECT bill_number as document_number, 'Bill' as document_type
            INTO document_record
            FROM bills WHERE id = NEW.applied_to_id;
        END IF;

        -- Create journal entry
        INSERT INTO journal_entries (
            org_id, date, description, reference,
            source_id, source_type, created_by, is_posted
        )
        VALUES (
            payment_record.org_id,
            payment_record.payment_date,
            'Payment Application: ' || COALESCE(document_record.document_type, 'Document') || ' ' || COALESCE(document_record.document_number, ''),
            'PAY-' || payment_record.id::text,
            NEW.id,
            'payment_application',
            payment_record.created_by,
            false
        )
        RETURNING id INTO new_journal_id;

        -- Create transaction lines based on payment type
        IF payment_record.payee_type = 'customer' THEN
            -- Customer payment: DR Cash, CR Accounts Receivable

            -- Debit: Cash/Bank Account
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                payment_record.org_id, new_journal_id, cash_account_id,
                NEW.amount_applied, 0,
                'Cash received - ' || COALESCE(document_record.document_type, 'Payment') || ' ' || COALESCE(document_record.document_number, '')
            );

            -- Credit: Accounts Receivable
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                payment_record.org_id, new_journal_id, ar_account_id,
                0, NEW.amount_applied,
                'Payment received - ' || COALESCE(document_record.document_type, 'Payment') || ' ' || COALESCE(document_record.document_number, '')
            );

        ELSE
            -- Vendor payment: DR Accounts Payable, CR Cash

            -- Debit: Accounts Payable
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                payment_record.org_id, new_journal_id, ap_account_id,
                NEW.amount_applied, 0,
                'Payment made - ' || COALESCE(document_record.document_type, 'Payment') || ' ' || COALESCE(document_record.document_number, '')
            );

            -- Credit: Cash/Bank Account
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                payment_record.org_id, new_journal_id, cash_account_id,
                0, NEW.amount_applied,
                'Cash paid - ' || COALESCE(document_record.document_type, 'Payment') || ' ' || COALESCE(document_record.document_number, '')
            );
        END IF;

        -- Validate the journal entry is balanced
        DECLARE
            balance_check RECORD;
        BEGIN
            SELECT * INTO balance_check
            FROM validate_journal_entry_balance(new_journal_id);

            IF NOT balance_check.is_balanced THEN
                RAISE EXCEPTION 'Payment application journal entry is not balanced. Debits: %, Credits: %, Difference: %',
                    balance_check.total_debits, balance_check.total_credits, balance_check.difference;
            END IF;
        END;

    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        PERFORM handle_journal_entry_error(
            payment_record.org_id,
            'payment_application',
            NEW.id,
            'database_error',
            SQLERRM,
            jsonb_build_object(
                'payment_id', NEW.payment_id,
                'applied_to_type', NEW.applied_to_type,
                'applied_to_id', NEW.applied_to_id,
                'amount_applied', NEW.amount_applied,
                'payee_type', payment_record.payee_type
            )
        );
    END;

    RETURN NEW;
END;
$$;

-- Create the trigger for payment application journal entries
DROP TRIGGER IF EXISTS payment_application_journal_trigger ON payment_applications;
CREATE TRIGGER payment_application_journal_trigger
    AFTER INSERT ON payment_applications
    FOR EACH ROW
    EXECUTE FUNCTION create_payment_application_journal_entry();