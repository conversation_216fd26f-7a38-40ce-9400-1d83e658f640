/**
 * Onboarding Utilities
 * Helper functions for managing user onboarding status and debugging
 */

import { supabase } from '@/integrations/supabase/client'
import { logger } from './logger'
import type { Profile, Organization } from '@/types/database'

/**
 * Validate that the Supabase client is properly initialized
 */
function validateSupabaseClient() {
  if (!supabase) {
    throw new Error('Supabase client is not initialized')
  }

  if (typeof supabase.from !== 'function') {
    throw new Error('Supabase client is not properly configured - missing from() method')
  }

  return true
}

export interface OnboardingStatus {
  needsOnboarding: boolean
  hasProfile: boolean
  hasOrganization: boolean
  hasCompletedOnboarding: boolean
  profile: Profile | null
  organization: Organization | null
  debugInfo: {
    userId: string
    profileOrgId: string | null
    onboardingCompletedAt: string | null
    checkTimestamp: string
  }
}

/**
 * Comprehensive onboarding status check
 * This function provides detailed information about a user's onboarding status
 */
export async function checkOnboardingStatus(userId: string): Promise<OnboardingStatus> {
  const checkTimestamp = new Date().toISOString()

  try {
    // Validate Supabase client before use
    validateSupabaseClient()

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle()

    if (profileError) {
      logger.error('Error fetching profile for onboarding check', {
        component: 'onboardingUtils',
        action: 'checkOnboardingStatus',
        metadata: { userId, error: profileError.message }
      })
      throw profileError
    }

    const hasProfile = profile !== null
    const hasOrganization = profile?.org_id !== null
    const hasCompletedOnboarding = profile?.onboarding_completed_at !== null

    let organization = null
    if (hasOrganization && profile?.org_id) {
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', profile.org_id)
        .maybeSingle()

      if (orgError) {
        logger.warn('Error fetching organization for onboarding check', {
          component: 'onboardingUtils',
          action: 'checkOnboardingStatus',
          metadata: { userId, orgId: profile.org_id, error: orgError.message }
        })
      } else {
        organization = orgData
      }
    }

    // Determine if user needs onboarding
    const needsOnboarding = !hasProfile || !hasOrganization || !hasCompletedOnboarding

    const status: OnboardingStatus = {
      needsOnboarding,
      hasProfile,
      hasOrganization,
      hasCompletedOnboarding,
      profile,
      organization,
      debugInfo: {
        userId,
        profileOrgId: profile?.org_id || null,
        onboardingCompletedAt: profile?.onboarding_completed_at || null,
        checkTimestamp
      }
    }

    logger.debug('Onboarding status checked', {
      component: 'onboardingUtils',
      action: 'checkOnboardingStatus',
      metadata: {
        userId,
        needsOnboarding,
        hasProfile,
        hasOrganization,
        hasCompletedOnboarding
      }
    })

    return status
  } catch (error) {
    // Enhanced error logging for debugging
    const errorMessage = (error as Error).message
    const errorStack = (error as Error).stack

    logger.error('Failed to check onboarding status', {
      component: 'onboardingUtils',
      action: 'checkOnboardingStatus',
      metadata: {
        userId,
        error: errorMessage,
        errorStack,
        supabaseClientType: typeof supabase,
        supabaseFromType: typeof supabase?.from,
        timestamp: checkTimestamp
      }
    })

    // Log additional debugging info to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Onboarding status check failed:', {
        error: errorMessage,
        stack: errorStack,
        supabaseClient: supabase,
        supabaseFromMethod: supabase?.from,
        userId
      })
    }

    // Return a safe default that requires onboarding
    return {
      needsOnboarding: true,
      hasProfile: false,
      hasOrganization: false,
      hasCompletedOnboarding: false,
      profile: null,
      organization: null,
      debugInfo: {
        userId,
        profileOrgId: null,
        onboardingCompletedAt: null,
        checkTimestamp
      }
    }
  }
}

/**
 * Mark user onboarding as completed
 */
export async function completeOnboarding(userId: string): Promise<boolean> {
  try {
    // Validate Supabase client before use
    validateSupabaseClient()

    const { error } = await supabase
      .from('profiles')
      .update({ onboarding_completed_at: new Date().toISOString() })
      .eq('id', userId)

    if (error) {
      logger.error('Failed to mark onboarding as completed', {
        component: 'onboardingUtils',
        action: 'completeOnboarding',
        metadata: { userId, error: error.message }
      })
      return false
    }

    logger.info('Onboarding marked as completed', {
      component: 'onboardingUtils',
      action: 'completeOnboarding',
      metadata: { userId }
    })

    return true
  } catch (error) {
    logger.error('Error completing onboarding', {
      component: 'onboardingUtils',
      action: 'completeOnboarding',
      metadata: { userId, error: (error as Error).message }
    })
    return false
  }
}

/**
 * Reset user onboarding status (for testing/debugging)
 */
export async function resetOnboarding(userId: string): Promise<boolean> {
  try {
    // Validate Supabase client before use
    validateSupabaseClient()

    const { error } = await supabase
      .from('profiles')
      .update({ onboarding_completed_at: null })
      .eq('id', userId)

    if (error) {
      logger.error('Failed to reset onboarding status', {
        component: 'onboardingUtils',
        action: 'resetOnboarding',
        metadata: { userId, error: error.message }
      })
      return false
    }

    logger.info('Onboarding status reset', {
      component: 'onboardingUtils',
      action: 'resetOnboarding',
      metadata: { userId }
    })

    return true
  } catch (error) {
    logger.error('Error resetting onboarding', {
      component: 'onboardingUtils',
      action: 'resetOnboarding',
      metadata: { userId, error: (error as Error).message }
    })
    return false
  }
}

/**
 * Debug function to log detailed onboarding information
 */
export async function debugOnboardingStatus(userId: string): Promise<void> {
  const status = await checkOnboardingStatus(userId)
  
  console.group('🔍 Onboarding Debug Information')
  console.log('User ID:', status.debugInfo.userId)
  console.log('Check Timestamp:', status.debugInfo.checkTimestamp)
  console.log('Needs Onboarding:', status.needsOnboarding)
  console.log('Has Profile:', status.hasProfile)
  console.log('Has Organization:', status.hasOrganization)
  console.log('Has Completed Onboarding:', status.hasCompletedOnboarding)
  
  if (status.profile) {
    console.log('Profile Data:', {
      id: status.profile.id,
      email: status.profile.email,
      org_id: status.profile.org_id,
      role: status.profile.role,
      created_at: status.profile.created_at,
      onboarding_completed_at: status.profile.onboarding_completed_at
    })
  } else {
    console.log('Profile Data: null')
  }
  
  if (status.organization) {
    console.log('Organization Data:', {
      id: status.organization.id,
      name: status.organization.name,
      created_at: status.organization.created_at
    })
  } else {
    console.log('Organization Data: null')
  }
  
  console.groupEnd()
}

/**
 * Validate onboarding completion requirements
 */
export function validateOnboardingRequirements(profile: Profile | null, organization: Organization | null): {
  isValid: boolean
  missingRequirements: string[]
} {
  const missingRequirements: string[] = []

  if (!profile) {
    missingRequirements.push('User profile')
  } else {
    if (!profile.email) missingRequirements.push('Email address')
    if (!profile.role) missingRequirements.push('User role')
    if (!profile.org_id) missingRequirements.push('Organization assignment')
  }

  if (!organization) {
    missingRequirements.push('Organization')
  } else {
    if (!organization.name) missingRequirements.push('Organization name')
  }

  return {
    isValid: missingRequirements.length === 0,
    missingRequirements
  }
}

/**
 * Get user's onboarding progress as a percentage
 */
export async function getOnboardingProgress(userId: string): Promise<{
  percentage: number
  completedSteps: string[]
  remainingSteps: string[]
}> {
  const status = await checkOnboardingStatus(userId)
  
  const allSteps = [
    'Create account',
    'Verify email',
    'Create profile',
    'Set up organization',
    'Complete onboarding'
  ]
  
  const completedSteps: string[] = ['Create account', 'Verify email'] // These are assumed complete if user is authenticated
  const remainingSteps: string[] = []
  
  if (status.hasProfile) {
    completedSteps.push('Create profile')
  } else {
    remainingSteps.push('Create profile')
  }
  
  if (status.hasOrganization) {
    completedSteps.push('Set up organization')
  } else {
    remainingSteps.push('Set up organization')
  }
  
  if (status.hasCompletedOnboarding) {
    completedSteps.push('Complete onboarding')
  } else {
    remainingSteps.push('Complete onboarding')
  }
  
  const percentage = Math.round((completedSteps.length / allSteps.length) * 100)
  
  return {
    percentage,
    completedSteps,
    remainingSteps
  }
}

// Export for use in development/debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as Record<string, unknown>).onboardingUtils = {
    checkOnboardingStatus,
    completeOnboarding,
    resetOnboarding,
    debugOnboardingStatus,
    validateOnboardingRequirements,
    getOnboardingProgress
  }
}
