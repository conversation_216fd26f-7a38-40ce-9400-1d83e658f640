/**
 * Service Worker for Push Notifications
 * Handles push notification display, click actions, and offline functionality
 */

const CACHE_NAME = 'kaya-finance-notifications-v1'
const NOTIFICATION_CACHE = 'notification-cache-v1'

// Install event - cache essential resources
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll([
        '/',
        '/notifications',
        '/manifest.json',
        '/icons/icon-192x192.png',
        '/icons/icon-512x512.png'
      ])
    })
  )
  
  // Skip waiting to activate immediately
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== NOTIFICATION_CACHE) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  
  // Claim all clients immediately
  return self.clients.claim()
})

// Push event - handle incoming push notifications
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event)
  
  if (!event.data) {
    console.log('Push event but no data')
    return
  }
  
  try {
    const data = event.data.json()
    console.log('Push data:', data)
    
    const options = {
      body: data.message || data.body,
      icon: data.icon || '/icons/icon-192x192.png',
      badge: data.badge || '/icons/badge-72x72.png',
      image: data.image,
      tag: data.tag || data.notificationId || 'default',
      data: {
        notificationId: data.notificationId,
        entityType: data.entityType,
        entityId: data.entityId,
        url: data.url || '/notifications',
        timestamp: Date.now(),
        ...data.data
      },
      actions: getNotificationActions(data),
      requireInteraction: data.priority === 'urgent',
      silent: data.priority === 'low',
      vibrate: getVibrationPattern(data.priority),
      timestamp: Date.now()
    }
    
    // Store notification for offline access
    event.waitUntil(
      Promise.all([
        self.registration.showNotification(data.title, options),
        cacheNotification(data)
      ])
    )
  } catch (error) {
    console.error('Error processing push notification:', error)
    
    // Fallback notification
    event.waitUntil(
      self.registration.showNotification('New Notification', {
        body: 'You have a new notification from Kaya Finance',
        icon: '/icons/icon-192x192.png',
        tag: 'fallback'
      })
    )
  }
})

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event)
  
  event.notification.close()
  
  const data = event.notification.data || {}
  const action = event.action
  
  event.waitUntil(
    handleNotificationClick(action, data)
  )
})

// Notification close event
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event)
  
  const data = event.notification.data || {}
  
  // Track notification dismissal
  if (data.notificationId) {
    trackNotificationEvent('dismissed', data.notificationId)
  }
})

// Background sync for offline notifications
self.addEventListener('sync', (event) => {
  console.log('Background sync:', event.tag)
  
  if (event.tag === 'notification-sync') {
    event.waitUntil(syncOfflineNotifications())
  }
})

// Message event for communication with main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker received message:', event.data)
  
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
    case 'GET_CACHED_NOTIFICATIONS':
      getCachedNotifications().then((notifications) => {
        event.ports[0].postMessage({ notifications })
      })
      break
    case 'CLEAR_NOTIFICATION_CACHE':
      clearNotificationCache().then(() => {
        event.ports[0].postMessage({ success: true })
      })
      break
    default:
      console.log('Unknown message type:', type)
  }
})

/**
 * Get notification actions based on notification type
 */
function getNotificationActions(data) {
  const actions = []
  
  switch (data.type) {
    case 'payment_pending_approval':
      actions.push(
        { action: 'approve', title: 'Approve', icon: '/icons/approve.png' },
        { action: 'view', title: 'View Details', icon: '/icons/view.png' }
      )
      break
    case 'invoice_overdue':
    case 'bill_overdue':
      actions.push(
        { action: 'view', title: 'View', icon: '/icons/view.png' },
        { action: 'remind', title: 'Send Reminder', icon: '/icons/remind.png' }
      )
      break
    default:
      actions.push(
        { action: 'view', title: 'View', icon: '/icons/view.png' },
        { action: 'dismiss', title: 'Dismiss', icon: '/icons/dismiss.png' }
      )
  }
  
  return actions.slice(0, 2) // Limit to 2 actions
}

/**
 * Get vibration pattern based on priority
 */
function getVibrationPattern(priority) {
  switch (priority) {
    case 'urgent':
      return [200, 100, 200, 100, 200]
    case 'high':
      return [200, 100, 200]
    case 'normal':
      return [200]
    case 'low':
      return []
    default:
      return [200]
  }
}

/**
 * Handle notification click actions
 */
async function handleNotificationClick(action, data) {
  const notificationId = data.notificationId
  let url = data.url || '/notifications'
  
  // Track notification click
  if (notificationId) {
    trackNotificationEvent('clicked', notificationId, action)
  }
  
  switch (action) {
    case 'approve':
      url = `/api/notifications/${notificationId}/approve`
      break
    case 'view':
      if (data.entityType && data.entityId) {
        url = `/${data.entityType}s/${data.entityId}`
      }
      break
    case 'remind':
      url = `/api/notifications/${notificationId}/remind`
      break
    default:
      // Default click action
      if (data.entityType && data.entityId) {
        url = `/${data.entityType}s/${data.entityId}`
      }
  }
  
  // Open or focus the app
  const clients = await self.clients.matchAll({
    type: 'window',
    includeUncontrolled: true
  })
  
  // Check if app is already open
  for (const client of clients) {
    if (client.url.includes(self.location.origin)) {
      await client.focus()
      client.postMessage({
        type: 'NOTIFICATION_CLICKED',
        payload: { action, data, url }
      })
      return
    }
  }
  
  // Open new window
  await self.clients.openWindow(url)
}

/**
 * Cache notification for offline access
 */
async function cacheNotification(notification) {
  try {
    const cache = await caches.open(NOTIFICATION_CACHE)
    const cacheKey = `notification-${notification.notificationId || Date.now()}`
    
    const response = new Response(JSON.stringify({
      ...notification,
      cached_at: Date.now()
    }), {
      headers: { 'Content-Type': 'application/json' }
    })
    
    await cache.put(cacheKey, response)
  } catch (error) {
    console.error('Error caching notification:', error)
  }
}

/**
 * Get cached notifications
 */
async function getCachedNotifications() {
  try {
    const cache = await caches.open(NOTIFICATION_CACHE)
    const keys = await cache.keys()
    const notifications = []
    
    for (const key of keys) {
      if (key.url.includes('notification-')) {
        const response = await cache.match(key)
        const notification = await response.json()
        notifications.push(notification)
      }
    }
    
    return notifications.sort((a, b) => b.cached_at - a.cached_at)
  } catch (error) {
    console.error('Error getting cached notifications:', error)
    return []
  }
}

/**
 * Clear notification cache
 */
async function clearNotificationCache() {
  try {
    await caches.delete(NOTIFICATION_CACHE)
    await caches.open(NOTIFICATION_CACHE) // Recreate empty cache
  } catch (error) {
    console.error('Error clearing notification cache:', error)
  }
}

/**
 * Sync offline notifications
 */
async function syncOfflineNotifications() {
  try {
    const notifications = await getCachedNotifications()
    
    // Send cached notifications to server for processing
    for (const notification of notifications) {
      if (notification.notificationId) {
        await fetch('/api/notifications/sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            notificationId: notification.notificationId,
            action: 'viewed',
            timestamp: notification.cached_at
          })
        })
      }
    }
  } catch (error) {
    console.error('Error syncing offline notifications:', error)
  }
}

/**
 * Track notification events
 */
async function trackNotificationEvent(event, notificationId, action = null) {
  try {
    await fetch('/api/notifications/track', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        notificationId,
        event,
        action,
        timestamp: Date.now(),
        userAgent: self.navigator.userAgent
      })
    })
  } catch (error) {
    console.error('Error tracking notification event:', error)
  }
}
