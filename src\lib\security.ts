/**
 * Security Hardening Module
 * Implements rate limiting, CSRF protection, input sanitization, and security headers
 */

import { config } from '@/lib/config'
import { logger, logSecurity } from '@/lib/logger'
import { handleSecurityError } from '@/lib/errorHandler'

// Rate limiting configuration
interface RateLimitConfig {
  windowMs: number
  maxRequests: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

interface RateLimitEntry {
  count: number
  resetTime: number
  blocked: boolean
}

class RateLimiter {
  private limits: Map<string, RateLimitEntry> = new Map()
  private config: RateLimitConfig

  constructor(config: RateLimitConfig) {
    this.config = config
    
    // Clean up expired entries every minute
    setInterval(() => this.cleanup(), 60000)
  }

  public isAllowed(identifier: string): boolean {
    const now = Date.now()
    const entry = this.limits.get(identifier)

    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired one
      this.limits.set(identifier, {
        count: 1,
        resetTime: now + this.config.windowMs,
        blocked: false
      })
      return true
    }

    if (entry.blocked) {
      return false
    }

    entry.count++

    if (entry.count > this.config.maxRequests) {
      entry.blocked = true
      logSecurity('Rate limit exceeded', 'medium', {
        component: 'RateLimiter',
        action: 'block',
        metadata: {
          identifier,
          count: entry.count,
          maxRequests: this.config.maxRequests
        }
      })
      return false
    }

    return true
  }

  public getRemainingRequests(identifier: string): number {
    const entry = this.limits.get(identifier)
    if (!entry || Date.now() > entry.resetTime) {
      return this.config.maxRequests
    }
    return Math.max(0, this.config.maxRequests - entry.count)
  }

  public getResetTime(identifier: string): number {
    const entry = this.limits.get(identifier)
    if (!entry || Date.now() > entry.resetTime) {
      return Date.now() + this.config.windowMs
    }
    return entry.resetTime
  }

  private cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.limits.entries()) {
      if (now > entry.resetTime) {
        this.limits.delete(key)
      }
    }
  }
}

// CSRF Protection
class CSRFProtection {
  private tokens: Map<string, { token: string; expires: number }> = new Map()
  private tokenExpiry = 60 * 60 * 1000 // 1 hour

  public generateToken(sessionId: string): string {
    const token = this.generateRandomToken()
    const expires = Date.now() + this.tokenExpiry

    this.tokens.set(sessionId, { token, expires })
    
    // Clean up expired tokens
    this.cleanup()
    
    return token
  }

  public validateToken(sessionId: string, token: string): boolean {
    const entry = this.tokens.get(sessionId)
    
    if (!entry) {
      logSecurity('CSRF token not found', 'high', {
        component: 'CSRFProtection',
        action: 'validate',
        metadata: { sessionId, reason: 'token_not_found' }
      })
      return false
    }

    if (Date.now() > entry.expires) {
      this.tokens.delete(sessionId)
      logSecurity('CSRF token expired', 'medium', {
        component: 'CSRFProtection',
        action: 'validate',
        metadata: { sessionId, reason: 'token_expired' }
      })
      return false
    }

    if (entry.token !== token) {
      logSecurity('CSRF token mismatch', 'high', {
        component: 'CSRFProtection',
        action: 'validate',
        metadata: { sessionId, reason: 'token_mismatch' }
      })
      return false
    }

    return true
  }

  public refreshToken(sessionId: string): string {
    return this.generateToken(sessionId)
  }

  private generateRandomToken(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  private cleanup(): void {
    const now = Date.now()
    for (const [sessionId, entry] of this.tokens.entries()) {
      if (now > entry.expires) {
        this.tokens.delete(sessionId)
      }
    }
  }
}

// Input Sanitization
export class InputSanitizer {
  private static readonly HTML_ESCAPE_MAP: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;'
  }

  private static readonly SQL_INJECTION_PATTERNS = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(--|\/\*|\*\/|;)/g,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi
  ]

  private static readonly XSS_PATTERNS = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi
  ]

  public static sanitizeHtml(input: string): string {
    if (typeof input !== 'string') return ''
    
    return input.replace(/[&<>"'/]/g, (char) => {
      return this.HTML_ESCAPE_MAP[char] || char
    })
  }

  public static sanitizeText(input: string | null | undefined): string {
    if (!input || typeof input !== 'string') return ''
    
    // Remove potential XSS patterns
    let sanitized = input
    this.XSS_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '')
    })
    
    // Remove potential SQL injection patterns
    this.SQL_INJECTION_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '')
    })
    
    // Trim and normalize whitespace
    return sanitized.trim().replace(/\s+/g, ' ')
  }

  public static sanitizeEmail(email: string | null | undefined): string {
    if (!email || typeof email !== 'string') return ''
    
    // Basic email sanitization
    const sanitized = email.toLowerCase().trim()
    
    // Validate email format
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    if (!emailRegex.test(sanitized)) {
      logSecurity('Invalid email format detected', 'low', {
        component: 'InputSanitizer',
        action: 'sanitizeEmail',
        metadata: { originalEmail: email }
      })
      return ''
    }
    
    return sanitized
  }

  public static sanitizePhone(phone: string | null | undefined): string {
    if (!phone || typeof phone !== 'string') return ''
    
    // Remove all non-digit characters except +
    const sanitized = phone.replace(/[^\d+]/g, '')
    
    // Validate phone format (basic)
    if (sanitized.length < 10 || sanitized.length > 15) {
      return ''
    }
    
    return sanitized
  }

  public static sanitizeNumber(num: number | string | null | undefined): number {
    if (num === null || num === undefined) return 0
    
    const parsed = typeof num === 'string' ? parseFloat(num) : num
    
    if (isNaN(parsed) || !isFinite(parsed)) {
      return 0
    }
    
    return parsed
  }

  public static validateAndSanitizeInput(
    input: unknown,
    type: 'text' | 'email' | 'phone' | 'number' | 'html',
    required: boolean = false
  ): { isValid: boolean; sanitized: string | number; error?: string } {
    if (required && (input === null || input === undefined || input === '')) {
      return { isValid: false, sanitized: '', error: 'Field is required' }
    }

    if (!required && (input === null || input === undefined || input === '')) {
      return { isValid: true, sanitized: '' }
    }

    try {
      switch (type) {
        case 'text': {
          const sanitizedText = this.sanitizeText(input)
          return { isValid: true, sanitized: sanitizedText }
        }

        case 'email': {
          const sanitizedEmail = this.sanitizeEmail(input)
          if (sanitizedEmail === '' && input !== '') {
            return { isValid: false, sanitized: '', error: 'Invalid email format' }
          }
          return { isValid: true, sanitized: sanitizedEmail }
        }

        case 'phone': {
          const sanitizedPhone = this.sanitizePhone(input)
          if (sanitizedPhone === '' && input !== '') {
            return { isValid: false, sanitized: '', error: 'Invalid phone format' }
          }
          return { isValid: true, sanitized: sanitizedPhone }
        }

        case 'number': {
          const sanitizedNumber = this.sanitizeNumber(input)
          return { isValid: true, sanitized: sanitizedNumber }
        }

        case 'html': {
          const sanitizedHtml = this.sanitizeHtml(input)
          return { isValid: true, sanitized: sanitizedHtml }
        }
          
        default:
          return { isValid: false, sanitized: '', error: 'Unknown validation type' }
      }
    } catch (error) {
      logSecurity('Input sanitization error', 'medium', {
        component: 'InputSanitizer',
        action: 'validateAndSanitizeInput',
        metadata: { type, error: (error as Error).message }
      })
      return { isValid: false, sanitized: '', error: 'Sanitization failed' }
    }
  }
}

// Security Headers
export const setSecurityHeaders = (): void => {
  if (typeof document !== 'undefined') {
    // Content Security Policy
    const csp = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.sentry-cdn.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https:",
      "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.sentry.io",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ')

    // Create meta tag for CSP
    const cspMeta = document.createElement('meta')
    cspMeta.httpEquiv = 'Content-Security-Policy'
    cspMeta.content = csp
    document.head.appendChild(cspMeta)

    // X-Frame-Options
    const frameMeta = document.createElement('meta')
    frameMeta.httpEquiv = 'X-Frame-Options'
    frameMeta.content = 'DENY'
    document.head.appendChild(frameMeta)

    // X-Content-Type-Options
    const contentTypeMeta = document.createElement('meta')
    contentTypeMeta.httpEquiv = 'X-Content-Type-Options'
    contentTypeMeta.content = 'nosniff'
    document.head.appendChild(contentTypeMeta)

    logger.info('Security headers configured', {
      component: 'Security',
      action: 'setSecurityHeaders'
    })
  }
}

// Initialize security systems
export const initializeSecurity = (): {
  rateLimiter: RateLimiter
  csrfProtection: CSRFProtection
} => {
  // Configure rate limiting
  const rateLimiter = new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per window
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  })

  // Initialize CSRF protection
  const csrfProtection = new CSRFProtection()

  // Set security headers
  setSecurityHeaders()

  logger.info('Security systems initialized', {
    component: 'Security',
    action: 'initialize'
  })

  return { rateLimiter, csrfProtection }
}

// Export security instances
export const security = initializeSecurity()
