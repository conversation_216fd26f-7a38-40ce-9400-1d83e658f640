import type {
  Product,
  ProductCategory,
  InventoryLocation,
  StockLevel,
  InventoryTransaction
} from './extended-database'

// =====================================================
// FORM DATA TYPES
// =====================================================

export interface ProductFormData {
  sku: string
  name: string
  description: string
  category_id: string
  unit_of_measure: string
  cost_price: number
  selling_price: number
  track_inventory: boolean
  reorder_level: number
  reorder_quantity: number
  barcode: string
  weight: number | null
  dimensions: string
  is_active: boolean
  is_sellable: boolean
  is_purchasable: boolean
}

export interface ProductCategoryFormData {
  name: string
  description: string
  parent_id: string | null
  code: string
  is_active: boolean
  sort_order: number
}

export interface InventoryLocationFormData {
  name: string
  code: string
  description: string
  address: string
  is_default: boolean
  is_active: boolean
}

export interface StockAdjustmentFormData {
  product_id: string
  location_id: string
  adjustment_type: 'increase' | 'decrease' | 'set'
  quantity: number
  reason_code: string
  notes: string
  unit_cost?: number
}

export interface InventoryTransactionFormData {
  product_id: string
  location_id: string
  transaction_type: InventoryTransactionType
  quantity: number
  unit_cost?: number
  reference_type?: string
  reference_id?: string
  reference_number?: string
  reason_code?: string
  notes?: string
  batch_number?: string
  expiry_date?: string
}

// =====================================================
// EXTENDED TYPES WITH RELATIONSHIPS
// =====================================================

export interface ProductWithCategory extends Product {
  category?: ProductCategory | null
}

export interface ProductWithStock extends Product {
  category?: ProductCategory | null
  stock_levels?: StockLevelWithLocation[]
  total_quantity_on_hand?: number
  total_quantity_available?: number
  is_low_stock?: boolean
}

export interface StockLevelWithLocation extends StockLevel {
  location?: InventoryLocation | null
}

export interface StockLevelWithProduct extends StockLevel {
  product?: Product | null
  location?: InventoryLocation | null
}

export interface InventoryTransactionWithDetails extends InventoryTransaction {
  product?: Product | null
  location?: InventoryLocation | null
}

export interface ProductCategoryWithChildren extends ProductCategory {
  children?: ProductCategory[]
  parent?: ProductCategory | null
  product_count?: number
}

// =====================================================
// ENUMS AND CONSTANTS
// =====================================================

export type InventoryTransactionType = 
  | 'purchase'
  | 'sale'
  | 'adjustment'
  | 'transfer'
  | 'return'
  | 'damage'
  | 'theft'
  | 'expired'
  | 'initial'

export type StockAdjustmentReason = 
  | 'damaged'
  | 'expired'
  | 'theft'
  | 'found'
  | 'recount'
  | 'return'
  | 'transfer'
  | 'initial_stock'
  | 'other'

export const UNIT_OF_MEASURE_OPTIONS = [
  'each',
  'kg',
  'g',
  'lb',
  'oz',
  'liter',
  'ml',
  'gallon',
  'meter',
  'cm',
  'inch',
  'foot',
  'box',
  'pack',
  'dozen',
  'case'
] as const

export type UnitOfMeasure = typeof UNIT_OF_MEASURE_OPTIONS[number]

// =====================================================
// FILTER AND SEARCH TYPES
// =====================================================

export interface ProductFilters {
  search?: string
  category_id?: string
  is_active?: boolean
  is_sellable?: boolean
  is_purchasable?: boolean
  track_inventory?: boolean
  low_stock?: boolean
  out_of_stock?: boolean
}

export interface InventoryTransactionFilters {
  product_id?: string
  location_id?: string
  transaction_type?: InventoryTransactionType
  date_from?: string
  date_to?: string
  reference_type?: string
}

// =====================================================
// DASHBOARD AND ANALYTICS TYPES
// =====================================================

export interface InventoryDashboardData {
  total_products: number
  active_products: number
  low_stock_products: number
  out_of_stock_products: number
  total_inventory_value: number
  recent_transactions: InventoryTransactionWithDetails[]
  top_selling_products: ProductWithStock[]
  low_stock_alerts: ProductWithStock[]
}

export interface StockMovementSummary {
  product_id: string
  product_name: string
  product_sku: string
  total_in: number
  total_out: number
  net_movement: number
  current_stock: number
  value_movement: number
}

// =====================================================
// API RESPONSE TYPES
// =====================================================

export interface ProductListResponse {
  data: ProductWithStock[]
  count: number
  page: number
  limit: number
}

export interface InventoryTransactionListResponse {
  data: InventoryTransactionWithDetails[]
  count: number
  page: number
  limit: number
}

// =====================================================
// VALIDATION TYPES
// =====================================================

export interface ProductValidationErrors {
  sku?: string
  name?: string
  category_id?: string
  unit_of_measure?: string
  cost_price?: string
  selling_price?: string
  reorder_level?: string
  reorder_quantity?: string
  barcode?: string
  weight?: string
  dimensions?: string
}

export interface StockAdjustmentValidationErrors {
  product_id?: string
  location_id?: string
  quantity?: string
  reason_code?: string
  unit_cost?: string
}

// =====================================================
// PRODUCT SELECTION TYPES FOR LINE ITEMS
// =====================================================

export interface ProductOption {
  id: string
  sku: string
  name: string
  description: string | null
  selling_price: number
  cost_price: number
  unit_of_measure: string
  is_active: boolean
  is_sellable: boolean
  category?: ProductCategory | null
}

export interface ProductSearchResult extends ProductOption {
  stock_info?: {
    total_quantity_on_hand: number
    total_quantity_available: number
    is_low_stock: boolean
    is_out_of_stock: boolean
  }
}

// =====================================================
// INVENTORY TRANSACTION TYPES
// =====================================================

export type InventoryTransactionTypeEnum =
  | 'purchase'
  | 'sale'
  | 'adjustment'
  | 'transfer_in'
  | 'transfer_out'
  | 'opening_balance'

export interface InventoryMovementSummary {
  product_id: string
  product_name: string
  product_sku: string
  location_id: string
  location_name: string
  opening_balance: number
  total_in: number
  total_out: number
  closing_balance: number
  value_movement: number
}
