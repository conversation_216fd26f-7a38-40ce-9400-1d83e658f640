// Role-Based Access Control for Backup Operations
// Provides granular permissions and approval workflows

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'

export interface BackupPermission {
  action: string
  resource: string
  conditions?: Record<string, unknown>
}

export interface ApprovalRequest {
  id: string
  orgId: string
  requestedBy: string
  requestedAt: string
  operation: string
  operationData: Record<string, unknown>
  status: 'pending' | 'approved' | 'rejected' | 'expired'
  approvedBy?: string
  approvedAt?: string
  rejectedBy?: string
  rejectedAt?: string
  rejectionReason?: string
  expiresAt: string
}

export interface RolePermissions {
  role: string
  permissions: BackupPermission[]
  requiresApproval: string[]
  canApprove: string[]
}

/**
 * Backup Role-Based Access Control Manager
 */
export class BackupRBACManager {
  private static readonly ROLE_PERMISSIONS: Record<string, RolePermissions> = {
    owner: {
      role: 'owner',
      permissions: [
        { action: '*', resource: '*' }, // Full access
      ],
      requiresApproval: [], // No approvals needed
      canApprove: ['*'] // Can approve everything
    },
    admin: {
      role: 'admin',
      permissions: [
        { action: 'create', resource: 'backup' },
        { action: 'restore', resource: 'backup', conditions: { mode: ['merge', 'preview'] } },
        { action: 'restore', resource: 'backup', conditions: { mode: ['replace'], requiresApproval: true } },
        { action: 'delete', resource: 'backup', conditions: { not_latest: true } },
        { action: 'download', resource: 'backup' },
        { action: 'view', resource: 'backup' },
        { action: 'manage', resource: 'backup_settings' }
      ],
      requiresApproval: ['restore:replace', 'delete:latest_backup'],
      canApprove: ['restore:merge', 'restore:preview', 'backup:create']
    },
    accountant: {
      role: 'accountant',
      permissions: [
        { action: 'create', resource: 'backup', conditions: { type: ['full', 'incremental'] } },
        { action: 'download', resource: 'backup' },
        { action: 'view', resource: 'backup' },
        { action: 'restore', resource: 'backup', conditions: { mode: ['preview'], requiresApproval: true } }
      ],
      requiresApproval: ['restore:preview'],
      canApprove: []
    },
    viewer: {
      role: 'viewer',
      permissions: [
        { action: 'view', resource: 'backup' },
        { action: 'download', resource: 'backup', conditions: { requiresApproval: true } }
      ],
      requiresApproval: ['download'],
      canApprove: []
    }
  }

  /**
   * Check if user has permission for specific action
   */
  static async hasPermission(
    userId: string,
    orgId: string,
    action: string,
    resource: string,
    context?: Record<string, unknown>
  ): Promise<{ allowed: boolean; requiresApproval?: boolean; reason?: string }> {
    try {
      // Get user profile
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .eq('org_id', orgId)
        .single()

      if (error || !profile) {
        return { allowed: false, reason: 'User profile not found' }
      }

      const rolePermissions = this.ROLE_PERMISSIONS[profile.role]
      if (!rolePermissions) {
        return { allowed: false, reason: 'Invalid user role' }
      }

      // Check permissions
      const permission = this.findMatchingPermission(rolePermissions.permissions, action, resource)
      if (!permission) {
        return { allowed: false, reason: 'Permission not granted for this action' }
      }

      // Check conditions
      const conditionResult = this.evaluateConditions(permission.conditions, context)
      if (!conditionResult.allowed) {
        return { 
          allowed: false, 
          requiresApproval: conditionResult.requiresApproval,
          reason: conditionResult.reason 
        }
      }

      // Check if approval is required
      const operationKey = `${action}:${resource}`
      const requiresApproval = rolePermissions.requiresApproval.includes(operationKey) ||
                              rolePermissions.requiresApproval.includes(`${action}:*`) ||
                              conditionResult.requiresApproval

      return { 
        allowed: true, 
        requiresApproval 
      }

    } catch (error) {
      console.error('Permission check error:', error)
      return { allowed: false, reason: 'Permission check failed' }
    }
  }

  /**
   * Request approval for an operation
   */
  static async requestApproval(
    userId: string,
    orgId: string,
    operation: string,
    operationData: Record<string, unknown>,
    expiryHours: number = 24
  ): Promise<{ success: boolean; approvalId?: string; error?: string }> {
    try {
      const expiresAt = new Date(Date.now() + expiryHours * 60 * 60 * 1000)

      const { data: approval, error } = await supabase
        .from('backup_approval_requests')
        .insert({
          org_id: orgId,
          requested_by: userId,
          operation,
          operation_data: operationData,
          status: 'pending',
          expires_at: expiresAt.toISOString()
        })
        .select()
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      // Log approval request
      await auditLogger.logActivity({
        entity_type: 'backup_approval',
        entity_id: approval.id,
        action: 'approval_requested',
        description: `Approval requested for ${operation}`,
        severity: 'info',
        category: 'security',
        metadata: { operation, operation_data: operationData }
      })

      // Notify approvers
      await this.notifyApprovers(orgId, approval.id, operation, userId)

      return { success: true, approvalId: approval.id }

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Approve or reject an approval request
   */
  static async processApproval(
    approvalId: string,
    approverId: string,
    action: 'approve' | 'reject',
    reason?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Get approval request
      const { data: approval, error: fetchError } = await supabase
        .from('backup_approval_requests')
        .select('*, profiles!backup_approval_requests_requested_by_fkey(org_id, role)')
        .eq('id', approvalId)
        .single()

      if (fetchError || !approval) {
        return { success: false, error: 'Approval request not found' }
      }

      // Check if approver has permission
      const canApprove = await this.canUserApprove(approverId, approval.org_id, approval.operation)
      if (!canApprove) {
        return { success: false, error: 'User not authorized to approve this request' }
      }

      // Check if request is still valid
      if (approval.status !== 'pending') {
        return { success: false, error: 'Approval request is no longer pending' }
      }

      if (new Date(approval.expires_at) < new Date()) {
        return { success: false, error: 'Approval request has expired' }
      }

      // Update approval status
      const updateData = {
        status: action === 'approve' ? 'approved' : 'rejected',
        [`${action === 'approve' ? 'approved' : 'rejected'}_by`]: approverId,
        [`${action === 'approve' ? 'approved' : 'rejected'}_at`]: new Date().toISOString(),
        ...(reason && { rejection_reason: reason })
      }

      const { error: updateError } = await supabase
        .from('backup_approval_requests')
        .update(updateData)
        .eq('id', approvalId)

      if (updateError) {
        return { success: false, error: updateError.message }
      }

      // Log approval action
      await auditLogger.logActivity({
        entity_type: 'backup_approval',
        entity_id: approvalId,
        action: `approval_${action}d`,
        description: `Approval request ${action}d for ${approval.operation}`,
        severity: 'info',
        category: 'security',
        metadata: { 
          operation: approval.operation,
          approver_id: approverId,
          reason 
        }
      })

      return { success: true }

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Get pending approvals for user
   */
  static async getPendingApprovals(userId: string, orgId: string): Promise<ApprovalRequest[]> {
    try {
      // Check if user can approve requests
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .eq('org_id', orgId)
        .single()

      if (!profile) return []

      const rolePermissions = this.ROLE_PERMISSIONS[profile.role]
      if (!rolePermissions || rolePermissions.canApprove.length === 0) {
        return []
      }

      const { data: approvals, error } = await supabase
        .from('backup_approval_requests')
        .select(`
          *,
          requester:profiles!backup_approval_requests_requested_by_fkey(id, email, full_name)
        `)
        .eq('org_id', orgId)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .order('requested_at', { ascending: false })

      if (error) {
        console.error('Error fetching pending approvals:', error)
        return []
      }

      // Filter approvals user can actually approve
      return (approvals || []).filter(approval => 
        this.canUserApproveOperation(rolePermissions, approval.operation)
      )

    } catch (error) {
      console.error('Error getting pending approvals:', error)
      return []
    }
  }

  /**
   * Check if approval is valid and approved
   */
  static async isApprovalValid(approvalId: string): Promise<boolean> {
    try {
      const { data: approval, error } = await supabase
        .from('backup_approval_requests')
        .select('status, expires_at')
        .eq('id', approvalId)
        .single()

      if (error || !approval) return false

      return approval.status === 'approved' && new Date(approval.expires_at) > new Date()

    } catch (error) {
      return false
    }
  }

  // Private helper methods

  private static findMatchingPermission(
    permissions: BackupPermission[],
    action: string,
    resource: string
  ): BackupPermission | null {
    return permissions.find(p => 
      (p.action === '*' || p.action === action) &&
      (p.resource === '*' || p.resource === resource)
    ) || null
  }

  private static evaluateConditions(
    conditions?: Record<string, unknown>,
    context?: Record<string, unknown>
  ): { allowed: boolean; requiresApproval?: boolean; reason?: string } {
    if (!conditions) return { allowed: true }

    // Check mode conditions
    if (conditions.mode && context?.mode) {
      const allowedModes = Array.isArray(conditions.mode) ? conditions.mode : [conditions.mode]
      if (!allowedModes.includes(context.mode)) {
        return { allowed: false, reason: `Mode '${context.mode}' not allowed` }
      }
    }

    // Check type conditions
    if (conditions.type && context?.type) {
      const allowedTypes = Array.isArray(conditions.type) ? conditions.type : [conditions.type]
      if (!allowedTypes.includes(context.type)) {
        return { allowed: false, reason: `Type '${context.type}' not allowed` }
      }
    }

    // Check if approval is required
    if (conditions.requiresApproval) {
      return { allowed: true, requiresApproval: true }
    }

    return { allowed: true }
  }

  private static async canUserApprove(userId: string, orgId: string, operation: string): Promise<boolean> {
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .eq('org_id', orgId)
      .single()

    if (!profile) return false

    const rolePermissions = this.ROLE_PERMISSIONS[profile.role]
    if (!rolePermissions) return false

    return this.canUserApproveOperation(rolePermissions, operation)
  }

  private static canUserApproveOperation(rolePermissions: RolePermissions, operation: string): boolean {
    return rolePermissions.canApprove.includes('*') ||
           rolePermissions.canApprove.includes(operation) ||
           rolePermissions.canApprove.some(pattern => operation.startsWith(pattern.replace('*', '')))
  }

  private static async notifyApprovers(
    orgId: string,
    approvalId: string,
    operation: string,
    requesterId: string
  ): Promise<void> {
    try {
      // Get potential approvers (owners and admins)
      const { data: approvers } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .eq('org_id', orgId)
        .in('role', ['owner', 'admin'])
        .neq('id', requesterId)

      if (!approvers || approvers.length === 0) return

      // Create notifications for approvers
      for (const approver of approvers) {
        await supabase.rpc('create_notification_from_template', {
          template_type: 'backup_approval_required',
          org_id: orgId,
          user_id: approver.id,
          template_data: {
            approval_id: approvalId,
            operation,
            requester_id: requesterId
          }
        })
      }
    } catch (error) {
      console.error('Failed to notify approvers:', error)
    }
  }
}
