import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { LoadingSpinner } from '@/components/ui/loading'
import { toast } from '@/components/ui/toast-utils'
import { Bell, Mail, Smartphone, Monitor, Save, RotateCcw, AlertCircle } from 'lucide-react'
import {
  useNotificationPreferences,
  useUpdateNotificationPreference,
  useBulkUpdateNotificationPreferences
} from '@/hooks/queries/useNotifications'
import { useAuth } from '@/hooks/useAuthHook'
import {
  getPushNotificationService,
  isPushNotificationSupported,
  initializePushNotifications
} from '@/lib/pushNotificationService'
import type { NotificationPreference, NotificationType } from '@/types/notifications'

// Notification types with descriptions
const NOTIFICATION_TYPES: Array<{
  type: NotificationType
  label: string
  description: string
  category: string
}> = [
  // Financial notifications
  { type: 'payment_pending_approval', label: 'Payment Approvals', description: 'When payments require your approval', category: 'Financial' },
  { type: 'payment_approved', label: 'Payment Approved', description: 'When your payments are approved', category: 'Financial' },
  { type: 'payment_rejected', label: 'Payment Rejected', description: 'When your payments are rejected', category: 'Financial' },
  { type: 'invoice_overdue', label: 'Overdue Invoices', description: 'When invoices become overdue', category: 'Financial' },
  { type: 'invoice_due_soon', label: 'Invoice Due Soon', description: 'When invoices are due soon', category: 'Financial' },
  { type: 'invoice_paid', label: 'Invoice Paid', description: 'When invoices are paid', category: 'Financial' },
  { type: 'bill_due_soon', label: 'Bill Due Soon', description: 'When bills are due soon', category: 'Financial' },
  { type: 'bill_overdue', label: 'Overdue Bills', description: 'When bills become overdue', category: 'Financial' },
  { type: 'budget_exceeded', label: 'Budget Exceeded', description: 'When budgets are exceeded', category: 'Financial' },
  { type: 'budget_warning', label: 'Budget Warning', description: 'When approaching budget limits', category: 'Financial' },
  
  // System notifications
  { type: 'user_invited', label: 'User Invitations', description: 'When users are invited to the organization', category: 'System' },
  { type: 'backup_completed', label: 'Backup Completed', description: 'When system backups complete successfully', category: 'System' },
  { type: 'backup_failed', label: 'Backup Failed', description: 'When system backups fail', category: 'System' },
  { type: 'system_maintenance', label: 'System Maintenance', description: 'System maintenance notifications', category: 'System' },
  { type: 'audit_alert', label: 'Audit Alerts', description: 'Security and audit alerts', category: 'System' }
]

interface PreferenceState {
  [key: string]: {
    enabled: boolean
    email_enabled: boolean
    in_app_enabled: boolean
    push_enabled: boolean
  }
}

export function NotificationSettings() {
  const { profile } = useAuth()
  const [preferences, setPreferences] = useState<PreferenceState>({})
  const [hasChanges, setHasChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [pushSupported, setPushSupported] = useState(false)
  const [pushPermission, setPushPermission] = useState<NotificationPermission>('default')
  const [pushSubscribed, setPushSubscribed] = useState(false)

  // Queries
  const { data: userPreferences = [], isLoading, refetch } = useNotificationPreferences()
  const updatePreference = useUpdateNotificationPreference()
  const bulkUpdatePreferences = useBulkUpdateNotificationPreferences()

  // Initialize push notification support
  useEffect(() => {
    const initPush = async () => {
      const supported = isPushNotificationSupported()
      setPushSupported(supported)

      if (supported) {
        // Initialize push service
        const vapidKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || 'demo-key'
        await initializePushNotifications(vapidKey)

        // Check current permission
        const permission = Notification.permission
        setPushPermission(permission)

        // Check subscription status
        if (profile?.id) {
          const pushService = getPushNotificationService()
          if (pushService) {
            const { isSubscribed } = await pushService.getSubscriptionStatus(profile.id)
            setPushSubscribed(isSubscribed)
          }
        }
      }
    }

    initPush()
  }, [profile?.id])

  // Initialize preferences state
  useEffect(() => {
    const initialState: PreferenceState = {}

    NOTIFICATION_TYPES.forEach(({ type }) => {
      const userPref = userPreferences.find(p => p.notification_type === type)
      initialState[type] = {
        enabled: userPref?.enabled ?? true,
        email_enabled: userPref?.email_enabled ?? true,
        in_app_enabled: userPref?.in_app_enabled ?? true,
        push_enabled: userPref?.push_enabled ?? true
      }
    })

    setPreferences(initialState)
    setHasChanges(false)
  }, [userPreferences])

  // Handle preference change
  const handlePreferenceChange = (
    notificationType: string,
    field: 'enabled' | 'email_enabled' | 'in_app_enabled' | 'push_enabled',
    value: boolean
  ) => {
    setPreferences(prev => ({
      ...prev,
      [notificationType]: {
        ...prev[notificationType],
        [field]: value,
        // If disabling main toggle, disable all sub-toggles
        ...(field === 'enabled' && !value ? {
          email_enabled: false,
          in_app_enabled: false,
          push_enabled: false
        } : {})
      }
    }))
    setHasChanges(true)
  }

  // Handle push notification subscription
  const handlePushSubscription = async (enable: boolean) => {
    if (!profile?.id || !profile?.org_id) return

    const pushService = getPushNotificationService()
    if (!pushService) {
      toast.error('Push notifications not available')
      return
    }

    try {
      if (enable) {
        const subscription = await pushService.subscribe(profile.id, profile.org_id)
        if (subscription) {
          setPushSubscribed(true)
          setPushPermission('granted')
          toast.success('Push notifications enabled')
        } else {
          toast.error('Failed to enable push notifications')
        }
      } else {
        const success = await pushService.unsubscribe(profile.id)
        if (success) {
          setPushSubscribed(false)
          toast.success('Push notifications disabled')
        } else {
          toast.error('Failed to disable push notifications')
        }
      }
    } catch (error) {
      console.error('Error managing push subscription:', error)
      toast.error('Failed to update push notification settings')
    }
  }

  // Send test push notification
  const handleTestPushNotification = async () => {
    const pushService = getPushNotificationService()
    if (!pushService) {
      toast.error('Push notifications not available')
      return
    }

    const success = await pushService.sendTestNotification()
    if (success) {
      toast.success('Test notification sent')
    } else {
      toast.error('Failed to send test notification')
    }
  }

  // Save preferences
  const handleSave = async () => {
    if (!profile?.id) return

    setIsSaving(true)
    try {
      const updates = Object.entries(preferences).map(([notificationType, prefs]) => ({
        notification_type: notificationType,
        enabled: prefs.enabled,
        email_enabled: prefs.email_enabled,
        in_app_enabled: prefs.in_app_enabled,
        push_enabled: prefs.push_enabled
      }))

      await bulkUpdatePreferences.mutateAsync(updates)
      setHasChanges(false)
      toast.success('Notification preferences saved successfully')
    } catch (error) {
      console.error('Error saving preferences:', error)
      toast.error('Failed to save notification preferences')
    } finally {
      setIsSaving(false)
    }
  }

  // Reset to defaults
  const handleReset = () => {
    const defaultState: PreferenceState = {}
    NOTIFICATION_TYPES.forEach(({ type }) => {
      defaultState[type] = {
        enabled: true,
        email_enabled: true,
        in_app_enabled: true,
        push_enabled: true
      }
    })
    setPreferences(defaultState)
    setHasChanges(true)
  }

  // Group notifications by category
  const groupedNotifications = NOTIFICATION_TYPES.reduce((acc, notification) => {
    if (!acc[notification.category]) {
      acc[notification.category] = []
    }
    acc[notification.category].push(notification)
    return acc
  }, {} as Record<string, typeof NOTIFICATION_TYPES>)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="md" text="Loading notification preferences..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Notification Preferences</h3>
          <p className="text-sm text-muted-foreground">
            Manage how you receive notifications for different events
          </p>
        </div>
        
        {hasChanges && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={isSaving}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          </div>
        )}
      </div>

      {/* Global Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Global Settings
          </CardTitle>
          <CardDescription>
            Master controls for all notification types
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">All Notifications</Label>
              <div className="text-sm text-muted-foreground">
                Enable or disable all notifications
              </div>
            </div>
            <Switch
              checked={Object.values(preferences).some(p => p.enabled)}
              onCheckedChange={(checked) => {
                const newPreferences = { ...preferences }
                Object.keys(newPreferences).forEach(key => {
                  newPreferences[key] = {
                    enabled: checked,
                    email_enabled: checked,
                    in_app_enabled: checked,
                    push_enabled: checked
                  }
                })
                setPreferences(newPreferences)
                setHasChanges(true)
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Push Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            Push Notifications
          </CardTitle>
          <CardDescription>
            Manage browser and mobile push notification settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!pushSupported ? (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                Push notifications are not supported in this browser
              </span>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Enable Push Notifications</Label>
                  <div className="text-sm text-muted-foreground">
                    Receive notifications even when the app is closed
                  </div>
                </div>
                <Switch
                  checked={pushSubscribed && pushPermission === 'granted'}
                  onCheckedChange={handlePushSubscription}
                  disabled={pushPermission === 'denied'}
                />
              </div>

              {pushPermission === 'denied' && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-800">
                    Push notifications are blocked. Please enable them in your browser settings.
                  </span>
                </div>
              )}

              {pushPermission === 'default' && (
                <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-800">
                    Click the toggle above to enable push notifications
                  </span>
                </div>
              )}

              {pushSubscribed && pushPermission === 'granted' && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-green-600">
                    ✓ Push notifications are enabled
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleTestPushNotification}
                  >
                    Send Test
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Notification Categories */}
      {Object.entries(groupedNotifications).map(([category, notifications]) => (
        <Card key={category}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {category === 'Financial' ? (
                <Monitor className="h-5 w-5" />
              ) : (
                <Smartphone className="h-5 w-5" />
              )}
              {category} Notifications
            </CardTitle>
            <CardDescription>
              Configure {category.toLowerCase()} notification preferences
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {notifications.map((notification, index) => {
                const pref = preferences[notification.type] || {
                  enabled: true,
                  email_enabled: true,
                  in_app_enabled: true,
                  push_enabled: true
                }

                return (
                  <div key={notification.type}>
                    {index > 0 && <Separator className="my-4" />}
                    <div className="space-y-4">
                      {/* Main toggle */}
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label className="text-base">{notification.label}</Label>
                          <div className="text-sm text-muted-foreground">
                            {notification.description}
                          </div>
                        </div>
                        <Switch
                          checked={pref.enabled}
                          onCheckedChange={(checked) =>
                            handlePreferenceChange(notification.type, 'enabled', checked)
                          }
                        />
                      </div>

                      {/* Sub-toggles */}
                      {pref.enabled && (
                        <div className="ml-6 space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Monitor className="h-4 w-4 text-muted-foreground" />
                              <Label className="text-sm">In-app notifications</Label>
                            </div>
                            <Switch
                              checked={pref.in_app_enabled}
                              onCheckedChange={(checked) =>
                                handlePreferenceChange(notification.type, 'in_app_enabled', checked)
                              }
                            />
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4 text-muted-foreground" />
                              <Label className="text-sm">Email notifications</Label>
                            </div>
                            <Switch
                              checked={pref.email_enabled}
                              onCheckedChange={(checked) =>
                                handlePreferenceChange(notification.type, 'email_enabled', checked)
                              }
                            />
                          </div>

                          {pushSupported && (
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Smartphone className="h-4 w-4 text-muted-foreground" />
                                <Label className="text-sm">Push notifications</Label>
                              </div>
                              <Switch
                                checked={pref.push_enabled && pushSubscribed}
                                onCheckedChange={(checked) =>
                                  handlePreferenceChange(notification.type, 'push_enabled', checked)
                                }
                                disabled={!pushSubscribed || pushPermission !== 'granted'}
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
