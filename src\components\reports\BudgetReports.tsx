import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { useBudgetAnalysis, useAllBudgetsAnalysis } from '@/hooks/queries/useBudgetAnalysis'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { LoadingSpinner } from '@/components/ui/loading'
import { RefreshCw, TrendingUp, TrendingDown, AlertTriangle } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import type { Budget, Account } from '@/types/database'

interface BudgetAnalysis {
  account: Account
  budgetAmount: number
  actualAmount: number
  variance: number
  variancePercent: number
}

export const BudgetReports = () => {
  const { profile } = useAuth()
  const [budgets, setBudgets] = useState<Budget[]>([])
  const [selectedBudget, setSelectedBudget] = useState<string>('')
  const [analysis, setAnalysis] = useState<BudgetAnalysis[]>([])
  const [loading, setLoading] = useState(false)

  // Use React Query for real-time budget analysis
  const { data: budgetAnalysis, isLoading: analysisLoading, refetch } = useBudgetAnalysis(
    selectedBudget || null,
    !!selectedBudget
  )
  const { data: allBudgetsAnalysis } = useAllBudgetsAnalysis()

  const fetchBudgets = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      const { data, error } = await supabase
        .from('budgets')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setBudgets(data || [])
      
      if (data?.length && !selectedBudget) {
        setSelectedBudget(data[0].id)
      }
    } catch (error) {
      console.error('Error fetching budgets:', error)
    }
  }, [profile?.org_id, selectedBudget])

  const fetchBudgetAnalysis = useCallback(async () => {
    if (!profile?.org_id || !selectedBudget) return

    try {
      setLoading(true)
      
      // Get budget details
      const { data: budget, error: budgetError } = await supabase
        .from('budgets')
        .select('*')
        .eq('id', selectedBudget)
        .single()

      if (budgetError) throw budgetError

      // Get budget lines with account details
      const { data: budgetLines, error: linesError } = await supabase
        .from('budget_lines')
        .select(`
          amount,
          account_id,
          accounts(*)
        `)
        .eq('budget_id', selectedBudget)

      if (linesError) throw linesError

      const analysisData: BudgetAnalysis[] = []

      for (const line of budgetLines || []) {
        if (!line.accounts) continue

        // Get actual amounts for the budget period
        const { data: transactions, error: transError } = await supabase
          .from('transaction_lines')
          .select('debit, credit')
          .eq('org_id', profile.org_id)
          .eq('account_id', line.account_id)
          .gte('created_at', `${budget.start_date}T00:00:00`)
          .lte('created_at', `${budget.end_date}T23:59:59`)

        if (transError) throw transError

        const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
        const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0

        // Calculate actual amount based on account type
        let actualAmount = 0
        if (line.accounts.type === 'expense') {
          actualAmount = debitTotal - creditTotal
        } else if (line.accounts.type === 'income') {
          actualAmount = creditTotal - debitTotal
        } else if (line.accounts.type === 'asset') {
          actualAmount = debitTotal - creditTotal
        } else {
          actualAmount = creditTotal - debitTotal
        }

        const budgetAmount = line.amount || 0
        const variance = actualAmount - budgetAmount
        const variancePercent = budgetAmount !== 0 ? (variance / budgetAmount) * 100 : 0

        analysisData.push({
          account: line.accounts,
          budgetAmount,
          actualAmount,
          variance,
          variancePercent
        })
      }

      setAnalysis(analysisData)
    } catch (error) {
      console.error('Error fetching budget analysis:', error)
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, selectedBudget])

  useEffect(() => {
    if (profile?.org_id) {
      fetchBudgets()
    }
  }, [profile?.org_id, fetchBudgets])

  useEffect(() => {
    if (selectedBudget) {
      fetchBudgetAnalysis()
    }
  }, [selectedBudget, fetchBudgetAnalysis])

  const totalBudget = analysis.reduce((sum, item) => sum + item.budgetAmount, 0)
  const totalActual = analysis.reduce((sum, item) => sum + item.actualAmount, 0)
  const totalVariance = totalActual - totalBudget
  const totalVariancePercent = totalBudget !== 0 ? (totalVariance / totalBudget) * 100 : 0

  const overBudgetItems = analysis.filter(item => item.variance > 0)
  const underBudgetItems = analysis.filter(item => item.variance < 0)

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Label htmlFor="budget">Budget:</Label>
          <Select value={selectedBudget} onValueChange={setSelectedBudget}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Select a budget" />
            </SelectTrigger>
            <SelectContent>
              {budgets.map((budget) => (
                <SelectItem key={budget.id} value={budget.id}>
                  {budget.name} ({budget.period})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Button
          onClick={() => refetch()}
          size="sm"
          disabled={!selectedBudget || analysisLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${analysisLoading ? 'animate-spin' : ''}`} />
          {analysisLoading ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {loading ? (
        <div className="p-8">
          <LoadingSpinner text="Loading budget analysis..." showText />
        </div>
      ) : (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Total Budget</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">{formatCurrency(totalBudget)}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Total Actual</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">{formatCurrency(totalActual)}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  {totalVariance > 0 ? (
                    <TrendingUp className="h-4 w-4 text-red-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-green-500" />
                  )}
                  Variance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className={`text-2xl font-bold ${totalVariance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {formatCurrency(Math.abs(totalVariance))}
                </p>
                <p className="text-sm text-muted-foreground">
                  {totalVariancePercent.toFixed(1)}% {totalVariance > 0 ? 'over' : 'under'}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-orange-600">{overBudgetItems.length}</p>
                <p className="text-sm text-muted-foreground">Over budget</p>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Budget vs Actual Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account</TableHead>
                      <TableHead className="text-right">Budget</TableHead>
                      <TableHead className="text-right">Actual</TableHead>
                      <TableHead className="text-right">Variance</TableHead>
                      <TableHead className="text-right">%</TableHead>
                      <TableHead>Progress</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {analysis.map((item) => (
                      <TableRow key={item.account.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{item.account.name}</p>
                            <p className="text-sm text-muted-foreground">{item.account.code}</p>
                          </div>
                        </TableCell>
                        <TableCell className="text-right font-mono">
                          {formatCurrency(item.budgetAmount)}
                        </TableCell>
                        <TableCell className="text-right font-mono">
                          {formatCurrency(item.actualAmount)}
                        </TableCell>
                        <TableCell className={`text-right font-mono ${
                          item.variance > 0 ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {item.variance > 0 ? '+' : ''}{formatCurrency(item.variance)}
                        </TableCell>
                        <TableCell className={`text-right ${
                          Math.abs(item.variancePercent) > 10 ? 'text-orange-600 font-semibold' : ''
                        }`}>
                          {item.variancePercent.toFixed(1)}%
                        </TableCell>
                        <TableCell>
                          <div className="w-24">
                            <Progress 
                              value={Math.min((item.actualAmount / item.budgetAmount) * 100, 100)} 
                              className="h-2"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-bold border-t-2 bg-primary/10">
                      <TableCell>Total</TableCell>
                      <TableCell className="text-right font-mono">
                        {formatCurrency(totalBudget)}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {formatCurrency(totalActual)}
                      </TableCell>
                      <TableCell className={`text-right font-mono ${
                        totalVariance > 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {totalVariance > 0 ? '+' : ''}{formatCurrency(totalVariance)}
                      </TableCell>
                      <TableCell className="text-right">
                        {totalVariancePercent.toFixed(1)}%
                      </TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
