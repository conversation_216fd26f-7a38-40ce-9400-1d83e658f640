import { useState, useEffect, useCallback, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuthHook'
import { useDebounce } from '@/hooks/use-debounce'
import { supabase } from '@/lib/supabase'
import type { SearchResult, SearchState, SearchFilters, SearchHookReturn } from '@/types/search'
import {
  getRecentSearches,
  addRecentSearch,
  removeRecentSearch as removeRecentSearchUtil,
  clearRecentSearches as clearRecentSearchesUtil,
  formatSearchResultTitle,
  formatCurrency,
  formatDate
} from '@/lib/searchUtils'

const initialState: SearchState = {
  query: '',
  results: [],
  recentSearches: [],
  loading: false,
  error: null,
  selectedIndex: -1,
  filters: {}
}

export function useGlobalSearch(): SearchHookReturn {
  const { profile } = useAuth()
  const navigate = useNavigate()
  const [searchState, setSearchState] = useState<SearchState>(initialState)
  const debouncedQuery = useDebounce(searchState.query, 300)
  const filtersRef = useRef<SearchFilters>({})

  // Load recent searches on mount
  useEffect(() => {
    setSearchState(prev => ({
      ...prev,
      recentSearches: getRecentSearches()
    }))
  }, [])

  // Update filters ref when filters change
  useEffect(() => {
    filtersRef.current = searchState.filters
  }, [searchState.filters])

  const performSearch = useCallback(async (query: string) => {
    if (!profile?.org_id) return

    setSearchState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const searchTerm = `%${query.toLowerCase()}%`
      const results: SearchResult[] = []

      // Get current filters from ref
      const currentFilters = filtersRef.current

      // Search customers
      if (!currentFilters.types || currentFilters.types.includes('customer')) {
        const { data: customers, error: customersError } = await supabase
          .from('customers')
          .select('id, name, email, phone')
          .eq('org_id', profile.org_id)
          .or(`name.ilike.${searchTerm},email.ilike.${searchTerm},phone.ilike.${searchTerm}`)
          .limit(5)

        if (!customersError && customers) {
          customers.forEach(customer => {
            results.push({
              id: customer.id,
              type: 'customer',
              title: customer.name,
              subtitle: customer.email || customer.phone || undefined,
              description: 'Customer',
              href: `/customers`
            })
          })
        }
      }

      // Search vendors
      if (!currentFilters.types || currentFilters.types.includes('vendor')) {
        const { data: vendors, error: vendorsError } = await supabase
          .from('vendors')
          .select('id, name, email, phone')
          .eq('org_id', profile.org_id)
          .or(`name.ilike.${searchTerm},email.ilike.${searchTerm},phone.ilike.${searchTerm}`)
          .limit(5)

        if (!vendorsError && vendors) {
          vendors.forEach(vendor => {
            results.push({
              id: vendor.id,
              type: 'vendor',
              title: vendor.name,
              subtitle: vendor.email || vendor.phone || undefined,
              description: 'Vendor',
              href: `/vendors`
            })
          })
        }
      }

      // Search invoices
      if (!currentFilters.types || currentFilters.types.includes('invoice')) {
        const { data: invoices, error: invoicesError } = await supabase
          .from('invoices')
          .select('id, invoice_number, total_amount, status, date_issued, customers(name)')
          .eq('org_id', profile.org_id)
          .ilike('invoice_number', searchTerm)
          .limit(5)

        if (!invoicesError && invoices) {
          invoices.forEach(invoice => {
            results.push({
              id: invoice.id,
              type: 'invoice',
              title: invoice.invoice_number,
              subtitle: invoice.customers?.name,
              description: `Invoice • ${formatDate(invoice.date_issued)}`,
              metadata: {
                status: invoice.status,
                amount: invoice.total_amount,
                currency: 'UGX'
              },
              href: `/invoices`
            })
          })
        }
      }

      // Search bills
      if (!currentFilters.types || currentFilters.types.includes('bill')) {
        const { data: bills, error: billsError } = await supabase
          .from('bills')
          .select('id, bill_number, total_amount, status, date_issued, vendors(name)')
          .eq('org_id', profile.org_id)
          .ilike('bill_number', searchTerm)
          .limit(5)

        if (!billsError && bills) {
          bills.forEach(bill => {
            results.push({
              id: bill.id,
              type: 'bill',
              title: bill.bill_number,
              subtitle: bill.vendors?.name,
              description: `Bill • ${formatDate(bill.date_issued)}`,
              metadata: {
                status: bill.status,
                amount: bill.total_amount,
                currency: 'UGX'
              },
              href: `/bills`
            })
          })
        }
      }

      // Search payments
      if (!currentFilters.types || currentFilters.types.includes('payment')) {
        const { data: payments, error: paymentsError } = await supabase
          .from('payments')
          .select('id, amount, payment_date, status, channel, transaction_id')
          .eq('org_id', profile.org_id)
          .or(`transaction_id.ilike.${searchTerm}`)
          .limit(5)

        if (!paymentsError && payments) {
          payments.forEach(payment => {
            results.push({
              id: payment.id,
              type: 'payment',
              title: payment.transaction_id || `Payment ${payment.id.slice(0, 8)}`,
              subtitle: payment.channel,
              description: `Payment • ${formatDate(payment.payment_date)}`,
              metadata: {
                status: payment.status,
                amount: payment.amount,
                currency: 'UGX'
              },
              href: `/payments`
            })
          })
        }
      }

      // Sort results by relevance (exact matches first, then partial matches)
      const sortedResults = results.sort((a, b) => {
        const aExact = a.title.toLowerCase() === query.toLowerCase()
        const bExact = b.title.toLowerCase() === query.toLowerCase()
        if (aExact && !bExact) return -1
        if (!aExact && bExact) return 1
        return 0
      })

      setSearchState(prev => ({
        ...prev,
        results: sortedResults,
        loading: false,
        selectedIndex: sortedResults.length > 0 ? 0 : -1
      }))

      // Add to recent searches if we have results
      if (sortedResults.length > 0) {
        addRecentSearch(query, sortedResults.length)
        setSearchState(prev => ({
          ...prev,
          recentSearches: getRecentSearches()
        }))
      }

    } catch (error) {
      console.error('Search error:', error)
      setSearchState(prev => ({
        ...prev,
        loading: false,
        error: 'Search failed. Please try again.'
      }))
    }
  }, [profile?.org_id])

  // Perform search when debounced query changes
  useEffect(() => {
    if (debouncedQuery.trim() && profile?.org_id) {
      performSearch(debouncedQuery)
    } else {
      setSearchState(prev => ({
        ...prev,
        results: [],
        loading: false,
        selectedIndex: -1
      }))
    }
  }, [debouncedQuery, profile?.org_id, performSearch])

  const setQuery = useCallback((query: string) => {
    setSearchState(prev => ({
      ...prev,
      query,
      selectedIndex: -1
    }))
  }, [])

  const setFilters = useCallback((filters: SearchFilters) => {
    setSearchState(prev => ({
      ...prev,
      filters,
      selectedIndex: -1
    }))
  }, [])

  const selectResult = useCallback((index: number) => {
    setSearchState(prev => ({
      ...prev,
      selectedIndex: Math.max(-1, Math.min(index, prev.results.length - 1))
    }))
  }, [])

  const navigateToResult = useCallback((result: SearchResult) => {
    navigate(result.href)
  }, [navigate])

  const removeRecentSearch = useCallback((id: string) => {
    removeRecentSearchUtil(id)
    setSearchState(prev => ({
      ...prev,
      recentSearches: getRecentSearches()
    }))
  }, [])

  const clearRecentSearches = useCallback(() => {
    clearRecentSearchesUtil()
    setSearchState(prev => ({
      ...prev,
      recentSearches: []
    }))
  }, [])

  return {
    searchState,
    setQuery,
    setFilters,
    selectResult,
    navigateToResult,
    clearRecentSearches,
    removeRecentSearch
  }
}
