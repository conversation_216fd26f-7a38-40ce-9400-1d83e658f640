-- =====================================================
-- CREATE USER ONBOARDING STATUS VIEW
-- =====================================================
-- This migration creates a view to easily check user onboarding status
-- Date: 2025-06-29
-- Purpose: Provide computed onboarding status fields for better UX

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating user onboarding status view...';
END $$;

-- Create the user_onboarding_status view
CREATE OR REPLACE VIEW user_onboarding_status AS
SELECT 
    p.id,
    p.email,
    p.org_id,
    p.role,
    p.created_at,
    p.onboarding_completed_at,
    
    -- Computed fields for onboarding status
    CASE 
        WHEN p.org_id IS NULL THEN true
        WHEN p.onboarding_completed_at IS NULL THEN true
        ELSE false
    END as needs_onboarding,
    
    CASE 
        WHEN p.org_id IS NOT NULL AND p.onboarding_completed_at IS NOT NULL THEN true
        ELSE false
    END as onboarding_completed
    
FROM profiles p;

-- Grant access to authenticated users
GRANT SELECT ON user_onboarding_status TO authenticated;

-- Create RLS policy for the view
CREATE POLICY "Users can view their own onboarding status" ON user_onboarding_status
    FOR SELECT USING (id = auth.uid());

-- Create a function to check if a user needs onboarding
CREATE OR REPLACE FUNCTION user_needs_onboarding(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    needs_onboarding BOOLEAN;
BEGIN
    SELECT 
        CASE 
            WHEN p.org_id IS NULL THEN true
            WHEN p.onboarding_completed_at IS NULL THEN true
            ELSE false
        END
    INTO needs_onboarding
    FROM profiles p
    WHERE p.id = user_id;
    
    -- If no profile found, user needs onboarding
    RETURN COALESCE(needs_onboarding, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to mark onboarding as completed
CREATE OR REPLACE FUNCTION complete_user_onboarding(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    updated_rows INTEGER;
BEGIN
    -- Only allow users to complete their own onboarding
    IF auth.uid() != user_id THEN
        RAISE EXCEPTION 'Unauthorized: Can only complete own onboarding';
    END IF;
    
    UPDATE profiles 
    SET onboarding_completed_at = NOW()
    WHERE id = user_id 
    AND onboarding_completed_at IS NULL;
    
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    
    RETURN updated_rows > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION user_needs_onboarding(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION complete_user_onboarding(UUID) TO authenticated;

DO $$
BEGIN
    RAISE NOTICE '✅ User onboarding status view and functions created successfully';
    RAISE NOTICE '';
    RAISE NOTICE '📊 CREATED OBJECTS:';
    RAISE NOTICE '  • user_onboarding_status view - Computed onboarding status';
    RAISE NOTICE '  • user_needs_onboarding(UUID) function - Check if user needs onboarding';
    RAISE NOTICE '  • complete_user_onboarding(UUID) function - Mark onboarding complete';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 USAGE:';
    RAISE NOTICE '  SELECT * FROM user_onboarding_status WHERE id = auth.uid();';
    RAISE NOTICE '  SELECT user_needs_onboarding(auth.uid());';
    RAISE NOTICE '  SELECT complete_user_onboarding(auth.uid());';
    RAISE NOTICE '';
END $$;
