// Comprehensive Backup System Integration Tests
// Tests complete backup and restore workflows with real data

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals'
import { supabase } from '../lib/supabase'
import { BackupService } from '../lib/backup-service'
import { BackupValidator } from '../lib/backup-validator'
import { RestoreSafetyChecker } from '../lib/backup-restore-safety'
import { BackupEncryptionManager } from '../lib/backup-encryption'
import { BackupRBACManager } from '../lib/backup-rbac'

// Test data setup
const TEST_ORG_ID = 'test-org-' + Date.now()
const TEST_USER_ID = 'test-user-' + Date.now()
const TEST_ADMIN_ID = 'test-admin-' + Date.now()

interface TestContext {
  orgId: string
  userId: string
  adminId: string
  backupId?: string
  testData: {
    customers: Record<string, unknown>[]
    invoices: Record<string, unknown>[]
    payments: Record<string, unknown>[]
  }
}

describe('Backup System Integration Tests', () => {
  let testContext: TestContext

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock authentication
    mockSupabase.auth.getSession.mockResolvedValue({
      data: {
        session: {
          access_token: 'test-token'
        }
      },
      error: null
    } as MockSupabaseResponse)

    // Mock crypto operations
    mockCrypto.subtle.generateKey.mockResolvedValue({} as CryptoKey)
    mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32))
    mockCrypto.subtle.importKey.mockResolvedValue({} as CryptoKey)
    mockCrypto.subtle.encrypt.mockResolvedValue(new ArrayBuffer(64))
    mockCrypto.subtle.decrypt.mockResolvedValue(new ArrayBuffer(32))
    mockCrypto.subtle.digest.mockResolvedValue(new ArrayBuffer(32))
    mockCrypto.getRandomValues.mockReturnValue(new Uint8Array(12))

    // Mock Supabase database operations
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: {
              id: 'key-id',
              key_data: btoa('test-key-data'),
              algorithm: 'AES-GCM-256'
            },
            error: null
          })
        })
      }),
      insert: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { id: 'new-key-id' },
            error: null
          })
        })
      })
    } as MockSupabaseQueryBuilder)
  })

  describe('Complete Backup Flow', () => {
    it('should complete full backup creation, verification, and restoration cycle', async () => {
      // Step 1: Create backup
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true,
          backup_id: testBackupId,
          message: 'Backup created successfully'
        })
      } as MockFetchResponse)

      const createResult = await BackupService.createBackup(testOrgId, 'full', testUserId)

      expect(createResult.success).toBe(true)
      expect(createResult.backup_id).toBe(testBackupId)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/backup/create'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token'
          }),
          body: JSON.stringify({
            org_id: testOrgId,
            backup_type: 'full',
            created_by: testUserId
          })
        })
      )

      // Step 2: Verify backup
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          valid: true,
          checksum_match: true,
          backup_size: 1024000,
          table_count: 5,
          record_count: 1000
        })
      } as MockFetchResponse)

      const verifyResult = await BackupService.verifyBackup(testBackupId, testOrgId)

      expect(verifyResult.valid).toBe(true)
      expect(verifyResult.checksum_match).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/backup/verify'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            backup_id: testBackupId,
            org_id: testOrgId
          })
        })
      )

      // Step 3: Restore backup
      const restoreOptions = {
        restore_type: 'full',
        restore_mode: 'replace',
        selected_tables: []
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true,
          restore_job_id: 'restore-job-id',
          message: 'Restoration started successfully'
        })
      } as MockFetchResponse)

      const restoreResult = await BackupService.restoreBackup(testBackupId, testOrgId, restoreOptions)

      expect(restoreResult.success).toBe(true)
      expect(restoreResult.restore_job_id).toBe('restore-job-id')
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/backup/restore'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            backup_id: testBackupId,
            org_id: testOrgId,
            ...restoreOptions
          })
        })
      )
    })

    it('should handle backup creation failure gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: jest.fn().mockResolvedValue({
          error: 'Insufficient storage space'
        })
      } as MockFetchResponse)

      await expect(
        BackupService.createBackup(testOrgId, 'full', testUserId)
      ).rejects.toThrow('Insufficient storage space')
    })

    it('should handle verification failure', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          valid: false,
          checksum_match: false,
          error: 'Backup file corrupted'
        })
      } as MockFetchResponse)

      const result = await BackupService.verifyBackup(testBackupId, testOrgId)

      expect(result.valid).toBe(false)
      expect(result.checksum_match).toBe(false)
      expect(result.error).toBe('Backup file corrupted')
    })
  })

  describe('Encryption Integration', () => {
    it('should encrypt and decrypt data successfully', async () => {
      const testData = JSON.stringify({
        customers: [{ id: 1, name: 'Test Customer' }],
        invoices: [{ id: 1, amount: 100 }]
      })

      // Encrypt data
      const encryptResult = await BackupEncryption.encryptData(testData, testOrgId)

      expect(encryptResult).toHaveProperty('encryptedData')
      expect(encryptResult).toHaveProperty('keyId')
      expect(encryptResult).toHaveProperty('iv')
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalled()

      // Mock decryption
      const mockDecryptedBuffer = new TextEncoder().encode(testData)
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecryptedBuffer.buffer)

      // Decrypt data
      const decryptResult = await BackupEncryption.decryptData(
        encryptResult.encryptedData,
        encryptResult.keyId,
        encryptResult.iv,
        testOrgId
      )

      expect(mockCrypto.subtle.decrypt).toHaveBeenCalled()
      expect(decryptResult).toBe(testData)
    })

    it('should handle encryption key creation', async () => {
      // Mock no existing key
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'No key found' }
            })
          })
        }),
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'new-key-id' },
              error: null
            })
          })
        })
      } as MockSupabaseQueryBuilder)

      const testData = 'test data'
      const result = await BackupEncryption.encryptData(testData, testOrgId)

      expect(mockCrypto.subtle.generateKey).toHaveBeenCalledWith(
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      )
      expect(result.keyId).toBe('new-key-id')
    })
  })

  describe('Checksum Validation', () => {
    it('should calculate consistent checksums', async () => {
      const testData = 'test data for checksum'
      
      // Mock digest to return consistent hash
      const mockHash = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8])
      mockCrypto.subtle.digest.mockResolvedValue(mockHash.buffer)

      const checksum1 = await calculateChecksum(testData)
      const checksum2 = await calculateChecksum(testData)

      expect(checksum1).toBe(checksum2)
      expect(mockCrypto.subtle.digest).toHaveBeenCalledWith(
        'SHA-256',
        expect.any(ArrayBuffer)
      )
    })

    it('should produce different checksums for different data', async () => {
      const testData1 = 'test data 1'
      const testData2 = 'test data 2'
      
      // Mock different hashes for different data
      mockCrypto.subtle.digest
        .mockResolvedValueOnce(new Uint8Array([1, 2, 3, 4]).buffer)
        .mockResolvedValueOnce(new Uint8Array([5, 6, 7, 8]).buffer)

      const checksum1 = await calculateChecksum(testData1)
      const checksum2 = await calculateChecksum(testData2)

      expect(checksum1).not.toBe(checksum2)
    })
  })

  describe('Error Handling', () => {
    it('should handle network failures', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(
        BackupService.createBackup(testOrgId, 'full', testUserId)
      ).rejects.toThrow('Network error')
    })

    it('should handle authentication failures', async () => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null
      } as MockSupabaseResponse)

      await expect(
        BackupService.createBackup(testOrgId, 'full', testUserId)
      ).rejects.toThrow('No authentication token available')
    })

    it('should handle encryption key retrieval failures', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Database error' }
            })
          })
        })
      } as MockSupabaseQueryBuilder)

      await expect(
        BackupEncryption.decryptData('encrypted-data', 'key-id', 'iv', testOrgId)
      ).rejects.toThrow('Encryption key not found')
    })
  })
})

// Helper functions for test setup and cleanup

async function setupTestEnvironment(): Promise<TestContext> {
  // Create test organization
  await supabase
    .from('organizations')
    .insert({
      id: TEST_ORG_ID,
      name: 'Test Organization',
      created_at: new Date().toISOString()
    })

  // Create test users
  await supabase
    .from('profiles')
    .insert([
      {
        id: TEST_USER_ID,
        org_id: TEST_ORG_ID,
        email: '<EMAIL>',
        role: 'viewer',
        full_name: 'Test User'
      },
      {
        id: TEST_ADMIN_ID,
        org_id: TEST_ORG_ID,
        email: '<EMAIL>',
        role: 'admin',
        full_name: 'Test Admin'
      }
    ])

  // Create backup settings
  await supabase
    .from('backup_settings')
    .insert({
      org_id: TEST_ORG_ID,
      encryption_enabled: false,
      retention_days: 30,
      auto_backup_enabled: false
    })

  return {
    orgId: TEST_ORG_ID,
    userId: TEST_USER_ID,
    adminId: TEST_ADMIN_ID,
    testData: {
      customers: [],
      invoices: [],
      payments: []
    }
  }
}

async function cleanupTestEnvironment(context: TestContext): Promise<void> {
  // Delete test data in reverse dependency order
  await supabase.from('backup_errors').delete().eq('org_id', context.orgId)
  await supabase.from('backup_approval_requests').delete().eq('org_id', context.orgId)
  await supabase.from('backup_encryption_keys').delete().eq('org_id', context.orgId)
  await supabase.from('backup_metadata').delete().eq('org_id', context.orgId)
  await supabase.from('backup_settings').delete().eq('org_id', context.orgId)
  await supabase.from('payments').delete().eq('org_id', context.orgId)
  await supabase.from('invoices').delete().eq('org_id', context.orgId)
  await supabase.from('customers').delete().eq('org_id', context.orgId)
  await supabase.from('profiles').delete().eq('org_id', context.orgId)
  await supabase.from('organizations').delete().eq('id', context.orgId)

  // Clean up storage files
  if (context.backupId) {
    await supabase.storage
      .from('backups')
      .remove([`${context.orgId}/${context.backupId}.json`])
  }
}

async function resetTestData(context: TestContext): Promise<void> {
  // Clear existing test data
  await supabase.from('payments').delete().eq('org_id', context.orgId)
  await supabase.from('invoices').delete().eq('org_id', context.orgId)
  await supabase.from('customers').delete().eq('org_id', context.orgId)
}

async function createTestData(context: TestContext): Promise<void> {
  // Create test customers
  const customers = [
    { id: 'cust-1', org_id: context.orgId, name: 'Test Customer 1', email: '<EMAIL>' },
    { id: 'cust-2', org_id: context.orgId, name: 'Test Customer 2', email: '<EMAIL>' }
  ]

  await supabase.from('customers').insert(customers)
  context.testData.customers = customers

  // Create test invoices
  const invoices = [
    { id: 'inv-1', org_id: context.orgId, customer_id: 'cust-1', amount: 1000, status: 'paid' },
    { id: 'inv-2', org_id: context.orgId, customer_id: 'cust-2', amount: 2000, status: 'pending' }
  ]

  await supabase.from('invoices').insert(invoices)
  context.testData.invoices = invoices

  // Create test payments
  const payments = [
    { id: 'pay-1', org_id: context.orgId, invoice_id: 'inv-1', amount: 1000 }
  ]

  await supabase.from('payments').insert(payments)
  context.testData.payments = payments
}

async function modifyTestData(context: TestContext): Promise<void> {
  // Add new customer after backup
  await supabase
    .from('customers')
    .insert({
      id: 'cust-3',
      org_id: context.orgId,
      name: 'New Customer',
      email: '<EMAIL>'
    })

  // Update existing invoice
  await supabase
    .from('invoices')
    .update({ status: 'paid' })
    .eq('id', 'inv-2')
}
