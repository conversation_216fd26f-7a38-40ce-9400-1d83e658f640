/**
 * Enhanced Email Service with Rich Templates and Delivery Tracking
 * Extends the base email service with notification-specific features
 */

import { supabase } from '@/integrations/supabase/client'
import { sendEmail, type EmailRequest, type EmailResponse } from './emailService'
import { EmailTemplate<PERSON>enderer, EMAIL_TEMPLATES } from './emailTemplates'
import type { NotificationType, NotificationTemplateData } from '@/types/notifications'

// Define organization config type
type OrganizationConfig = Partial<{
  name: string
  tagline: string
  logo_url: string
  primary_color: string
  support_email: string
  website_url: string
}>

// Define error type for error handling
type ErrorWithMessage = Error | { message: string } | string

// Helper function to extract error message
function getErrorMessage(error: ErrorWithMessage): string {
  if (typeof error === 'string') return error
  if (error && typeof error === 'object' && 'message' in error) return error.message
  return 'Unknown error occurred'
}

export interface EmailDeliveryStatus {
  id: string
  email: string
  status: 'pending' | 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'failed'
  sent_at?: string
  delivered_at?: string
  opened_at?: string
  clicked_at?: string
  error_message?: string
  notification_id?: string
  org_id: string
  message_id?: string
  template_type?: string
  created_at: string
  updated_at: string
}

export interface EmailAnalytics {
  total: number
  sent: number
  delivered: number
  opened: number
  clicked: number
  bounced: number
  failed: number
  delivery_rate: number
  open_rate: number
  click_rate: number
  bounce_rate: number
}

/**
 * Enhanced Email Service with delivery tracking and rich templates
 */
export class EnhancedEmailService {
  private templateRenderer: EmailTemplateRenderer
  private baseUrl: string

  constructor(orgConfig: OrganizationConfig, baseUrl: string = 'https://app.kayafinance.com') {
    this.templateRenderer = new EmailTemplateRenderer(orgConfig)
    this.baseUrl = baseUrl
  }

  /**
   * Send rich notification email with tracking
   */
  async sendNotificationEmail(
    notificationType: NotificationType,
    recipientEmail: string,
    templateData: NotificationTemplateData,
    orgId: string,
    notificationId?: string
  ): Promise<EmailResponse & { deliveryId?: string }> {
    try {
      // Check if template exists
      if (!EMAIL_TEMPLATES[notificationType]) {
        throw new Error(`No email template found for notification type: ${notificationType}`)
      }

      // Render email template
      const { subject, html, text } = this.templateRenderer.renderTemplate(
        notificationType,
        templateData,
        recipientEmail,
        this.baseUrl
      )

      // Create delivery tracking record
      const deliveryId = await this.createDeliveryRecord({
        email: recipientEmail,
        status: 'pending',
        notification_id: notificationId,
        org_id: orgId,
        template_type: notificationType
      })

      // Add tracking to HTML
      const trackedHtml = this.addEmailTracking(html, deliveryId)

      // Send email using base email service
      const response = await sendEmail({
        type: 'notification',
        to: recipientEmail,
        data: {
          subject,
          html: trackedHtml,
          text,
          deliveryId,
          notificationType
        },
        org_id: orgId
      })

      if (response.success) {
        // Update delivery status
        await this.updateDeliveryStatus(deliveryId, {
          status: 'sent',
          sent_at: new Date().toISOString(),
          message_id: response.email_id
        })
      } else {
        // Update delivery status with error
        await this.updateDeliveryStatus(deliveryId, {
          status: 'failed',
          error_message: response.error || 'Unknown error'
        })
      }

      return {
        ...response,
        deliveryId
      }
    } catch (error: ErrorWithMessage) {
      console.error('Error sending notification email:', error)
      return {
        success: false,
        message: 'Failed to send notification email',
        error: getErrorMessage(error)
      }
    }
  }

  /**
   * Add email tracking pixels and links
   */
  private addEmailTracking(html: string, deliveryId: string): string {
    // Add tracking pixel for open tracking
    const trackingPixel = `<img src="${this.baseUrl}/api/email/track/open/${deliveryId}" width="1" height="1" style="display:none;" alt="" />`
    
    // Add tracking to links for click tracking
    const trackedHtml = html.replace(
      /<a\s+href="([^"]+)"([^>]*)>/gi,
      (match, url, attributes) => {
        const trackingUrl = `${this.baseUrl}/api/email/track/click/${deliveryId}?url=${encodeURIComponent(url)}`
        return `<a href="${trackingUrl}"${attributes}>`
      }
    )

    // Insert tracking pixel before closing body tag
    return trackedHtml.replace('</body>', `${trackingPixel}</body>`)
  }

  /**
   * Create email delivery tracking record
   */
  private async createDeliveryRecord(data: Partial<EmailDeliveryStatus>): Promise<string> {
    const deliveryId = `delivery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const { error } = await supabase
      .from('email_deliveries')
      .insert({
        id: deliveryId,
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (error) {
      console.error('Error creating delivery record:', error)
      // Don't throw error, just log it - email sending should continue
    }

    return deliveryId
  }

  /**
   * Update email delivery status
   */
  private async updateDeliveryStatus(
    deliveryId: string, 
    updates: Partial<EmailDeliveryStatus>
  ): Promise<void> {
    const { error } = await supabase
      .from('email_deliveries')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', deliveryId)

    if (error) {
      console.error('Error updating delivery status:', error)
    }
  }

  /**
   * Track email open
   */
  async trackEmailOpen(deliveryId: string): Promise<void> {
    await this.updateDeliveryStatus(deliveryId, {
      status: 'opened',
      opened_at: new Date().toISOString()
    })
  }

  /**
   * Track email click
   */
  async trackEmailClick(deliveryId: string, clickedUrl: string): Promise<void> {
    await this.updateDeliveryStatus(deliveryId, {
      status: 'clicked',
      clicked_at: new Date().toISOString()
    })

    // Log the click for analytics
    await supabase
      .from('email_clicks')
      .insert({
        delivery_id: deliveryId,
        clicked_url: clickedUrl,
        clicked_at: new Date().toISOString()
      })
  }

  /**
   * Get email delivery analytics for organization
   */
  async getDeliveryAnalytics(orgId: string, days: number = 30): Promise<EmailAnalytics> {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const { data, error } = await supabase
      .from('email_deliveries')
      .select('*')
      .eq('org_id', orgId)
      .gte('created_at', startDate.toISOString())

    if (error) {
      console.error('Error fetching delivery analytics:', error)
      return this.getEmptyAnalytics()
    }

    if (!data || data.length === 0) {
      return this.getEmptyAnalytics()
    }

    // Calculate analytics
    const total = data.length
    const sent = data.filter(d => d.status !== 'pending' && d.status !== 'failed').length
    const delivered = data.filter(d => d.delivered_at).length
    const opened = data.filter(d => d.opened_at).length
    const clicked = data.filter(d => d.clicked_at).length
    const bounced = data.filter(d => d.status === 'bounced').length
    const failed = data.filter(d => d.status === 'failed').length

    return {
      total,
      sent,
      delivered,
      opened,
      clicked,
      bounced,
      failed,
      delivery_rate: total > 0 ? (delivered / total) * 100 : 0,
      open_rate: delivered > 0 ? (opened / delivered) * 100 : 0,
      click_rate: opened > 0 ? (clicked / opened) * 100 : 0,
      bounce_rate: total > 0 ? (bounced / total) * 100 : 0
    }
  }

  /**
   * Get email delivery history
   */
  async getDeliveryHistory(
    orgId: string, 
    limit: number = 50,
    offset: number = 0
  ): Promise<EmailDeliveryStatus[]> {
    const { data, error } = await supabase
      .from('email_deliveries')
      .select('*')
      .eq('org_id', orgId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching delivery history:', error)
      return []
    }

    return data || []
  }

  /**
   * Get delivery status for specific notification
   */
  async getNotificationDeliveryStatus(notificationId: string): Promise<EmailDeliveryStatus[]> {
    const { data, error } = await supabase
      .from('email_deliveries')
      .select('*')
      .eq('notification_id', notificationId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching notification delivery status:', error)
      return []
    }

    return data || []
  }

  /**
   * Resend failed email
   */
  async resendFailedEmail(deliveryId: string): Promise<EmailResponse> {
    try {
      const { data: delivery, error } = await supabase
        .from('email_deliveries')
        .select('*')
        .eq('id', deliveryId)
        .single()

      if (error || !delivery) {
        throw new Error('Delivery record not found')
      }

      if (delivery.status !== 'failed') {
        throw new Error('Can only resend failed emails')
      }

      // Get original notification data
      const { data: notification, error: notificationError } = await supabase
        .from('notifications')
        .select('*')
        .eq('id', delivery.notification_id)
        .single()

      if (notificationError || !notification) {
        throw new Error('Original notification not found')
      }

      // Resend the email
      return await this.sendNotificationEmail(
        delivery.template_type as NotificationType,
        delivery.email,
        notification.data || {},
        delivery.org_id,
        delivery.notification_id
      )
    } catch (error: ErrorWithMessage) {
      console.error('Error resending email:', error)
      return {
        success: false,
        message: 'Failed to resend email',
        error: getErrorMessage(error)
      }
    }
  }

  /**
   * Get empty analytics object
   */
  private getEmptyAnalytics(): EmailAnalytics {
    return {
      total: 0,
      sent: 0,
      delivered: 0,
      opened: 0,
      clicked: 0,
      bounced: 0,
      failed: 0,
      delivery_rate: 0,
      open_rate: 0,
      click_rate: 0,
      bounce_rate: 0
    }
  }
}

/**
 * Create enhanced email service instance
 */
export function createEnhancedEmailService(orgConfig: OrganizationConfig): EnhancedEmailService {
  return new EnhancedEmailService(orgConfig)
}

/**
 * Send notification email with rich template (convenience function)
 */
export async function sendRichNotificationEmail(
  notificationType: NotificationType,
  recipientEmail: string,
  templateData: NotificationTemplateData,
  orgId: string,
  orgConfig: OrganizationConfig,
  notificationId?: string
): Promise<EmailResponse & { deliveryId?: string }> {
  const emailService = createEnhancedEmailService(orgConfig)
  return await emailService.sendNotificationEmail(
    notificationType,
    recipientEmail,
    templateData,
    orgId,
    notificationId
  )
}
