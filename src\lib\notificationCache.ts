/**
 * Notification Caching and Query Optimization
 * Implements advanced caching strategies and query optimization for notifications
 */

import { QueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { queryKeys } from '@/lib/queryKeys'
import type { 
  NotificationWithMeta, 
  NotificationFilters,
  NotificationStats 
} from '@/types/notifications'

// Cache configuration
const CACHE_CONFIG = {
  NOTIFICATION_STALE_TIME: 30 * 1000, // 30 seconds
  NOTIFICATION_CACHE_TIME: 5 * 60 * 1000, // 5 minutes
  COUNT_STALE_TIME: 10 * 1000, // 10 seconds
  COUNT_CACHE_TIME: 2 * 60 * 1000, // 2 minutes
  STATS_STALE_TIME: 60 * 1000, // 1 minute
  STATS_CACHE_TIME: 10 * 60 * 1000, // 10 minutes
  MAX_CACHE_SIZE: 100, // Maximum number of cached queries
  PREFETCH_THRESHOLD: 0.8 // Prefetch when 80% through current data
}

/**
 * Notification Cache Manager
 */
export class NotificationCacheManager {
  private queryClient: QueryClient
  private prefetchQueue: Set<string> = new Set()

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient
  }

  /**
   * Optimized notification query with caching
   */
  async getOptimizedNotifications(
    userId: string,
    orgId: string,
    filters?: NotificationFilters,
    useCache: boolean = true
  ): Promise<NotificationWithMeta[]> {
    const cacheKey = queryKeys.notifications.filtered(userId, filters || {})

    // Try to get from cache first
    if (useCache) {
      const cachedData = this.queryClient.getQueryData<NotificationWithMeta[]>(cacheKey)
      if (cachedData) {
        return cachedData
      }
    }

    // Build optimized query
    let query = supabase
      .from('notifications')
      .select(`
        id,
        type,
        category,
        priority,
        title,
        message,
        data,
        entity_type,
        entity_id,
        is_read,
        is_archived,
        read_at,
        expires_at,
        created_at,
        updated_at
      `)
      .or(`user_id.eq.${userId},and(user_id.is.null,org_id.eq.${orgId})`)
      .order('created_at', { ascending: false })

    // Apply filters with optimized conditions
    if (filters) {
      // Use indexed columns first for better performance
      if (filters.is_read !== undefined) {
        query = query.eq('is_read', filters.is_read)
      }
      if (filters.is_archived !== undefined) {
        query = query.eq('is_archived', filters.is_archived)
      }
      if (filters.category) {
        query = query.eq('category', filters.category)
      }
      if (filters.priority) {
        query = query.eq('priority', filters.priority)
      }
      if (filters.entity_type) {
        query = query.eq('entity_type', filters.entity_type)
      }
      
      // Date filters (use indexes)
      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from)
      }
      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to)
      }
    }

    // Limit results for performance
    query = query.limit(50)

    try {
      const { data, error } = await query

      if (error) throw error

      const enhancedData = (data || []).map(notification => ({
        ...notification,
        isExpired: notification.expires_at ? new Date(notification.expires_at) < new Date() : false,
        timeAgo: this.getTimeAgo(notification.created_at)
      }))

      // Cache the results
      if (useCache) {
        this.queryClient.setQueryData(cacheKey, enhancedData, {
          updatedAt: Date.now()
        })
      }

      return enhancedData
    } catch (error) {
      console.error('Error in optimized notification query:', error)
      throw error
    }
  }

  /**
   * Optimized notification count query
   */
  async getOptimizedNotificationCount(
    userId: string,
    orgId: string,
    useCache: boolean = true
  ): Promise<number> {
    const cacheKey = queryKeys.notifications.count(userId)

    // Try cache first
    if (useCache) {
      const cachedCount = this.queryClient.getQueryData<number>(cacheKey)
      if (cachedCount !== undefined) {
        return cachedCount
      }
    }

    try {
      // Use optimized count query with indexes
      const { count, error } = await supabase
        .from('notifications')
        .select('id', { count: 'exact', head: true })
        .or(`user_id.eq.${userId},and(user_id.is.null,org_id.eq.${orgId})`)
        .eq('is_read', false)
        .eq('is_archived', false)

      if (error) throw error

      const result = count || 0

      // Cache the count
      if (useCache) {
        this.queryClient.setQueryData(cacheKey, result, {
          updatedAt: Date.now()
        })
      }

      return result
    } catch (error) {
      console.error('Error in optimized count query:', error)
      return 0
    }
  }

  /**
   * Prefetch notifications based on user behavior
   */
  async prefetchNotifications(
    userId: string,
    orgId: string,
    currentFilters?: NotificationFilters
  ): Promise<void> {
    const prefetchKey = `${userId}-${JSON.stringify(currentFilters)}`
    
    if (this.prefetchQueue.has(prefetchKey)) {
      return // Already prefetching
    }

    this.prefetchQueue.add(prefetchKey)

    try {
      // Prefetch common filter combinations
      const commonFilters: NotificationFilters[] = [
        { is_read: false }, // Unread notifications
        { category: 'financial' }, // Financial notifications
        { priority: 'high' }, // High priority
        { priority: 'urgent' }, // Urgent notifications
        {} // All notifications
      ]

      const prefetchPromises = commonFilters.map(filters => 
        this.queryClient.prefetchQuery({
          queryKey: queryKeys.notifications.filtered(userId, filters),
          queryFn: () => this.getOptimizedNotifications(userId, orgId, filters, false),
          staleTime: CACHE_CONFIG.NOTIFICATION_STALE_TIME
        })
      )

      await Promise.allSettled(prefetchPromises)
    } catch (error) {
      console.error('Error prefetching notifications:', error)
    } finally {
      this.prefetchQueue.delete(prefetchKey)
    }
  }

  /**
   * Invalidate notification caches
   */
  invalidateNotificationCaches(userId: string): void {
    // Invalidate all notification-related queries for the user
    this.queryClient.invalidateQueries({
      queryKey: queryKeys.notifications.all(userId)
    })
    this.queryClient.invalidateQueries({
      queryKey: queryKeys.notifications.count(userId)
    })
    this.queryClient.invalidateQueries({
      queryKey: queryKeys.notifications.unread(userId)
    })
  }

  /**
   * Update notification in cache
   */
  updateNotificationInCache(
    userId: string,
    notificationId: string,
    updates: Partial<NotificationWithMeta>
  ): void {
    // Update all relevant cached queries
    const allQueries = this.queryClient.getQueriesData({
      queryKey: queryKeys.notifications.all(userId)
    })

    allQueries.forEach(([queryKey, data]) => {
      if (Array.isArray(data)) {
        const updatedData = data.map(notification => 
          notification.id === notificationId 
            ? { ...notification, ...updates }
            : notification
        )
        this.queryClient.setQueryData(queryKey, updatedData)
      }
    })
  }

  /**
   * Remove notification from cache
   */
  removeNotificationFromCache(userId: string, notificationId: string): void {
    const allQueries = this.queryClient.getQueriesData({
      queryKey: queryKeys.notifications.all(userId)
    })

    allQueries.forEach(([queryKey, data]) => {
      if (Array.isArray(data)) {
        const filteredData = data.filter(notification => notification.id !== notificationId)
        this.queryClient.setQueryData(queryKey, filteredData)
      }
    })
  }

  /**
   * Add notification to cache
   */
  addNotificationToCache(userId: string, notification: NotificationWithMeta): void {
    const allQueries = this.queryClient.getQueriesData({
      queryKey: queryKeys.notifications.all(userId)
    })

    allQueries.forEach(([queryKey, data]) => {
      if (Array.isArray(data)) {
        // Add to beginning of array (most recent first)
        const updatedData = [notification, ...data]
        this.queryClient.setQueryData(queryKey, updatedData)
      }
    })

    // Update count cache
    const countKey = queryKeys.notifications.count(userId)
    const currentCount = this.queryClient.getQueryData<number>(countKey)
    if (currentCount !== undefined && !notification.is_read) {
      this.queryClient.setQueryData(countKey, currentCount + 1)
    }
  }

  /**
   * Clean up old cache entries
   */
  cleanupCache(): void {
    const now = Date.now()
    const maxAge = CACHE_CONFIG.NOTIFICATION_CACHE_TIME

    // Get all cached queries
    const cache = this.queryClient.getQueryCache()
    const queries = cache.getAll()

    queries.forEach(query => {
      const isNotificationQuery = query.queryKey.some(key => 
        typeof key === 'string' && key.includes('notifications')
      )

      if (isNotificationQuery && query.state.dataUpdatedAt) {
        const age = now - query.state.dataUpdatedAt
        if (age > maxAge) {
          cache.remove(query)
        }
      }
    })
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalQueries: number
    notificationQueries: number
    cacheHitRate: number
    memoryUsage: number
  } {
    const cache = this.queryClient.getQueryCache()
    const queries = cache.getAll()
    
    const notificationQueries = queries.filter(query =>
      query.queryKey.some(key => 
        typeof key === 'string' && key.includes('notifications')
      )
    )

    const totalHits = queries.reduce((sum, query) => sum + (query.state.fetchFailureCount || 0), 0)
    const totalFetches = queries.reduce((sum, query) => sum + (query.state.dataUpdateCount || 0), 0)
    const hitRate = totalFetches > 0 ? (totalFetches - totalHits) / totalFetches : 0

    return {
      totalQueries: queries.length,
      notificationQueries: notificationQueries.length,
      cacheHitRate: hitRate,
      memoryUsage: JSON.stringify(cache).length // Rough estimate
    }
  }

  /**
   * Helper function to calculate time ago
   */
  private getTimeAgo(dateString: string): string {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }
}

// Export singleton instance
let cacheManager: NotificationCacheManager | null = null

export function getNotificationCacheManager(queryClient: QueryClient): NotificationCacheManager {
  if (!cacheManager) {
    cacheManager = new NotificationCacheManager(queryClient)
  }
  return cacheManager
}

// Export cache configuration
export { CACHE_CONFIG }
