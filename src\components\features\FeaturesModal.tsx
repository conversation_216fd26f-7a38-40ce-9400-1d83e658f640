import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { LoadingButton } from '@/components/ui/loading';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import {
  Smartphone,
  Building2,
  FileText,
  Shield,
  TrendingUp,
  Wifi,
  Plus,
  Check,
  Clock,
  Crown,
  Search,
  Filter,
  Calculator,
  BarChart3
} from 'lucide-react';

interface FeaturesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  status: 'available' | 'coming_soon' | 'enterprise';
  category: 'integration' | 'core' | 'advanced';
  hideButton?: boolean; // Optional flag to hide the action button
}

const features: Feature[] = [
  {
    id: 'mobile_money',
    title: 'Mobile Money Integration',
    description: 'Native support for MTN Mobile Money and Airtel Money with automatic transaction recording and reconciliation.',
    icon: Smartphone,
    status: 'enterprise',
    category: 'integration'
  },
  {
    id: 'bank_integration',
    title: 'Bank Integration',
    description: 'Connect with major Ugandan banks for automatic transaction import and account reconciliation.',
    icon: Building2,
    status: 'enterprise',
    category: 'integration'
  },
  {
    id: 'ura_integration',
    title: 'URA Integration',
    description: 'Direct integration with Uganda Revenue Authority for seamless tax filing and compliance reporting.',
    icon: FileText,
    status: 'enterprise',
    category: 'integration'
  },
  {
    id: 'advanced_audit',
    title: 'Advanced Audit Trail',
    description: 'Enhanced logging and tracking with comprehensive change history and compliance reporting.',
    icon: Shield,
    status: 'available',
    category: 'core',
    hideButton: true
  },
  {
    id: 'budget_management',
    title: 'Budget Management',
    description: 'Multi-level budget approval workflows with variance analysis and performance tracking.',
    icon: TrendingUp,
    status: 'available',
    category: 'advanced',
    hideButton: true
  },
  {
    id: 'double_entry',
    title: 'Double Entry System',
    description: 'Comprehensive double-entry accounting with automatic validation, real-time reconciliation, and complete audit trails for all financial transactions.',
    icon: Calculator,
    status: 'available',
    category: 'core',
    hideButton: true
  },
  {
    id: 'reporting_cashflow',
    title: 'Reporting & Cash Flow Analysis',
    description: 'Complete financial reporting suite including Trial Balance, P&L, Balance Sheet, Cash Flow statements, and advanced analytics.',
    icon: BarChart3,
    status: 'available',
    category: 'core',
    hideButton: true
  },
  {
    id: 'offline_capability',
    title: 'Offline Capability',
    description: 'Work without internet connection with automatic data synchronization when online.',
    icon: Wifi,
    status: 'enterprise',
    category: 'core'
  },
  {
    id: 'multi_currency',
    title: 'Multi-Currency Support',
    description: 'Handle multiple currencies with automatic exchange rate updates and conversion tracking.',
    icon: TrendingUp,
    status: 'coming_soon',
    category: 'advanced'
  },
  {
    id: 'api_access',
    title: 'API Access',
    description: 'RESTful API access for custom integrations and third-party application connections.',
    icon: Shield,
    status: 'enterprise',
    category: 'advanced'
  }
];

const statusConfig = {
  available: {
    label: 'Available',
    variant: 'default' as const,
    icon: Check,
    buttonText: 'Add Feature'
  },
  coming_soon: {
    label: 'Coming Soon',
    variant: 'secondary' as const,
    icon: Clock,
    buttonText: 'Notify Me'
  },
  enterprise: {
    label: 'Enterprise',
    variant: 'outline' as const,
    icon: Crown,
    buttonText: 'Contact Us'
  }
};

export function FeaturesModal({ open, onOpenChange }: FeaturesModalProps) {
  const { toast } = useToast();
  const [addingFeature, setAddingFeature] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  const handleAddFeature = async (feature: Feature) => {
    setAddingFeature(feature.id);

    try {
      if (feature.status === 'enterprise') {
        // Redirect to WhatsApp for enterprise features
        const message = encodeURIComponent(`Hi! I'm interested in the ${feature.title} feature for KAYA Finance. Can you provide more information?`);
        window.open(`https://wa.me/256777959328?text=${message}`, '_blank');

        toast({
          title: "Redirecting to WhatsApp",
          description: "You'll be connected with our team to discuss enterprise features.",
        });
        setAddingFeature(null);
        return;
      }

      // Simulate API call for other features
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (feature.status === 'available') {
        toast({
          title: "Feature Added",
          description: `${feature.title} has been added to your platform.`,
        });
      } else if (feature.status === 'coming_soon') {
        toast({
          title: "Notification Set",
          description: `You'll be notified when ${feature.title} becomes available.`,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add feature. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAddingFeature(null);
    }
  };

  const getStatusIcon = (status: Feature['status']) => {
    const config = statusConfig[status];
    const IconComponent = config.icon;
    return <IconComponent className="h-4 w-4" />;
  };

  // Filter features based on search and filters
  const filteredFeatures = features.filter((feature) => {
    const matchesSearch = feature.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         feature.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || feature.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || feature.status === selectedStatus;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Platform Features</DialogTitle>
          <DialogDescription>
            Enhance your KAYA Finance platform with powerful integrations and advanced features.
          </DialogDescription>
        </DialogHeader>

        {/* Feature Summary */}
        <div className="grid grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">
              {features.filter(f => f.status === 'available').length}
            </div>
            <div className="text-sm text-muted-foreground">Available Now</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {features.filter(f => f.status === 'coming_soon').length}
            </div>
            <div className="text-sm text-muted-foreground">Coming Soon</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {features.filter(f => f.status === 'enterprise').length}
            </div>
            <div className="text-sm text-muted-foreground">Enterprise</div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="space-y-4 mt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Features</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search features..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="integration">Integration</SelectItem>
                    <SelectItem value="core">Core</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[130px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="available">Available</SelectItem>
                    <SelectItem value="coming_soon">Coming Soon</SelectItem>
                    <SelectItem value="enterprise">Enterprise</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('all');
                    setSelectedStatus('all');
                  }}
                  className="h-10"
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>

          {/* Results count */}
          <div className="text-sm text-muted-foreground">
            Showing {filteredFeatures.length} of {features.length} features
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {filteredFeatures.length === 0 ? (
            <div className="col-span-full text-center py-8">
              <Filter className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No features found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          ) : (
            filteredFeatures.map((feature) => {
            const config = statusConfig[feature.status];
            const isLoading = addingFeature === feature.id;
            
            return (
              <Card key={feature.id} className="relative">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="bg-primary/10 p-2 rounded-lg">
                        <feature.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{feature.title}</CardTitle>
                        <Badge variant={config.variant} className="mt-1">
                          {getStatusIcon(feature.status)}
                          <span className="ml-1">{config.label}</span>
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    {feature.description}
                  </p>
                  {!feature.hideButton && (
                    <LoadingButton
                      onClick={() => handleAddFeature(feature)}
                      loading={isLoading}
                      className="w-full"
                      variant={feature.status === 'available' ? 'default' : 'outline'}
                      loadingText="Processing..."
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {config.buttonText}
                    </LoadingButton>
                  )}
                  {feature.hideButton && feature.status === 'available' && (
                    <div className="w-full text-center py-2">
                      <span className="text-sm text-green-600 font-medium flex items-center justify-center gap-2">
                        <Check className="h-4 w-4" />
                        Already Available
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
            })
          )}
        </div>
        
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="font-semibold mb-2">Need Help?</h4>
          <p className="text-sm text-muted-foreground">
            Contact our support team on{' '}
            <a
              href="https://wa.me/256777959328?text=Hi!%20I%20need%20help%20with%20KAYA%20Finance%20features."
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              WhatsApp
            </a>{' '}
            for assistance with feature setup and configuration.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
