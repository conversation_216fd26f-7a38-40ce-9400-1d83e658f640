import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'
import { queryKeys } from '@/lib/queryKeys'
import type {
  PurchaseOrder,
  PurchaseOrderWithVendor,
  PurchaseOrderWithDetails,
  PurchaseOrderFormData,
  PurchaseOrderLineData,
  PurchaseOrderReceiptFormData,
  PurchaseOrderStatus,
  PurchaseOrderFilters
} from '@/types/purchase-orders'

/**
 * Hook to fetch all purchase orders for the organization
 */
export function usePurchaseOrders(filters?: PurchaseOrderFilters) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['purchase-orders', profile?.org_id, filters],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      let query = supabase
        .from('purchase_orders')
        .select(`
          *,
          vendor:vendors(
            id,
            name,
            email,
            phone,
            address
          )
        `)
        .eq('org_id', profile.org_id)
        .order('date_issued', { ascending: false })

      // Apply filters
      if (filters?.status && filters.status.length > 0) {
        query = query.in('status', filters.status)
      }
      if (filters?.vendor_id) {
        query = query.eq('vendor_id', filters.vendor_id)
      }
      if (filters?.date_from) {
        query = query.gte('date_issued', filters.date_from)
      }
      if (filters?.date_to) {
        query = query.lte('date_issued', filters.date_to)
      }
      if (filters?.amount_min) {
        query = query.gte('total_amount', filters.amount_min)
      }
      if (filters?.amount_max) {
        query = query.lte('total_amount', filters.amount_max)
      }

      const { data, error } = await query

      if (error) throw error
      return data as PurchaseOrderWithVendor[]
    },
    enabled: !!profile?.org_id,
  })
}

/**
 * Hook to fetch a single purchase order with full details
 */
export function usePurchaseOrder(purchaseOrderId: string | null) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['purchase-order', purchaseOrderId],
    queryFn: async () => {
      if (!profile?.org_id || !purchaseOrderId) throw new Error('Missing required parameters')

      const { data, error } = await supabase
        .from('purchase_orders')
        .select(`
          *,
          vendor:vendors(*),
          lines:purchase_order_lines(
            *,
            product:products(
              id,
              name,
              sku,
              cost_price,
              selling_price,
              track_inventory
            )
          ),
          receipts:purchase_order_receipts(
            *,
            lines:purchase_order_receipt_lines(
              *,
              po_line:purchase_order_lines(
                *,
                product:products(
                  id,
                  name,
                  sku
                )
              )
            ),
            received_by_user:auth.users(
              id,
              email,
              raw_user_meta_data
            )
          )
        `)
        .eq('id', purchaseOrderId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data as PurchaseOrderWithDetails
    },
    enabled: !!profile?.org_id && !!purchaseOrderId,
  })
}

/**
 * Hook to create a new purchase order
 */
export function useCreatePurchaseOrder() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      formData,
      lines
    }: {
      formData: PurchaseOrderFormData
      lines: PurchaseOrderLineData[]
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Calculate totals
      const totals = lines.reduce((acc, line) => {
        const lineTotal = line.quantity * line.unit_price
        const lineTax = lineTotal * (line.tax_rate_pct / 100)
        return {
          subtotal: acc.subtotal + lineTotal,
          taxAmount: acc.taxAmount + lineTax
        }
      }, { subtotal: 0, taxAmount: 0 })

      // Create purchase order
      const { data: purchaseOrder, error: poError } = await supabase
        .from('purchase_orders')
        .insert({
          ...formData,
          org_id: profile.org_id,
          subtotal: totals.subtotal,
          tax_amount: totals.taxAmount,
          total_amount: totals.subtotal + totals.taxAmount,
          created_by: profile.id
        })
        .select()
        .single()

      if (poError) throw poError

      // Create purchase order lines
      if (lines.length > 0) {
        const linesData = lines.map(line => ({
          ...line,
          org_id: profile.org_id,
          purchase_order_id: purchaseOrder.id,
          product_id: line.product_id || null
        }))

        const { error: linesError } = await supabase
          .from('purchase_order_lines')
          .insert(linesData)

        if (linesError) throw linesError
      }

      return purchaseOrder
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders', profile?.org_id] })
      toast.success('Purchase order created successfully')
    },
    onError: (error) => {
      console.error('Failed to create purchase order:', error)
      toast.error('Failed to create purchase order')
    }
  })
}

/**
 * Hook to update a purchase order
 */
export function useUpdatePurchaseOrder() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      purchaseOrderId,
      formData,
      lines
    }: {
      purchaseOrderId: string
      formData: PurchaseOrderFormData
      lines: PurchaseOrderLineData[]
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Calculate totals
      const totals = lines.reduce((acc, line) => {
        const lineTotal = line.quantity * line.unit_price
        const lineTax = lineTotal * (line.tax_rate_pct / 100)
        return {
          subtotal: acc.subtotal + lineTotal,
          taxAmount: acc.taxAmount + lineTax
        }
      }, { subtotal: 0, taxAmount: 0 })

      // Update purchase order
      const { error: poError } = await supabase
        .from('purchase_orders')
        .update({
          ...formData,
          subtotal: totals.subtotal,
          tax_amount: totals.taxAmount,
          total_amount: totals.subtotal + totals.taxAmount,
          updated_at: new Date().toISOString()
        })
        .eq('id', purchaseOrderId)
        .eq('org_id', profile.org_id)

      if (poError) throw poError

      // Delete existing lines
      const { error: deleteError } = await supabase
        .from('purchase_order_lines')
        .delete()
        .eq('purchase_order_id', purchaseOrderId)
        .eq('org_id', profile.org_id)

      if (deleteError) throw deleteError

      // Create new lines
      if (lines.length > 0) {
        const linesData = lines.map(line => ({
          ...line,
          org_id: profile.org_id,
          purchase_order_id: purchaseOrderId,
          product_id: line.product_id || null
        }))

        const { error: linesError } = await supabase
          .from('purchase_order_lines')
          .insert(linesData)

        if (linesError) throw linesError
      }
    },
    onSuccess: (_, { purchaseOrderId }) => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: ['purchase-order', purchaseOrderId] })
      toast.success('Purchase order updated successfully')
    },
    onError: (error) => {
      console.error('Failed to update purchase order:', error)
      toast.error('Failed to update purchase order')
    }
  })
}

/**
 * Hook to update purchase order status
 */
export function useUpdatePurchaseOrderStatus() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      purchaseOrderId,
      status
    }: {
      purchaseOrderId: string
      status: PurchaseOrderStatus
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { error } = await supabase
        .from('purchase_orders')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', purchaseOrderId)
        .eq('org_id', profile.org_id)

      if (error) throw error
    },
    onSuccess: (_, { purchaseOrderId }) => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: ['purchase-order', purchaseOrderId] })
      toast.success('Purchase order status updated successfully')
    },
    onError: (error) => {
      console.error('Failed to update purchase order status:', error)
      toast.error('Failed to update purchase order status')
    }
  })
}

/**
 * Hook to create a purchase order receipt
 */
export function useCreatePurchaseOrderReceipt() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (receiptData: PurchaseOrderReceiptFormData) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Create receipt
      const { data: receipt, error: receiptError } = await supabase
        .from('purchase_order_receipts')
        .insert({
          org_id: profile.org_id,
          purchase_order_id: receiptData.purchase_order_id,
          receipt_number: receiptData.receipt_number,
          receipt_date: receiptData.receipt_date,
          notes: receiptData.notes,
          received_by: profile.id
        })
        .select()
        .single()

      if (receiptError) throw receiptError

      // Create receipt lines
      if (receiptData.lines.length > 0) {
        const linesData = receiptData.lines.map(line => ({
          ...line,
          org_id: profile.org_id,
          receipt_id: receipt.id
        }))

        const { error: linesError } = await supabase
          .from('purchase_order_receipt_lines')
          .insert(linesData)

        if (linesError) throw linesError
      }

      return receipt
    },
    onSuccess: (_, { purchase_order_id }) => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: ['purchase-order', purchase_order_id] })
      toast.success('Purchase order receipt created successfully')
    },
    onError: (error) => {
      console.error('Failed to create purchase order receipt:', error)
      toast.error('Failed to create purchase order receipt')
    }
  })
}

/**
 * Hook to delete a purchase order
 */
export function useDeletePurchaseOrder() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (purchaseOrderId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { error } = await supabase
        .from('purchase_orders')
        .delete()
        .eq('id', purchaseOrderId)
        .eq('org_id', profile.org_id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders', profile?.org_id] })
      toast.success('Purchase order deleted successfully')
    },
    onError: (error) => {
      console.error('Failed to delete purchase order:', error)
      toast.error('Failed to delete purchase order')
    }
  })
}
