import { Navigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuthHook'
import { LoadingPage } from '@/components/ui/loading'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'admin' | 'accountant'
}

export const ProtectedRoute = ({ children, requiredRole }: ProtectedRouteProps) => {
  const { user, profile, loading } = useAuth()

  if (loading) {
    return <LoadingPage text="Loading..." />
  }

  if (!user || !profile) {
    return <Navigate to="/" replace />
  }

  if (requiredRole && profile.role !== requiredRole) {
    return <Navigate to="/dashboard" replace />
  }

  return <>{children}</>
}
