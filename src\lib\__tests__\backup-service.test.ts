import { BackupService, BackupEncryption, calculateChecksum } from '../backup-service'
import { supabase } from '../supabase'

// Mock dependencies
jest.mock('../supabase')
const mockSupabase = supabase as jest.Mocked<typeof supabase>

// Mock fetch
global.fetch = jest.fn()
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

// Mock crypto.subtle for encryption tests
const mockCrypto = {
  subtle: {
    generateKey: jest.fn(),
    exportKey: jest.fn(),
    importKey: jest.fn(),
    encrypt: jest.fn(),
    decrypt: jest.fn(),
    digest: jest.fn()
  },
  getRandomValues: jest.fn()
}

Object.defineProperty(global, 'crypto', {
  value: mockCrypto
})

describe('BackupService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock auth session
    mockSupabase.auth.getSession.mockResolvedValue({
      data: {
        session: {
          access_token: 'test-token'
        }
      },
      error: null
    } as { data: { session: { access_token: string } | null }; error: null })

    // Mock successful fetch response
    mockFetch.mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue({ success: true })
    } as { ok: boolean; json: jest.Mock })
  })

  describe('createBackup', () => {
    it('should call the correct endpoint with proper parameters', async () => {
      const result = await BackupService.createBackup('test-org-id', 'full', 'test-user-id')

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/backup/create'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token'
          }),
          body: JSON.stringify({
            org_id: 'test-org-id',
            backup_type: 'full',
            created_by: 'test-user-id'
          })
        })
      )

      expect(result).toEqual({ success: true })
    })

    it('should handle API errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        json: jest.fn().mockResolvedValue({ error: 'API Error' })
      } as { ok: boolean; json: jest.Mock })

      await expect(
        BackupService.createBackup('test-org-id', 'full', 'test-user-id')
      ).rejects.toThrow('API Error')
    })

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(
        BackupService.createBackup('test-org-id', 'full', 'test-user-id')
      ).rejects.toThrow('Network error')
    })
  })

  describe('verifyBackup', () => {
    it('should call the verify endpoint correctly', async () => {
      const result = await BackupService.verifyBackup('backup-id', 'org-id')

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/backup/verify'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            backup_id: 'backup-id',
            org_id: 'org-id'
          })
        })
      )

      expect(result).toEqual({ success: true })
    })
  })

  describe('restoreBackup', () => {
    it('should call the restore endpoint with options', async () => {
      const options = {
        restoreType: 'partial',
        selectedTables: ['customers', 'invoices']
      }

      const result = await BackupService.restoreBackup('backup-id', 'org-id', options)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/backup/restore'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            backup_id: 'backup-id',
            org_id: 'org-id',
            ...options
          })
        })
      )

      expect(result).toEqual({ success: true })
    })
  })

  describe('authentication', () => {
    it('should handle missing authentication token', async () => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null
      } as { data: { session: null }; error: null })

      await expect(
        BackupService.createBackup('test-org-id', 'full', 'test-user-id')
      ).rejects.toThrow('No authentication token available')
    })
  })
})

describe('BackupEncryption', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Mock crypto operations
    mockCrypto.subtle.generateKey.mockResolvedValue({} as CryptoKey)
    mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32))
    mockCrypto.subtle.importKey.mockResolvedValue({} as CryptoKey)
    mockCrypto.subtle.encrypt.mockResolvedValue(new ArrayBuffer(64))
    mockCrypto.subtle.decrypt.mockResolvedValue(new ArrayBuffer(32))
    mockCrypto.getRandomValues.mockReturnValue(new Uint8Array(12))

    // Mock Supabase operations
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: {
              id: 'key-id',
              key_data: 'base64-key-data'
            },
            error: null
          })
        })
      }),
      insert: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { id: 'new-key-id' },
            error: null
          })
        })
      })
    } as { from: jest.Mock })
  })

  describe('encryptData', () => {
    it('should encrypt data successfully', async () => {
      const result = await BackupEncryption.encryptData('test data', 'org-id')

      expect(result).toHaveProperty('encryptedData')
      expect(result).toHaveProperty('keyId')
      expect(result).toHaveProperty('iv')
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalled()
    })

    it('should create new key if none exists', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'No key found' }
            })
          })
        }),
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'new-key-id' },
              error: null
            })
          })
        })
      } as { from: jest.Mock })

      const result = await BackupEncryption.encryptData('test data', 'org-id')

      expect(mockCrypto.subtle.generateKey).toHaveBeenCalled()
      expect(result.keyId).toBe('new-key-id')
    })
  })

  describe('decryptData', () => {
    it('should decrypt data successfully', async () => {
      const result = await BackupEncryption.decryptData(
        'encrypted-data',
        'key-id',
        'iv-data',
        'org-id'
      )

      expect(mockCrypto.subtle.decrypt).toHaveBeenCalled()
      expect(typeof result).toBe('string')
    })

    it('should throw error if key not found', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Key not found' }
            })
          })
        })
      } as { from: jest.Mock })

      await expect(
        BackupEncryption.decryptData('encrypted-data', 'key-id', 'iv-data', 'org-id')
      ).rejects.toThrow('Encryption key not found')
    })
  })
})

describe('calculateChecksum', () => {
  beforeEach(() => {
    mockCrypto.subtle.digest.mockResolvedValue(new ArrayBuffer(32))
  })

  it('should calculate SHA-256 checksum', async () => {
    const result = await calculateChecksum('test data')

    expect(mockCrypto.subtle.digest).toHaveBeenCalledWith(
      'SHA-256',
      expect.any(ArrayBuffer)
    )
    expect(typeof result).toBe('string')
  })
})
