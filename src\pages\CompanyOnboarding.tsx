
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuthHook';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { BookOpen } from 'lucide-react';
import type { AccountType } from '@/types/database';
import type { Database } from '@/generated/db';
import { validatePhoneNumber, validateTinNumber, formatPhoneNumber, getPhoneNumberHelper, getTinNumberHelper } from '@/lib/validators';
import { useAuditLogger } from '@/lib/auditLogger';
import { OnboardingDebug } from '@/components/debug/OnboardingDebug';

export const CompanyOnboarding = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const auditLogger = useAuditLogger();
  
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    companyName: '',
    tinNumber: '',
    businessRegNumber: '',
    uraTaxOffice: '',
    phone: '',
    role: 'accountant' as 'admin' | 'accountant'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation functions
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Company name validation
    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
    } else if (formData.companyName.trim().length < 2) {
      newErrors.companyName = 'Company name must be at least 2 characters';
    }

    // Phone validation
    if (formData.phone.trim()) {
      const phoneValidation = validatePhoneNumber(formData.phone);
      if (!phoneValidation.isValid) {
        newErrors.phone = phoneValidation.message || 'Invalid phone number';
      }
    }

    // TIN validation
    if (formData.tinNumber.trim()) {
      const tinValidation = validateTinNumber(formData.tinNumber);
      if (!tinValidation.isValid) {
        newErrors.tinNumber = tinValidation.message || 'Invalid TIN number';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFieldBlur = (fieldName: string) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    validateForm();
  };

  const handleFieldChange = (fieldName: string, value: string) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }));

    // Auto-format phone number
    if (fieldName === 'phone') {
      const formatted = formatPhoneNumber(value);
      if (formatted !== value) {
        setFormData(prev => ({ ...prev, phone: formatted }));
      }
    }

    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({ ...prev, [fieldName]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    // Validate form before submission
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors below and try again.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Set audit logger context
      auditLogger.setContext({ userId: user.id, orgId: null });

      // Debug: Check user authentication state
      const session = await supabase.auth.getSession()
      console.log('🔐 User authentication state:', {
        userId: user.id,
        email: user.email,
        isAuthenticated: !!user,
        sessionExists: !!session.data.session,
        sessionError: session.error?.message
      });

      // Debug: Check Supabase client
      console.log('🔧 Supabase client check:', {
        clientExists: !!supabase,
        fromMethod: typeof supabase.from,
        rpcMethod: typeof supabase.rpc,
        authMethod: typeof supabase.auth
      });

      // Debug: Check authentication context from database
      console.log('🔐 Checking database authentication context...');
      const { data: authContext } = await supabase.rpc('debug_auth_context');

      console.log('Auth Debug:', {
        user: user?.id,
        session: !!session.data.session,
        authContext: authContext?.[0]
      });

      // Method 1: Try using the helper function for atomic operation
      console.log('🚀 Attempting to create organization with helper function...');
      const { data: helperResult, error: helperError } = await supabase
        .rpc('create_organization_with_profile', {
          org_name: formData.companyName,
          tin_number: formData.tinNumber || null,
          business_reg_number: formData.businessRegNumber || null,
          ura_tax_office: formData.uraTaxOffice || null,
          user_phone: formData.phone || null,
          user_role: formData.role,
          country_code: 'UG',
          currency_code: 'UGX'
        });

      console.log('Helper function result:', { helperResult, helperError });

      if (helperError || !helperResult?.[0]?.success) {
        console.error('Helper function failed:', {
          helperError,
          helperResult,
          errorMessage: helperResult?.[0]?.error_message,
          authContext: authContext?.[0]
        });

        // Don't fall back to manual creation as it violates RLS policies
        // Instead, throw an error with the specific details
        const errorMessage = helperError?.message || helperResult?.[0]?.error_message || 'Unknown error';
        throw new Error(`Failed to create organization: ${errorMessage}`);
      }

      // Helper function succeeded
      const orgData: Database['public']['Tables']['organizations']['Row'] = {
        id: helperResult[0].organization_id,
        name: formData.companyName,
        tin_number: formData.tinNumber || null,
        business_reg_number: formData.businessRegNumber || null,
        ura_tax_office: formData.uraTaxOffice || null,
        currency_code: 'UGX',
        timezone: 'Africa/Kampala',
        created_at: new Date().toISOString(),
        updated_at: null,
        address: null,
        country_id: null,
        description: null,
        email: null,
        email_settings: null,
        fiscal_year_end: '2025-12-31',
        fiscal_year_start: '2025-01-01',
        phone: null,
        website: null
      } as Database['public']['Tables']['organizations']['Row'];

      // Log organization creation
      await auditLogger.logCreate('organization', orgData.id, {
        name: orgData.name,
        tin_number: orgData.tin_number,
        business_reg_number: orgData.business_reg_number,
        ura_tax_office: orgData.ura_tax_office
      }, 'Organization created during onboarding');

      // Log profile creation
      await auditLogger.logCreate('profile', user.id, {
        email: user.email,
        phone: formData.phone,
        role: formData.role,
        org_id: orgData.id
      }, 'User profile created during onboarding');

      // Create default chart of accounts for the organization
      const defaultAccounts: Array<{
        code: string;
        name: string;
        type: AccountType;
        parent_id: null;
        org_id: string;
        created_by: string;
        is_active: boolean;
        is_tax_account: boolean;
      }> = [
        { code: '1000', name: 'Cash and Cash Equivalents', type: 'asset', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '1100', name: 'Accounts Receivable', type: 'asset', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '1200', name: 'Inventory', type: 'asset', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '1500', name: 'Property, Plant & Equipment', type: 'asset', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '2000', name: 'Accounts Payable', type: 'liability', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '2100', name: 'Accrued Expenses', type: 'liability', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '2500', name: 'Long-term Debt', type: 'liability', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '3000', name: 'Share Capital', type: 'equity', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '3100', name: 'Retained Earnings', type: 'equity', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '4000', name: 'Revenue', type: 'income', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '5000', name: 'Cost of Goods Sold', type: 'expense', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '6000', name: 'Operating Expenses', type: 'expense', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '7000', name: 'Other Income', type: 'income', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false },
        { code: '8000', name: 'Other Expenses', type: 'expense', parent_id: null, org_id: orgData.id, created_by: user.id, is_active: true, is_tax_account: false }
      ];

      const { error: accountsError } = await supabase
        .from('accounts')
        .insert(defaultAccounts);

      if (accountsError) throw accountsError;

      // Mark onboarding as completed
      const { error: onboardingError } = await supabase
        .from('profiles')
        .update({ onboarding_completed_at: new Date().toISOString() })
        .eq('id', user.id);

      if (onboardingError) {
        console.warn('Failed to mark onboarding as completed:', onboardingError);
        // Don't fail the whole process for this
      }

      toast({
        title: "Welcome to KAYA Finance!",
        description: "Your organization has been set up successfully.",
      });

      // Force a page reload to refresh the auth state
      window.location.href = '/dashboard';

    } catch (error: unknown) {
      console.error('Onboarding error:', error);

      // Enhanced error handling with specific messages
      let errorMessage = 'Failed to set up your organization. Please try again.';
      let errorDetails = '';

      if (error instanceof Error) {
        errorMessage = error.message;

        // Provide specific guidance for common RLS errors
        if (error.message.includes('row-level security policy')) {
          errorMessage = 'Permission denied. Please ensure you are properly authenticated.';
          errorDetails = 'This might be a temporary issue. Please try refreshing the page and logging in again.';
        } else if (error.message.includes('duplicate key')) {
          errorMessage = 'An organization with this information already exists.';
          errorDetails = 'Please check if you already have an account or contact support.';
        } else if (error.message.includes('violates foreign key')) {
          errorMessage = 'Invalid data provided. Please check your input and try again.';
        }
      }

      // Debug information for development
      if (process.env.NODE_ENV === 'development') {
        console.group('🔍 Onboarding Debug Information');
        console.log('User ID:', user?.id);
        console.log('User Email:', user?.email);
        console.log('Form Data:', formData);
        console.log('Error Details:', error);
        console.groupEnd();
      }

      toast({
        title: "Setup failed",
        description: errorDetails ? `${errorMessage} ${errorDetails}` : errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    navigate('/login');
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 px-4">
      <div className="w-full max-w-4xl space-y-6">
        <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-green-600 p-3 rounded-full">
              <BookOpen className="h-8 w-8 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl text-green-600">Welcome to KAYA Finance</CardTitle>
          <CardDescription>
            Let's set up your organization to get you started
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="companyName">Company Name *</Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => handleFieldChange('companyName', e.target.value)}
                  onBlur={() => handleFieldBlur('companyName')}
                  required
                  placeholder="Enter your company name"
                  className={errors.companyName && touched.companyName ? 'border-red-500' : ''}
                />
                {errors.companyName && touched.companyName && (
                  <p className="text-sm text-red-600">{errors.companyName}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tinNumber">TIN Number</Label>
                <Input
                  id="tinNumber"
                  value={formData.tinNumber}
                  onChange={(e) => handleFieldChange('tinNumber', e.target.value)}
                  onBlur={() => handleFieldBlur('tinNumber')}
                  placeholder="e.g., 1234567890"
                  className={errors.tinNumber && touched.tinNumber ? 'border-red-500' : ''}
                />
                {errors.tinNumber && touched.tinNumber && (
                  <p className="text-sm text-red-600">{errors.tinNumber}</p>
                )}
                <p className="text-sm text-gray-600">{getTinNumberHelper()}</p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="businessRegNumber">Business Registration Number</Label>
                <Input
                  id="businessRegNumber"
                  value={formData.businessRegNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, businessRegNumber: e.target.value }))}
                  placeholder="Registration number"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="uraTaxOffice">URA Tax Office</Label>
                <Input
                  id="uraTaxOffice"
                  value={formData.uraTaxOffice}
                  onChange={(e) => setFormData(prev => ({ ...prev, uraTaxOffice: e.target.value }))}
                  placeholder="e.g., Kampala"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleFieldChange('phone', e.target.value)}
                  onBlur={() => handleFieldBlur('phone')}
                  placeholder="e.g., +*********** 000"
                  className={errors.phone && touched.phone ? 'border-red-500' : ''}
                />
                {errors.phone && touched.phone && (
                  <p className="text-sm text-red-600">{errors.phone}</p>
                )}
                <p className="text-sm text-gray-600">{getPhoneNumberHelper()}</p>
              </div>
              
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="role">Your Role</Label>
                <Select value={formData.role} onValueChange={(value: 'admin' | 'accountant') => setFormData(prev => ({ ...prev, role: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="accountant">Accountant</SelectItem>
                    <SelectItem value="admin">Administrator</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <Button
              type="submit"
              className="w-full bg-green-600 hover:bg-green-700"
              disabled={loading || !formData.companyName || Object.keys(errors).some(key => errors[key])}
            >
              {loading ? 'Setting up your organization...' : 'Complete Setup'}
            </Button>
          </form>
        </CardContent>
        </Card>

        {/* Debug panel for development */}
        {process.env.NODE_ENV === 'development' && <OnboardingDebug />}
      </div>
    </div>
  );
};
