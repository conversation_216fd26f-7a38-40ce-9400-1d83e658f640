/**
 * Invite User Dialog Component
 * Allows admins to invite new users with email notifications
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Mail, UserPlus, Send, Check, X, AlertCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/hooks/useAuthHook'
import { sendInvitationEmail, isValidEmail, type InvitationEmailData } from '@/lib/emailNotifications'
import type { UserRole } from '@/types/database'

interface InviteUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

interface InvitationForm {
  email: string
  role: UserRole
  customMessage: string
}

export function InviteUserDialog({
  open,
  onOpenChange,
  onSuccess
}: InviteUserDialogProps) {
  const { profile } = useAuth()
  const { toast } = useToast()
  const [sending, setSending] = useState(false)
  const [sent, setSent] = useState(false)
  
  const [formData, setFormData] = useState<InvitationForm>({
    email: '',
    role: 'accountant',
    customMessage: ''
  })

  const [emailError, setEmailError] = useState('')

  const validateEmail = (email: string) => {
    if (!email) {
      setEmailError('Email is required')
      return false
    }
    if (!isValidEmail(email)) {
      setEmailError('Please enter a valid email address')
      return false
    }
    setEmailError('')
    return true
  }

  const handleEmailChange = (email: string) => {
    setFormData(prev => ({ ...prev, email }))
    if (email) {
      validateEmail(email)
    } else {
      setEmailError('')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateEmail(formData.email)) {
      return
    }

    if (!profile?.org_id) {
      toast({
        title: "Error",
        description: "Organization not found",
        variant: "destructive"
      })
      return
    }

    setSending(true)
    try {
      const invitationData: InvitationEmailData = {
        inviteeEmail: formData.email,
        inviterName: profile.full_name || 'Team Administrator',
        inviterEmail: profile.email || '',
        organizationName: profile.organizations?.name || 'Your Organization',
        organizationId: profile.org_id,
        role: formData.role,
        customMessage: formData.customMessage || undefined
      }

      const result = await sendInvitationEmail(invitationData)

      if (result.success) {
        setSent(true)
        toast({
          title: "Invitation Sent!",
          description: `Invitation email has been sent to ${formData.email}`,
        })
        
        // Reset form after short delay
        setTimeout(() => {
          setFormData({
            email: '',
            role: 'accountant',
            customMessage: ''
          })
          setSent(false)
          onOpenChange(false)
          onSuccess?.()
        }, 2000)
        
      } else {
        throw new Error(result.error || 'Failed to send invitation')
      }

    } catch (error) {
      console.error('Failed to send invitation:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive"
      })
    } finally {
      setSending(false)
    }
  }

  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'default'
      case 'accountant':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const getRoleDescription = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'Full access to all features, can manage users and organization settings'
      case 'accountant':
        return 'Access to financial features, can manage invoices, bills, and reports'
      default:
        return ''
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {sent ? (
              <>
                <Check className="h-5 w-5 text-green-500" />
                Invitation Sent!
              </>
            ) : (
              <>
                <UserPlus className="h-5 w-5" />
                Invite New User
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {sent 
              ? `Invitation email has been sent to ${formData.email}. They will receive instructions to create their account.`
              : 'Send an invitation email to add a new team member to your organization.'
            }
          </DialogDescription>
        </DialogHeader>

        {!sent && (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Email Input */}
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => handleEmailChange(e.target.value)}
                  className={`pl-10 ${emailError ? 'border-red-500' : ''}`}
                  required
                />
              </div>
              {emailError && (
                <div className="flex items-center gap-2 text-sm text-red-600">
                  <AlertCircle className="h-3 w-3" />
                  {emailError}
                </div>
              )}
            </div>

            {/* Role Selection */}
            <div className="space-y-2">
              <Label htmlFor="role">Role *</Label>
              <Select 
                value={formData.role} 
                onValueChange={(value: UserRole) => setFormData(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="accountant">
                    <div className="flex items-center gap-2">
                      <Badge variant={getRoleBadgeVariant('accountant')}>Accountant</Badge>
                    </div>
                  </SelectItem>
                  <SelectItem value="admin">
                    <div className="flex items-center gap-2">
                      <Badge variant={getRoleBadgeVariant('admin')}>Admin</Badge>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-600">
                {getRoleDescription(formData.role)}
              </p>
            </div>

            {/* Custom Message */}
            <div className="space-y-2">
              <Label htmlFor="customMessage">Personal Message (Optional)</Label>
              <Textarea
                id="customMessage"
                placeholder="Add a personal welcome message..."
                value={formData.customMessage}
                onChange={(e) => setFormData(prev => ({ ...prev, customMessage: e.target.value }))}
                rows={3}
                className="resize-none"
              />
              <p className="text-xs text-gray-600">
                This message will be included in the invitation email
              </p>
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={sending}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={sending || !formData.email || !!emailError}
              >
                {sending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Invitation
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        )}

        {sent && (
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  )
}
