/**
 * Metrics API Endpoint
 * Provides Prometheus-compatible metrics for monitoring
 */

import { NextApiRequest, NextApiResponse } from 'next'
import { notificationMetrics } from '@/lib/monitoring'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const metrics = generatePrometheusMetrics()
    
    res.setHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8')
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
    res.status(200).send(metrics)
  } catch (error) {
    console.error('Metrics generation failed:', error)
    res.status(500).json({ error: 'Metrics generation failed' })
  }
}

function generatePrometheusMetrics(): string {
  const lines: string[] = []
  
  // Add metadata
  lines.push('# HELP kaya_notifications_created_total Total number of notifications created')
  lines.push('# TYPE kaya_notifications_created_total counter')
  
  lines.push('# HELP kaya_notifications_delivered_total Total number of notifications delivered')
  lines.push('# TYPE kaya_notifications_delivered_total counter')
  
  lines.push('# HELP kaya_notification_read_time_seconds Time taken to read notifications')
  lines.push('# TYPE kaya_notification_read_time_seconds histogram')
  
  lines.push('# HELP kaya_api_response_time_seconds API response time in seconds')
  lines.push('# TYPE kaya_api_response_time_seconds histogram')
  
  lines.push('# HELP kaya_realtime_connections_active Number of active real-time connections')
  lines.push('# TYPE kaya_realtime_connections_active gauge')
  
  lines.push('# HELP kaya_db_query_time_seconds Database query time in seconds')
  lines.push('# TYPE kaya_db_query_time_seconds histogram')

  // Get all metric names
  const metricNames = notificationMetrics.getMetricNames()
  
  for (const metricName of metricNames) {
    const metrics = notificationMetrics.getMetrics(metricName)
    
    // Group metrics by labels
    const groupedMetrics = new Map<string, typeof metrics>()
    
    for (const metric of metrics) {
      const labelString = metric.labels ? 
        Object.entries(metric.labels)
          .map(([k, v]) => `${k}="${v}"`)
          .join(',') : ''
      
      if (!groupedMetrics.has(labelString)) {
        groupedMetrics.set(labelString, [])
      }
      groupedMetrics.get(labelString)!.push(metric)
    }
    
    // Generate metric lines
    for (const [labelString, metricGroup] of groupedMetrics) {
      const stats = calculateStats(metricGroup)
      const labels = labelString ? `{${labelString}}` : ''
      
      if (metricName.includes('_total')) {
        // Counter metric
        lines.push(`kaya_${metricName}${labels} ${stats.sum}`)
      } else if (metricName.includes('_active')) {
        // Gauge metric
        lines.push(`kaya_${metricName}${labels} ${stats.latest || 0}`)
      } else if (metricName.includes('_time_') || metricName.includes('_duration_')) {
        // Histogram metric
        lines.push(`kaya_${metricName}_sum${labels} ${stats.sum}`)
        lines.push(`kaya_${metricName}_count${labels} ${stats.count}`)
        
        // Add histogram buckets
        const buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10]
        let cumulativeCount = 0
        
        for (const bucket of buckets) {
          const bucketCount = metricGroup.filter(m => m.value <= bucket).length
          cumulativeCount += bucketCount
          const bucketLabels = labelString ? 
            `{${labelString},le="${bucket}"}` : 
            `{le="${bucket}"}`
          lines.push(`kaya_${metricName}_bucket${bucketLabels} ${cumulativeCount}`)
        }
        
        // Add +Inf bucket
        const infLabels = labelString ? 
          `{${labelString},le="+Inf"}` : 
          `{le="+Inf"}`
        lines.push(`kaya_${metricName}_bucket${infLabels} ${stats.count}`)
      }
    }
  }
  
  // Add system metrics
  lines.push('')
  lines.push('# HELP nodejs_process_uptime_seconds Process uptime in seconds')
  lines.push('# TYPE nodejs_process_uptime_seconds gauge')
  lines.push(`nodejs_process_uptime_seconds ${process.uptime()}`)
  
  lines.push('')
  lines.push('# HELP nodejs_memory_usage_bytes Memory usage in bytes')
  lines.push('# TYPE nodejs_memory_usage_bytes gauge')
  const memUsage = process.memoryUsage()
  lines.push(`nodejs_memory_usage_bytes{type="rss"} ${memUsage.rss}`)
  lines.push(`nodejs_memory_usage_bytes{type="heapUsed"} ${memUsage.heapUsed}`)
  lines.push(`nodejs_memory_usage_bytes{type="heapTotal"} ${memUsage.heapTotal}`)
  lines.push(`nodejs_memory_usage_bytes{type="external"} ${memUsage.external}`)
  
  return lines.join('\n') + '\n'
}

function calculateStats(metrics: Array<{ value: number }>) {
  if (metrics.length === 0) {
    return { count: 0, sum: 0, avg: 0, min: 0, max: 0, latest: null }
  }
  
  const values = metrics.map(m => m.value)
  const sum = values.reduce((a, b) => a + b, 0)
  
  return {
    count: metrics.length,
    sum,
    avg: sum / metrics.length,
    min: Math.min(...values),
    max: Math.max(...values),
    latest: values[values.length - 1]
  }
}
