// Backup System Monitoring and Alerting
// Provides comprehensive health checks, metrics collection, and alerting

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'

export interface HealthCheck {
  id: string
  name: string
  status: 'healthy' | 'warning' | 'critical' | 'unknown'
  message: string
  lastChecked: string
  responseTime: number
  details?: Record<string, unknown>
}

export interface SystemMetrics {
  backupSuccess: {
    total: number
    successful: number
    failed: number
    successRate: number
  }
  performance: {
    averageBackupTime: number
    averageRestoreTime: number
    averageDataSize: number
    throughput: number
  }
  resources: {
    activeBackups: number
    activeRestores: number
    storageUsed: number
    errorRate: number
  }
  availability: {
    uptime: number
    lastDowntime?: string
    mttr: number // Mean Time To Recovery
  }
}

export interface Alert {
  id: string
  type: 'backup_failed' | 'storage_full' | 'performance_degraded' | 'security_breach' | 'system_down'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  orgId?: string
  backupId?: string
  createdAt: string
  acknowledged: boolean
  acknowledgedBy?: string
  acknowledgedAt?: string
  resolved: boolean
  resolvedAt?: string
}

/**
 * Backup System Monitor
 */
export class BackupMonitor {
  /**
   * Perform comprehensive health checks
   */
  static async performHealthChecks(orgId?: string): Promise<HealthCheck[]> {
    const checks: HealthCheck[] = []

    // Database connectivity check
    checks.push(await this.checkDatabaseHealth())

    // Storage system check
    checks.push(await this.checkStorageHealth())

    // Backup service check
    checks.push(await this.checkBackupServiceHealth())

    // Encryption system check
    checks.push(await this.checkEncryptionHealth())

    // Resource usage check
    if (orgId) {
      checks.push(await this.checkResourceHealth(orgId))
    }

    return checks
  }

  /**
   * Check database health
   */
  private static async checkDatabaseHealth(): Promise<HealthCheck> {
    const startTime = Date.now()
    
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('id')
        .limit(1)

      const responseTime = Date.now() - startTime

      if (error) {
        return {
          id: 'database_health',
          name: 'Database Connectivity',
          status: 'critical',
          message: `Database error: ${error.message}`,
          lastChecked: new Date().toISOString(),
          responseTime
        }
      }

      const status = responseTime > 5000 ? 'warning' : 'healthy'
      const message = responseTime > 5000 
        ? `Database responding slowly (${responseTime}ms)`
        : 'Database connectivity normal'

      return {
        id: 'database_health',
        name: 'Database Connectivity',
        status,
        message,
        lastChecked: new Date().toISOString(),
        responseTime
      }

    } catch (error) {
      return {
        id: 'database_health',
        name: 'Database Connectivity',
        status: 'critical',
        message: 'Database connection failed',
        lastChecked: new Date().toISOString(),
        responseTime: Date.now() - startTime
      }
    }
  }

  /**
   * Check storage system health
   */
  private static async checkStorageHealth(): Promise<HealthCheck> {
    const startTime = Date.now()
    
    try {
      const { data, error } = await supabase.storage
        .from('backups')
        .list('', { limit: 1 })

      const responseTime = Date.now() - startTime

      if (error) {
        return {
          id: 'storage_health',
          name: 'Storage System',
          status: 'critical',
          message: `Storage error: ${error.message}`,
          lastChecked: new Date().toISOString(),
          responseTime
        }
      }

      return {
        id: 'storage_health',
        name: 'Storage System',
        status: 'healthy',
        message: 'Storage system operational',
        lastChecked: new Date().toISOString(),
        responseTime
      }

    } catch (error) {
      return {
        id: 'storage_health',
        name: 'Storage System',
        status: 'critical',
        message: 'Storage system unavailable',
        lastChecked: new Date().toISOString(),
        responseTime: Date.now() - startTime
      }
    }
  }

  /**
   * Check backup service health
   */
  private static async checkBackupServiceHealth(): Promise<HealthCheck> {
    try {
      // Check for stuck backups
      const { data: stuckBackups } = await supabase
        .from('backup_metadata')
        .select('id, created_at')
        .in('status', ['pending', 'in_progress'])
        .lt('created_at', new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()) // 2 hours ago

      if (stuckBackups && stuckBackups.length > 0) {
        return {
          id: 'backup_service_health',
          name: 'Backup Service',
          status: 'warning',
          message: `${stuckBackups.length} backup(s) appear to be stuck`,
          lastChecked: new Date().toISOString(),
          responseTime: 0,
          details: { stuck_backups: stuckBackups.length }
        }
      }

      // Check recent backup success rate
      const { data: recentBackups } = await supabase
        .from('backup_metadata')
        .select('status')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

      if (recentBackups) {
        const total = recentBackups.length
        const failed = recentBackups.filter(b => b.status === 'failed').length
        const failureRate = total > 0 ? (failed / total) * 100 : 0

        if (failureRate > 20) {
          return {
            id: 'backup_service_health',
            name: 'Backup Service',
            status: 'warning',
            message: `High failure rate: ${failureRate.toFixed(1)}%`,
            lastChecked: new Date().toISOString(),
            responseTime: 0,
            details: { failure_rate: failureRate, total_backups: total }
          }
        }
      }

      return {
        id: 'backup_service_health',
        name: 'Backup Service',
        status: 'healthy',
        message: 'Backup service operating normally',
        lastChecked: new Date().toISOString(),
        responseTime: 0
      }

    } catch (error) {
      return {
        id: 'backup_service_health',
        name: 'Backup Service',
        status: 'critical',
        message: 'Unable to check backup service status',
        lastChecked: new Date().toISOString(),
        responseTime: 0
      }
    }
  }

  /**
   * Check encryption system health
   */
  private static async checkEncryptionHealth(): Promise<HealthCheck> {
    try {
      // Check for encryption keys nearing expiration
      const { data: expiringKeys } = await supabase
        .from('backup_encryption_keys')
        .select('id, expires_at')
        .eq('is_active', true)
        .lt('expires_at', new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()) // 7 days

      if (expiringKeys && expiringKeys.length > 0) {
        return {
          id: 'encryption_health',
          name: 'Encryption System',
          status: 'warning',
          message: `${expiringKeys.length} encryption key(s) expiring soon`,
          lastChecked: new Date().toISOString(),
          responseTime: 0,
          details: { expiring_keys: expiringKeys.length }
        }
      }

      return {
        id: 'encryption_health',
        name: 'Encryption System',
        status: 'healthy',
        message: 'Encryption system operational',
        lastChecked: new Date().toISOString(),
        responseTime: 0
      }

    } catch (error) {
      return {
        id: 'encryption_health',
        name: 'Encryption System',
        status: 'critical',
        message: 'Unable to check encryption system',
        lastChecked: new Date().toISOString(),
        responseTime: 0
      }
    }
  }

  /**
   * Check resource health for organization
   */
  private static async checkResourceHealth(orgId: string): Promise<HealthCheck> {
    try {
      // This would integrate with BackupResourceManager
      // For now, return a basic check
      return {
        id: 'resource_health',
        name: 'Resource Usage',
        status: 'healthy',
        message: 'Resource usage within limits',
        lastChecked: new Date().toISOString(),
        responseTime: 0
      }

    } catch (error) {
      return {
        id: 'resource_health',
        name: 'Resource Usage',
        status: 'unknown',
        message: 'Unable to check resource usage',
        lastChecked: new Date().toISOString(),
        responseTime: 0
      }
    }
  }

  /**
   * Collect system metrics
   */
  static async collectMetrics(orgId?: string): Promise<SystemMetrics> {
    try {
      const timeRange = new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours

      // Backup success metrics
      const { data: backups } = await supabase
        .from('backup_metadata')
        .select('status, created_at, completed_at, size_bytes')
        .gte('created_at', timeRange.toISOString())
        .apply(query => orgId ? query.eq('org_id', orgId) : query)

      const total = backups?.length || 0
      const successful = backups?.filter(b => b.status === 'completed').length || 0
      const failed = backups?.filter(b => b.status === 'failed').length || 0
      const successRate = total > 0 ? (successful / total) * 100 : 0

      // Performance metrics
      const completedBackups = backups?.filter(b => b.status === 'completed' && b.completed_at) || []
      const averageBackupTime = completedBackups.length > 0
        ? completedBackups.reduce((sum, b) => {
            const duration = new Date(b.completed_at).getTime() - new Date(b.created_at).getTime()
            return sum + duration
          }, 0) / completedBackups.length
        : 0

      const averageDataSize = completedBackups.length > 0
        ? completedBackups.reduce((sum, b) => sum + (b.size_bytes || 0), 0) / completedBackups.length
        : 0

      // Resource metrics
      const { data: activeBackups } = await supabase
        .from('backup_metadata')
        .select('id')
        .in('status', ['pending', 'in_progress'])
        .apply(query => orgId ? query.eq('org_id', orgId) : query)

      const { data: activeRestores } = await supabase
        .from('backup_restorations')
        .select('id')
        .in('status', ['pending', 'in_progress'])
        .apply(query => orgId ? query.eq('org_id', orgId) : query)

      return {
        backupSuccess: {
          total,
          successful,
          failed,
          successRate
        },
        performance: {
          averageBackupTime: averageBackupTime / 1000, // Convert to seconds
          averageRestoreTime: 0, // Would need restore timing data
          averageDataSize: averageDataSize / (1024 * 1024), // Convert to MB
          throughput: 0 // Would need throughput calculation
        },
        resources: {
          activeBackups: activeBackups?.length || 0,
          activeRestores: activeRestores?.length || 0,
          storageUsed: 0, // Would need storage calculation
          errorRate: total > 0 ? (failed / total) * 100 : 0
        },
        availability: {
          uptime: 99.9, // Would need uptime tracking
          mttr: 0 // Would need incident tracking
        }
      }

    } catch (error) {
      console.error('Error collecting metrics:', error)
      
      // Return default metrics on error
      return {
        backupSuccess: { total: 0, successful: 0, failed: 0, successRate: 0 },
        performance: { averageBackupTime: 0, averageRestoreTime: 0, averageDataSize: 0, throughput: 0 },
        resources: { activeBackups: 0, activeRestores: 0, storageUsed: 0, errorRate: 0 },
        availability: { uptime: 0, mttr: 0 }
      }
    }
  }

  /**
   * Create alert
   */
  static async createAlert(alert: Omit<Alert, 'id' | 'createdAt' | 'acknowledged' | 'resolved'>): Promise<string> {
    const alertId = crypto.randomUUID()
    
    try {
      await supabase
        .from('backup_alerts')
        .insert({
          id: alertId,
          type: alert.type,
          severity: alert.severity,
          message: alert.message,
          org_id: alert.orgId,
          backup_id: alert.backupId,
          acknowledged: false,
          resolved: false,
          created_at: new Date().toISOString()
        })

      // Log alert creation
      await auditLogger.logActivity({
        entity_type: 'backup_alert',
        entity_id: alertId,
        action: 'alert_created',
        description: `Alert created: ${alert.message}`,
        severity: alert.severity === 'critical' ? 'error' : 'warning',
        category: 'system',
        metadata: alert
      })

      return alertId

    } catch (error) {
      console.error('Failed to create alert:', error)
      throw error
    }
  }

  /**
   * Get system status summary
   */
  static async getSystemStatus(orgId?: string): Promise<{
    overall: 'healthy' | 'warning' | 'critical'
    healthChecks: HealthCheck[]
    metrics: SystemMetrics
    activeAlerts: number
  }> {
    const healthChecks = await this.performHealthChecks(orgId)
    const metrics = await this.collectMetrics(orgId)
    
    // Count active alerts
    const { data: alerts } = await supabase
      .from('backup_alerts')
      .select('id')
      .eq('resolved', false)
      .apply(query => orgId ? query.eq('org_id', orgId) : query)

    const activeAlerts = alerts?.length || 0

    // Determine overall status
    const hasCritical = healthChecks.some(check => check.status === 'critical')
    const hasWarning = healthChecks.some(check => check.status === 'warning')
    
    const overall = hasCritical ? 'critical' : hasWarning ? 'warning' : 'healthy'

    return {
      overall,
      healthChecks,
      metrics,
      activeAlerts
    }
  }
}
