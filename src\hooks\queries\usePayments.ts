import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { usePaymentEnhancement } from '@/hooks/usePaymentEnhancement'
import { queryKeys, type QueryFilters } from '@/lib/queryKeys'
import { toast } from 'sonner'
import type { Payment } from '@/types/database'

export interface PaymentWithDetails extends Payment {
  payee_name?: string
  applications_total?: number
  related_documents?: Array<{
    type: string
    id: string
    number: string
    amount: number
  }>
}

/**
 * Hook to fetch all payments for the organization
 */
export function usePayments(filters?: QueryFilters) {
  const { profile } = useAuth()
  const { enhancePayments } = usePaymentEnhancement({ orgId: profile?.org_id || '' })

  return useQuery({
    queryKey: filters 
      ? queryKeys.payments.filtered(profile?.org_id || '', filters)
      : queryKeys.payments.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('payments')
        .select(`
          *,
          payment_applications(
            amount_applied,
            applied_to_type,
            applied_to_id
          )
        `)
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`reference_number.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }
      
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      // Apply ordering
      query = query.order('payment_date', { ascending: false })

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit)
      }
      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1)
      }

      const { data: payments, error } = await query

      if (error) throw error

      if (!payments) return []

      // Fetch customers and vendors for payee names
      const [customersRes, vendorsRes] = await Promise.all([
        supabase
          .from('customers')
          .select('id, name')
          .eq('org_id', profile.org_id),
        supabase
          .from('vendors')
          .select('id, name')
          .eq('org_id', profile.org_id)
      ])

      const customers = customersRes.data || []
      const vendors = vendorsRes.data || []

      // Enhance payments with additional information using the custom hook
      const enhancedPayments = await enhancePayments(payments, customers, vendors)

      return enhancedPayments
    },
    enabled: !!profile?.org_id,
    staleTime: 2 * 60 * 1000, // 2 minutes - payments change frequently
  })
}

/**
 * Hook to fetch recent payments
 */
export function useRecentPayments(limit: number = 5) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.payments.recent(profile?.org_id || '', limit),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('payments')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('payment_date', { ascending: false })
        .limit(limit)

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 1 * 60 * 1000, // 1 minute - recent data should be fresh
  })
}

/**
 * Hook to fetch payments by entity (customer or vendor)
 */
export function usePaymentsByEntity(entityType: 'customer' | 'vendor', entityId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.payments.byEntity(profile?.org_id || '', entityType, entityId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !entityId) return []

      const { data, error } = await supabase
        .from('payments')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('payee_type', entityType)
        .eq('payee_id', entityId)
        .order('payment_date', { ascending: false })

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id && !!entityId,
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

/**
 * Hook to fetch a single payment by ID
 */
export function usePayment(paymentId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.payments.detail(profile?.org_id || '', paymentId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !paymentId) return null

      const { data, error } = await supabase
        .from('payments')
        .select(`
          *,
          payment_applications(*)
        `)
        .eq('id', paymentId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id && !!paymentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to create a new payment
 */
export function useCreatePayment() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (paymentData: Omit<Payment, 'id' | 'org_id' | 'created_at' | 'updated_at'>) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('payments')
        .insert({
          ...paymentData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (newPayment) => {
      // Invalidate and refetch payments list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.payments.all(profile?.org_id || '') 
      })
      
      // Invalidate recent payments
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.payments.recent(profile?.org_id || '') 
      })

      // Invalidate entity-specific payments
      if (newPayment.payee_type && newPayment.payee_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.payments.byEntity(profile?.org_id || '', newPayment.payee_type as 'customer' | 'vendor', newPayment.payee_id) 
        })
      }

      // Invalidate dashboard recent transactions
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.dashboard.recentTransactions(profile?.org_id || '') 
      })

      toast.success('Payment created successfully')
    },
    onError: (error) => {
      console.error('Error creating payment:', error)
      toast.error('Failed to create payment')
    },
  })
}

/**
 * Hook to update an existing payment
 */
export function useUpdatePayment() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      paymentId, 
      paymentData 
    }: { 
      paymentId: string
      paymentData: Partial<Omit<Payment, 'id' | 'org_id' | 'created_at'>>
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('payments')
        .update({
          ...paymentData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', paymentId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (updatedPayment) => {
      // Update the payment in the cache
      queryClient.setQueryData(
        queryKeys.payments.detail(profile?.org_id || '', updatedPayment.id),
        updatedPayment
      )

      // Invalidate payments list to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.payments.all(profile?.org_id || '') 
      })

      // Invalidate recent payments
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.payments.recent(profile?.org_id || '') 
      })

      // Invalidate entity-specific payments
      if (updatedPayment.payee_type && updatedPayment.payee_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.payments.byEntity(profile?.org_id || '', updatedPayment.payee_type as 'customer' | 'vendor', updatedPayment.payee_id) 
        })
      }

      toast.success('Payment updated successfully')
    },
    onError: (error) => {
      console.error('Error updating payment:', error)
      toast.error('Failed to update payment')
    },
  })
}

/**
 * Hook to delete a payment
 */
export function useDeletePayment() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (paymentId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Delete payment applications first
      await supabase
        .from('payment_applications')
        .delete()
        .eq('payment_id', paymentId)

      // Delete the payment
      const { error } = await supabase
        .from('payments')
        .delete()
        .eq('id', paymentId)
        .eq('org_id', profile.org_id)

      if (error) throw error
      return paymentId
    },
    onSuccess: (deletedPaymentId) => {
      // Remove the payment from the cache
      queryClient.removeQueries({ 
        queryKey: queryKeys.payments.detail(profile?.org_id || '', deletedPaymentId) 
      })

      // Invalidate payments list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.payments.all(profile?.org_id || '') 
      })

      // Invalidate recent payments
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.payments.recent(profile?.org_id || '') 
      })

      toast.success('Payment deleted successfully')
    },
    onError: (error) => {
      console.error('Error deleting payment:', error)
      toast.error('Failed to delete payment')
    },
  })
}
