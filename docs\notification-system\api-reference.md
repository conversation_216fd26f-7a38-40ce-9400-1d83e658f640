# API Reference

Complete API documentation for the Kaya Finance Notification System.

## Table of Contents

1. [Notification API](#notification-api)
2. [Preferences API](#preferences-api)
3. [Analytics API](#analytics-api)
4. [Integration API](#integration-api)
5. [Real-time API](#real-time-api)
6. [Types & Interfaces](#types--interfaces)

## Notification API

### `createNotification(payload, userId, orgId)`

Creates a new notification.

**Parameters:**
- `payload` (NotificationPayload): Notification data
- `userId` (string | null): Target user ID (null for org-wide)
- `orgId` (string): Organization ID

**Returns:** `Promise<NotificationWithMeta | null>`

**Example:**
```typescript
const notification = await createNotification({
  type: 'payment_pending_approval',
  title: 'Payment Approval Required',
  message: 'Payment of $1,000 requires approval',
  category: 'financial',
  priority: 'high',
  data: { amount: 1000, payee: 'Vendor ABC' },
  entity_type: 'payment',
  entity_id: 'payment-123'
}, 'user-456', 'org-789')
```

### `NotificationAPI.getNotifications(userId, orgId, filters?, page?, limit?)`

Retrieves notifications with pagination and filtering.

**Parameters:**
- `userId` (string): User ID
- `orgId` (string): Organization ID
- `filters` (NotificationFilters, optional): Filter criteria
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20)

**Returns:** `Promise<ApiResponse<PaginatedResponse<NotificationWithMeta>>>`

**Example:**
```typescript
const response = await NotificationAPI.getNotifications(
  'user-456',
  'org-789',
  { is_read: false, category: 'financial' },
  1,
  20
)

if (response.success) {
  console.log('Notifications:', response.data.data)
  console.log('Total:', response.data.total)
  console.log('Has more:', response.data.has_more)
}
```

### `NotificationAPI.markAsRead(notificationId)`

Marks a notification as read.

**Parameters:**
- `notificationId` (string): Notification ID

**Returns:** `Promise<ApiResponse<NotificationWithMeta>>`

**Example:**
```typescript
const response = await NotificationAPI.markAsRead('notification-123')
if (response.success) {
  console.log('Marked as read:', response.data)
}
```

### `NotificationAPI.bulkMarkAsRead(notificationIds)`

Marks multiple notifications as read.

**Parameters:**
- `notificationIds` (string[]): Array of notification IDs

**Returns:** `Promise<ApiResponse<NotificationWithMeta[]>>`

**Example:**
```typescript
const response = await NotificationAPI.bulkMarkAsRead([
  'notification-1',
  'notification-2',
  'notification-3'
])
```

### `NotificationAPI.archiveNotification(notificationId)`

Archives a notification.

**Parameters:**
- `notificationId` (string): Notification ID

**Returns:** `Promise<ApiResponse<NotificationWithMeta>>`

### `NotificationAPI.deleteNotification(notificationId)`

Deletes a notification permanently.

**Parameters:**
- `notificationId` (string): Notification ID

**Returns:** `Promise<ApiResponse<void>>`

### `NotificationAPI.getNotificationCount(userId, orgId, filters?)`

Gets the count of notifications matching criteria.

**Parameters:**
- `userId` (string): User ID
- `orgId` (string): Organization ID
- `filters` (NotificationFilters, optional): Filter criteria

**Returns:** `Promise<ApiResponse<number>>`

**Example:**
```typescript
const response = await NotificationAPI.getNotificationCount(
  'user-456',
  'org-789',
  { is_read: false }
)

if (response.success) {
  console.log('Unread count:', response.data)
}
```

## Preferences API

### `NotificationAPI.getPreferences(userId)`

Gets user notification preferences.

**Parameters:**
- `userId` (string): User ID

**Returns:** `Promise<ApiResponse<NotificationPreference[]>>`

**Example:**
```typescript
const response = await NotificationAPI.getPreferences('user-456')
if (response.success) {
  console.log('Preferences:', response.data)
}
```

### `NotificationAPI.updatePreferences(userId, preferences)`

Updates user notification preferences.

**Parameters:**
- `userId` (string): User ID
- `preferences` (NotificationPreference): Preference data

**Returns:** `Promise<ApiResponse<NotificationPreference>>`

**Example:**
```typescript
const response = await NotificationAPI.updatePreferences('user-456', {
  notification_type: 'payment_pending_approval',
  enabled: true,
  email_enabled: true,
  in_app_enabled: true,
  push_enabled: false
})
```

### `NotificationAPI.bulkUpdatePreferences(userId, preferences)`

Updates multiple notification preferences at once.

**Parameters:**
- `userId` (string): User ID
- `preferences` (NotificationPreference[]): Array of preferences

**Returns:** `Promise<ApiResponse<NotificationPreference[]>>`

## Analytics API

### `NotificationAnalyticsAPI.getAnalytics(orgId, startDate, endDate)`

Gets comprehensive notification analytics.

**Parameters:**
- `orgId` (string): Organization ID
- `startDate` (string): Start date (ISO format)
- `endDate` (string): End date (ISO format)

**Returns:** `Promise<ApiResponse<AnalyticsData>>`

**Example:**
```typescript
const response = await NotificationAnalyticsAPI.getAnalytics(
  'org-789',
  '2025-06-01',
  '2025-06-30'
)

if (response.success) {
  const analytics = response.data
  console.log('Total sent:', analytics.overview.total_sent)
  console.log('Delivery rate:', analytics.overview.delivery_rate)
  console.log('Open rate:', analytics.overview.open_rate)
}
```

### `NotificationAnalyticsAPI.getEmailAnalytics(orgId, days?)`

Gets email-specific analytics.

**Parameters:**
- `orgId` (string): Organization ID
- `days` (number, optional): Number of days back (default: 30)

**Returns:** `Promise<ApiResponse<EmailAnalytics>>`

### `NotificationAnalyticsAPI.getPushAnalytics(orgId, days?)`

Gets push notification analytics.

**Parameters:**
- `orgId` (string): Organization ID
- `days` (number, optional): Number of days back (default: 30)

**Returns:** `Promise<ApiResponse<PushAnalytics>>`

### `NotificationAnalyticsAPI.exportAnalyticsReport(orgId, startDate, endDate, format?)`

Exports analytics data as CSV or JSON.

**Parameters:**
- `orgId` (string): Organization ID
- `startDate` (string): Start date
- `endDate` (string): End date
- `format` ('csv' | 'json', optional): Export format (default: 'csv')

**Returns:** `Promise<ApiResponse<string>>`

## Integration API

### `IntegrationAPI.getIntegrations(orgId)`

Gets all external integrations for an organization.

**Parameters:**
- `orgId` (string): Organization ID

**Returns:** `Promise<IntegrationConfig[]>`

### `IntegrationAPI.createIntegration(orgId, integration)`

Creates a new external integration.

**Parameters:**
- `orgId` (string): Organization ID
- `integration` (Omit<IntegrationConfig, 'id' | 'org_id' | 'created_at' | 'updated_at'>): Integration data

**Returns:** `Promise<IntegrationConfig | null>`

**Example:**
```typescript
const integration = await IntegrationAPI.createIntegration('org-789', {
  type: 'slack',
  name: 'Main Slack Channel',
  config: {
    webhook_url: 'https://hooks.slack.com/services/...',
    channel: '#notifications',
    username: 'Kaya Finance'
  },
  notification_types: ['payment_pending_approval', 'invoice_overdue'],
  is_active: true
})
```

### `IntegrationAPI.updateIntegration(integrationId, updates)`

Updates an existing integration.

**Parameters:**
- `integrationId` (string): Integration ID
- `updates` (Partial<IntegrationConfig>): Updates to apply

**Returns:** `Promise<IntegrationConfig | null>`

### `IntegrationAPI.deleteIntegration(integrationId)`

Deletes an integration.

**Parameters:**
- `integrationId` (string): Integration ID

**Returns:** `Promise<boolean>`

### `IntegrationAPI.testIntegration(integration)`

Tests an integration configuration.

**Parameters:**
- `integration` (IntegrationConfig): Integration to test

**Returns:** `Promise<boolean>`

## Real-time API

### `useNotificationRealtime(userId?, orgId?)`

Hook for real-time notification updates.

**Parameters:**
- `userId` (string, optional): User ID
- `orgId` (string, optional): Organization ID

**Returns:** `NotificationRealtimeState`

**Example:**
```typescript
function NotificationComponent() {
  const { isConnected, connectionError, reconnect, isUsingPolling } = useNotificationRealtime()

  if (connectionError) {
    return (
      <div>
        Connection error: {connectionError.message}
        <button onClick={reconnect}>Reconnect</button>
      </div>
    )
  }

  return (
    <div>
      Status: {isConnected ? 'Connected' : 'Connecting...'}
      {isUsingPolling && <span> (Using polling fallback)</span>}
    </div>
  )
}
```

## Types & Interfaces

### `NotificationPayload`

```typescript
interface NotificationPayload {
  type: NotificationType
  title: string
  message: string
  category: NotificationCategory
  priority: NotificationPriority
  data?: Record<string, any>
  entity_type?: string
  entity_id?: string
  user_id?: string | null
}
```

### `NotificationWithMeta`

```typescript
interface NotificationWithMeta {
  id: string
  type: NotificationType
  category: NotificationCategory
  priority: NotificationPriority
  title: string
  message: string
  data: Record<string, any>
  is_read: boolean
  is_archived: boolean
  created_at: string
  updated_at: string
  read_at: string | null
  user_id: string | null
  org_id: string
  entity_type: string | null
  entity_id: string | null
}
```

### `NotificationFilters`

```typescript
interface NotificationFilters {
  is_read?: boolean
  is_archived?: boolean
  category?: NotificationCategory
  priority?: NotificationPriority
  type?: NotificationType
  entity_type?: string
  date_from?: string
  date_to?: string
  search?: string
}
```

### `NotificationPreference`

```typescript
interface NotificationPreference {
  id?: string
  user_id: string
  notification_type: NotificationType
  enabled: boolean
  email_enabled: boolean
  in_app_enabled: boolean
  push_enabled: boolean
  created_at?: string
  updated_at?: string
}
```

### `PaginatedResponse<T>`

```typescript
interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  has_more: boolean
  next_cursor?: string
}
```

### `ApiResponse<T>`

```typescript
interface ApiResponse<T> {
  success: boolean
  data: T | null
  error: string | null
  message?: string
}
```

### Notification Types

```typescript
type NotificationType = 
  | 'payment_pending_approval'
  | 'payment_approved'
  | 'payment_rejected'
  | 'invoice_overdue'
  | 'invoice_due_soon'
  | 'invoice_paid'
  | 'bill_due_soon'
  | 'bill_overdue'
  | 'budget_exceeded'
  | 'budget_warning'
  | 'user_invited'
  | 'backup_completed'
  | 'backup_failed'
  | 'system_maintenance'
  | 'audit_alert'
```

### Priority Levels

```typescript
type NotificationPriority = 'urgent' | 'high' | 'normal' | 'low'
```

### Categories

```typescript
type NotificationCategory = 'financial' | 'system' | 'security' | 'user'
```

## Error Handling

All API functions return standardized error responses:

```typescript
interface ApiError {
  success: false
  data: null
  error: string
  message?: string
}
```

Common error scenarios:
- **Authentication errors**: User not authenticated or insufficient permissions
- **Validation errors**: Invalid input data or missing required fields
- **Not found errors**: Requested resource doesn't exist
- **Rate limiting**: Too many requests in a short period
- **Server errors**: Internal server or database errors

**Example error handling:**
```typescript
const response = await NotificationAPI.getNotifications(userId, orgId)

if (!response.success) {
  console.error('API Error:', response.error)
  // Handle specific error cases
  switch (response.error) {
    case 'UNAUTHORIZED':
      // Redirect to login
      break
    case 'NOT_FOUND':
      // Show not found message
      break
    default:
      // Show generic error message
  }
}
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Notification creation**: 100 requests per minute per organization
- **Bulk operations**: 10 requests per minute per user
- **Analytics queries**: 20 requests per minute per organization
- **Integration tests**: 5 requests per minute per integration

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Requests remaining in current window
- `X-RateLimit-Reset`: Time when rate limit resets (Unix timestamp)
