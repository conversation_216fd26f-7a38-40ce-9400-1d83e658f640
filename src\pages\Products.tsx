import React, { useState } from 'react'
import { ProductList } from '@/components/inventory/ProductList'
import { ProductForm } from '@/components/inventory/ProductForm'
import { ProductDetailView } from '@/components/inventory/ProductDetailView'
import { CategoryManagement } from '@/components/inventory/CategoryManagement'
import { ProductImportExport } from '@/components/inventory/ProductImportExport'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Package,
  FolderTree,
  Plus,
  Settings,
  FileSpreadsheet
} from 'lucide-react'
import type { ProductWithStock } from '@/types/inventory'

export function Products() {
  const [showProductForm, setShowProductForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<ProductWithStock | null>(null)
  const [viewingProduct, setViewingProduct] = useState<ProductWithStock | null>(null)
  const [activeTab, setActiveTab] = useState('products')
  const [showImportExport, setShowImportExport] = useState(false)

  const handleCreateProduct = () => {
    setEditingProduct(null)
    setShowProductForm(true)
  }

  const handleEditProduct = (product: ProductWithStock) => {
    setEditingProduct(product)
    setShowProductForm(true)
  }

  const handleViewProduct = (product: ProductWithStock) => {
    setViewingProduct(product)
  }

  const handleProductFormSuccess = () => {
    setShowProductForm(false)
    setEditingProduct(null)
    // Optionally show a success message
  }

  const handleCloseProductDetail = () => {
    setViewingProduct(null)
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Products</h1>
          <p className="text-muted-foreground">
            Manage your product catalog and inventory items
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setShowImportExport(true)}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Import/Export
          </Button>
          <Button variant="outline" onClick={() => setActiveTab('categories')}>
            <FolderTree className="h-4 w-4 mr-2" />
            Manage Categories
          </Button>
          <Button onClick={handleCreateProduct}>
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="products" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Products
          </TabsTrigger>
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <FolderTree className="h-4 w-4" />
            Categories
          </TabsTrigger>
        </TabsList>

        <TabsContent value="products" className="space-y-6">
          <ProductList
            onCreateProduct={handleCreateProduct}
            onEditProduct={handleEditProduct}
            onViewProduct={handleViewProduct}
          />
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <CategoryManagement />
        </TabsContent>
      </Tabs>

      {/* Product Form Dialog */}
      <ProductForm
        open={showProductForm}
        onOpenChange={setShowProductForm}
        product={editingProduct}
        onSuccess={handleProductFormSuccess}
      />

      {/* Product Detail View Dialog */}
      {viewingProduct && (
        <ProductDetailView
          product={viewingProduct}
          open={!!viewingProduct}
          onOpenChange={(open) => {
            if (!open) {
              handleCloseProductDetail()
            }
          }}
          onEdit={() => {
            handleEditProduct(viewingProduct)
            setViewingProduct(null)
          }}
        />
      )}

      {/* Import/Export Dialog */}
      <ProductImportExport
        open={showImportExport}
        onOpenChange={setShowImportExport}
      />
    </div>
  )
}

export default Products
