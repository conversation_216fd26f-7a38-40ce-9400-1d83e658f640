# Deployment Guide

This guide covers deploying Kaya Finance to production and staging environments.

## Overview

Kaya Finance uses a modern deployment strategy with:
- **Automated CI/CD** via GitHub Actions
- **Staging environment** for testing
- **Production environment** for live users
- **Zero-downtime deployments**
- **Automated rollbacks** on failure

## Deployment Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│                 │    │                 │    │                 │
│ Local Machine   │───▶│ staging.app.com │───▶│  app.com        │
│ Feature Branches│    │ Auto-deploy     │    │ Manual deploy   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Prerequisites

### Required Accounts
- **GitHub** account with repository access
- **Vercel** account for hosting (or alternative)
- **Supabase** projects for staging and production
- **Domain** configured for custom URLs

### Required Secrets
Configure these secrets in your GitHub repository:

```bash
# Vercel Configuration
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_vercel_org_id
VERCEL_PROJECT_ID=your_vercel_project_id

# Supabase - Staging
VITE_SUPABASE_URL_STAGING=your_staging_supabase_url
VITE_SUPABASE_ANON_KEY_STAGING=your_staging_anon_key

# Supabase - Production
VITE_SUPABASE_URL_PRODUCTION=your_production_supabase_url
VITE_SUPABASE_ANON_KEY_PRODUCTION=your_production_anon_key

# Monitoring
VITE_SENTRY_DSN_STAGING=your_staging_sentry_dsn
VITE_SENTRY_DSN_PRODUCTION=your_production_sentry_dsn

# Notifications
SLACK_WEBHOOK_URL=your_slack_webhook_url
```

## Environment Setup

### Staging Environment

1. **Create Supabase Project**
   ```bash
   # Create staging project at supabase.com
   # Note the project URL and anon key
   ```

2. **Configure Database**
   ```bash
   # Set up database schema
   npx supabase db push --db-url your_staging_db_url
   
   # Seed with test data
   npx supabase db seed --db-url your_staging_db_url
   ```

3. **Configure Vercel Project**
   ```bash
   # Link to Vercel project
   vercel link
   
   # Set environment variables
   vercel env add VITE_SUPABASE_URL staging
   vercel env add VITE_SUPABASE_ANON_KEY staging
   ```

### Production Environment

1. **Create Production Supabase Project**
   ```bash
   # Create production project at supabase.com
   # Enable all required features
   # Configure Row Level Security policies
   ```

2. **Set up Custom Domain**
   ```bash
   # Configure DNS records
   # Set up SSL certificates
   # Configure domain in Vercel
   ```

3. **Configure Monitoring**
   ```bash
   # Set up Sentry project
   # Configure error tracking
   # Set up performance monitoring
   ```

## Deployment Process

### Automated Deployment

#### Staging Deployment
Automatically triggered on push to `develop` branch:

```yaml
# .github/workflows/deploy.yml
on:
  push:
    branches: [develop]
```

#### Production Deployment
Automatically triggered on push to `main` branch:

```yaml
# .github/workflows/deploy.yml
on:
  push:
    branches: [main]
```

### Manual Deployment

#### Using Scripts

```bash
# Deploy to staging
pnpm deploy:staging

# Deploy to production
pnpm deploy:production

# Force deploy (skip tests)
pnpm deploy:production --force
```

#### Using GitHub Actions

1. Go to **Actions** tab in GitHub
2. Select **Deploy to Production/Staging** workflow
3. Click **Run workflow**
4. Choose environment and options

### Deployment Steps

The automated deployment process includes:

1. **Pre-deployment Checks**
   - Code quality validation
   - Security scanning
   - Test execution

2. **Build Process**
   - Install dependencies
   - Run TypeScript compilation
   - Build optimized bundle
   - Generate source maps

3. **Database Migration**
   - Run pending migrations
   - Verify schema integrity
   - Update RLS policies

4. **Deployment**
   - Deploy to hosting platform
   - Update environment variables
   - Configure custom domains

5. **Post-deployment Verification**
   - Health checks
   - Smoke tests
   - Performance validation

6. **Notifications**
   - Slack notifications
   - Email alerts
   - GitHub status updates

## Environment Configuration

### Staging Environment Variables

```env
# Application
VITE_ENVIRONMENT=staging
VITE_APP_URL=https://staging.kaya-finance.com

# Supabase
VITE_SUPABASE_URL=https://your-staging-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_staging_anon_key

# Monitoring
VITE_SENTRY_DSN=your_staging_sentry_dsn
VITE_LOG_LEVEL=debug

# Features
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_MONITORING=true
```

### Production Environment Variables

```env
# Application
VITE_ENVIRONMENT=production
VITE_APP_URL=https://app.kaya-finance.com

# Supabase
VITE_SUPABASE_URL=https://your-production-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key

# Monitoring
VITE_SENTRY_DSN=your_production_sentry_dsn
VITE_LOG_LEVEL=warn

# Features
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_MONITORING=true
```

## Database Migrations

### Running Migrations

```bash
# Staging
npx supabase db push --db-url $STAGING_DB_URL

# Production
npx supabase db push --db-url $PRODUCTION_DB_URL
```

### Migration Best Practices

1. **Test migrations in staging first**
2. **Create backup before production migration**
3. **Use transactions for complex migrations**
4. **Plan for rollback scenarios**

### Example Migration

```sql
-- Migration: Add payment_terms to customers
BEGIN;

-- Add column with default value
ALTER TABLE customers 
ADD COLUMN payment_terms INTEGER DEFAULT 30;

-- Update existing records
UPDATE customers 
SET payment_terms = 30 
WHERE payment_terms IS NULL;

-- Make column NOT NULL
ALTER TABLE customers 
ALTER COLUMN payment_terms SET NOT NULL;

COMMIT;
```

## Monitoring and Alerting

### Health Checks

Automated health checks run after deployment:

```bash
# Check application health
curl -f https://app.kaya-finance.com/api/health

# Check database connectivity
curl -f https://app.kaya-finance.com/api/health/db

# Check authentication service
curl -f https://app.kaya-finance.com/api/health/auth
```

### Performance Monitoring

- **Lighthouse CI** for performance metrics
- **Sentry** for error tracking
- **Custom metrics** for business KPIs

### Alerting

Alerts are configured for:
- Deployment failures
- Health check failures
- High error rates
- Performance degradation

## Rollback Procedures

### Automatic Rollback

Automatic rollback triggers on:
- Health check failures
- High error rates
- Performance degradation

### Manual Rollback

```bash
# Rollback to previous version
vercel rollback --token $VERCEL_TOKEN

# Or use specific deployment
vercel rollback deployment-url --token $VERCEL_TOKEN
```

### Database Rollback

```bash
# Restore from backup
npx supabase db restore backup-id

# Or run rollback migration
npx supabase migration down
```

## Security Considerations

### Secrets Management

- Store secrets in GitHub Secrets
- Use environment-specific secrets
- Rotate secrets regularly
- Never commit secrets to code

### Access Control

- Limit deployment permissions
- Use service accounts for CI/CD
- Enable two-factor authentication
- Audit access regularly

### Network Security

- Use HTTPS for all connections
- Configure security headers
- Implement rate limiting
- Monitor for suspicious activity

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check build logs
   vercel logs deployment-url
   
   # Local build test
   pnpm build
   ```

2. **Environment Variable Issues**
   ```bash
   # List environment variables
   vercel env ls
   
   # Update environment variable
   vercel env add VARIABLE_NAME production
   ```

3. **Database Connection Issues**
   ```bash
   # Test database connection
   npx supabase db ping --db-url $DATABASE_URL
   
   # Check RLS policies
   npx supabase db inspect
   ```

4. **Performance Issues**
   ```bash
   # Run performance audit
   npx lighthouse https://app.kaya-finance.com
   
   # Check bundle size
   pnpm analyze
   ```

### Getting Help

1. Check deployment logs in GitHub Actions
2. Review Vercel deployment logs
3. Check Sentry for runtime errors
4. Contact the development team

## Best Practices

### Deployment Strategy

1. **Always deploy to staging first**
2. **Run comprehensive tests**
3. **Monitor deployment metrics**
4. **Have rollback plan ready**

### Code Quality

1. **Maintain high test coverage**
2. **Use automated quality checks**
3. **Follow security best practices**
4. **Document all changes**

### Monitoring

1. **Set up comprehensive monitoring**
2. **Configure meaningful alerts**
3. **Monitor business metrics**
4. **Regular performance audits**

## Support

For deployment support:

- 📖 [Deployment Documentation](environment.md)
- 💬 [GitHub Discussions](https://github.com/your-username/kaya-finance/discussions)
- 🐛 [Report Issues](https://github.com/your-username/kaya-finance/issues)
- 📧 [DevOps Support](mailto:<EMAIL>)

---

*For environment-specific configuration details, see [Environment Configuration](environment.md).*
