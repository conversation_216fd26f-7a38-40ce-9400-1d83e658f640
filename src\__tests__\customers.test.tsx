import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { CustomerDialog } from '@/components/customers/CustomerDialog'
import { useCreateCustomer, useUpdateCustomer } from '@/hooks/queries'
import { validatePhoneNumber, validateTinNumber, validateEmail } from '@/lib/validators'

// Mock the hooks
jest.mock('@/hooks/queries', () => ({
  useCreateCustomer: jest.fn(),
  useUpdateCustomer: jest.fn(),
}))

jest.mock('@/hooks/useAuthHook', () => ({
  useAuth: () => ({
    profile: { org_id: 'test-org-id' }
  })
}))

jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}))

const mockCreateCustomer = useCreateCustomer as jest.MockedFunction<typeof useCreateCustomer>
const mockUpdateCustomer = useUpdateCustomer as jest.MockedFunction<typeof useUpdateCustomer>

describe('Customer Management', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    mockCreateCustomer.mockReturnValue({
      mutate: jest.fn(),
      isPending: false,
      error: null,
    })

    mockUpdateCustomer.mockReturnValue({
      mutate: jest.fn(),
      isPending: false,
      error: null,
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    )
  }

  describe('CustomerDialog', () => {
    it('renders create customer form correctly', () => {
      renderWithProviders(
        <CustomerDialog
          open={true}
          onOpenChange={jest.fn()}
          customer={null}
          onSuccess={jest.fn()}
        />
      )

      expect(screen.getByText('Create New Customer')).toBeInTheDocument()
      expect(screen.getByLabelText(/customer name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/phone/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/tin number/i)).toBeInTheDocument()
    })

    it('validates required fields', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <CustomerDialog
          open={true}
          onOpenChange={jest.fn()}
          customer={null}
          onSuccess={jest.fn()}
        />
      )

      const submitButton = screen.getByRole('button', { name: /create customer/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/customer name is required/i)).toBeInTheDocument()
      })
    })

    it('validates phone number format', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <CustomerDialog
          open={true}
          onOpenChange={jest.fn()}
          customer={null}
          onSuccess={jest.fn()}
        />
      )

      const phoneInput = screen.getByLabelText(/phone/i)
      await user.type(phoneInput, 'invalid-phone')

      const submitButton = screen.getByRole('button', { name: /create customer/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid ugandan phone number/i)).toBeInTheDocument()
      })
    })

    it('creates customer with valid data', async () => {
      const user = userEvent.setup()
      const mockMutate = jest.fn()
      const onSuccess = jest.fn()

      mockCreateCustomer.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
        error: null,
      })

      renderWithProviders(
        <CustomerDialog
          open={true}
          onOpenChange={jest.fn()}
          customer={null}
          onSuccess={onSuccess}
        />
      )

      await user.type(screen.getByLabelText(/customer name/i), 'Test Customer')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/phone/i), '777123456')
      await user.type(screen.getByLabelText(/tin number/i), '**********')

      const submitButton = screen.getByRole('button', { name: /create customer/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockMutate).toHaveBeenCalledWith({
          name: 'Test Customer',
          email: '<EMAIL>',
          phone: '+256777123456',
          tin_number: '**********',
          address: '',
          notes: '',
          payment_terms: 30,
        })
      })
    })
  })

  describe('Validation Functions', () => {
    describe('validatePhoneNumber', () => {
      it('validates correct Ugandan phone numbers', () => {
        expect(validatePhoneNumber('777123456')).toEqual({
          isValid: true,
          formatted: '+256777123456'
        })
        
        expect(validatePhoneNumber('+256777123456')).toEqual({
          isValid: true,
          formatted: '+256777123456'
        })
        
        expect(validatePhoneNumber('0777123456')).toEqual({
          isValid: true,
          formatted: '+256777123456'
        })
      })

      it('rejects invalid phone numbers', () => {
        expect(validatePhoneNumber('123456')).toEqual({
          isValid: false,
          message: 'Please enter a valid Ugandan phone number (e.g., 777123456 or +256777123456)'
        })
        
        expect(validatePhoneNumber('invalid')).toEqual({
          isValid: false,
          message: 'Please enter a valid Ugandan phone number (e.g., 777123456 or +256777123456)'
        })
      })

      it('handles empty phone numbers when not required', () => {
        expect(validatePhoneNumber('', false)).toEqual({
          isValid: true
        })
      })

      it('requires phone number when specified', () => {
        expect(validatePhoneNumber('', true)).toEqual({
          isValid: false,
          message: 'Phone number is required'
        })
      })
    })

    describe('validateTinNumber', () => {
      it('validates correct TIN numbers', () => {
        expect(validateTinNumber('**********')).toEqual({
          isValid: true
        })
      })

      it('rejects invalid TIN numbers', () => {
        expect(validateTinNumber('123')).toEqual({
          isValid: false,
          message: 'TIN number must be exactly 10 digits'
        })
        
        expect(validateTinNumber('**********1')).toEqual({
          isValid: false,
          message: 'TIN number must be exactly 10 digits'
        })
        
        expect(validateTinNumber('abcd123456')).toEqual({
          isValid: false,
          message: 'TIN number must contain only digits'
        })
      })
    })

    describe('validateEmail', () => {
      it('validates correct email addresses', () => {
        expect(validateEmail('<EMAIL>')).toEqual({
          isValid: true
        })
      })

      it('rejects invalid email addresses', () => {
        expect(validateEmail('invalid-email')).toEqual({
          isValid: false,
          message: 'Please enter a valid email address (e.g., <EMAIL>)'
        })
      })

      it('handles empty email addresses', () => {
        expect(validateEmail('')).toEqual({
          isValid: true
        })
      })
    })
  })
})
