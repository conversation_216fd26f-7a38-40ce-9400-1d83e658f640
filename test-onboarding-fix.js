/**
 * Test script to verify the onboarding status check works
 * Run this in the browser console to test the fix
 */

// Test the specific user that was having issues
const testUserId = 'e02bb86f-4e74-4332-9ec4-c60c8739838b';

console.log('🧪 Testing onboarding status check for user:', testUserId);

// Import the function (this would work in the browser context)
// import { checkOnboardingStatus } from '@/lib/onboardingUtils';

// Test function that can be run in browser console
async function testOnboardingStatus() {
  try {
    console.log('1. Testing Supabase client method chaining...');
    
    // Test the exact method chain that was failing
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', testUserId)
      .maybeSingle();

    if (profileError) {
      console.error('❌ Profile query failed:', profileError);
      return;
    }

    console.log('✅ Profile query successful:', profile);

    if (profile) {
      console.log('2. Testing organization query...');
      
      const { data: org, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', profile.org_id)
        .maybeSingle();

      if (orgError) {
        console.error('❌ Organization query failed:', orgError);
      } else {
        console.log('✅ Organization query successful:', org);
      }

      // Determine onboarding status
      const hasProfile = profile !== null;
      const hasOrganization = profile?.org_id !== null;
      const hasCompletedOnboarding = profile?.onboarding_completed_at !== null;
      const needsOnboarding = !hasProfile || !hasOrganization || !hasCompletedOnboarding;

      console.log('3. Onboarding Status Summary:');
      console.log('   - Has Profile:', hasProfile);
      console.log('   - Has Organization:', hasOrganization);
      console.log('   - Has Completed Onboarding:', hasCompletedOnboarding);
      console.log('   - Needs Onboarding:', needsOnboarding);

      if (!needsOnboarding) {
        console.log('🎉 SUCCESS: User should NOT need onboarding');
      } else {
        console.log('⚠️  User still needs onboarding');
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    });
  }
}

// Instructions for manual testing
console.log(`
📋 MANUAL TEST INSTRUCTIONS:
1. Open browser developer tools
2. Navigate to the application
3. Run: testOnboardingStatus()
4. Check the console output

Expected result: User should NOT need onboarding
`);

// Export for browser console use
if (typeof window !== 'undefined') {
  window.testOnboardingStatus = testOnboardingStatus;
}
