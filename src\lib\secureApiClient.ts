import { supabase } from '@/lib/supabase'
import { InputSanitizer } from '@/lib/security'
import { logger, logSecurity } from '@/lib/logger'
import { handleSecurityError } from '@/lib/errorHandler'

// Type definitions for API data
interface CustomerData {
  name: string
  email?: string
  phone?: string
  tin_number?: string
  address?: string
  notes?: string
  payment_terms?: number
  org_id: string
}

interface VendorData {
  name: string
  email?: string
  phone?: string
  tin_number?: string
  address?: string
  notes?: string
  payment_terms?: number
  org_id: string
}

interface InvoiceData {
  customer_id: string
  invoice_number: string
  description?: string
  date_issued: string
  due_date: string
  subtotal?: number
  tax_amount?: number
  total_amount?: number
  status: string
  org_id: string
}

interface PaymentData {
  payee_id: string
  payee_type: string
  amount: number
  payment_date: string
  channel: string
  reference?: string
  description?: string
  bank_account_id?: string
  org_id: string
}

// Input sanitization utilities
export class InputSanitizer {
  static sanitizeText(input: string | null | undefined): string | null {
    if (!input || typeof input !== 'string') return null
    
    // Remove potentially dangerous characters but preserve normal text
    return input
      .trim()
      .replace(/[<>"']/g, '') // Remove HTML/script injection chars
      .replace(/[^\x20-\x7E]/g, '') // Remove non-printable characters
      .substring(0, 1000) // Limit length
  }

  static sanitizeEmail(email: string | null | undefined): string | null {
    if (!email) return null
    
    const sanitized = this.sanitizeText(email)
    if (!sanitized) return null
    
    // Basic email validation
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/
    return emailRegex.test(sanitized) ? sanitized : null
  }

  static sanitizePhone(phone: string | null | undefined): string | null {
    if (!phone) return null
    
    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '')
    
    // Validate Ugandan phone format
    const phoneRegex = /^\+256[789][0-9]{8}$/
    return phoneRegex.test(cleaned) ? cleaned : null
  }

  static sanitizeNumber(value: unknown): number | null {
    if (value === null || value === undefined || value === '') return null
    
    const num = Number(value)
    return isNaN(num) ? null : num
  }

  static sanitizeSearchQuery(query: string | null | undefined): string | null {
    if (!query) return null
    
    return query
      .trim()
      .replace(/[<>"'%;()&+]/g, '') // Remove dangerous chars
      .replace(/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b/gi, '') // Remove SQL keywords
      .substring(0, 100) // Limit length
  }
}

// Enhanced security logging
const logSecureOperation = (operation: string, data: Record<string, unknown> | null, success: boolean, error?: Error) => {
  const logLevel = success ? 'info' : 'error'
  logger[logLevel](`Secure API operation: ${operation}`, {
    component: 'SecureApiClient',
    action: operation,
    metadata: {
      success,
      dataKeys: Object.keys(data || {}),
      error: error?.message
    }
  })

  if (!success && error) {
    handleSecurityError(error, {
      component: 'SecureApiClient',
      action: operation,
      metadata: { operation, data: Object.keys(data || {}) }
    })
  }
}

// Secure API client with CSRF protection
export class SecureApiClient {
  private csrfManager: CSRFTokenManager

  constructor() {
    this.csrfManager = CSRFTokenManager.getInstance()
  }

  private getSecureHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    const csrfToken = this.csrfManager.getToken()
    if (csrfToken) {
      headers['X-CSRF-Token'] = csrfToken
    }

    return headers
  }

  // Secure customer operations
  async createCustomer(customerData: CustomerData) {
    const sanitizedData = {
      name: InputSanitizer.sanitizeText(customerData.name),
      email: InputSanitizer.sanitizeEmail(customerData.email),
      phone: InputSanitizer.sanitizePhone(customerData.phone),
      tin_number: InputSanitizer.sanitizeText(customerData.tin_number),
      address: InputSanitizer.sanitizeText(customerData.address),
      notes: InputSanitizer.sanitizeText(customerData.notes),
      payment_terms: InputSanitizer.sanitizeNumber(customerData.payment_terms),
      org_id: customerData.org_id
    }

    // Validate required fields
    if (!sanitizedData.name || !sanitizedData.org_id) {
      throw new Error('Name and organization are required')
    }

    const { data, error } = await supabase
      .from('customers')
      .insert([sanitizedData])
      .select()

    if (error) throw error
    return data
  }

  async updateCustomer(id: string, customerData: Partial<CustomerData>) {
    const sanitizedData = {
      name: InputSanitizer.sanitizeText(customerData.name),
      email: InputSanitizer.sanitizeEmail(customerData.email),
      phone: InputSanitizer.sanitizePhone(customerData.phone),
      tin_number: InputSanitizer.sanitizeText(customerData.tin_number),
      address: InputSanitizer.sanitizeText(customerData.address),
      notes: InputSanitizer.sanitizeText(customerData.notes),
      payment_terms: InputSanitizer.sanitizeNumber(customerData.payment_terms)
    }

    const { data, error } = await supabase
      .from('customers')
      .update(sanitizedData)
      .eq('id', id)
      .select()

    if (error) throw error
    return data
  }

  // Secure vendor operations
  async createVendor(vendorData: VendorData) {
    const sanitizedData = {
      name: InputSanitizer.sanitizeText(vendorData.name),
      email: InputSanitizer.sanitizeEmail(vendorData.email),
      phone: InputSanitizer.sanitizePhone(vendorData.phone),
      tin_number: InputSanitizer.sanitizeText(vendorData.tin_number),
      address: InputSanitizer.sanitizeText(vendorData.address),
      notes: InputSanitizer.sanitizeText(vendorData.notes),
      payment_terms: InputSanitizer.sanitizeNumber(vendorData.payment_terms),
      org_id: vendorData.org_id
    }

    if (!sanitizedData.name || !sanitizedData.org_id) {
      throw new Error('Name and organization are required')
    }

    const { data, error } = await supabase
      .from('vendors')
      .insert([sanitizedData])
      .select()

    if (error) throw error
    return data
  }

  // Secure search operations
  async searchCustomers(orgId: string, searchQuery: string) {
    const sanitizedQuery = InputSanitizer.sanitizeSearchQuery(searchQuery)
    if (!sanitizedQuery) return []

    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('org_id', orgId)
      .or(`name.ilike.%${sanitizedQuery}%,email.ilike.%${sanitizedQuery}%,phone.ilike.%${sanitizedQuery}%`)
      .limit(50) // Limit results to prevent DoS

    if (error) throw error
    return data || []
  }

  async searchVendors(orgId: string, searchQuery: string) {
    const sanitizedQuery = InputSanitizer.sanitizeSearchQuery(searchQuery)
    if (!sanitizedQuery) return []

    const { data, error } = await supabase
      .from('vendors')
      .select('*')
      .eq('org_id', orgId)
      .or(`name.ilike.%${sanitizedQuery}%,email.ilike.%${sanitizedQuery}%,phone.ilike.%${sanitizedQuery}%`)
      .limit(50)

    if (error) throw error
    return data || []
  }

  async searchInvoices(orgId: string, searchQuery: string) {
    const sanitizedQuery = InputSanitizer.sanitizeSearchQuery(searchQuery)
    if (!sanitizedQuery) return []

    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        customers(*)
      `)
      .eq('org_id', orgId)
      .or(`invoice_number.ilike.%${sanitizedQuery}%,description.ilike.%${sanitizedQuery}%`)
      .limit(50)

    if (error) throw error
    return data || []
  }

  // Secure financial operations
  async createInvoice(invoiceData: InvoiceData) {
    const sanitizedData = {
      customer_id: invoiceData.customer_id,
      invoice_number: InputSanitizer.sanitizeText(invoiceData.invoice_number),
      description: InputSanitizer.sanitizeText(invoiceData.description),
      date_issued: invoiceData.date_issued,
      due_date: invoiceData.due_date,
      subtotal: InputSanitizer.sanitizeNumber(invoiceData.subtotal),
      tax_amount: InputSanitizer.sanitizeNumber(invoiceData.tax_amount),
      total_amount: InputSanitizer.sanitizeNumber(invoiceData.total_amount),
      status: invoiceData.status,
      org_id: invoiceData.org_id
    }

    // Validate required fields and business logic
    if (!sanitizedData.customer_id || !sanitizedData.org_id) {
      throw new Error('Customer and organization are required')
    }

    if (sanitizedData.total_amount && sanitizedData.total_amount <= 0) {
      throw new Error('Total amount must be positive')
    }

    const { data, error } = await supabase
      .from('invoices')
      .insert([sanitizedData])
      .select()

    if (error) throw error
    return data
  }

  async createPayment(paymentData: PaymentData) {
    const sanitizedData = {
      payee_id: paymentData.payee_id,
      payee_type: paymentData.payee_type,
      amount: InputSanitizer.sanitizeNumber(paymentData.amount),
      payment_date: paymentData.payment_date,
      channel: paymentData.channel,
      reference: InputSanitizer.sanitizeText(paymentData.reference),
      description: InputSanitizer.sanitizeText(paymentData.description),
      bank_account_id: paymentData.bank_account_id,
      org_id: paymentData.org_id
    }

    if (!sanitizedData.payee_id || !sanitizedData.amount || sanitizedData.amount <= 0) {
      throw new Error('Valid payee and positive amount are required')
    }

    const { data, error } = await supabase
      .from('payments')
      .insert([sanitizedData])
      .select()

    if (error) throw error
    return data
  }
}

// Export singleton instance
export const secureApiClient = new SecureApiClient()
