import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'
import { RestoreOptions } from '@/components/backup/RestoreBackupDialog'
import { BackupService } from '@/lib/backup-service'

export interface RestoreJob {
  id: string
  org_id: string
  backup_id: string
  restore_type: 'full' | 'partial' | 'point_in_time'
  restore_mode: 'replace' | 'merge' | 'preview'
  status: 'pending' | 'validating' | 'downloading' | 'restoring' | 'completed' | 'failed' | 'cancelled'
  selected_tables?: string[]
  restore_point?: string
  total_tables: number
  completed_tables: number
  total_records: number
  restored_records: number
  progress_percentage: number
  error_code?: string
  error_message?: string
  warnings?: string[]
  backup_validated: boolean
  validation_errors?: string[]
  checksum_verified: boolean
  requested_at: string
  started_at?: string
  completed_at?: string
  estimated_completion?: string
  requested_by: string
  approved_by?: string
  restore_notes?: string
  pre_restore_snapshot_id?: string
}

export interface RestoreLog {
  id: string
  restoration_id: string
  log_level: 'info' | 'warning' | 'error' | 'debug'
  message: string
  details?: Record<string, unknown>
  table_name?: string
  operation?: string
  created_at: string
}

export function useRestoration() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  // Get restoration jobs for organization
  const restoreJobsQuery = useQuery({
    queryKey: ['restore-jobs', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('backup_restorations')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('requested_at', { ascending: false })

      if (error) throw error
      return data as RestoreJob[]
    },
    enabled: !!profile?.org_id,
    refetchInterval: (query) => {
      // Refetch every 2 seconds if there are active restoration jobs
      const hasActiveJobs = Array.isArray(query.data) && query.data.some(job =>
        ['pending', 'validating', 'downloading', 'restoring'].includes(job.status)
      )
      return hasActiveJobs ? 2000 : false
    }
  })

  // Get restoration logs for a specific job
  const useRestoreLogs = (restorationId?: string) => {
    return useQuery({
      queryKey: ['restore-logs', restorationId],
      queryFn: async () => {
        if (!restorationId) return []

        const { data, error } = await supabase
          .from('restoration_logs')
          .select('*')
          .eq('restoration_id', restorationId)
          .order('created_at', { ascending: false })

        if (error) throw error
        return data as RestoreLog[]
      },
      enabled: !!restorationId,
      refetchInterval: 2000 // Refetch logs every 2 seconds
    })
  }

  // Get restoration statistics
  const statisticsQuery = useQuery({
    queryKey: ['restore-statistics', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .rpc('get_restoration_statistics', { org_id_param: profile.org_id })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
  })

  // Create restoration job mutation
  const createRestoreJobMutation = useMutation({
    mutationFn: async (options: RestoreOptions) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .rpc('create_restoration_job', {
          backup_id_param: options.backupId,
          restore_type_param: options.restoreType,
          restore_mode_param: options.restoreMode,
          selected_tables_param: options.selectedTables || null,
          restore_notes_param: options.notes || null
        })

      if (error) throw error
      return data as string // Returns restoration job ID
    },
    onSuccess: (restorationId, options) => {
      queryClient.invalidateQueries({ queryKey: ['restore-jobs'] })
      
      if (options.restoreMode === 'preview') {
        toast.success('Restoration preview started')
      } else {
        toast.success('Restoration job created successfully')
      }
      
      // Start polling for this specific job
      queryClient.invalidateQueries({ queryKey: ['restore-logs', restorationId] })
    },
    onError: (error) => {
      toast.error('Failed to create restoration job: ' + error.message)
    },
  })

  // Cancel restoration job mutation
  const cancelRestoreJobMutation = useMutation({
    mutationFn: async (restorationId: string) => {
      const { error } = await supabase
        .rpc('update_restoration_progress', {
          restoration_id_param: restorationId,
          status_param: 'cancelled'
        })

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restore-jobs'] })
      toast.success('Restoration job cancelled')
    },
    onError: (error) => {
      toast.error('Failed to cancel restoration: ' + error.message)
    },
  })

  // Approve restoration job (admin only)
  const approveRestoreJobMutation = useMutation({
    mutationFn: async (restorationId: string) => {
      if (profile?.role !== 'admin') {
        throw new Error('Only administrators can approve restoration jobs')
      }

      const { error } = await supabase
        .from('backup_restorations')
        .update({ 
          approved_by: profile.id,
          status: 'pending' // Move from any status to pending for processing
        })
        .eq('id', restorationId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restore-jobs'] })
      toast.success('Restoration job approved')
    },
    onError: (error) => {
      toast.error('Failed to approve restoration: ' + error.message)
    },
  })

  // Delete restoration job (completed/failed only)
  const deleteRestoreJobMutation = useMutation({
    mutationFn: async (restorationId: string) => {
      const { error } = await supabase
        .from('backup_restorations')
        .delete()
        .eq('id', restorationId)
        .in('status', ['completed', 'failed', 'cancelled'])

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restore-jobs'] })
      toast.success('Restoration job deleted')
    },
    onError: (error) => {
      toast.error('Failed to delete restoration job: ' + error.message)
    },
  })

  return {
    // Data
    restoreJobs: restoreJobsQuery.data || [],
    statistics: statisticsQuery.data,
    isLoading: restoreJobsQuery.isLoading || statisticsQuery.isLoading,
    error: restoreJobsQuery.error || statisticsQuery.error,

    // Mutations
    createRestoreJob: createRestoreJobMutation.mutateAsync,
    cancelRestoreJob: cancelRestoreJobMutation.mutateAsync,
    approveRestoreJob: approveRestoreJobMutation.mutateAsync,
    deleteRestoreJob: deleteRestoreJobMutation.mutateAsync,

    // Mutation states
    isCreating: createRestoreJobMutation.isPending,
    isCancelling: cancelRestoreJobMutation.isPending,
    isApproving: approveRestoreJobMutation.isPending,
    isDeleting: deleteRestoreJobMutation.isPending,

    // Utility hook
    useRestoreLogs
  }
}

// Hook for real-time restoration progress
export function useRestorationProgress(restorationId?: string) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['restore-progress', restorationId],
    queryFn: async () => {
      if (!restorationId || !profile?.org_id) return null

      const { data, error } = await supabase
        .from('backup_restorations')
        .select('*')
        .eq('id', restorationId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data as RestoreJob
    },
    enabled: !!restorationId && !!profile?.org_id,
    refetchInterval: (query) => {
      // Refetch every 2 seconds if restoration is in progress
      const isInProgress = query.data && ['pending', 'validating', 'downloading', 'restoring'].includes(query.data.status)
      return isInProgress ? 2000 : false
    }
  })
}
