import { supabase } from '@/integrations/supabase/client'
import { sendUserInvitation } from '@/lib/emailService'
import type { UserRole } from '@/types/database'

/**
 * Generate a secure token using Web Crypto API (fallback)
 */
function generateSecureToken(): string {
  const array = new Uint8Array(32)
  crypto.getRandomValues(array)

  // Convert to base64url format (URL-safe base64)
  return btoa(String.fromCharCode(...array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '')
}

export interface CreateInvitationRequest {
  email: string
  role: UserRole
  phone?: string
  orgId: string
  inviterName: string
  organizationName: string
}

export interface InvitationResponse {
  success: boolean
  message: string
  invitationId?: string
  error?: string
}

export interface UserInvitation {
  id: string
  email: string
  role: UserRole
  phone?: string
  token: string
  status: 'pending' | 'accepted' | 'expired' | 'cancelled'
  invited_by: string
  invited_at: string
  expires_at: string
  accepted_at?: string
  accepted_by?: string
}

/**
 * Create and send a user invitation
 */
export async function createUserInvitation(request: CreateInvitationRequest): Promise<InvitationResponse> {
  try {
    // Generate a secure invitation token
    let token: string

    try {
      const { data: tokenData, error: tokenError } = await supabase.rpc('generate_invitation_token')

      if (tokenError) {
        console.warn('Database token generation failed, using fallback:', tokenError)
        // Fallback to client-side token generation
        token = generateSecureToken()
      } else {
        token = tokenData as string
      }
    } catch (error) {
      console.warn('RPC call failed, using fallback token generation:', error)
      // Fallback to client-side token generation
      token = generateSecureToken()
    }

    // Get current user for invited_by field
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      throw new Error('Authentication required')
    }

    // Create the invitation record
    const { data: invitation, error: invitationError } = await supabase
      .from('user_invitations')
      .insert({
        email: request.email.toLowerCase().trim(),
        role: request.role,
        phone: request.phone || null,
        org_id: request.orgId,
        token: token,
        invited_by: user.id,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
      })
      .select()
      .single()

    if (invitationError) {
      // Check if it's a duplicate invitation
      if (invitationError.code === '23505') {
        throw new Error('An invitation for this email is already pending')
      }
      throw new Error(`Failed to create invitation: ${invitationError.message}`)
    }

    // Try to send the invitation email (optional - system works without it)
    let emailSent = false
    let emailError = ''

    try {
      const emailResult = await sendUserInvitation(
        request.email,
        request.role,
        request.inviterName,
        request.organizationName,
        token,
        request.orgId
      )

      if (emailResult.success) {
        emailSent = true
      } else {
        emailError = emailResult.error || 'Email service unavailable'
        console.warn('Email sending failed:', emailError)
      }
    } catch (error) {
      emailError = error instanceof Error ? error.message : 'Email service unavailable'
      console.warn('Email service error:', error)
    }

    // Return success with email status information
    const message = emailSent
      ? 'Invitation sent successfully with email notification'
      : emailError.includes('Edge Function')
        ? 'Invitation created successfully (email service not configured - user can be invited manually)'
        : `Invitation created successfully (email failed: ${emailError})`

    return {
      success: true,
      message: message,
      invitationId: invitation.id
    }

  } catch (error) {
    console.error('Error creating user invitation:', error)

    // Provide more specific error messages
    let errorMessage = 'Failed to send invitation'
    if (error instanceof Error) {
      if (error.message.includes('generate_invitation_token')) {
        errorMessage = 'Database function not available. Please ensure migrations are run.'
      } else if (error.message.includes('user_invitations')) {
        errorMessage = 'Invitations table not found. Please run database migrations.'
      } else if (error.message.includes('duplicate')) {
        errorMessage = 'An invitation for this email is already pending.'
      } else {
        errorMessage = error.message
      }
    }

    return {
      success: false,
      message: errorMessage,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Validate an invitation token
 */
export async function validateInvitationToken(token: string) {
  try {
    const { data, error } = await supabase.rpc('validate_invitation_token', {
      invitation_token: token
    })

    if (error) {
      throw error
    }

    return data[0] || null
  } catch (error) {
    console.error('Error validating invitation token:', error)
    return null
  }
}

/**
 * Accept an invitation and create user account
 */
export async function acceptInvitation(
  token: string,
  password: string,
  fullName?: string
): Promise<InvitationResponse> {
  try {
    // First validate the token
    const validation = await validateInvitationToken(token)
    
    if (!validation || !validation.is_valid) {
      throw new Error(validation?.error_message || 'Invalid invitation token')
    }

    // Create the user account
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: validation.email,
      password: password,
      options: {
        data: {
          role: validation.role,
          full_name: fullName
        }
      }
    })

    if (authError) {
      throw new Error(`Failed to create account: ${authError.message}`)
    }

    if (!authData.user) {
      throw new Error('Failed to create user account')
    }

    // Create the user profile with onboarding completed
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email: validation.email,
        role: validation.role,
        phone: validation.phone,
        org_id: validation.org_id,
        onboarding_completed_at: new Date().toISOString() // Mark onboarding as completed for invited users
      })

    if (profileError) {
      throw new Error(`Failed to create user profile: ${profileError.message}`)
    }

    // Mark the invitation as accepted
    const { error: updateError } = await supabase
      .from('user_invitations')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString(),
        accepted_by: authData.user.id
      })
      .eq('id', validation.invitation_id)

    if (updateError) {
      console.error('Failed to update invitation status:', updateError)
      // Don't fail the whole process for this
    }

    return {
      success: true,
      message: 'Account created successfully'
    }

  } catch (error) {
    console.error('Error accepting invitation:', error)
    return {
      success: false,
      message: 'Failed to accept invitation',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Get pending invitations for an organization
 */
export async function getOrganizationInvitations(orgId: string): Promise<UserInvitation[]> {
  try {
    const { data, error } = await supabase
      .from('user_invitations')
      .select(`
        *,
        inviter:invited_by(email),
        accepter:accepted_by(email)
      `)
      .eq('org_id', orgId)
      .order('invited_at', { ascending: false })

    if (error) {
      throw error
    }

    return data || []
  } catch (error) {
    console.error('Error fetching invitations:', error)
    return []
  }
}

/**
 * Cancel a pending invitation
 */
export async function cancelInvitation(invitationId: string): Promise<InvitationResponse> {
  try {
    const { error } = await supabase
      .from('user_invitations')
      .update({ status: 'cancelled' })
      .eq('id', invitationId)
      .eq('status', 'pending') // Only cancel pending invitations

    if (error) {
      throw error
    }

    return {
      success: true,
      message: 'Invitation cancelled successfully'
    }
  } catch (error) {
    console.error('Error cancelling invitation:', error)
    return {
      success: false,
      message: 'Failed to cancel invitation',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Resend an invitation
 */
export async function resendInvitation(
  invitationId: string,
  inviterName: string,
  organizationName: string
): Promise<InvitationResponse> {
  try {
    // Get the invitation details
    const { data: invitation, error: fetchError } = await supabase
      .from('user_invitations')
      .select('*')
      .eq('id', invitationId)
      .eq('status', 'pending')
      .single()

    if (fetchError || !invitation) {
      throw new Error('Invitation not found or already processed')
    }

    // Check if invitation has expired
    if (new Date(invitation.expires_at) < new Date()) {
      // Extend expiration by 7 days
      const newExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      
      await supabase
        .from('user_invitations')
        .update({ expires_at: newExpiresAt })
        .eq('id', invitationId)
    }

    // Resend the email
    const emailResult = await sendUserInvitation(
      invitation.email,
      invitation.role,
      inviterName,
      organizationName,
      invitation.token,
      invitation.org_id
    )

    if (!emailResult.success) {
      throw new Error(`Failed to resend invitation email: ${emailResult.error}`)
    }

    return {
      success: true,
      message: 'Invitation resent successfully'
    }

  } catch (error) {
    console.error('Error resending invitation:', error)
    return {
      success: false,
      message: 'Failed to resend invitation',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Clean up expired invitations
 */
export async function cleanupExpiredInvitations(): Promise<number> {
  try {
    const { data, error } = await supabase.rpc('cleanup_expired_invitations')

    if (error) {
      throw error
    }

    return data || 0
  } catch (error) {
    console.error('Error cleaning up expired invitations:', error)
    return 0
  }
}

/**
 * Generate invitation URL for manual sharing
 */
export function generateInvitationUrl(token: string): string {
  const baseUrl = window.location.origin
  return `${baseUrl}/accept-invitation?token=${token}`
}

/**
 * Copy invitation URL to clipboard
 */
export async function copyInvitationUrl(token: string): Promise<boolean> {
  try {
    const url = generateInvitationUrl(token)
    await navigator.clipboard.writeText(url)
    return true
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return false
  }
}
