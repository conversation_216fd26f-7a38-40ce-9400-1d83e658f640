import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys, type QueryFilters } from '@/lib/queryKeys'
import { toast } from 'sonner'

/**
 * Hook to fetch tax rates for the organization
 */
export function useTaxRates(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.tax.rates(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('tax_rates')
        .select('*')
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }
      
      if (filters?.active !== undefined) {
        query = query.eq('is_active', filters.active)
      }

      query = query.order('name')

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes - tax rates change infrequently
  })
}

/**
 * Hook to fetch withholding tax rates for the organization
 */
export function useWithholdingTaxRates(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.tax.withholdingRates(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('withholding_tax_rates')
        .select('*')
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }
      
      if (filters?.active !== undefined) {
        query = query.eq('is_active', filters.active)
      }

      query = query.order('name')

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes - withholding rates change infrequently
  })
}

/**
 * Hook to fetch active withholding tax rates only
 */
export function useActiveWithholdingTaxRates() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.tax.withholdingRates(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('withholding_tax_rates')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('name')

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 15 * 60 * 1000, // 15 minutes - active rates are very stable
  })
}

/**
 * Hook to fetch URA tax filings for the organization
 */
export function useUraTaxFilings(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.tax.filings(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('ura_tax_filings')
        .select('*')
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`filing_type.ilike.%${filters.search}%,reference_number.ilike.%${filters.search}%`)
      }
      
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      query = query.order('filing_date', { ascending: false })

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes - filings can change status
  })
}

/**
 * Hook to create a new tax rate
 */
export function useCreateTaxRate() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (taxRateData: {
      name: string
      rate: number
      description?: string
      is_active?: boolean
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('tax_rates')
        .insert({
          ...taxRateData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      // Invalidate and refetch tax rates
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.tax.rates(profile?.org_id || '') 
      })

      toast.success('Tax rate created successfully')
    },
    onError: (error) => {
      console.error('Error creating tax rate:', error)
      toast.error('Failed to create tax rate')
    },
  })
}

/**
 * Hook to create a new withholding tax rate
 */
export function useCreateWithholdingTaxRate() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (withholdingRateData: {
      name: string
      rate: number
      description?: string
      is_active?: boolean
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('withholding_tax_rates')
        .insert({
          ...withholdingRateData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      // Invalidate and refetch withholding tax rates
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.tax.withholdingRates(profile?.org_id || '') 
      })

      toast.success('Withholding tax rate created successfully')
    },
    onError: (error) => {
      console.error('Error creating withholding tax rate:', error)
      toast.error('Failed to create withholding tax rate')
    },
  })
}

/**
 * Hook to create a new URA tax filing
 */
export function useCreateUraTaxFiling() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (filingData: {
      filing_type: string
      filing_date: string
      due_date: string
      amount: number
      reference_number?: string
      status?: string
      description?: string
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('ura_tax_filings')
        .insert({
          ...filingData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      // Invalidate and refetch URA tax filings
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.tax.filings(profile?.org_id || '') 
      })

      toast.success('URA tax filing created successfully')
    },
    onError: (error) => {
      console.error('Error creating URA tax filing:', error)
      toast.error('Failed to create URA tax filing')
    },
  })
}

/**
 * Hook to update a tax rate
 */
export function useUpdateTaxRate() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      taxRateId, 
      taxRateData 
    }: { 
      taxRateId: string
      taxRateData: {
        name?: string
        rate?: number
        description?: string
        is_active?: boolean
      }
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('tax_rates')
        .update({
          ...taxRateData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', taxRateId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      // Invalidate tax rates
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.tax.rates(profile?.org_id || '') 
      })

      toast.success('Tax rate updated successfully')
    },
    onError: (error) => {
      console.error('Error updating tax rate:', error)
      toast.error('Failed to update tax rate')
    },
  })
}

/**
 * Hook to update a withholding tax rate
 */
export function useUpdateWithholdingTaxRate() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      withholdingRateId, 
      withholdingRateData 
    }: { 
      withholdingRateId: string
      withholdingRateData: {
        name?: string
        rate?: number
        description?: string
        is_active?: boolean
      }
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('withholding_tax_rates')
        .update({
          ...withholdingRateData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', withholdingRateId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      // Invalidate withholding tax rates
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.tax.withholdingRates(profile?.org_id || '') 
      })

      toast.success('Withholding tax rate updated successfully')
    },
    onError: (error) => {
      console.error('Error updating withholding tax rate:', error)
      toast.error('Failed to update withholding tax rate')
    },
  })
}

/**
 * Hook to delete a tax rate
 */
export function useDeleteTaxRate() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (taxRateId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { error } = await supabase
        .from('tax_rates')
        .delete()
        .eq('id', taxRateId)
        .eq('org_id', profile.org_id)

      if (error) throw error
      return taxRateId
    },
    onSuccess: () => {
      // Invalidate tax rates
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.tax.rates(profile?.org_id || '') 
      })

      toast.success('Tax rate deleted successfully')
    },
    onError: (error) => {
      console.error('Error deleting tax rate:', error)
      toast.error('Failed to delete tax rate')
    },
  })
}
