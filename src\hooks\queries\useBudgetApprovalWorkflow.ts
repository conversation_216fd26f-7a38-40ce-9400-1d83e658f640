import { useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from '@/components/ui/toast-utils'
import { useBudgetValidation } from './useBudgetValidation'
import { ApprovalEngine } from '@/lib/approval-engine/core'
import type { DocumentType } from '@/types/database'

export interface BudgetApprovalSubmissionRequest {
  documentType: DocumentType
  documentId: string
  documentAmount: number
  accountId: string
  currencyCode?: string
  metadata?: Record<string, unknown>
  budgetOverrideJustification?: string
  requiresBudgetApproval?: boolean
}

export interface BudgetApprovalResult {
  approvalInstanceId: string
  requiresBudgetApproval: boolean
  budgetExceedanceAmount: number
  escalationLevel: 'normal' | 'budget_override' | 'critical_override'
  message: string
}

/**
 * Hook to submit documents for approval with budget validation
 */
export const useBudgetAwareApprovalSubmission = () => {
  const queryClient = useQueryClient()
  const { user, profile } = useAuth()

  return useMutation({
    mutationFn: async (request: BudgetApprovalSubmissionRequest): Promise<BudgetApprovalResult> => {
      if (!user?.id || !profile?.org_id) {
        throw new Error('User authentication required')
      }

      // Get budget validation for the account and amount
      const budgetResponse = await supabase
        .from('budget_lines')
        .select(`
          amount,
          budget_id,
          account_id,
          accounts!inner(name, code, type),
          budgets!inner(
            id,
            name,
            start_date,
            end_date,
            status
          )
        `)
        .eq('account_id', request.accountId)
        .eq('budgets.status', 'approved')
        .lte('budgets.start_date', new Date().toISOString().split('T')[0])
        .gte('budgets.end_date', new Date().toISOString().split('T')[0])
        .order('budgets.created_at', { ascending: false })
        .limit(1)

      let budgetExceedanceAmount = 0
      let requiresBudgetApproval = false
      let escalationLevel: 'normal' | 'budget_override' | 'critical_override' = 'normal'

      if (budgetResponse.data && budgetResponse.data.length > 0) {
        const budgetLine = budgetResponse.data[0]
        const budget = budgetLine.budgets
        const account = budgetLine.accounts

        // Get actual spending for this account in the budget period
        const { data: transactions } = await supabase
          .from('transaction_lines')
          .select('debit, credit')
          .eq('org_id', profile.org_id)
          .eq('account_id', request.accountId)
          .gte('created_at', `${budget.start_date}T00:00:00`)
          .lte('created_at', `${budget.end_date}T23:59:59`)

        // Calculate actual amount based on account type
        const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
        const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0
        
        let actualAmount = 0
        if (account.type === 'expense') {
          actualAmount = debitTotal - creditTotal
        } else if (account.type === 'income') {
          actualAmount = creditTotal - debitTotal
        } else if (account.type === 'asset') {
          actualAmount = debitTotal - creditTotal
        } else {
          actualAmount = creditTotal - debitTotal
        }

        const budgetAmount = budgetLine.amount || 0
        const projectedAmount = actualAmount + request.documentAmount

        if (projectedAmount > budgetAmount) {
          budgetExceedanceAmount = projectedAmount - budgetAmount
          requiresBudgetApproval = true

          // Determine escalation level based on exceedance
          const exceedancePercent = (budgetExceedanceAmount / budgetAmount) * 100
          if (exceedancePercent > 50) {
            escalationLevel = 'critical_override'
          } else {
            escalationLevel = 'budget_override'
          }
        }
      }

      // Prepare metadata with budget information
      const enhancedMetadata = {
        ...request.metadata,
        budget_validation: {
          account_id: request.accountId,
          budget_exceedance_amount: budgetExceedanceAmount,
          requires_budget_approval: requiresBudgetApproval,
          escalation_level: escalationLevel,
          budget_override_justification: request.budgetOverrideJustification,
          validated_at: new Date().toISOString()
        }
      }

      // Determine workflow template based on budget impact
      let workflowType = request.documentType
      if (requiresBudgetApproval) {
        workflowType = escalationLevel === 'critical_override' 
          ? `${request.documentType}_critical_budget_override` as DocumentType
          : `${request.documentType}_budget_override` as DocumentType
      }

      // Submit for approval using the approval engine
      const approvalEngine = new ApprovalEngine()
      const approvalInstance = await approvalEngine.submitForApproval({
        documentType: workflowType,
        documentId: request.documentId,
        submittedBy: user.id,
        organizationId: profile.org_id,
        documentAmount: request.documentAmount,
        currencyCode: request.currencyCode || 'UGX',
        metadata: enhancedMetadata
      })

      // Create budget approval record if needed
      if (requiresBudgetApproval) {
        await supabase
          .from('budget_approvals')
          .insert({
            approval_instance_id: approvalInstance.id,
            account_id: request.accountId,
            document_type: request.documentType,
            document_id: request.documentId,
            exceedance_amount: budgetExceedanceAmount,
            escalation_level: escalationLevel,
            justification: request.budgetOverrideJustification,
            org_id: profile.org_id,
            created_by: user.id
          })
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['approvals'] })
      queryClient.invalidateQueries({ queryKey: ['budget-alerts', profile.org_id] })

      const message = requiresBudgetApproval
        ? `Document submitted for ${escalationLevel.replace('_', ' ')} approval due to budget exceedance of ${budgetExceedanceAmount.toLocaleString()}`
        : 'Document submitted for standard approval'

      return {
        approvalInstanceId: approvalInstance.id,
        requiresBudgetApproval,
        budgetExceedanceAmount,
        escalationLevel,
        message
      }
    },
    onSuccess: (result) => {
      toast.success(result.message)
    },
    onError: (error) => {
      console.error('Budget approval submission error:', error)
      toast.error('Failed to submit for approval')
    }
  })
}

/**
 * Hook to get budget approval details for an approval instance
 */
export const useBudgetApprovalDetails = (approvalInstanceId: string) => {
  return useQuery({
    queryKey: ['budget-approval', approvalInstanceId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('budget_approvals')
        .select(`
          *,
          accounts(name, code),
          approval_instances(
            document_type,
            document_amount,
            metadata
          )
        `)
        .eq('approval_instance_id', approvalInstanceId)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!approvalInstanceId
  })
}

/**
 * Hook to approve/reject budget overrides
 */
export const useBudgetApprovalAction = () => {
  const queryClient = useQueryClient()
  const { user } = useAuth()

  return useMutation({
    mutationFn: async ({
      approvalInstanceId,
      action,
      comments,
      budgetAdjustment
    }: {
      approvalInstanceId: string
      action: 'approve' | 'reject'
      comments?: string
      budgetAdjustment?: number
    }) => {
      if (!user?.id) {
        throw new Error('User authentication required')
      }

      // Update budget approval record
      await supabase
        .from('budget_approvals')
        .update({
          status: action === 'approve' ? 'approved' : 'rejected',
          approved_by: action === 'approve' ? user.id : null,
          approved_at: action === 'approve' ? new Date().toISOString() : null,
          rejection_reason: action === 'reject' ? comments : null,
          budget_adjustment: budgetAdjustment
        })
        .eq('approval_instance_id', approvalInstanceId)

      // Process the approval through the standard workflow
      const approvalEngine = new ApprovalEngine()
      return await approvalEngine.processApprovalAction({
        approvalInstanceId,
        action,
        comments,
        actionBy: user.id
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['approvals'] })
      queryClient.invalidateQueries({ queryKey: ['budget-approval'] })
      toast.success('Budget approval processed successfully')
    },
    onError: (error) => {
      console.error('Budget approval action error:', error)
      toast.error('Failed to process budget approval')
    }
  })
}
