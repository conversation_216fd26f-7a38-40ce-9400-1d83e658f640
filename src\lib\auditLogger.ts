import { supabase } from '@/lib/supabase'
import type { AuditLogEntry, AuditAction, AuditSeverity, AuditCategory } from '@/types/database'

interface AuditContext {
  userId?: string
  orgId?: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
}

class AuditLogger {
  private context: AuditContext = {}
  private cachedIP: string | null = null
  private ipCacheExpiry: number = 0

  setContext(context: Partial<AuditContext>) {
    this.context = { ...this.context, ...context }
  }

  private async getClientInfo() {
    const ipAddress = this.context.ipAddress || await this.getClientIP()
    return {
      ip_address: ipAddress,
      user_agent: this.context.userAgent || navigator.userAgent,
      session_id: this.context.sessionId || this.getSessionId()
    }
  }

  private async getClientIP(): Promise<string> {
    // Check cache first (cache for 1 hour)
    const now = Date.now()
    if (this.cachedIP && now < this.ipCacheExpiry) {
      return this.cachedIP
    }

    try {
      let detectedIP = 'unknown'

      // Method 1: Try ipify API (free, reliable)
      try {
        const response = await fetch('https://api.ipify.org?format=json', {
          signal: AbortSignal.timeout(3000)
        })
        if (response.ok) {
          const data = await response.json()
          detectedIP = data.ip
        }
      } catch (e) {
        console.debug('ipify API failed:', e)
      }

      // Method 2: Try ipapi.co (backup) - only if first method failed
      if (detectedIP === 'unknown') {
        try {
          const response = await fetch('https://ipapi.co/ip/', {
            signal: AbortSignal.timeout(3000)
          })
          if (response.ok) {
            const ip = await response.text()
            detectedIP = ip.trim()
          }
        } catch (e) {
          console.debug('ipapi.co failed:', e)
        }
      }

      // Cache the result for 1 hour
      this.cachedIP = detectedIP
      this.ipCacheExpiry = now + (60 * 60 * 1000) // 1 hour

      return detectedIP
    } catch (error) {
      console.debug('All IP detection methods failed:', error)

      // Cache the unknown result for 5 minutes to avoid repeated failures
      this.cachedIP = 'unknown'
      this.ipCacheExpiry = now + (5 * 60 * 1000) // 5 minutes

      return 'unknown'
    }
  }

  private getSessionId(): string {
    // Generate or retrieve session ID
    let sessionId = sessionStorage.getItem('audit_session_id')
    if (!sessionId) {
      // Use cryptographically secure random ID generation
      sessionId = `session_${Date.now()}_${crypto.randomUUID()}`
      sessionStorage.setItem('audit_session_id', sessionId)
    }
    return sessionId
  }

  private getSeverityForAction(action: AuditAction, entityType: string): AuditSeverity {
    // Define severity rules
    const criticalActions = ['delete', 'purge', 'permission_change']
    const highActions = ['approve', 'reject', 'reconcile', 'password_change']
    const mediumActions = ['create', 'update', 'send', 'cancel']
    
    const financialEntities = ['payments', 'invoices', 'bills', 'journal_entries', 'budgets']
    
    if (criticalActions.includes(action)) return 'critical'
    if (highActions.includes(action)) return 'high'
    if (mediumActions.includes(action) && financialEntities.includes(entityType)) return 'high'
    if (mediumActions.includes(action)) return 'medium'
    
    return 'low'
  }

  private getCategoryForEntity(entityType: string, action: AuditAction): AuditCategory {
    const authActions = ['login', 'logout', 'password_change']
    const authzActions = ['permission_change']
    const financialEntities = ['payments', 'invoices', 'bills', 'journal_entries', 'budgets']
    const userEntities = ['profiles', 'users']
    const systemEntities = ['organizations', 'settings']

    if (authActions.includes(action)) return 'authentication'
    if (authzActions.includes(action)) return 'authorization'
    if (financialEntities.includes(entityType)) return 'financial_transaction'
    if (userEntities.includes(entityType)) return 'user_management'
    if (systemEntities.includes(entityType)) return 'system_configuration'
    if (action === 'view' || action === 'export') return 'data_access'
    
    return 'data_modification'
  }

  async log(entry: Partial<AuditLogEntry> & {
    entity_type: string
    entity_id: string
    action: AuditAction
  }): Promise<void> {
    try {
      const clientInfo = await this.getClientInfo()
      const severity = entry.severity || this.getSeverityForAction(entry.action, entry.entity_type)
      const category = entry.category || this.getCategoryForEntity(entry.entity_type, entry.action)

      // Combine old_values and new_values into changed_data if they exist
      let combinedChangedData = entry.changed_data
      if (entry.old_values || entry.new_values) {
        combinedChangedData = {
          ...combinedChangedData,
          ...(entry.old_values && { old_values: entry.old_values }),
          ...(entry.new_values && { new_values: entry.new_values })
        }
      }

      const auditEntry: AuditLogEntry = {
        entity_type: entry.entity_type,
        entity_id: entry.entity_id,
        action: entry.action,
        changed_data: combinedChangedData,
        description: entry.description,
        severity,
        category,
        metadata: {
          ...entry.metadata,
          client_timestamp: new Date().toISOString(),
          user_id: this.context.userId,
          org_id: this.context.orgId
        },
        ...clientInfo
      }

      const { error } = await supabase
        .from('audit_logs')
        .insert({
          entity_type: auditEntry.entity_type,
          entity_id: auditEntry.entity_id,
          action: auditEntry.action,
          changed_data: auditEntry.changed_data || null,
          description: auditEntry.description || null,
          severity: auditEntry.severity,
          category: auditEntry.category,
          ip_address: auditEntry.ip_address,
          user_agent: auditEntry.user_agent,
          session_id: auditEntry.session_id,
          metadata: auditEntry.metadata || {},
          org_id: this.context.orgId!,
          profile_id: this.context.userId || null
        })

      if (error) {
        console.error('Failed to log audit entry:', error)
      }
    } catch (error) {
      console.error('Audit logging error:', error)
    }
  }

  // Convenience methods for common actions
  async logCreate(entityType: string, entityId: string, data: Record<string, unknown>, description?: string) {
    await this.log({
      entity_type: entityType,
      entity_id: entityId,
      action: 'create',
      changed_data: { new_values: data },
      description: description || `Created ${entityType} ${entityId}`
    })
  }

  async logUpdate(entityType: string, entityId: string, oldData: Record<string, unknown>, newData: Record<string, unknown>, description?: string) {
    const changes = this.getChanges(oldData, newData)
    await this.log({
      entity_type: entityType,
      entity_id: entityId,
      action: 'update',
      changed_data: {
        old_values: oldData,
        new_values: newData,
        changes: changes
      },
      description: description || `Updated ${entityType} ${entityId}`
    })
  }

  async logDelete(entityType: string, entityId: string, data: Record<string, unknown>, description?: string) {
    await this.log({
      entity_type: entityType,
      entity_id: entityId,
      action: 'delete',
      changed_data: { old_values: data },
      description: description || `Deleted ${entityType} ${entityId}`,
      severity: 'high'
    })
  }

  async logView(entityType: string, entityId: string, description?: string) {
    await this.log({
      entity_type: entityType,
      entity_id: entityId,
      action: 'view',
      description: description || `Viewed ${entityType} ${entityId}`,
      severity: 'low'
    })
  }

  async logExport(entityType: string, entityIds: string[], format: string, description?: string) {
    await this.log({
      entity_type: entityType,
      entity_id: entityIds.join(','),
      action: 'export',
      metadata: { format, entity_count: entityIds.length },
      description: description || `Exported ${entityIds.length} ${entityType} records as ${format}`,
      severity: 'medium'
    })
  }

  async logLogin(userId: string, success: boolean, method?: string) {
    await this.log({
      entity_type: 'authentication',
      entity_id: userId,
      action: 'login',
      metadata: { success, method },
      description: success ? 'User logged in successfully' : 'Failed login attempt',
      severity: success ? 'low' : 'medium'
    })
  }

  async logLogout(userId: string) {
    await this.log({
      entity_type: 'authentication',
      entity_id: userId,
      action: 'logout',
      description: 'User logged out',
      severity: 'low'
    })
  }

  async logApproval(entityType: string, entityId: string, approved: boolean, comments?: string) {
    await this.log({
      entity_type: entityType,
      entity_id: entityId,
      action: approved ? 'approve' : 'reject',
      metadata: { approved, comments },
      description: `${approved ? 'Approved' : 'Rejected'} ${entityType} ${entityId}`,
      severity: 'high'
    })
  }

  private getChanges(oldData: Record<string, unknown>, newData: Record<string, unknown>): Record<string, unknown> {
    const changes: Record<string, unknown> = {}
    
    // Find changed fields
    for (const key in newData) {
      if (oldData[key] !== newData[key]) {
        changes[key] = {
          from: oldData[key],
          to: newData[key]
        }
      }
    }
    
    // Find removed fields
    for (const key in oldData) {
      if (!(key in newData)) {
        changes[key] = {
          from: oldData[key],
          to: null
        }
      }
    }
    
    return changes
  }
}

// Create singleton instance
export const auditLogger = new AuditLogger()

// Hook for React components
export function useAuditLogger() {
  return auditLogger
}
