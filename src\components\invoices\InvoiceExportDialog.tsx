/**
 * Invoice Export Dialog Component
 * Provides options for exporting invoice data
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Download, FileText, Database, Calendar, Filter } from 'lucide-react'
import { useInvoiceExport } from '@/hooks/useInvoiceExport'
import { type InvoiceExportOptions } from '@/lib/invoiceExport'
import type { Customer, InvoiceStatus } from '@/types/database'

interface InvoiceExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  customers: Customer[]
  totalInvoices: number
}

const INVOICE_STATUSES: InvoiceStatus[] = ['draft', 'sent', 'paid', 'overdue', 'cancelled']

export function InvoiceExportDialog({
  open,
  onOpenChange,
  customers,
  totalInvoices
}: InvoiceExportDialogProps) {
  const { exportInvoiceData, isExporting } = useInvoiceExport()
  
  const [exportOptions, setExportOptions] = useState<InvoiceExportOptions>({
    format: 'csv',
    includeLineItems: false,
    dateRange: undefined,
    status: undefined,
    customerId: undefined
  })

  const [useDateRange, setUseDateRange] = useState(false)
  const [useStatusFilter, setUseStatusFilter] = useState(false)
  const [useCustomerFilter, setUseCustomerFilter] = useState(false)
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])

  const handleExport = async () => {
    const options: InvoiceExportOptions = {
      ...exportOptions,
      dateRange: useDateRange ? exportOptions.dateRange : undefined,
      status: useStatusFilter && selectedStatuses.length > 0 ? selectedStatuses : undefined,
      customerId: useCustomerFilter ? exportOptions.customerId : undefined
    }

    const success = await exportInvoiceData(options)
    if (success) {
      onOpenChange(false)
    }
  }

  const handleStatusToggle = (status: string, checked: boolean) => {
    if (checked) {
      setSelectedStatuses(prev => [...prev, status])
    } else {
      setSelectedStatuses(prev => prev.filter(s => s !== status))
    }
  }

  const getStatusBadgeVariant = (status: InvoiceStatus) => {
    switch (status) {
      case 'paid': return 'default'
      case 'sent': return 'secondary'
      case 'overdue': return 'destructive'
      case 'cancelled': return 'outline'
      default: return 'secondary'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Invoices
          </DialogTitle>
          <DialogDescription>
            Export your invoice data in various formats. Total invoices available: {totalInvoices}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Format */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Export Format</Label>
            <Select
              value={exportOptions.format}
              onValueChange={(value: 'csv' | 'json') => 
                setExportOptions(prev => ({ ...prev, format: value }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="csv">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    CSV (Comma Separated Values)
                  </div>
                </SelectItem>
                <SelectItem value="json">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    JSON (JavaScript Object Notation)
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Include Line Items */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="includeLineItems"
              checked={exportOptions.includeLineItems}
              onCheckedChange={(checked) =>
                setExportOptions(prev => ({ ...prev, includeLineItems: !!checked }))
              }
            />
            <Label htmlFor="includeLineItems" className="text-sm font-medium">
              Include line items details
            </Label>
          </div>

          {/* Date Range Filter */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="useDateRange"
                checked={useDateRange}
                onCheckedChange={setUseDateRange}
              />
              <Label htmlFor="useDateRange" className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Filter by date range
              </Label>
            </div>
            
            {useDateRange && (
              <div className="grid grid-cols-2 gap-4 ml-6">
                <div>
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={exportOptions.dateRange?.start || ''}
                    onChange={(e) =>
                      setExportOptions(prev => ({
                        ...prev,
                        dateRange: {
                          start: e.target.value,
                          end: prev.dateRange?.end || ''
                        }
                      }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={exportOptions.dateRange?.end || ''}
                    onChange={(e) =>
                      setExportOptions(prev => ({
                        ...prev,
                        dateRange: {
                          start: prev.dateRange?.start || '',
                          end: e.target.value
                        }
                      }))
                    }
                  />
                </div>
              </div>
            )}
          </div>

          {/* Status Filter */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="useStatusFilter"
                checked={useStatusFilter}
                onCheckedChange={setUseStatusFilter}
              />
              <Label htmlFor="useStatusFilter" className="text-sm font-medium flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter by status
              </Label>
            </div>
            
            {useStatusFilter && (
              <div className="ml-6 space-y-2">
                <div className="flex flex-wrap gap-2">
                  {INVOICE_STATUSES.map(status => (
                    <div key={status} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status}`}
                        checked={selectedStatuses.includes(status)}
                        onCheckedChange={(checked) => handleStatusToggle(status, !!checked)}
                      />
                      <Label htmlFor={`status-${status}`} className="text-sm">
                        <Badge variant={getStatusBadgeVariant(status)}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </Badge>
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Customer Filter */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="useCustomerFilter"
                checked={useCustomerFilter}
                onCheckedChange={setUseCustomerFilter}
              />
              <Label htmlFor="useCustomerFilter" className="text-sm font-medium">
                Filter by specific customer
              </Label>
            </div>
            
            {useCustomerFilter && (
              <div className="ml-6">
                <Select
                  value={exportOptions.customerId || ''}
                  onValueChange={(value) =>
                    setExportOptions(prev => ({ ...prev, customerId: value || undefined }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a customer" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.map(customer => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
