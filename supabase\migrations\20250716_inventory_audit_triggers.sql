-- =====================================================
-- INVENTORY AUDIT TRIGGERS MIGRATION
-- =====================================================
-- This migration creates database triggers to automatically log
-- inventory-related operations to the audit_logs table

-- Function to log inventory transaction audit events
CREATE OR REPLACE FUNCTION log_inventory_transaction_audit()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    product_info RECORD;
    location_info RECORD;
    audit_description TEXT;
    audit_severity TEXT := 'medium';
    audit_metadata JSONB;
BEGIN
    -- Get product information
    SELECT name, sku INTO product_info
    FROM products 
    WHERE id = NEW.product_id AND org_id = NEW.org_id;
    
    -- Get location information
    SELECT name INTO location_info
    FROM inventory_locations 
    WHERE id = NEW.location_id AND org_id = NEW.org_id;
    
    -- Build audit description
    audit_description := format(
        '%s transaction: %s units of %s (%s) at %s',
        initcap(NEW.transaction_type),
        ABS(NEW.quantity),
        COALESCE(product_info.name, 'Unknown Product'),
        COALESCE(product_info.sku, NEW.product_id::text),
        COALESCE(location_info.name, 'Unknown Location')
    );
    
    -- Add cost information if available
    IF NEW.unit_cost IS NOT NULL THEN
        audit_description := audit_description || format(' at UGX %s each', NEW.unit_cost);
    END IF;
    
    -- Add reference information if available
    IF NEW.reference_number IS NOT NULL THEN
        audit_description := audit_description || format(' (Ref: %s)', NEW.reference_number);
    END IF;
    
    -- Determine severity based on transaction type
    IF NEW.transaction_type IN ('adjustment', 'write_off', 'transfer') THEN
        audit_severity := 'high';
    END IF;
    
    -- Build metadata
    audit_metadata := jsonb_build_object(
        'inventory_operation', true,
        'transaction_type', NEW.transaction_type,
        'product_id', NEW.product_id,
        'product_name', product_info.name,
        'product_sku', product_info.sku,
        'location_id', NEW.location_id,
        'location_name', location_info.name,
        'quantity', NEW.quantity,
        'unit_cost', NEW.unit_cost,
        'total_cost', NEW.total_cost,
        'reference_type', NEW.reference_type,
        'reference_id', NEW.reference_id,
        'reference_number', NEW.reference_number
    );
    
    -- Insert audit log entry
    INSERT INTO audit_logs (
        entity_type,
        entity_id,
        action,
        description,
        severity,
        category,
        changed_data,
        metadata,
        org_id,
        profile_id
    )
    VALUES (
        'inventory_transaction',
        NEW.id::text,
        'create',
        audit_description,
        audit_severity,
        'data_modification',
        jsonb_build_object(
            'transaction_type', NEW.transaction_type,
            'product_id', NEW.product_id,
            'location_id', NEW.location_id,
            'quantity', NEW.quantity,
            'unit_cost', NEW.unit_cost,
            'total_cost', NEW.total_cost,
            'reference_type', NEW.reference_type,
            'reference_id', NEW.reference_id
        ),
        audit_metadata,
        NEW.org_id,
        NEW.created_by
    );
    
    RETURN NEW;
END;
$$;

-- Function to log stock level changes
CREATE OR REPLACE FUNCTION log_stock_level_audit()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    product_info RECORD;
    location_info RECORD;
    audit_description TEXT;
    audit_severity TEXT := 'medium';
    audit_metadata JSONB;
    quantity_change DECIMAL(15,3);
    cost_change DECIMAL(15,4);
BEGIN
    -- Get product information
    SELECT name, sku INTO product_info
    FROM products 
    WHERE id = NEW.product_id AND org_id = NEW.org_id;
    
    -- Get location information
    SELECT name INTO location_info
    FROM inventory_locations 
    WHERE id = NEW.location_id AND org_id = NEW.org_id;
    
    -- Calculate changes
    quantity_change := NEW.quantity_on_hand - COALESCE(OLD.quantity_on_hand, 0);
    cost_change := NEW.average_cost - COALESCE(OLD.average_cost, 0);
    
    -- Build audit description
    IF quantity_change != 0 THEN
        audit_description := format(
            'Stock %s by %s units for %s (%s) at %s',
            CASE WHEN quantity_change > 0 THEN 'increased' ELSE 'decreased' END,
            ABS(quantity_change),
            COALESCE(product_info.name, 'Unknown Product'),
            COALESCE(product_info.sku, NEW.product_id::text),
            COALESCE(location_info.name, 'Unknown Location')
        );
    ELSE
        audit_description := format(
            'Stock level updated for %s (%s) at %s',
            COALESCE(product_info.name, 'Unknown Product'),
            COALESCE(product_info.sku, NEW.product_id::text),
            COALESCE(location_info.name, 'Unknown Location')
        );
    END IF;
    
    -- Add cost change information if significant
    IF ABS(cost_change) > 0.01 THEN
        audit_description := audit_description || format(
            '. Cost %s by UGX %s',
            CASE WHEN cost_change > 0 THEN 'increased' ELSE 'decreased' END,
            ABS(cost_change)
        );
    END IF;
    
    -- Determine severity based on change magnitude
    IF ABS(quantity_change) > 100 OR ABS(cost_change * NEW.quantity_on_hand) > 100000 THEN
        audit_severity := 'high';
    END IF;
    
    -- Build metadata
    audit_metadata := jsonb_build_object(
        'inventory_operation', true,
        'stock_movement', true,
        'product_id', NEW.product_id,
        'product_name', product_info.name,
        'product_sku', product_info.sku,
        'location_id', NEW.location_id,
        'location_name', location_info.name,
        'quantity_change', quantity_change,
        'cost_change', cost_change
    );
    
    -- Insert audit log entry
    INSERT INTO audit_logs (
        entity_type,
        entity_id,
        action,
        description,
        severity,
        category,
        changed_data,
        metadata,
        org_id,
        profile_id
    )
    VALUES (
        'stock_movement',
        format('%s-%s', NEW.product_id, NEW.location_id),
        'update',
        audit_description,
        audit_severity,
        'data_modification',
        jsonb_build_object(
            'old_values', jsonb_build_object(
                'quantity_on_hand', COALESCE(OLD.quantity_on_hand, 0),
                'average_cost', COALESCE(OLD.average_cost, 0),
                'last_cost', COALESCE(OLD.last_cost, 0)
            ),
            'new_values', jsonb_build_object(
                'quantity_on_hand', NEW.quantity_on_hand,
                'average_cost', NEW.average_cost,
                'last_cost', NEW.last_cost
            ),
            'changes', jsonb_build_object(
                'quantity_change', quantity_change,
                'cost_change', cost_change
            )
        ),
        audit_metadata,
        NEW.org_id,
        auth.uid()
    );
    
    RETURN NEW;
END;
$$;

-- Function to log stock reservation changes
CREATE OR REPLACE FUNCTION log_stock_reservation_audit()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    product_info RECORD;
    location_info RECORD;
    audit_description TEXT;
    audit_action TEXT;
    audit_metadata JSONB;
BEGIN
    -- Get product information
    SELECT name, sku INTO product_info
    FROM products 
    WHERE id = NEW.product_id AND org_id = NEW.org_id;
    
    -- Get location information
    SELECT name INTO location_info
    FROM inventory_locations 
    WHERE id = NEW.location_id AND org_id = NEW.org_id;
    
    -- Determine action and description based on operation
    IF TG_OP = 'INSERT' THEN
        audit_action := 'create';
        audit_description := format(
            'Reserved %s units of %s (%s) for %s %s',
            NEW.quantity_reserved,
            COALESCE(product_info.name, 'Unknown Product'),
            COALESCE(product_info.sku, NEW.product_id::text),
            NEW.reference_type,
            COALESCE(NEW.reference_number, NEW.reference_id::text)
        );
    ELSE
        audit_action := 'update';
        audit_description := format(
            'Updated reservation for %s (%s): %s',
            COALESCE(product_info.name, 'Unknown Product'),
            COALESCE(product_info.sku, NEW.product_id::text),
            CASE 
                WHEN NEW.status = 'cancelled' THEN 'Released reservation'
                WHEN NEW.status = 'fulfilled' THEN 'Fulfilled reservation'
                WHEN NEW.status = 'expired' THEN 'Expired reservation'
                ELSE 'Updated reservation'
            END
        );
    END IF;
    
    -- Build metadata
    audit_metadata := jsonb_build_object(
        'inventory_operation', true,
        'reservation_operation', true,
        'product_id', NEW.product_id,
        'product_name', product_info.name,
        'product_sku', product_info.sku,
        'location_id', NEW.location_id,
        'location_name', location_info.name,
        'reservation_type', NEW.reservation_type,
        'reference_type', NEW.reference_type,
        'reference_id', NEW.reference_id,
        'quantity_reserved', NEW.quantity_reserved,
        'status', NEW.status
    );
    
    -- Insert audit log entry
    INSERT INTO audit_logs (
        entity_type,
        entity_id,
        action,
        description,
        severity,
        category,
        changed_data,
        metadata,
        org_id,
        profile_id
    )
    VALUES (
        'stock_reservation',
        NEW.id::text,
        audit_action,
        audit_description,
        'medium',
        'data_modification',
        CASE 
            WHEN TG_OP = 'INSERT' THEN
                jsonb_build_object(
                    'quantity_reserved', NEW.quantity_reserved,
                    'reservation_type', NEW.reservation_type,
                    'reference_type', NEW.reference_type,
                    'reference_id', NEW.reference_id,
                    'status', NEW.status
                )
            ELSE
                jsonb_build_object(
                    'old_values', jsonb_build_object('status', OLD.status),
                    'new_values', jsonb_build_object('status', NEW.status)
                )
        END,
        audit_metadata,
        NEW.org_id,
        NEW.created_by
    );
    
    RETURN NEW;
END;
$$;

-- Create triggers for inventory audit logging
DROP TRIGGER IF EXISTS inventory_transaction_audit_trigger ON inventory_transactions;
CREATE TRIGGER inventory_transaction_audit_trigger
    AFTER INSERT ON inventory_transactions
    FOR EACH ROW
    EXECUTE FUNCTION log_inventory_transaction_audit();

DROP TRIGGER IF EXISTS stock_level_audit_trigger ON stock_levels;
CREATE TRIGGER stock_level_audit_trigger
    AFTER UPDATE ON stock_levels
    FOR EACH ROW
    EXECUTE FUNCTION log_stock_level_audit();

DROP TRIGGER IF EXISTS stock_reservation_audit_trigger ON stock_reservations;
CREATE TRIGGER stock_reservation_audit_trigger
    AFTER INSERT OR UPDATE ON stock_reservations
    FOR EACH ROW
    EXECUTE FUNCTION log_stock_reservation_audit();

-- Function to log purchase order audit events
CREATE OR REPLACE FUNCTION log_purchase_order_audit()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    vendor_info RECORD;
    audit_description TEXT;
    audit_action TEXT;
    audit_severity TEXT := 'medium';
    audit_metadata JSONB;
BEGIN
    -- Get vendor information
    SELECT name INTO vendor_info
    FROM vendors 
    WHERE id = NEW.vendor_id AND org_id = NEW.org_id;
    
    -- Determine action and description
    IF TG_OP = 'INSERT' THEN
        audit_action := 'create';
        audit_description := format(
            'Created purchase order %s for %s (Total: UGX %s)',
            NEW.po_number,
            COALESCE(vendor_info.name, 'Unknown Vendor'),
            NEW.total_amount
        );
    ELSIF TG_OP = 'UPDATE' THEN
        audit_action := 'update';
        IF OLD.status != NEW.status THEN
            audit_description := format(
                'Purchase order %s status changed from %s to %s',
                NEW.po_number,
                OLD.status,
                NEW.status
            );
            IF NEW.status IN ('cancelled', 'received') THEN
                audit_severity := 'high';
            END IF;
        ELSE
            audit_description := format(
                'Updated purchase order %s for %s',
                NEW.po_number,
                COALESCE(vendor_info.name, 'Unknown Vendor')
            );
        END IF;
    ELSE
        audit_action := 'delete';
        audit_description := format(
            'Deleted purchase order %s for %s',
            OLD.po_number,
            COALESCE(vendor_info.name, 'Unknown Vendor')
        );
        audit_severity := 'high';
    END IF;
    
    -- Build metadata
    audit_metadata := jsonb_build_object(
        'inventory_operation', true,
        'purchase_order_operation', true,
        'po_number', COALESCE(NEW.po_number, OLD.po_number),
        'vendor_id', COALESCE(NEW.vendor_id, OLD.vendor_id),
        'vendor_name', vendor_info.name,
        'total_amount', COALESCE(NEW.total_amount, OLD.total_amount),
        'status', COALESCE(NEW.status, OLD.status)
    );
    
    -- Insert audit log entry
    INSERT INTO audit_logs (
        entity_type,
        entity_id,
        action,
        description,
        severity,
        category,
        changed_data,
        metadata,
        org_id,
        profile_id
    )
    VALUES (
        'purchase_order',
        COALESCE(NEW.id, OLD.id)::text,
        audit_action,
        audit_description,
        audit_severity,
        'data_modification',
        CASE 
            WHEN TG_OP = 'INSERT' THEN
                jsonb_build_object(
                    'po_number', NEW.po_number,
                    'vendor_id', NEW.vendor_id,
                    'total_amount', NEW.total_amount,
                    'status', NEW.status
                )
            WHEN TG_OP = 'UPDATE' THEN
                jsonb_build_object(
                    'old_values', jsonb_build_object('status', OLD.status, 'total_amount', OLD.total_amount),
                    'new_values', jsonb_build_object('status', NEW.status, 'total_amount', NEW.total_amount)
                )
            ELSE
                jsonb_build_object(
                    'deleted_po', jsonb_build_object(
                        'po_number', OLD.po_number,
                        'vendor_id', OLD.vendor_id,
                        'total_amount', OLD.total_amount,
                        'status', OLD.status
                    )
                )
        END,
        audit_metadata,
        COALESCE(NEW.org_id, OLD.org_id),
        COALESCE(NEW.created_by, OLD.created_by)
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Create trigger for purchase order audit logging
DROP TRIGGER IF EXISTS purchase_order_audit_trigger ON purchase_orders;
CREATE TRIGGER purchase_order_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON purchase_orders
    FOR EACH ROW
    EXECUTE FUNCTION log_purchase_order_audit();

-- Comments for documentation
COMMENT ON FUNCTION log_inventory_transaction_audit() IS 'Automatically logs inventory transaction operations to audit_logs';
COMMENT ON FUNCTION log_stock_level_audit() IS 'Automatically logs stock level changes to audit_logs';
COMMENT ON FUNCTION log_stock_reservation_audit() IS 'Automatically logs stock reservation operations to audit_logs';
COMMENT ON FUNCTION log_purchase_order_audit() IS 'Automatically logs purchase order operations to audit_logs';
