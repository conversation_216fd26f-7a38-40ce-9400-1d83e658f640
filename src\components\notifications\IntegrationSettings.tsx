/**
 * Integration Settings Component
 * Manage external notification integrations (Slack, Teams, Webhooks, SMS)
 */

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { LoadingSpinner } from '@/components/ui/loading'
import { toast } from '@/components/ui/toast-utils'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger 
} from '@/components/ui/dialog'
import { 
  Plus, 
  Slack, 
  MessageSquare, 
  Webhook, 
  Smartphone, 
  Settings, 
  Trash2, 
  TestTube,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuthHook'
import { 
  IntegrationAPI, 
  type IntegrationConfig, 
  type SlackConfig, 
  type TeamsConfig, 
  type WebhookConfig, 
  type SMSConfig 
} from '@/lib/externalIntegrations'
import type { NotificationType } from '@/types/notifications'

const NOTIFICATION_TYPES: Array<{ value: NotificationType; label: string }> = [
  { value: 'payment_pending_approval', label: 'Payment Approvals' },
  { value: 'payment_approved', label: 'Payment Approved' },
  { value: 'payment_rejected', label: 'Payment Rejected' },
  { value: 'invoice_overdue', label: 'Overdue Invoices' },
  { value: 'invoice_due_soon', label: 'Invoice Due Soon' },
  { value: 'invoice_paid', label: 'Invoice Paid' },
  { value: 'bill_due_soon', label: 'Bill Due Soon' },
  { value: 'bill_overdue', label: 'Overdue Bills' },
  { value: 'budget_exceeded', label: 'Budget Exceeded' },
  { value: 'budget_warning', label: 'Budget Warning' },
  { value: 'user_invited', label: 'User Invitations' },
  { value: 'backup_completed', label: 'Backup Completed' },
  { value: 'backup_failed', label: 'Backup Failed' },
  { value: 'system_maintenance', label: 'System Maintenance' },
  { value: 'audit_alert', label: 'Audit Alerts' }
]

interface IntegrationFormData {
  type: 'slack' | 'teams' | 'webhook' | 'sms'
  name: string
  config: Record<string, unknown>
  notification_types: NotificationType[]
  is_active: boolean
}

export function IntegrationSettings() {
  const { profile } = useAuth()
  const [integrations, setIntegrations] = useState<IntegrationConfig[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingIntegration, setEditingIntegration] = useState<IntegrationConfig | null>(null)
  const [testingIntegration, setTestingIntegration] = useState<string | null>(null)

  const [formData, setFormData] = useState<IntegrationFormData>({
    type: 'slack',
    name: '',
    config: {},
    notification_types: [],
    is_active: true
  })

  // Load integrations
  useEffect(() => {
    loadIntegrations()
  }, [loadIntegrations])

  const loadIntegrations = useCallback(async () => {
    if (!profile?.org_id) return

    setIsLoading(true)
    try {
      const data = await IntegrationAPI.getIntegrations(profile.org_id)
      setIntegrations(data)
    } catch (error) {
      console.error('Error loading integrations:', error)
      toast.error('Failed to load integrations')
    } finally {
      setIsLoading(false)
    }
  }, [profile?.org_id])

  const handleCreateIntegration = () => {
    setEditingIntegration(null)
    setFormData({
      type: 'slack',
      name: '',
      config: {},
      notification_types: [],
      is_active: true
    })
    setDialogOpen(true)
  }

  const handleEditIntegration = (integration: IntegrationConfig) => {
    setEditingIntegration(integration)
    setFormData({
      type: integration.type,
      name: integration.name,
      config: integration.config,
      notification_types: integration.notification_types,
      is_active: integration.is_active
    })
    setDialogOpen(true)
  }

  const handleSaveIntegration = async () => {
    if (!profile?.org_id) return

    try {
      if (editingIntegration) {
        // Update existing integration
        const updated = await IntegrationAPI.updateIntegration(editingIntegration.id, formData)
        if (updated) {
          setIntegrations(prev => 
            prev.map(int => int.id === editingIntegration.id ? updated : int)
          )
          toast.success('Integration updated successfully')
        } else {
          throw new Error('Failed to update integration')
        }
      } else {
        // Create new integration
        const created = await IntegrationAPI.createIntegration(profile.org_id, formData)
        if (created) {
          setIntegrations(prev => [...prev, created])
          toast.success('Integration created successfully')
        } else {
          throw new Error('Failed to create integration')
        }
      }

      setDialogOpen(false)
    } catch (error) {
      console.error('Error saving integration:', error)
      toast.error('Failed to save integration')
    }
  }

  const handleDeleteIntegration = async (integrationId: string) => {
    try {
      const success = await IntegrationAPI.deleteIntegration(integrationId)
      if (success) {
        setIntegrations(prev => prev.filter(int => int.id !== integrationId))
        toast.success('Integration deleted successfully')
      } else {
        throw new Error('Failed to delete integration')
      }
    } catch (error) {
      console.error('Error deleting integration:', error)
      toast.error('Failed to delete integration')
    }
  }

  const handleTestIntegration = async (integration: IntegrationConfig) => {
    setTestingIntegration(integration.id)
    try {
      const success = await IntegrationAPI.testIntegration(integration)
      if (success) {
        toast.success('Test notification sent successfully')
      } else {
        toast.error('Test notification failed')
      }
    } catch (error) {
      console.error('Error testing integration:', error)
      toast.error('Failed to test integration')
    } finally {
      setTestingIntegration(null)
    }
  }

  const getIntegrationIcon = (type: string) => {
    switch (type) {
      case 'slack':
        return <Slack className="h-5 w-5" />
      case 'teams':
        return <MessageSquare className="h-5 w-5" />
      case 'webhook':
        return <Webhook className="h-5 w-5" />
      case 'sms':
        return <Smartphone className="h-5 w-5" />
      default:
        return <Settings className="h-5 w-5" />
    }
  }

  const renderConfigForm = () => {
    switch (formData.type) {
      case 'slack':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="webhook_url">Webhook URL</Label>
              <Input
                id="webhook_url"
                placeholder="https://hooks.slack.com/services/..."
                value={formData.config.webhook_url || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, webhook_url: e.target.value }
                }))}
              />
            </div>
            <div>
              <Label htmlFor="channel">Channel (optional)</Label>
              <Input
                id="channel"
                placeholder="#general"
                value={formData.config.channel || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, channel: e.target.value }
                }))}
              />
            </div>
            <div>
              <Label htmlFor="username">Username (optional)</Label>
              <Input
                id="username"
                placeholder="Kaya Finance"
                value={formData.config.username || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, username: e.target.value }
                }))}
              />
            </div>
          </div>
        )

      case 'teams':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="webhook_url">Webhook URL</Label>
              <Input
                id="webhook_url"
                placeholder="https://outlook.office.com/webhook/..."
                value={formData.config.webhook_url || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, webhook_url: e.target.value }
                }))}
              />
            </div>
            <div>
              <Label htmlFor="title_color">Title Color (optional)</Label>
              <Input
                id="title_color"
                placeholder="#0078d4"
                value={formData.config.title_color || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, title_color: e.target.value }
                }))}
              />
            </div>
          </div>
        )

      case 'webhook':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="url">Webhook URL</Label>
              <Input
                id="url"
                placeholder="https://api.example.com/webhooks/notifications"
                value={formData.config.url || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, url: e.target.value }
                }))}
              />
            </div>
            <div>
              <Label htmlFor="method">HTTP Method</Label>
              <Select
                value={formData.config.method || 'POST'}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, method: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="PATCH">PATCH</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="auth_type">Authentication</Label>
              <Select
                value={formData.config.auth_type || 'none'}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, auth_type: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="bearer">Bearer Token</SelectItem>
                  <SelectItem value="basic">Basic Auth</SelectItem>
                  <SelectItem value="api_key">API Key</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )

      case 'sms':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="provider">SMS Provider</Label>
              <Select
                value={formData.config.provider || 'twilio'}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, provider: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="twilio">Twilio</SelectItem>
                  <SelectItem value="aws_sns">AWS SNS</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="api_key">API Key</Label>
              <Input
                id="api_key"
                type="password"
                value={formData.config.api_key || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, api_key: e.target.value }
                }))}
              />
            </div>
            <div>
              <Label htmlFor="from_number">From Number</Label>
              <Input
                id="from_number"
                placeholder="+1234567890"
                value={formData.config.from_number || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, from_number: e.target.value }
                }))}
              />
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="md" text="Loading integrations..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">External Integrations</h3>
          <p className="text-sm text-muted-foreground">
            Connect with external services to receive notifications
          </p>
        </div>
        
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleCreateIntegration}>
              <Plus className="h-4 w-4 mr-2" />
              Add Integration
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingIntegration ? 'Edit Integration' : 'Add Integration'}
              </DialogTitle>
              <DialogDescription>
                Configure external service integration for notifications
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-6">
              {/* Basic Settings */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="integration_type">Integration Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: 'slack' | 'teams' | 'webhook' | 'sms') => setFormData(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="slack">Slack</SelectItem>
                      <SelectItem value="teams">Microsoft Teams</SelectItem>
                      <SelectItem value="webhook">Webhook</SelectItem>
                      <SelectItem value="sms">SMS</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="integration_name">Name</Label>
                  <Input
                    id="integration_name"
                    placeholder="My Integration"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
              </div>

              <Separator />

              {/* Configuration */}
              <div>
                <h4 className="text-sm font-medium mb-4">Configuration</h4>
                {renderConfigForm()}
              </div>

              <Separator />

              {/* Notification Types */}
              <div>
                <h4 className="text-sm font-medium mb-4">Notification Types</h4>
                <div className="grid grid-cols-2 gap-2">
                  {NOTIFICATION_TYPES.map(type => (
                    <label key={type.value} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.notification_types.includes(type.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData(prev => ({
                              ...prev,
                              notification_types: [...prev.notification_types, type.value]
                            }))
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              notification_types: prev.notification_types.filter(t => t !== type.value)
                            }))
                          }
                        }}
                        className="rounded"
                      />
                      <span className="text-sm">{type.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Active Toggle */}
              <div className="flex items-center justify-between">
                <Label htmlFor="is_active">Active</Label>
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveIntegration}>
                {editingIntegration ? 'Update' : 'Create'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Integrations List */}
      <div className="grid gap-4">
        {integrations.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <Settings className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No integrations configured</h3>
              <p className="text-sm text-muted-foreground text-center mb-4">
                Connect with external services to receive notifications in your preferred channels
              </p>
              <Button onClick={handleCreateIntegration}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Integration
              </Button>
            </CardContent>
          </Card>
        ) : (
          integrations.map(integration => (
            <Card key={integration.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getIntegrationIcon(integration.type)}
                    <div>
                      <CardTitle className="text-base">{integration.name}</CardTitle>
                      <CardDescription className="capitalize">
                        {integration.type} integration
                      </CardDescription>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant={integration.is_active ? 'default' : 'secondary'}>
                      {integration.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestIntegration(integration)}
                      disabled={testingIntegration === integration.id}
                    >
                      {testingIntegration === integration.id ? (
                        <LoadingSpinner size="sm" className="mr-2" />
                      ) : (
                        <TestTube className="h-4 w-4 mr-2" />
                      )}
                      Test
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditIntegration(integration)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteIntegration(integration.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-2">
                  <div className="text-sm">
                    <span className="font-medium">Notification Types:</span>{' '}
                    {integration.notification_types.length > 0 ? (
                      <span>{integration.notification_types.length} selected</span>
                    ) : (
                      <span className="text-muted-foreground">None selected</span>
                    )}
                  </div>
                  
                  {integration.last_used_at && (
                    <div className="text-sm text-muted-foreground">
                      Last used: {new Date(integration.last_used_at).toLocaleString()}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
