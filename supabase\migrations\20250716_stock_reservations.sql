-- =====================================================
-- STOCK RESERVATIONS MIGRATION
-- =====================================================
-- This migration creates the stock reservation system for pending invoices
-- to prevent overselling with automatic reservation/release on invoice status changes

-- Stock Reservations Table
CREATE TABLE IF NOT EXISTS stock_reservations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES inventory_locations(id) ON DELETE CASCADE,
    
    -- Reservation details
    quantity_reserved DECIMAL(15,3) NOT NULL CHECK (quantity_reserved > 0),
    reservation_type VARCHAR(50) NOT NULL DEFAULT 'invoice' CHECK (reservation_type IN ('invoice', 'quote', 'manual')),
    
    -- Reference to the document that created this reservation
    reference_type VARCHAR(50) NOT NULL CHECK (reference_type IN ('invoice', 'quote', 'manual')),
    reference_id UUID NOT NULL,
    reference_number VARCHAR(100),
    
    -- Reservation status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'fulfilled', 'cancelled', 'expired')),
    
    -- Expiration (for quotes and manual reservations)
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Notes
    notes TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    UNIQUE(org_id, reference_type, reference_id, product_id, location_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_stock_reservations_org_id ON stock_reservations(org_id);
CREATE INDEX IF NOT EXISTS idx_stock_reservations_product_id ON stock_reservations(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_reservations_location_id ON stock_reservations(location_id);
CREATE INDEX IF NOT EXISTS idx_stock_reservations_reference ON stock_reservations(reference_type, reference_id);
CREATE INDEX IF NOT EXISTS idx_stock_reservations_status ON stock_reservations(status);
CREATE INDEX IF NOT EXISTS idx_stock_reservations_expires_at ON stock_reservations(expires_at);

-- RLS Policies
ALTER TABLE stock_reservations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view stock reservations in their organization" ON stock_reservations
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert stock reservations in their organization" ON stock_reservations
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update stock reservations in their organization" ON stock_reservations
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete stock reservations in their organization" ON stock_reservations
    FOR DELETE USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

-- Function to create stock reservations for invoice lines
CREATE OR REPLACE FUNCTION create_invoice_stock_reservations()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    line_record RECORD;
    default_location_id UUID;
    product_info RECORD;
    available_quantity DECIMAL(15,3);
BEGIN
    -- Only process when invoice status changes to 'sent' or when new invoice is created with 'sent' status
    IF (TG_OP = 'INSERT' AND NEW.status = 'sent') OR
       (TG_OP = 'UPDATE' AND NEW.status = 'sent' AND (OLD.status IS NULL OR OLD.status != 'sent')) THEN

        -- Get default location for this organization
        SELECT id INTO default_location_id
        FROM inventory_locations
        WHERE org_id = NEW.org_id 
        AND is_default = true 
        AND is_active = true
        LIMIT 1;

        IF default_location_id IS NULL THEN
            -- No default location found, skip reservation
            RETURN NEW;
        END IF;

        -- Process each invoice line that has a product
        FOR line_record IN
            SELECT il.*, p.track_inventory, p.name as product_name
            FROM invoice_lines il
            JOIN products p ON il.product_id = p.id
            WHERE il.invoice_id = NEW.id
            AND il.product_id IS NOT NULL
            AND p.track_inventory = true
        LOOP
            -- Check available quantity (on hand - already reserved)
            SELECT 
                sl.quantity_on_hand,
                COALESCE(sl.quantity_reserved, 0) as current_reserved,
                (sl.quantity_on_hand - COALESCE(sl.quantity_reserved, 0)) as available
            INTO product_info
            FROM stock_levels sl
            WHERE sl.org_id = NEW.org_id
            AND sl.product_id = line_record.product_id
            AND sl.location_id = default_location_id;

            IF FOUND AND product_info.available >= line_record.quantity THEN
                -- Create reservation
                INSERT INTO stock_reservations (
                    org_id,
                    product_id,
                    location_id,
                    quantity_reserved,
                    reservation_type,
                    reference_type,
                    reference_id,
                    reference_number,
                    status,
                    notes,
                    created_by
                )
                VALUES (
                    NEW.org_id,
                    line_record.product_id,
                    default_location_id,
                    line_record.quantity,
                    'invoice',
                    'invoice',
                    NEW.id,
                    NEW.invoice_number,
                    'active',
                    'Auto-reserved for invoice ' || NEW.invoice_number,
                    NEW.created_by
                )
                ON CONFLICT (org_id, reference_type, reference_id, product_id, location_id)
                DO UPDATE SET
                    quantity_reserved = EXCLUDED.quantity_reserved,
                    updated_at = NOW();

                -- Update stock level reserved quantity
                UPDATE stock_levels
                SET 
                    quantity_reserved = quantity_reserved + line_record.quantity,
                    last_updated = NOW()
                WHERE org_id = NEW.org_id
                AND product_id = line_record.product_id
                AND location_id = default_location_id;
            END IF;
        END LOOP;

    -- Handle invoice cancellation or status change away from 'sent'
    ELSIF TG_OP = 'UPDATE' AND OLD.status = 'sent' AND NEW.status != 'sent' THEN
        -- Release reservations for this invoice
        PERFORM release_stock_reservations('invoice', NEW.id, NEW.org_id);
    END IF;

    RETURN NEW;
END;
$$;

-- Function to release stock reservations
CREATE OR REPLACE FUNCTION release_stock_reservations(
    p_reference_type VARCHAR(50),
    p_reference_id UUID,
    p_org_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    reservation_record RECORD;
BEGIN
    -- Process each active reservation for this reference
    FOR reservation_record IN
        SELECT *
        FROM stock_reservations
        WHERE org_id = p_org_id
        AND reference_type = p_reference_type
        AND reference_id = p_reference_id
        AND status = 'active'
    LOOP
        -- Update stock level to reduce reserved quantity
        UPDATE stock_levels
        SET 
            quantity_reserved = GREATEST(0, quantity_reserved - reservation_record.quantity_reserved),
            last_updated = NOW()
        WHERE org_id = p_org_id
        AND product_id = reservation_record.product_id
        AND location_id = reservation_record.location_id;

        -- Mark reservation as cancelled
        UPDATE stock_reservations
        SET 
            status = 'cancelled',
            updated_at = NOW()
        WHERE id = reservation_record.id;
    END LOOP;
END;
$$;

-- Function to fulfill stock reservations (when invoice is paid/delivered)
CREATE OR REPLACE FUNCTION fulfill_stock_reservations(
    p_reference_type VARCHAR(50),
    p_reference_id UUID,
    p_org_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    reservation_record RECORD;
BEGIN
    -- Process each active reservation for this reference
    FOR reservation_record IN
        SELECT *
        FROM stock_reservations
        WHERE org_id = p_org_id
        AND reference_type = p_reference_type
        AND reference_id = p_reference_id
        AND status = 'active'
    LOOP
        -- Update stock level to reduce both on_hand and reserved quantities
        UPDATE stock_levels
        SET 
            quantity_on_hand = GREATEST(0, quantity_on_hand - reservation_record.quantity_reserved),
            quantity_reserved = GREATEST(0, quantity_reserved - reservation_record.quantity_reserved),
            last_updated = NOW()
        WHERE org_id = p_org_id
        AND product_id = reservation_record.product_id
        AND location_id = reservation_record.location_id;

        -- Mark reservation as fulfilled
        UPDATE stock_reservations
        SET 
            status = 'fulfilled',
            updated_at = NOW()
        WHERE id = reservation_record.id;
    END LOOP;
END;
$$;

-- Function to check and expire old reservations
CREATE OR REPLACE FUNCTION expire_old_reservations()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    reservation_record RECORD;
BEGIN
    -- Find expired reservations
    FOR reservation_record IN
        SELECT *
        FROM stock_reservations
        WHERE status = 'active'
        AND expires_at IS NOT NULL
        AND expires_at < NOW()
    LOOP
        -- Release the expired reservation
        PERFORM release_stock_reservations(
            reservation_record.reference_type,
            reservation_record.reference_id,
            reservation_record.org_id
        );
    END LOOP;
END;
$$;

-- Create trigger for invoice stock reservations
DROP TRIGGER IF EXISTS invoice_stock_reservation_trigger ON invoices;
CREATE TRIGGER invoice_stock_reservation_trigger
    AFTER INSERT OR UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION create_invoice_stock_reservations();

-- Function to handle invoice payment and fulfill reservations
CREATE OR REPLACE FUNCTION handle_invoice_payment()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- When invoice status changes to 'paid', fulfill the reservations
    IF TG_OP = 'UPDATE' AND NEW.status = 'paid' AND OLD.status != 'paid' THEN
        PERFORM fulfill_stock_reservations('invoice', NEW.id, NEW.org_id);
    END IF;

    RETURN NEW;
END;
$$;

-- Create trigger for invoice payment fulfillment
DROP TRIGGER IF EXISTS invoice_payment_fulfillment_trigger ON invoices;
CREATE TRIGGER invoice_payment_fulfillment_trigger
    AFTER UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION handle_invoice_payment();

-- Create a scheduled job to expire old reservations (this would typically be done via pg_cron or similar)
-- For now, we'll create the function and organizations can call it manually or via a cron job

-- Comments for documentation
COMMENT ON TABLE stock_reservations IS 'Stock reservations for pending invoices and quotes to prevent overselling';
COMMENT ON FUNCTION create_invoice_stock_reservations() IS 'Automatically creates stock reservations when invoices are sent';
COMMENT ON FUNCTION release_stock_reservations(VARCHAR, UUID, UUID) IS 'Releases stock reservations and updates stock levels';
COMMENT ON FUNCTION fulfill_stock_reservations(VARCHAR, UUID, UUID) IS 'Fulfills stock reservations by reducing actual stock levels';
COMMENT ON FUNCTION expire_old_reservations() IS 'Expires old reservations that have passed their expiration date';
