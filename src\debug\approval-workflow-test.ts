import { supabase } from '@/lib/supabase'
import { ApprovalEngine } from '@/lib/approval-engine/core'
import { ApprovalActionHandler } from '@/lib/approval-engine/action-handler'
import { ApprovalInstanceManager } from '@/lib/approval-engine/instance-manager'

/**
 * Comprehensive test suite for the approval workflow system
 */
export class ApprovalWorkflowTest {
  
  /**
   * Test 1: Check if approval instances exist and have proper structure
   */
  static async testApprovalInstancesStructure() {
    console.log('🔍 Testing approval instances structure...')
    
    try {
      const { data: instances, error } = await supabase
        .from('approval_instances')
        .select(`
          *,
          workflow_template:workflow_templates(*),
          approval_actions(*)
        `)
        .limit(5)

      if (error) {
        console.error('❌ Error fetching approval instances:', error)
        return false
      }

      console.log('✅ Found approval instances:', instances?.length || 0)
      
      if (instances && instances.length > 0) {
        const instance = instances[0]
        console.log('📋 Sample instance structure:')
        console.log('- ID:', instance.id)
        console.log('- Document Type:', instance.document_type)
        console.log('- Document ID:', instance.document_id)
        console.log('- Status:', instance.status)
        console.log('- Current Step:', instance.current_step_order)
        console.log('- Workflow Template:', instance.workflow_template?.name)
        
        return instance
      }
      
      return null
    } catch (error) {
      console.error('❌ Test failed:', error)
      return false
    }
  }

  /**
   * Test 2: Check document details enrichment
   */
  static async testDocumentDetailsEnrichment(instance: Record<string, unknown>) {
    if (!instance) return false
    
    console.log('🔍 Testing document details enrichment...')
    
    try {
      const enrichedInstance = await ApprovalInstanceManager.getApprovalInstance(instance.id)
      
      if (enrichedInstance?.document_details) {
        console.log('✅ Document details found:')
        console.log('- Title:', enrichedInstance.document_details.title)
        console.log('- Number:', enrichedInstance.document_details.number)
        console.log('- Amount:', enrichedInstance.document_details.amount)
        console.log('- Currency:', enrichedInstance.document_details.currency_code)
        return enrichedInstance
      } else {
        console.log('❌ No document details found')
        return false
      }
    } catch (error) {
      console.error('❌ Document details test failed:', error)
      return false
    }
  }

  /**
   * Test 3: Check line items fetching
   */
  static async testLineItemsFetching(instance: Record<string, unknown>) {
    if (!instance) return false
    
    console.log('🔍 Testing line items fetching...')
    
    try {
      const { document_type, document_id } = instance
      let tableName: string
      let foreignKey: string

      switch (document_type) {
        case 'invoice':
          tableName = 'invoice_lines'
          foreignKey = 'invoice_id'
          break
        case 'bill':
          tableName = 'bill_lines'
          foreignKey = 'bill_id'
          break
        case 'budget':
          tableName = 'budget_lines'
          foreignKey = 'budget_id'
          break
        default:
          console.log('ℹ️ No line items for document type:', document_type)
          return true
      }

      const { data: lineItems, error } = await supabase
        .from(tableName)
        .select('*')
        .eq(foreignKey, document_id)

      if (error) {
        console.error('❌ Error fetching line items:', error)
        return false
      }

      console.log(`✅ Found ${lineItems?.length || 0} line items for ${document_type}`)
      
      if (lineItems && lineItems.length > 0) {
        console.log('📋 Sample line item:', lineItems[0])
      }
      
      return lineItems
    } catch (error) {
      console.error('❌ Line items test failed:', error)
      return false
    }
  }

  /**
   * Test 4: Test approval authority checking
   */
  static async testApprovalAuthority(instance: Record<string, unknown>, userId: string) {
    if (!instance || instance.status !== 'pending') {
      console.log('ℹ️ Skipping approval authority test - no pending instance')
      return true
    }

    console.log('🔍 Testing approval authority checking...')

    try {
      // Test authority checking
      const authority = await ApprovalEngine.checkApprovalAuthority(
        userId,
        instance.id,
        instance.document_amount
      )

      console.log('✅ Approval authority check:')
      console.log('- Can Approve:', authority.can_approve)
      console.log('- Can Reject:', authority.can_reject)
      console.log('- Can Delegate:', authority.can_delegate)

      return true
    } catch (error) {
      console.error('❌ Approval authority test failed:', error)
      return false
    }
  }

  /**
   * Test 5: Check database function availability
   */
  static async testDatabaseFunctions() {
    console.log('🔍 Testing database functions...')
    
    try {
      // Test advance_approval_workflow function exists
      const { data, error } = await supabase.rpc('advance_approval_workflow', {
        p_approval_instance_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        p_action: 'approved',
        p_approver_id: '00000000-0000-0000-0000-000000000000'
      })
      
      // We expect this to fail with a specific error (instance not found)
      // If it fails with "function does not exist", that's the real problem
      if (error && error.message.includes('function') && error.message.includes('does not exist')) {
        console.error('❌ Database function advance_approval_workflow does not exist')
        return false
      } else {
        console.log('✅ Database function advance_approval_workflow exists')
        return true
      }
    } catch (error) {
      console.error('❌ Database function test failed:', error)
      return false
    }
  }

  /**
   * Run all tests
   */
  static async runAllTests(userId?: string) {
    console.log('🚀 Starting comprehensive approval workflow tests...')
    console.log('=' .repeat(50))
    
    const results = {
      instancesStructure: false,
      documentDetails: false,
      lineItems: false,
      approvalAction: false,
      databaseFunctions: false
    }
    
    // Test 1: Instances structure
    const instance = await this.testApprovalInstancesStructure()
    results.instancesStructure = !!instance
    
    console.log('-'.repeat(50))
    
    // Test 2: Document details
    if (instance) {
      const enrichedInstance = await this.testDocumentDetailsEnrichment(instance)
      results.documentDetails = !!enrichedInstance
      
      console.log('-'.repeat(50))
      
      // Test 3: Line items
      const lineItems = await this.testLineItemsFetching(enrichedInstance || instance)
      results.lineItems = !!lineItems
      
      console.log('-'.repeat(50))
      
      // Test 4: Approval authority (if user provided)
      if (userId) {
        results.approvalAction = await this.testApprovalAuthority(instance, userId)
        console.log('-'.repeat(50))
      }
    }
    
    // Test 5: Database functions
    results.databaseFunctions = await this.testDatabaseFunctions()
    
    console.log('=' .repeat(50))
    console.log('📊 Test Results Summary:')
    console.log('- Instances Structure:', results.instancesStructure ? '✅ PASS' : '❌ FAIL')
    console.log('- Document Details:', results.documentDetails ? '✅ PASS' : '❌ FAIL')
    console.log('- Line Items:', results.lineItems ? '✅ PASS' : '❌ FAIL')
    console.log('- Approval Action:', results.approvalAction ? '✅ PASS' : '❌ FAIL')
    console.log('- Database Functions:', results.databaseFunctions ? '✅ PASS' : '❌ FAIL')
    
    return results
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as { ApprovalWorkflowTest?: typeof ApprovalWorkflowTest }).ApprovalWorkflowTest = ApprovalWorkflowTest
}
