/**
 * Monitoring Dashboard Component
 * Real-time monitoring and alerting dashboard for notification system
 */

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { LoadingSpinner } from '@/components/ui/loading'
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download
} from 'lucide-react'
import { 
  notificationMetrics, 
  notificationHealthChecker, 
  NotificationHealthChecks,
  type HealthCheck 
} from '@/lib/monitoring'

interface MonitoringDashboardProps {
  className?: string
}

interface MetricSummary {
  name: string
  current: number
  previous: number
  trend: 'up' | 'down' | 'stable'
  unit?: string
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1']

export function MonitoringDashboard({ className = '' }: MonitoringDashboardProps) {
  const [healthChecks, setHealthChecks] = useState<HealthCheck[]>([])
  const [metrics, setMetrics] = useState<MetricSummary[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Load monitoring data
  useEffect(() => {
    loadMonitoringData()
    
    if (autoRefresh) {
      const interval = setInterval(loadMonitoringData, 30000) // 30 seconds
      return () => clearInterval(interval)
    }
  }, [autoRefresh, loadMonitoringData])

  const loadMonitoringData = useCallback(async () => {
    setIsLoading(true)
    try {
      // Run health checks
      const [dbHealth, realtimeHealth, perfHealth] = await Promise.all([
        notificationHealthChecker.runCheck('database', NotificationHealthChecks.database),
        notificationHealthChecker.runCheck('realtime', NotificationHealthChecks.realtime),
        notificationHealthChecker.runCheck('performance', NotificationHealthChecks.notificationPerformance)
      ])

      setHealthChecks([dbHealth, realtimeHealth, perfHealth])

      // Get metrics summary
      const now = new Date()
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000)

      const metricsSummary: MetricSummary[] = [
        {
          name: 'Notifications Created',
          current: notificationMetrics.getStats('notifications_created_total', oneHourAgo).sum,
          previous: notificationMetrics.getStats('notifications_created_total', twoHoursAgo).sum,
          trend: 'stable',
          unit: 'count'
        },
        {
          name: 'Delivery Success Rate',
          current: calculateDeliveryRate(oneHourAgo),
          previous: calculateDeliveryRate(twoHoursAgo),
          trend: 'stable',
          unit: '%'
        },
        {
          name: 'Avg Response Time',
          current: notificationMetrics.getStats('api_response_time_seconds', oneHourAgo).avg,
          previous: notificationMetrics.getStats('api_response_time_seconds', twoHoursAgo).avg,
          trend: 'stable',
          unit: 'ms'
        },
        {
          name: 'Active Connections',
          current: notificationMetrics.getStats('realtime_connections_active').latest || 0,
          previous: 0,
          trend: 'stable',
          unit: 'count'
        }
      ]

      // Calculate trends
      metricsSummary.forEach(metric => {
        if (metric.previous > 0) {
          const change = ((metric.current - metric.previous) / metric.previous) * 100
          if (change > 5) metric.trend = 'up'
          else if (change < -5) metric.trend = 'down'
          else metric.trend = 'stable'
        }
      })

      setMetrics(metricsSummary)
      setLastRefresh(new Date())
    } catch (error) {
      console.error('Error loading monitoring data:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const calculateDeliveryRate = (since: Date): number => {
    const delivered = notificationMetrics.getStats('notifications_delivered_total', since)
    const total = notificationMetrics.getStats('notifications_created_total', since)
    
    if (total.sum === 0) return 100
    return (delivered.sum / total.sum) * 100
  }

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600'
      case 'degraded': return 'text-yellow-600'
      case 'unhealthy': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'degraded': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'unhealthy': return <XCircle className="h-4 w-4 text-red-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />
      default: return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const exportMetrics = () => {
    const data = {
      timestamp: new Date().toISOString(),
      healthChecks,
      metrics,
      detailedMetrics: {
        notifications: notificationMetrics.getMetrics('notifications_created_total'),
        deliveries: notificationMetrics.getMetrics('notifications_delivered_total'),
        responseTimes: notificationMetrics.getMetrics('api_response_time_seconds')
      }
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `monitoring-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (isLoading && healthChecks.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="md" text="Loading monitoring data..." />
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">System Monitoring</h2>
          <p className="text-muted-foreground">
            Real-time monitoring of notification system health and performance
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </Badge>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Activity className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-pulse' : ''}`} />
            Auto Refresh: {autoRefresh ? 'On' : 'Off'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={loadMonitoringData}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={exportMetrics}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Health Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {healthChecks.map((check) => (
          <Card key={check.name}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium capitalize">
                {check.name.replace('_', ' ')}
              </CardTitle>
              {getHealthStatusIcon(check.status)}
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getHealthStatusColor(check.status)}`}>
                {check.status.toUpperCase()}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {check.message}
              </p>
              {check.responseTime && (
                <p className="text-xs text-muted-foreground">
                  Response time: {check.responseTime}ms
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => (
          <Card key={metric.name}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
              {getTrendIcon(metric.trend)}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metric.unit === '%' ? metric.current.toFixed(1) : Math.round(metric.current)}
                {metric.unit && <span className="text-sm text-muted-foreground ml-1">{metric.unit}</span>}
              </div>
              {metric.previous > 0 && (
                <p className="text-xs text-muted-foreground">
                  Previous: {metric.unit === '%' ? metric.previous.toFixed(1) : Math.round(metric.previous)}{metric.unit}
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Monitoring */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="delivery">Delivery</TabsTrigger>
          <TabsTrigger value="errors">Errors</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Response Time Trends</CardTitle>
              <CardDescription>
                API response times over the last hour
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={getResponseTimeData()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="responseTime" stroke="#8884d8" name="Response Time (ms)" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="delivery" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Delivery Success Rate</CardTitle>
                <CardDescription>
                  Notification delivery success by channel
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={getDeliveryData()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: ${value}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {getDeliveryData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Notification Volume</CardTitle>
                <CardDescription>
                  Notifications created over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={getVolumeData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Error Rate</CardTitle>
              <CardDescription>
                System errors and failures over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                <p>No critical errors detected in the last 24 hours</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Real-time Connections</CardTitle>
              <CardDescription>
                Active WebSocket connections and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={getConnectionData()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="connections" stroke="#8884d8" name="Active Connections" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )

  // Helper functions for chart data
  function getResponseTimeData() {
    const metrics = notificationMetrics.getMetrics('api_response_time_seconds')
    return metrics.slice(-20).map((metric, index) => ({
      time: metric.timestamp.toLocaleTimeString(),
      responseTime: metric.value * 1000 // Convert to ms
    }))
  }

  function getDeliveryData() {
    return [
      { name: 'Email', value: 95 },
      { name: 'Push', value: 88 },
      { name: 'In-App', value: 99 },
      { name: 'Slack', value: 92 }
    ]
  }

  function getVolumeData() {
    const metrics = notificationMetrics.getMetrics('notifications_created_total')
    const hourlyData = new Map()
    
    metrics.forEach(metric => {
      const hour = new Date(metric.timestamp).getHours()
      hourlyData.set(hour, (hourlyData.get(hour) || 0) + metric.value)
    })

    return Array.from(hourlyData.entries()).map(([hour, count]) => ({
      time: `${hour}:00`,
      count
    }))
  }

  function getConnectionData() {
    const metrics = notificationMetrics.getMetrics('realtime_connections_active')
    return metrics.slice(-20).map(metric => ({
      time: metric.timestamp.toLocaleTimeString(),
      connections: metric.value
    }))
  }
}
