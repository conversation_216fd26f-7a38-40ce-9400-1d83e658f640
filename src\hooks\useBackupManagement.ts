import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'
import { BackupService } from '@/lib/backup-service'
import type { BackupMetadata } from '@/types/database'

export interface BackupSettings {
  id: string
  org_id: string
  auto_backup_enabled: boolean
  backup_frequency: 'hourly' | 'daily' | 'weekly'
  retention_days: number
  backup_time: string
  notification_enabled: boolean
  last_backup_at?: string
  created_at: string
  updated_at: string
}

export interface BackupStatistics {
  total_backups: number
  successful_backups: number
  failed_backups: number
  total_size_bytes: number
  last_backup_date?: string
  next_scheduled_backup?: string
}

export function useBackupManagement() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  // Get backup metadata list
  const backupsQuery = useQuery({
    queryKey: ['backups', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('backup_metadata')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as BackupMetadata[]
    },
    enabled: !!profile?.org_id,
  })

  // Get backup settings
  const settingsQuery = useQuery({
    queryKey: ['backup-settings', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('backup_settings')
        .select('*')
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data as BackupSettings
    },
    enabled: !!profile?.org_id,
  })

  // Get backup statistics
  const statisticsQuery = useQuery({
    queryKey: ['backup-statistics', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase.rpc('get_backup_statistics', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data[0] as BackupStatistics
    },
    enabled: !!profile?.org_id,
  })

  // Create backup mutation
  const createBackupMutation = useMutation({
    mutationFn: async (backupType: 'full' | 'incremental' | 'differential' = 'full') => {
      if (!profile?.org_id) throw new Error('No organization ID')

      try {
        // Call the backup service
        const result = await BackupService.createBackup(
          profile.org_id,
          backupType,
          profile.id
        )
        return result
      } catch (error) {
        console.error('Backup creation failed:', error)
        throw error
      }
    },
    onSuccess: () => {
      toast.success('Backup created successfully')
      queryClient.invalidateQueries({ queryKey: ['backups'] })
      queryClient.invalidateQueries({ queryKey: ['backup-statistics'] })
    },
    onError: (error) => {
      toast.error('Failed to create backup: ' + error.message)
    },
  })

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (settings: Partial<BackupSettings>) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('backup_settings')
        .update({
          ...settings,
          updated_at: new Date().toISOString()
        })
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      toast.success('Backup settings updated successfully')
      queryClient.invalidateQueries({ queryKey: ['backup-settings'] })
    },
    onError: (error) => {
      toast.error('Failed to update settings: ' + error.message)
    },
  })

  // Delete backup mutation
  const deleteBackupMutation = useMutation({
    mutationFn: async (backupId: string) => {
      const { error } = await supabase
        .from('backup_metadata')
        .delete()
        .eq('id', backupId)

      if (error) throw error
    },
    onSuccess: () => {
      toast.success('Backup deleted successfully')
      queryClient.invalidateQueries({ queryKey: ['backups'] })
      queryClient.invalidateQueries({ queryKey: ['backup-statistics'] })
    },
    onError: (error) => {
      toast.error('Failed to delete backup: ' + error.message)
    },
  })

  // Verify backup mutation
  const verifyBackupMutation = useMutation({
    mutationFn: async (backupId: string) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      try {
        // Call the backup verification service
        const result = await BackupService.verifyBackup(backupId, profile.org_id)
        return result
      } catch (error) {
        console.error('Backup verification failed:', error)
        throw error
      }
    },
    onSuccess: (result) => {
      if (result.valid) {
        toast.success('Backup verification successful')
      } else {
        toast.error('Backup verification failed')
      }
    },
    onError: (error) => {
      toast.error('Failed to verify backup: ' + error.message)
    },
  })



  return {
    // Data
    backups: backupsQuery.data || [],
    settings: settingsQuery.data,
    statistics: statisticsQuery.data,
    
    // Loading states
    isLoading: backupsQuery.isLoading || settingsQuery.isLoading,
    isCreating: createBackupMutation.isPending,
    isUpdatingSettings: updateSettingsMutation.isPending,
    isDeleting: deleteBackupMutation.isPending,
    isVerifying: verifyBackupMutation.isPending,
    
    // Error states
    error: backupsQuery.error?.message || settingsQuery.error?.message,
    
    // Actions
    createBackup: createBackupMutation.mutate,
    updateSettings: updateSettingsMutation.mutate,
    deleteBackup: deleteBackupMutation.mutate,
    verifyBackup: verifyBackupMutation.mutate,
    
    // Refetch
    refetch: () => {
      backupsQuery.refetch()
      settingsQuery.refetch()
      statisticsQuery.refetch()
    }
  }
}
