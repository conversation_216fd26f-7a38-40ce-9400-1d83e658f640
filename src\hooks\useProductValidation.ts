import { useMemo } from 'react'
import { useProducts, useActiveProductCategories } from '@/hooks/queries'
import { 
  validateProduct, 
  validateField, 
  type ValidationContext, 
  type ValidationResult 
} from '@/lib/validation/productValidation'
import type { ProductFormData } from '@/types/inventory'

interface UseProductValidationOptions {
  currentProductId?: string // For edit mode
  realTimeValidation?: boolean // Enable real-time validation
}

export function useProductValidation(options: UseProductValidationOptions = {}) {
  const { data: products = [] } = useProducts()
  const { data: categories = [] } = useActiveProductCategories()

  // Create validation context
  const validationContext: ValidationContext = useMemo(() => ({
    existingProducts: products.map(p => ({
      id: p.id,
      sku: p.sku,
      barcode: p.barcode
    })),
    categories: categories.map(c => ({
      id: c.id,
      name: c.name
    })),
    currentProductId: options.currentProductId
  }), [products, categories, options.currentProductId])

  // Validate complete product data
  const validateProductData = (data: ProductFormData): ValidationResult => {
    return validateProduct(data, validationContext)
  }

  // Validate single field
  const validateSingleField = (
    field: string, 
    value: any, 
    allData: Partial<ProductFormData>
  ): ValidationResult => {
    return validateField(field, value, allData, validationContext)
  }

  // Check if SKU is unique
  const isSkuUnique = (sku: string): boolean => {
    if (!sku) return true
    return !products.some(p => 
      p.sku.toLowerCase() === sku.toLowerCase() && 
      p.id !== options.currentProductId
    )
  }

  // Check if barcode is unique
  const isBarcodeUnique = (barcode: string): boolean => {
    if (!barcode) return true
    return !products.some(p => 
      p.barcode && 
      p.barcode.toLowerCase() === barcode.toLowerCase() && 
      p.id !== options.currentProductId
    )
  }

  // Get category by ID
  const getCategoryById = (categoryId: string) => {
    return categories.find(c => c.id === categoryId)
  }

  // Get category by name
  const getCategoryByName = (categoryName: string) => {
    return categories.find(c => 
      c.name.toLowerCase() === categoryName.toLowerCase()
    )
  }

  // Validate SKU format
  const validateSkuFormat = (sku: string): { isValid: boolean; message?: string } => {
    if (!sku) {
      return { isValid: false, message: 'SKU is required' }
    }

    if (sku.length < 1) {
      return { isValid: false, message: 'SKU cannot be empty' }
    }

    if (sku.length > 100) {
      return { isValid: false, message: 'SKU must be 100 characters or less' }
    }

    if (!/^[A-Za-z0-9\-_]+$/.test(sku)) {
      return { isValid: false, message: 'SKU can only contain letters, numbers, hyphens, and underscores' }
    }

    if (sku !== sku.trim()) {
      return { isValid: false, message: 'SKU cannot have leading or trailing spaces' }
    }

    return { isValid: true }
  }

  // Validate pricing
  const validatePricing = (costPrice: number, sellingPrice: number): {
    isValid: boolean
    warnings: string[]
    errors: string[]
  } => {
    const warnings: string[] = []
    const errors: string[] = []

    if (costPrice < 0) {
      errors.push('Cost price cannot be negative')
    }

    if (sellingPrice < 0) {
      errors.push('Selling price cannot be negative')
    }

    if (costPrice > 999999.99) {
      errors.push('Cost price cannot exceed $999,999.99')
    }

    if (sellingPrice > 999999.99) {
      errors.push('Selling price cannot exceed $999,999.99')
    }

    if (costPrice > 0 && sellingPrice > 0) {
      if (sellingPrice < costPrice) {
        warnings.push('Selling price is below cost price. You will lose money on each sale.')
      } else {
        const margin = ((sellingPrice - costPrice) / sellingPrice) * 100
        if (margin < 10) {
          warnings.push('Very low profit margin. Consider increasing the selling price.')
        } else if (margin < 20) {
          warnings.push('Low profit margin. Consider reviewing your pricing strategy.')
        }
      }
    }

    return {
      isValid: errors.length === 0,
      warnings,
      errors
    }
  }

  // Calculate profit margin
  const calculateMargin = (costPrice: number, sellingPrice: number): {
    marginAmount: number
    marginPercentage: number
    markupPercentage: number
  } => {
    if (costPrice <= 0 || sellingPrice <= 0) {
      return { marginAmount: 0, marginPercentage: 0, markupPercentage: 0 }
    }

    const marginAmount = sellingPrice - costPrice
    const marginPercentage = (marginAmount / sellingPrice) * 100
    const markupPercentage = (marginAmount / costPrice) * 100

    return {
      marginAmount: Number(marginAmount.toFixed(2)),
      marginPercentage: Number(marginPercentage.toFixed(1)),
      markupPercentage: Number(markupPercentage.toFixed(1))
    }
  }

  // Generate SKU suggestions
  const generateSkuSuggestions = (productName: string, categoryId?: string): string[] => {
    const suggestions: string[] = []
    
    if (!productName) return suggestions

    const category = categoryId ? getCategoryById(categoryId) : null
    const categoryPrefix = category?.code || category?.name?.substring(0, 3).toUpperCase() || ''
    
    // Clean product name
    const cleanName = productName
      .replace(/[^A-Za-z0-9\s]/g, '')
      .split(' ')
      .filter(word => word.length > 0)
      .slice(0, 3)
      .map(word => word.substring(0, 8).toUpperCase())
      .join('-')

    // Generate different variations
    if (categoryPrefix) {
      suggestions.push(`${categoryPrefix}-${cleanName}`)
      suggestions.push(`${categoryPrefix}-${cleanName}-001`)
    }
    
    suggestions.push(cleanName)
    suggestions.push(`${cleanName}-001`)
    
    // Add random suffix for uniqueness
    const randomSuffix = Math.random().toString(36).substring(2, 5).toUpperCase()
    suggestions.push(`${cleanName}-${randomSuffix}`)
    
    if (categoryPrefix) {
      suggestions.push(`${categoryPrefix}-${cleanName}-${randomSuffix}`)
    }

    // Filter out duplicates and existing SKUs
    return suggestions
      .filter((sku, index, arr) => arr.indexOf(sku) === index)
      .filter(sku => isSkuUnique(sku))
      .slice(0, 5)
  }

  // Validate inventory settings
  const validateInventorySettings = (
    trackInventory: boolean,
    reorderLevel: number,
    reorderQuantity: number
  ): { warnings: string[]; errors: string[] } => {
    const warnings: string[] = []
    const errors: string[] = []

    if (reorderLevel < 0) {
      errors.push('Reorder level cannot be negative')
    }

    if (reorderQuantity < 0) {
      errors.push('Reorder quantity cannot be negative')
    }

    if (trackInventory) {
      if (reorderLevel > 0 && reorderQuantity === 0) {
        warnings.push('Reorder level is set but reorder quantity is zero')
      }
      
      if (reorderLevel === 0 && reorderQuantity > 0) {
        warnings.push('Reorder quantity is set but reorder level is zero')
      }
    }

    return { warnings, errors }
  }

  return {
    // Main validation functions
    validateProductData,
    validateSingleField,
    
    // Uniqueness checks
    isSkuUnique,
    isBarcodeUnique,
    
    // Category helpers
    getCategoryById,
    getCategoryByName,
    
    // Field-specific validation
    validateSkuFormat,
    validatePricing,
    validateInventorySettings,
    
    // Utility functions
    calculateMargin,
    generateSkuSuggestions,
    
    // Context data
    validationContext,
    categories,
    products: products.map(p => ({ id: p.id, sku: p.sku, barcode: p.barcode }))
  }
}
