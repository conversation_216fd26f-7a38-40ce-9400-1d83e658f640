/**
 * Password Validation Utilities
 * Provides comprehensive password validation with real-time feedback
 */

export interface PasswordRequirement {
  id: string
  label: string
  test: (password: string) => boolean
  description: string
}

export interface PasswordValidationResult {
  isValid: boolean
  score: number // 0-100
  requirements: Array<PasswordRequirement & { met: boolean }>
  feedback: string[]
  strength: 'weak' | 'fair' | 'good' | 'strong'
}

// Define password requirements
export const PASSWORD_REQUIREMENTS: PasswordRequirement[] = [
  {
    id: 'length',
    label: 'At least 8 characters',
    test: (password: string) => password.length >= 8,
    description: 'Password must be at least 8 characters long'
  },
  {
    id: 'uppercase',
    label: 'One uppercase letter',
    test: (password: string) => /[A-Z]/.test(password),
    description: 'Password must contain at least one uppercase letter (A-Z)'
  },
  {
    id: 'lowercase',
    label: 'One lowercase letter',
    test: (password: string) => /[a-z]/.test(password),
    description: 'Password must contain at least one lowercase letter (a-z)'
  },
  {
    id: 'number',
    label: 'One number',
    test: (password: string) => /\d/.test(password),
    description: 'Password must contain at least one number (0-9)'
  },
  {
    id: 'special',
    label: 'One special character',
    test: (password: string) => /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password),
    description: 'Password must contain at least one special character (!@#$%^&*)'
  }
]

// Minimum requirements for basic validation
export const MINIMUM_REQUIREMENTS = ['length', 'uppercase', 'lowercase']

/**
 * Validate password against all requirements
 */
export function validatePassword(password: string): PasswordValidationResult {
  const requirements = PASSWORD_REQUIREMENTS.map(req => ({
    ...req,
    met: req.test(password)
  }))

  const metRequirements = requirements.filter(req => req.met)
  const metMinimumRequirements = MINIMUM_REQUIREMENTS.every(reqId => 
    requirements.find(req => req.id === reqId)?.met
  )

  // Calculate score based on requirements met
  const score = Math.round((metRequirements.length / PASSWORD_REQUIREMENTS.length) * 100)

  // Determine strength
  let strength: 'weak' | 'fair' | 'good' | 'strong'
  if (score >= 80) {
    strength = 'strong'
  } else if (score >= 60) {
    strength = 'good'
  } else if (score >= 40) {
    strength = 'fair'
  } else {
    strength = 'weak'
  }

  // Generate feedback
  const feedback: string[] = []
  const unmetRequirements = requirements.filter(req => !req.met)

  if (unmetRequirements.length > 0) {
    feedback.push(`Missing: ${unmetRequirements.map(req => req.label.toLowerCase()).join(', ')}`)
  }

  // Additional security feedback
  if (password.length > 0) {
    if (password.length < 8) {
      feedback.push('Password is too short')
    } else if (password.length >= 12) {
      feedback.push('Good length!')
    }

    // Check for common patterns
    if (/(.)\1{2,}/.test(password)) {
      feedback.push('Avoid repeating characters')
    }

    if (/123|abc|qwe|password|admin/i.test(password)) {
      feedback.push('Avoid common patterns and words')
    }
  }

  return {
    isValid: metMinimumRequirements,
    score,
    requirements,
    feedback,
    strength
  }
}

/**
 * Check if password meets minimum security requirements
 */
export function isPasswordSecure(password: string): boolean {
  const validation = validatePassword(password)
  return validation.isValid
}

/**
 * Get password strength color for UI
 */
export function getPasswordStrengthColor(strength: 'weak' | 'fair' | 'good' | 'strong'): string {
  switch (strength) {
    case 'weak':
      return 'text-red-600'
    case 'fair':
      return 'text-orange-600'
    case 'good':
      return 'text-yellow-600'
    case 'strong':
      return 'text-green-600'
    default:
      return 'text-gray-600'
  }
}

/**
 * Get password strength background color for progress bars
 */
export function getPasswordStrengthBgColor(strength: 'weak' | 'fair' | 'good' | 'strong'): string {
  switch (strength) {
    case 'weak':
      return 'bg-red-500'
    case 'fair':
      return 'bg-orange-500'
    case 'good':
      return 'bg-yellow-500'
    case 'strong':
      return 'bg-green-500'
    default:
      return 'bg-gray-300'
  }
}

/**
 * Generate password requirements text for display
 */
export function getPasswordRequirementsText(): string {
  return 'Password must contain at least 8 characters, including uppercase and lowercase letters. For better security, also include numbers and special characters.'
}

/**
 * Validate password confirmation
 */
export function validatePasswordConfirmation(password: string, confirmPassword: string): {
  isValid: boolean
  message?: string
} {
  if (!confirmPassword) {
    return { isValid: false, message: 'Please confirm your password' }
  }

  if (password !== confirmPassword) {
    return { isValid: false, message: 'Passwords do not match' }
  }

  return { isValid: true }
}
