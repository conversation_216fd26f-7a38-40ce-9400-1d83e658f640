import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading'
import type { SearchResult } from '@/types/search'
import { formatCurrency, getSearchResultIcon } from '@/lib/searchUtils'
import { cn } from '@/lib/utils'

interface SearchResultsProps {
  results: SearchResult[]
  loading: boolean
  error: string | null
  selectedIndex: number
  onResultClick: (result: SearchResult) => void
  onResultHover: (index: number) => void
}

export function SearchResults({ 
  results, 
  loading, 
  error, 
  selectedIndex,
  onResultClick,
  onResultHover
}: SearchResultsProps) {
  if (loading) {
    return (
      <div className="p-8">
        <LoadingSpinner text="Searching..." showText />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <p className="text-sm text-destructive">{error}</p>
      </div>
    )
  }

  if (results.length === 0) {
    return (
      <div className="p-8 text-center text-muted-foreground">
        <p className="text-sm">No results found</p>
        <p className="text-xs mt-1">Try adjusting your search terms</p>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'pending':
      case 'sent':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      case 'approved':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <div className="p-2">
      <div className="px-2 py-1 mb-2">
        <span className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
          Search Results ({results.length})
        </span>
      </div>
      
      <div className="space-y-1">
        {results.map((result, index) => (
          <div
            key={`${result.type}-${result.id}`}
            className={cn(
              "group flex items-center gap-3 px-3 py-3 rounded-md cursor-pointer transition-colors",
              "hover:bg-accent",
              selectedIndex === index && "bg-accent"
            )}
            onClick={() => onResultClick(result)}
            onMouseEnter={() => onResultHover(index)}
          >
            {/* Icon */}
            <div className="flex-shrink-0 text-lg">
              {getSearchResultIcon(result.type)}
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="text-sm font-medium truncate">{result.title}</h4>
                {result.metadata?.status && (
                  <Badge 
                    variant="secondary" 
                    className={cn("text-xs", getStatusColor(result.metadata.status))}
                  >
                    {result.metadata.status}
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex flex-col gap-1">
                  {result.subtitle && (
                    <p className="text-xs text-muted-foreground truncate">
                      {result.subtitle}
                    </p>
                  )}
                  {result.description && (
                    <p className="text-xs text-muted-foreground">
                      {result.description}
                    </p>
                  )}
                </div>
                
                {result.metadata?.amount && (
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {formatCurrency(result.metadata.amount, result.metadata.currency)}
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Type indicator */}
            <div className="flex-shrink-0">
              <Badge variant="outline" className="text-xs capitalize">
                {result.type}
              </Badge>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
