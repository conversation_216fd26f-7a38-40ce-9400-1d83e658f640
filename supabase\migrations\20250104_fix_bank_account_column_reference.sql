-- Fix bank account column reference in payment automation functions
-- The functions were referencing ba.account_name but the column is actually ba.name

DO $$
BEGIN
    RAISE NOTICE '🔧 Fixing bank account column references in payment automation functions...';
END $$;

-- Drop and recreate the payment application automation trigger function
DROP FUNCTION IF EXISTS handle_payment_application_journal_entry() CASCADE;

CREATE OR REPLACE FUNCTION handle_payment_application_journal_entry()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    automation_enabled BOOLEAN;
    payment_record RECORD;
    ar_account_id UUID;
    ap_account_id UUID;
    cash_account_id UUID;
    new_journal_id UUID;
    document_record RECORD;
    document_type TEXT;
    document_table TEXT;
BEGIN
    -- Check if automation is enabled for this organization
    SELECT enable_journal_automation INTO automation_enabled
    FROM organizations
    WHERE id = NEW.org_id;

    IF NOT automation_enabled THEN
        RETURN NEW;
    END IF;

    -- Get payment details (FIXED: use ba.name instead of ba.account_name)
    SELECT p.*, ba.name as bank_account_name
    INTO payment_record
    FROM payments p
    LEFT JOIN bank_accounts ba ON p.bank_account_id = ba.id
    WHERE p.id = NEW.payment_id;

    -- Check if journal entry already exists for this payment application
    IF EXISTS (
        SELECT 1 FROM journal_entries
        WHERE source_id = NEW.id AND source_type = 'payment_application'
    ) THEN
        RETURN NEW;
    END IF;

    -- Get required account mappings
    SELECT account_id INTO ar_account_id
    FROM account_mappings
    WHERE org_id = NEW.org_id AND mapping_type = 'accounts_receivable' AND is_default = true;

    SELECT account_id INTO ap_account_id
    FROM account_mappings
    WHERE org_id = NEW.org_id AND mapping_type = 'accounts_payable' AND is_default = true;

    SELECT account_id INTO cash_account_id
    FROM account_mappings
    WHERE org_id = NEW.org_id AND mapping_type = 'cash' AND is_default = true;

    -- Validate required accounts exist
    IF ar_account_id IS NULL OR ap_account_id IS NULL OR cash_account_id IS NULL THEN
        RAISE WARNING 'Missing required account mappings for payment application automation in org %', NEW.org_id;
        RETURN NEW;
    END IF;

    -- Determine document type and get document details
    IF NEW.applied_to_type = 'invoice' THEN
        document_type := 'invoice';
        document_table := 'invoices';
        
        SELECT invoice_number as number, customer_id as entity_id
        INTO document_record
        FROM invoices
        WHERE id = NEW.applied_to_id;
    ELSE
        document_type := 'bill';
        document_table := 'bills';
        
        SELECT bill_number as number, vendor_id as entity_id
        INTO document_record
        FROM bills
        WHERE id = NEW.applied_to_id;
    END IF;

    -- Create journal entry for payment application
    INSERT INTO journal_entries (
        org_id, date, description, reference,
        source_id, source_type, created_by, is_posted
    )
    VALUES (
        NEW.org_id,
        payment_record.date,
        'Payment Application: ' || document_record.number,
        'PAY-' || payment_record.reference_number,
        NEW.id,
        'payment_application',
        payment_record.created_by,
        false
    )
    RETURNING id INTO new_journal_id;

    -- Create transaction lines based on document type
    IF NEW.applied_to_type = 'invoice' THEN
        -- Customer payment: DR Cash, CR Accounts Receivable
        
        -- Debit: Cash/Bank Account
        INSERT INTO transaction_lines (
            org_id, journal_entry_id, account_id,
            debit, credit, description
        )
        VALUES (
            NEW.org_id, new_journal_id, cash_account_id,
            NEW.amount_applied, 0, 'Cash received - ' || document_record.number
        );

        -- Credit: Accounts Receivable
        INSERT INTO transaction_lines (
            org_id, journal_entry_id, account_id,
            debit, credit, description
        )
        VALUES (
            NEW.org_id, new_journal_id, ar_account_id,
            0, NEW.amount_applied, 'Payment received - ' || document_record.number
        );

    ELSE
        -- Vendor payment: DR Accounts Payable, CR Cash
        
        -- Debit: Accounts Payable
        INSERT INTO transaction_lines (
            org_id, journal_entry_id, account_id,
            debit, credit, description
        )
        VALUES (
            NEW.org_id, new_journal_id, ap_account_id,
            NEW.amount_applied, 0, 'Payment made - ' || document_record.number
        );

        -- Credit: Cash/Bank Account
        INSERT INTO transaction_lines (
            org_id, journal_entry_id, account_id,
            debit, credit, description
        )
        VALUES (
            NEW.org_id, new_journal_id, cash_account_id,
            0, NEW.amount_applied, 'Cash paid - ' || document_record.number
        );
    END IF;

    RETURN NEW;
END;
$$;

-- Recreate the trigger
CREATE TRIGGER payment_application_journal_trigger
    AFTER INSERT ON payment_applications
    FOR EACH ROW
    EXECUTE FUNCTION handle_payment_application_journal_entry();

DO $$
BEGIN
    RAISE NOTICE '✅ Fixed bank account column references in payment automation functions';
    RAISE NOTICE '   Changed ba.account_name to ba.name to match actual database schema';
END $$;
