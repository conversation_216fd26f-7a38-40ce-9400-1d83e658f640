#!/usr/bin/env node

/**
 * Supabase Client Test
 * Tests if the Supabase client is configured correctly
 */

import { createClient } from '@supabase/supabase-js'

// Test both import methods
console.log('🧪 Testing Supabase Client Configuration')
console.log('=' .repeat(50))

// Method 1: Direct import (like in scripts)
console.log('📦 Method 1: Direct createClient import')
const supabase1 = createClient(
  'https://kmejequnwwngmzwkszqs.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImttZWplcXVud3duZ216d2tzenFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDk3ODQsImV4cCI6MjA2Mzc4NTc4NH0.ROGVGPdlDh_o1TJJJijM1BTubWUhUXUh5oZWxOKDdjw'
)

console.log('Client 1 created:', !!supabase1)
console.log('Client 1 methods:', {
  from: typeof supabase1.from,
  auth: typeof supabase1.auth,
  rpc: typeof supabase1.rpc
})

// Test the problematic method chain
console.log('\n🔧 Testing method chaining...')

try {
  const query = supabase1
    .from('organizations')
    .insert({
      name: 'Test Org',
      currency_code: 'UGX',
      timezone: 'Africa/Kampala'
    })

  console.log('Insert query created:', !!query)
  console.log('Insert query methods:', {
    select: typeof query.select,
    single: typeof query.single,
    then: typeof query.then
  })

  // Test if select method exists
  if (typeof query.select === 'function') {
    const selectQuery = query.select()
    console.log('Select query created:', !!selectQuery)
    console.log('Select query methods:', {
      single: typeof selectQuery.single,
      then: typeof selectQuery.then
    })

    if (typeof selectQuery.single === 'function') {
      const finalQuery = selectQuery.single()
      console.log('Final query created:', !!finalQuery)
      console.log('Final query type:', typeof finalQuery)
    } else {
      console.log('❌ .single() method not available on select query')
    }
  } else {
    console.log('❌ .select() method not available on insert query')
  }

} catch (error) {
  console.error('❌ Error creating query chain:', error.message)
}

// Test RPC method
console.log('\n🔧 Testing RPC method...')
try {
  const rpcQuery = supabase1.rpc('create_organization_with_profile', {
    org_name: 'Test',
    user_role: 'owner'
  })
  
  console.log('RPC query created:', !!rpcQuery)
  console.log('RPC query type:', typeof rpcQuery)
  console.log('RPC query methods:', {
    then: typeof rpcQuery.then,
    select: typeof rpcQuery.select
  })
} catch (error) {
  console.error('❌ Error creating RPC query:', error.message)
}

// Test version information
console.log('\n📋 Version Information:')
try {
  // Try to get version info
  console.log('Supabase client version: Available methods suggest v2.x')
  console.log('Node.js version:', process.version)
} catch (error) {
  console.log('Could not determine version:', error.message)
}

console.log('\n✅ Client configuration test completed')
console.log('\n💡 If you see method availability issues:')
console.log('1. Check @supabase/supabase-js version in package.json')
console.log('2. Ensure you\'re using the correct import syntax')
console.log('3. Verify the client is properly configured')
console.log('4. Check for TypeScript compilation issues')
