import { useState, useEffect, useCallback } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { 
  CreditCard, 
  User, 
  Building, 
  Calendar, 
  Hash, 
  DollarSign,
  FileText,
  Phone,
  Mail,
  MapPin,
  Banknote,
  Smartphone
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { LoadingSpinner } from '@/components/ui/loading'
import type { TransactionDetails, PaymentApplication } from '@/types/transactions'
import type { Customer, Vendor } from '@/types/database'
import { formatCurrency } from '@/lib/utils'

interface TransactionDetailsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  transactionId: string
}

export function TransactionDetailsModal({ 
  open, 
  onOpenChange, 
  transactionId 
}: TransactionDetailsModalProps) {
  const { profile } = useAuth()
  const [transaction, setTransaction] = useState<TransactionDetails | null>(null)
  const [applications, setApplications] = useState<PaymentApplication[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTransactionDetails = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Fetch payment details
      const { data: payment, error: paymentError } = await supabase
        .from('payments')
        .select('*')
        .eq('id', transactionId)
        .single()

      if (paymentError) throw paymentError

      // Fetch payee details
      let payeeDetails = null
      let payeeName = 'Unknown'

      if (payment.payee_type === 'customer') {
        const { data: customer } = await supabase
          .from('customers')
          .select('*')
          .eq('id', payment.payee_id)
          .single()
        
        payeeDetails = customer
        payeeName = customer?.name || 'Customer'
      } else if (payment.payee_type === 'vendor') {
        const { data: vendor } = await supabase
          .from('vendors')
          .select('*')
          .eq('id', payment.payee_id)
          .single()
        
        payeeDetails = vendor
        payeeName = vendor?.name || 'Vendor'
      }

      // Fetch bank account details if applicable
      let bankAccountDetails = null
      if (payment.bank_account_id) {
        const { data: bankAccount } = await supabase
          .from('bank_accounts')
          .select('*')
          .eq('id', payment.bank_account_id)
          .single()
        
        bankAccountDetails = bankAccount
      }

      // Fetch payment applications
      const { data: apps, error: appsError } = await supabase
        .from('payment_applications')
        .select('*')
        .eq('payment_id', transactionId)

      if (appsError) throw appsError

      // Fetch related document details
      const relatedDocuments = []
      const applicationsTotal = apps?.reduce((sum, app) => sum + Number(app.amount_applied), 0) || 0

      if (apps && apps.length > 0) {
        for (const app of apps) {
          if (app.applied_to_type === 'invoice') {
            const { data: invoice } = await supabase
              .from('invoices')
              .select('id, invoice_number, date_issued, total_amount, status')
              .eq('id', app.applied_to_id)
              .single()

            if (invoice) {
              relatedDocuments.push({
                id: invoice.id,
                type: 'invoice' as const,
                number: invoice.invoice_number,
                amount: invoice.total_amount,
                amount_applied: app.amount_applied,
                date: invoice.date_issued,
                status: invoice.status
              })
            }
          } else if (app.applied_to_type === 'bill') {
            const { data: bill } = await supabase
              .from('bills')
              .select('id, bill_number, date_issued, total_amount, status')
              .eq('id', app.applied_to_id)
              .single()

            if (bill) {
              relatedDocuments.push({
                id: bill.id,
                type: 'bill' as const,
                number: bill.bill_number,
                amount: bill.total_amount,
                amount_applied: app.amount_applied,
                date: bill.date_issued,
                status: bill.status
              })
            }
          }
        }
      }

      const enhancedTransaction: TransactionDetails = {
        ...payment,
        payee_name: payeeName,
        payee_details: payeeDetails,
        applications_total: applicationsTotal,
        remaining_balance: payment.amount - applicationsTotal,
        related_documents: relatedDocuments,
        bank_account_details: bankAccountDetails
      }

      setTransaction(enhancedTransaction)
      setApplications(apps || [])

    } catch (error) {
      console.error('Error fetching transaction details:', error)
      setError('Failed to load transaction details')
    } finally {
      setLoading(false)
    }
  }, [transactionId])

  useEffect(() => {
    if (open && transactionId) {
      fetchTransactionDetails()
    }
  }, [open, transactionId, fetchTransactionDetails])

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'bank':
        return <Banknote className="h-4 w-4" />
      case 'mtn_momo':
      case 'airtel_money':
        return <Smartphone className="h-4 w-4" />
      case 'cash':
        return <DollarSign className="h-4 w-4" />
      default:
        return <CreditCard className="h-4 w-4" />
    }
  }

  const formatChannelName = (channel: string) => {
    switch (channel) {
      case 'mtn_momo':
        return 'MTN Mobile Money'
      case 'airtel_money':
        return 'Airtel Money'
      case 'bank':
        return 'Bank Transfer'
      case 'cash':
        return 'Cash'
      default:
        return channel
    }
  }

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <LoadingSpinner size="lg" text="Loading transaction details..." showText className="h-64" />
        </DialogContent>
      </Dialog>
    )
  }

  if (error || !transaction) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <div className="text-center py-8">
            <p className="text-destructive">{error || 'Transaction not found'}</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Transaction Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Payment Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Payment Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label className="text-muted-foreground">Amount</Label>
                  <p className="text-2xl font-bold text-primary">
                    {formatCurrency(transaction.amount)}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Status</Label>
                  <Badge className={getStatusColor(transaction.status)}>
                    {transaction.status}
                  </Badge>
                </div>
                <div>
                  <Label className="text-muted-foreground">Date</Label>
                  <p className="font-medium">
                    {new Date(transaction.payment_date).toLocaleDateString('en-UG', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Reconciled</Label>
                  <Badge variant={transaction.is_reconciled ? 'default' : 'secondary'}>
                    {transaction.is_reconciled ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Payee Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {transaction.payee_type === 'customer' ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <Building className="h-4 w-4" />
                  )}
                  {transaction.payee_type === 'customer' ? 'Customer' : 'Vendor'} Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-muted-foreground">Name</Label>
                  <p className="font-medium">{transaction.payee_name}</p>
                </div>
                {transaction.payee_details?.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{transaction.payee_details.email}</span>
                  </div>
                )}
                {transaction.payee_details?.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{transaction.payee_details.phone}</span>
                  </div>
                )}
                {transaction.payee_details?.address && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{transaction.payee_details.address}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Method */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getChannelIcon(transaction.channel)}
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-muted-foreground">Channel</Label>
                  <p className="font-medium">{formatChannelName(transaction.channel)}</p>
                </div>
                {transaction.transaction_id && (
                  <div>
                    <Label className="text-muted-foreground">Transaction ID</Label>
                    <p className="font-mono text-sm bg-muted p-2 rounded">
                      {transaction.transaction_id}
                    </p>
                  </div>
                )}
                {transaction.mobile_money_number && (
                  <div>
                    <Label className="text-muted-foreground">Mobile Money Number</Label>
                    <p className="font-medium">{transaction.mobile_money_number}</p>
                  </div>
                )}
                {transaction.bank_account_details && (
                  <div>
                    <Label className="text-muted-foreground">Bank Account</Label>
                    <p className="font-medium">
                      {transaction.bank_account_details.account_name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {transaction.bank_account_details.bank_name} - 
                      {transaction.bank_account_details.account_number}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Application Summary */}
          {applications.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Application Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <Label className="text-muted-foreground">Total Applied</Label>
                    <p className="text-lg font-semibold text-green-600">
                      {formatCurrency(transaction.applications_total || 0)}
                    </p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Remaining Balance</Label>
                    <p className="text-lg font-semibold text-blue-600">
                      {formatCurrency(transaction.remaining_balance || 0)}
                    </p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Application Status</Label>
                    <Badge variant={(transaction.remaining_balance || 0) > 0 ? 'secondary' : 'default'}>
                      {(transaction.remaining_balance || 0) > 0 ? 'Partially Applied' : 'Fully Applied'}
                    </Badge>
                  </div>
                </div>

                {transaction.related_documents && transaction.related_documents.length > 0 && (
                  <div>
                    <Label className="text-muted-foreground mb-2 block">Related Documents</Label>
                    <div className="space-y-2">
                      {transaction.related_documents.map((doc) => (
                        <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="font-medium">{doc.number}</p>
                              <p className="text-sm text-muted-foreground">
                                {doc.type.toUpperCase()} • {new Date(doc.date).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">{formatCurrency(doc.amount_applied)}</p>
                            <p className="text-sm text-muted-foreground">
                              of {formatCurrency(doc.amount)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {transaction.notes && (
            <Card>
              <CardHeader>
                <CardTitle>Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">{transaction.notes}</p>
              </CardContent>
            </Card>
          )}

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Transaction Metadata</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-muted-foreground">Transaction ID</Label>
                  <p className="font-mono">{transaction.id}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Currency</Label>
                  <p>{transaction.currency_code}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Created</Label>
                  <p>{new Date(transaction.created_at).toLocaleString()}</p>
                </div>
                {transaction.updated_at && (
                  <div>
                    <Label className="text-muted-foreground">Last Updated</Label>
                    <p>{new Date(transaction.updated_at).toLocaleString()}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
