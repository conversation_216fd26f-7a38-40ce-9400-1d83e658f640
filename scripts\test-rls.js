#!/usr/bin/env node

/**
 * RLS Policy Tester
 * Tests if RLS policies are working correctly for onboarding
 */

import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://kmejequnwwngmzwkszqs.supabase.co'
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImttZWplcXVud3duZ216d2tzenFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDk3ODQsImV4cCI6MjA2Mzc4NTc4NH0.ROGVGPdlDh_o1TJJJijM1BTubWUhUXUh5oZWxOKDdjw'

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function testRLSPolicies() {
  console.log('🔧 Testing RLS Policies for Onboarding...')
  console.log('=' .repeat(60))

  try {
    // Test 1: Check if we can connect to Supabase
    console.log('📡 Testing Supabase connection...')
    const { data: connectionTest, error: connectionError } = await supabase
      .from('organizations')
      .select('count')
      .limit(1)

    if (connectionError) {
      console.log('❌ Connection failed:', connectionError.message)
      return
    }
    console.log('✅ Supabase connection successful')

    // Test 2: Try to create a test user (this will fail without auth, which is expected)
    console.log('\n🔐 Testing authentication requirement...')
    const { data: orgTest, error: orgError } = await supabase
      .from('organizations')
      .insert({
        name: 'Test Organization',
        currency_code: 'UGX',
        timezone: 'Africa/Kampala'
      })
      .select()

    if (orgError) {
      if (orgError.message.includes('new row violates row-level security') || 
          orgError.message.includes('permission denied') ||
          orgError.code === '42501') {
        console.log('✅ RLS is working - unauthenticated users cannot create organizations')
        console.log('   Error:', orgError.message)
      } else {
        console.log('❌ Unexpected error:', orgError.message)
      }
    } else {
      console.log('❌ WARNING: Unauthenticated user was able to create organization!')
      console.log('   This indicates RLS policies may not be working correctly')
    }

    // Test 3: Check if the database function exists
    console.log('\n🔧 Testing database function...')
    const { data: functionTest, error: functionError } = await supabase
      .rpc('create_organization_with_profile', {
        org_name: 'Test Org',
        tin_number: null,
        business_reg_number: null,
        ura_tax_office: null,
        user_phone: null,
        user_role: 'owner'
      })

    if (functionError) {
      if (functionError.message.includes('function') && functionError.message.includes('does not exist')) {
        console.log('❌ Database function does not exist')
        console.log('   You may need to run the migration: 20241231_fix_onboarding_rls.sql')
      } else if (functionError.message.includes('permission denied') || 
                 functionError.message.includes('authentication required')) {
        console.log('✅ Database function exists but requires authentication (expected)')
      } else {
        console.log('❌ Unexpected function error:', functionError.message)
      }
    } else if (functionTest?.[0]?.success === false) {
      console.log('✅ Function properly rejects unauthenticated users')
      console.log('   Error:', functionTest[0].error_message)
    } else {
      console.log('❌ WARNING: Function allowed unauthenticated access!')
      console.log('   Result:', functionTest)
    }

    // Test 4: Check table permissions
    console.log('\n📋 Testing table access permissions...')
    
    const tables = ['organizations', 'profiles', 'accounts']
    
    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('count')
        .limit(1)

      if (error) {
        if (error.message.includes('permission denied')) {
          console.log(`✅ ${table}: Properly protected (no read access without auth)`)
        } else {
          console.log(`❌ ${table}: Unexpected error - ${error.message}`)
        }
      } else {
        console.log(`⚠️  ${table}: Readable without authentication (may be intentional)`)
      }
    }

    console.log('\n📊 Summary:')
    console.log('- If RLS is working correctly, you should see authentication errors')
    console.log('- If you see "permission denied" or "row-level security" errors, that\'s good!')
    console.log('- If operations succeed without authentication, RLS may need fixing')

  } catch (error) {
    console.error('💥 Test failed:', error.message)
  }
}

async function testWithAuth() {
  console.log('\n🔐 Testing with Authentication...')
  console.log('=' .repeat(60))

  // For this test, we would need actual user credentials
  // This is just a placeholder to show what authenticated testing would look like
  
  console.log('ℹ️  To test with authentication:')
  console.log('1. Sign up a test user in the application')
  console.log('2. Get their JWT token')
  console.log('3. Use supabase.auth.setSession() with the token')
  console.log('4. Then test organization creation')
  
  console.log('\nExample authenticated test:')
  console.log(`
// After authentication
const { data, error } = await supabase
  .from('organizations')
  .insert({
    name: 'My Company',
    currency_code: 'UGX',
    timezone: 'Africa/Kampala'
  })
  .select()

// This should work for authenticated users
`)
}

// Run the tests
console.log('🧪 Kaya Finance RLS Policy Test Suite')
console.log('Testing Date:', new Date().toISOString())
console.log('')

testRLSPolicies()
  .then(() => testWithAuth())
  .then(() => {
    console.log('\n✅ RLS testing completed')
    console.log('\n💡 Next Steps:')
    console.log('1. If you see authentication errors, RLS is working correctly')
    console.log('2. Try the onboarding flow with a real user account')
    console.log('3. Check browser console for detailed error messages')
    console.log('4. If issues persist, check Supabase dashboard for RLS policy status')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Test suite failed:', error.message)
    process.exit(1)
  })
