import { Bell, CheckCircle, DollarSign, Settings } from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/loading'

interface EmptyNotificationsProps {
  type: 'all' | 'unread' | 'financial' | 'system' | 'archived' | 'approval'
  isLoading?: boolean
  searchQuery?: string
}

export function EmptyNotifications({ type, isLoading, searchQuery }: EmptyNotificationsProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="sm" text="Loading notifications..." />
      </div>
    )
  }

  const getEmptyState = () => {
    switch (type) {
      case 'unread':
        return {
          icon: CheckCircle,
          title: 'All caught up!',
          description: 'You have no unread notifications.',
          iconColor: 'text-green-500'
        }
      case 'financial':
        return {
          icon: DollarSign,
          title: 'No financial notifications',
          description: 'No payment approvals, invoice updates, or financial alerts.',
          iconColor: 'text-blue-500'
        }
      case 'system':
        return {
          icon: Settings,
          title: 'No system notifications',
          description: 'No system updates, user invitations, or maintenance alerts.',
          iconColor: 'text-gray-500'
        }
      case 'archived':
        return {
          icon: Bell,
          title: 'No archived notifications',
          description: 'Archived notifications will appear here.',
          iconColor: 'text-gray-400'
        }
      case 'approval':
        return {
          icon: CheckCircle,
          title: 'No approval notifications',
          description: 'No pending approvals or approval updates.',
          iconColor: 'text-orange-500'
        }
      default:
        return {
          icon: Bell,
          title: searchQuery ? 'No matching notifications' : 'No notifications',
          description: searchQuery
            ? `No notifications found matching "${searchQuery}".`
            : 'You\'ll see important updates and alerts here.',
          iconColor: 'text-gray-400'
        }
    }
  }

  const { icon: Icon, title, description, iconColor } = getEmptyState()

  return (
    <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
      <Icon className={`h-12 w-12 mb-4 ${iconColor}`} />
      <h3 className="text-sm font-medium text-foreground mb-2">{title}</h3>
      <p className="text-xs text-muted-foreground max-w-xs">{description}</p>
    </div>
  )
}
