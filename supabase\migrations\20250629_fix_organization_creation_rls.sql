-- =====================================================
-- FIX ORGANIZATION CREATION RLS ISSUE
-- =====================================================
-- This migration fixes the RLS violation error when creating organizations
-- by changing the function from SECURITY DEFINER to SECURITY INVOKER
-- and ensuring proper RLS policies are in place.

-- =====================================================
-- STEP 1: ENSURE RLS POLICIES ARE CORRECT
-- =====================================================

-- Enable RLS on organizations table
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate them cleanly
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON organizations;
DROP POLICY IF EXISTS "Users can view their organization" ON organizations;
DROP POLICY IF EXISTS "Owners and admins can update their organization" ON organizations;

-- Create organization policies
CREATE POLICY "Authenticated users can create organizations" ON organizations
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Users can view their organization" ON organizations
    FOR SELECT USING (
        id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
    );

CREATE POLICY "Owners and admins can update their organization" ON organizations
    FOR UPDATE USING (
        id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND role IN ('owner', 'admin')
        )
    );

-- =====================================================
-- STEP 2: ENSURE PROFILES RLS POLICIES ARE CORRECT
-- =====================================================

-- Enable RLS on profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate them cleanly
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;

-- Create profile policies (no recursion)
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (id = auth.uid());

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (id = auth.uid());

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (id = auth.uid());

-- =====================================================
-- STEP 3: FIX THE ORGANIZATION CREATION FUNCTION
-- =====================================================

-- Drop the existing function
DROP FUNCTION IF EXISTS create_organization_with_profile(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT);

-- Recreate the function with SECURITY INVOKER to preserve auth context
CREATE OR REPLACE FUNCTION create_organization_with_profile(
    org_name TEXT,
    tin_number TEXT DEFAULT NULL,
    business_reg_number TEXT DEFAULT NULL,
    ura_tax_office TEXT DEFAULT NULL,
    user_phone TEXT DEFAULT NULL,
    user_role TEXT DEFAULT 'accountant',
    country_code TEXT DEFAULT 'UG',
    currency_code TEXT DEFAULT 'UGX'
)
RETURNS TABLE (
    organization_id UUID,
    profile_id UUID,
    success BOOLEAN,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY INVOKER  -- This preserves the auth.uid() context
AS $$
DECLARE
    new_org_id UUID;
    current_user_id UUID;
    selected_country_id UUID;
    selected_timezone TEXT;
BEGIN
    -- Get the current user ID
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, FALSE, 'User not authenticated'::TEXT;
        RETURN;
    END IF;

    -- Get country information
    SELECT id, default_timezone INTO selected_country_id, selected_timezone
    FROM countries 
    WHERE iso_code = country_code AND is_active = true;
    
    -- If country not found, default to Uganda
    IF selected_country_id IS NULL THEN
        SELECT id, default_timezone INTO selected_country_id, selected_timezone
        FROM countries 
        WHERE iso_code = 'UG' AND is_active = true;
    END IF;
    
    -- If still no country found, use defaults
    IF selected_country_id IS NULL THEN
        selected_timezone := 'Africa/Kampala';
    END IF;

    BEGIN
        -- Create the organization
        INSERT INTO organizations (
            name,
            tin_number,
            business_reg_number,
            ura_tax_office,
            currency_code,
            timezone,
            country_id,
            fiscal_year_start,
            fiscal_year_end
        ) VALUES (
            org_name,
            tin_number,
            business_reg_number,
            ura_tax_office,
            currency_code,
            COALESCE(selected_timezone, 'Africa/Kampala'),
            selected_country_id,
            '01-01',
            '12-31'
        ) RETURNING id INTO new_org_id;

        -- Create the user profile
        INSERT INTO profiles (
            id,
            email,
            phone,
            role,
            org_id,
            onboarding_completed_at
        ) VALUES (
            current_user_id,
            (SELECT email FROM auth.users WHERE id = current_user_id),
            user_phone,
            user_role::user_role,
            new_org_id,
            NOW()
        );

        -- Return success
        RETURN QUERY SELECT new_org_id, current_user_id, TRUE, NULL::TEXT;

    EXCEPTION WHEN OTHERS THEN
        -- Return error
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, FALSE, SQLERRM::TEXT;
    END;
END;
$$;

-- =====================================================
-- STEP 4: VERIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Organization creation RLS fix completed';
    RAISE NOTICE '   - RLS policies updated for organizations and profiles';
    RAISE NOTICE '   - Function changed to SECURITY INVOKER';
    RAISE NOTICE '   - Auth context preserved for proper RLS evaluation';
END $$;
