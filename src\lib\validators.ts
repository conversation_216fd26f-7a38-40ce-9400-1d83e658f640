// Phone number validation and formatting utilities
export function formatPhoneNumber(phone: string): string {
  if (!phone || phone.trim() === '') return ''

  // Remove all spaces and special characters except +
  const cleaned = phone.replace(/[\s\-()]/g, '')

  // If it starts with 7, 8, or 9 (common Ugandan mobile prefixes) and is 9 digits, add +256
  if (/^[789][0-9]{8}$/.test(cleaned)) {
    return `+256${cleaned}`
  }

  // If it starts with 0 and is 10 digits, replace 0 with +256
  if (/^0[789][0-9]{8}$/.test(cleaned)) {
    return `+256${cleaned.substring(1)}`
  }

  // If it already has +256, ensure it's properly formatted
  if (cleaned.startsWith('+256') && /^\+256[789][0-9]{8}$/.test(cleaned)) {
    return cleaned
  }

  // Return as-is if it doesn't match expected patterns
  return phone
}

export function validatePhoneNumber(phone: string, required: boolean = false): { isValid: boolean; message?: string } {
  if (!phone || phone.trim() === '') {
    if (required) {
      return { isValid: false, message: 'Phone number is required' }
    }
    return { isValid: true }
  }

  const formatted = formatPhoneNumber(phone)
  const phoneRegex = /^\+256[789][0-9]{8}$/

  if (!phoneRegex.test(formatted)) {
    return {
      isValid: false,
      message: 'Please enter a valid Ugandan phone number (e.g., ********* or +256*********)'
    }
  }

  return { isValid: true, formatted }
}

// TIN validation utilities
export function validateTinNumber(tin: string): { isValid: boolean; message?: string } {
  if (!tin || tin.trim() === '') {
    return { isValid: true } // TIN is optional
  }

  // Remove spaces and hyphens
  const cleaned = tin.replace(/[\s-]/g, '')
  const tinRegex = /^[0-9]{10}$/

  if (!tinRegex.test(cleaned)) {
    return {
      isValid: false,
      message: 'TIN must be exactly 10 digits (e.g., 1000123456)'
    }
  }

  return { isValid: true, formatted: cleaned }
}

// Email validation utilities
export function validateEmail(email: string): { isValid: boolean; message?: string } {
  if (!email || email.trim() === '') {
    return { isValid: true } // Email is optional
  }

  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/

  if (!emailRegex.test(email.trim())) {
    return {
      isValid: false,
      message: 'Please enter a valid email address (e.g., <EMAIL>)'
    }
  }

  return { isValid: true }
}

// Helper function to get validation message for phone numbers
export function getPhoneNumberHelper(): string {
  return 'Enter phone number without leading 0 (e.g., *********). We\'ll automatically add +256 prefix.'
}

// Helper function to get validation message for TIN numbers
export function getTinNumberHelper(): string {
  return 'Optional. Tax Identification Number from URA (10 digits, e.g., 1000123456)'
}
