-- =====================================================
-- CREATE VALIDATE ACCOUNT MAPPINGS FUNCTION
-- =====================================================
-- This migration creates the missing validate_account_mappings function
-- Date: 2024-12-31
-- Purpose: Add function to validate account mappings configuration

-- =====================================================
-- CREATE VALIDATE ACCOUNT MAPPINGS FUNCTION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating validate_account_mappings function...';
END $$;

-- Function to validate account mappings configuration
CREATE OR REPLACE FUNCTION validate_account_mappings(org_id_param UUID)
RETURNS TABLE (
    mapping_type TEXT,
    account_name TEXT,
    account_code TEXT,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    required_mappings TEXT[] := ARRAY[
        'accounts_receivable',
        'accounts_payable', 
        'sales_revenue',
        'vat_payable',
        'vat_input'
    ];
    mapping_type_item TEXT;
    mapping_record RECORD;
BEGIN
    -- Check each required mapping type
    FOREACH mapping_type_item IN ARRAY required_mappings
    LOOP
        -- Look for existing mapping
        SELECT 
            am.mapping_type,
            a.name as account_name,
            a.code as account_code
        INTO mapping_record
        FROM account_mappings am
        JOIN accounts a ON a.id = am.account_id
        WHERE am.org_id = org_id_param 
        AND am.mapping_type = mapping_type_item
        AND am.is_default = true
        AND a.is_active = true
        LIMIT 1;
        
        IF FOUND THEN
            -- Mapping exists and is valid
            RETURN QUERY SELECT 
                mapping_record.mapping_type,
                mapping_record.account_name,
                mapping_record.account_code,
                'configured'::TEXT;
        ELSE
            -- Mapping is missing
            RETURN QUERY SELECT 
                mapping_type_item,
                NULL::TEXT,
                NULL::TEXT,
                'missing'::TEXT;
        END IF;
    END LOOP;
END;
$$;

-- =====================================================
-- CREATE HELPER FUNCTION FOR ACCOUNT MAPPINGS SETUP
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating setup_default_account_mappings function...';
END $$;

-- Function to help set up default account mappings
CREATE OR REPLACE FUNCTION setup_default_account_mappings(org_id_param UUID)
RETURNS TABLE (
    mapping_type TEXT,
    suggested_account_id UUID,
    suggested_account_name TEXT,
    suggested_account_code TEXT,
    account_type TEXT,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    ar_account RECORD;
    ap_account RECORD;
    revenue_account RECORD;
    vat_payable_account RECORD;
    vat_input_account RECORD;
BEGIN
    -- Find suitable accounts for each mapping type
    
    -- Accounts Receivable (Asset account)
    SELECT id, name, code, type INTO ar_account
    FROM accounts 
    WHERE org_id = org_id_param 
    AND type = 'asset' 
    AND is_active = true
    AND (name ILIKE '%receivable%' OR name ILIKE '%debtors%' OR code ILIKE '%AR%' OR code ILIKE '%1200%')
    ORDER BY 
        CASE 
            WHEN name ILIKE '%accounts receivable%' THEN 1
            WHEN name ILIKE '%receivable%' THEN 2
            WHEN name ILIKE '%debtors%' THEN 3
            ELSE 4
        END
    LIMIT 1;
    
    IF FOUND THEN
        RETURN QUERY SELECT 
            'accounts_receivable'::TEXT,
            ar_account.id,
            ar_account.name,
            ar_account.code,
            ar_account.type,
            'suggested'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            'accounts_receivable'::TEXT,
            NULL::UUID,
            'No suitable asset account found'::TEXT,
            NULL::TEXT,
            'asset'::TEXT,
            'missing'::TEXT;
    END IF;
    
    -- Accounts Payable (Liability account)
    SELECT id, name, code, type INTO ap_account
    FROM accounts 
    WHERE org_id = org_id_param 
    AND type = 'liability' 
    AND is_active = true
    AND (name ILIKE '%payable%' OR name ILIKE '%creditors%' OR code ILIKE '%AP%' OR code ILIKE '%2100%')
    ORDER BY 
        CASE 
            WHEN name ILIKE '%accounts payable%' THEN 1
            WHEN name ILIKE '%payable%' THEN 2
            WHEN name ILIKE '%creditors%' THEN 3
            ELSE 4
        END
    LIMIT 1;
    
    IF FOUND THEN
        RETURN QUERY SELECT 
            'accounts_payable'::TEXT,
            ap_account.id,
            ap_account.name,
            ap_account.code,
            ap_account.type,
            'suggested'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            'accounts_payable'::TEXT,
            NULL::UUID,
            'No suitable liability account found'::TEXT,
            NULL::TEXT,
            'liability'::TEXT,
            'missing'::TEXT;
    END IF;
    
    -- Sales Revenue (Income account)
    SELECT id, name, code, type INTO revenue_account
    FROM accounts 
    WHERE org_id = org_id_param 
    AND type = 'income' 
    AND is_active = true
    AND (name ILIKE '%sales%' OR name ILIKE '%revenue%' OR name ILIKE '%income%' OR code ILIKE '%4%')
    ORDER BY 
        CASE 
            WHEN name ILIKE '%sales revenue%' THEN 1
            WHEN name ILIKE '%sales%' THEN 2
            WHEN name ILIKE '%revenue%' THEN 3
            ELSE 4
        END
    LIMIT 1;
    
    IF FOUND THEN
        RETURN QUERY SELECT 
            'sales_revenue'::TEXT,
            revenue_account.id,
            revenue_account.name,
            revenue_account.code,
            revenue_account.type,
            'suggested'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            'sales_revenue'::TEXT,
            NULL::UUID,
            'No suitable income account found'::TEXT,
            NULL::TEXT,
            'income'::TEXT,
            'missing'::TEXT;
    END IF;
    
    -- VAT Payable (Liability account)
    SELECT id, name, code, type INTO vat_payable_account
    FROM accounts 
    WHERE org_id = org_id_param 
    AND type = 'liability' 
    AND is_active = true
    AND (name ILIKE '%vat%' OR name ILIKE '%tax%' OR code ILIKE '%VAT%' OR code ILIKE '%2200%')
    AND (name ILIKE '%payable%' OR name ILIKE '%output%' OR name NOT ILIKE '%input%')
    ORDER BY 
        CASE 
            WHEN name ILIKE '%vat payable%' THEN 1
            WHEN name ILIKE '%vat output%' THEN 2
            WHEN name ILIKE '%vat%' THEN 3
            ELSE 4
        END
    LIMIT 1;
    
    IF FOUND THEN
        RETURN QUERY SELECT 
            'vat_payable'::TEXT,
            vat_payable_account.id,
            vat_payable_account.name,
            vat_payable_account.code,
            vat_payable_account.type,
            'suggested'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            'vat_payable'::TEXT,
            NULL::UUID,
            'No suitable VAT liability account found'::TEXT,
            NULL::TEXT,
            'liability'::TEXT,
            'missing'::TEXT;
    END IF;
    
    -- VAT Input (Asset account)
    SELECT id, name, code, type INTO vat_input_account
    FROM accounts 
    WHERE org_id = org_id_param 
    AND type = 'asset' 
    AND is_active = true
    AND (name ILIKE '%vat%' OR name ILIKE '%tax%' OR code ILIKE '%VAT%' OR code ILIKE '%1300%')
    AND (name ILIKE '%input%' OR name ILIKE '%receivable%')
    ORDER BY 
        CASE 
            WHEN name ILIKE '%vat input%' THEN 1
            WHEN name ILIKE '%vat receivable%' THEN 2
            WHEN name ILIKE '%vat%' THEN 3
            ELSE 4
        END
    LIMIT 1;
    
    IF FOUND THEN
        RETURN QUERY SELECT 
            'vat_input'::TEXT,
            vat_input_account.id,
            vat_input_account.name,
            vat_input_account.code,
            vat_input_account.type,
            'suggested'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            'vat_input'::TEXT,
            NULL::UUID,
            'No suitable VAT asset account found'::TEXT,
            NULL::TEXT,
            'asset'::TEXT,
            'missing'::TEXT;
    END IF;
END;
$$;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ ACCOUNT MAPPING VALIDATION FUNCTIONS CREATED!';
    RAISE NOTICE '==============================================';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 FUNCTIONS AVAILABLE:';
    RAISE NOTICE '  • validate_account_mappings(org_id) - Validates current mappings';
    RAISE NOTICE '  • setup_default_account_mappings(org_id) - Suggests default accounts';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Account Mappings UI now fully functional!';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================
