// Enhanced Backup Encryption Key Management
// Provides secure key generation, rotation, storage, and recovery

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'

export interface EncryptionKey {
  id: string
  orgId: string
  keyData: string
  algorithm: string
  version: number
  isActive: boolean
  createdAt: string
  expiresAt?: string
  rotatedAt?: string
  rotatedBy?: string
}

export interface EncryptionResult {
  encryptedData: string
  keyId: string
  iv: string
  algorithm: string
  version: number
}

export interface DecryptionContext {
  encryptedData: string
  keyId: string
  iv: string
  algorithm: string
  version: number
}

/**
 * Enhanced Backup Encryption Manager
 * Handles secure key lifecycle, rotation, and recovery
 */
export class BackupEncryptionManager {
  private static readonly KEY_ROTATION_DAYS = 90
  private static readonly KEY_ALGORITHM = 'AES-GCM'
  private static readonly KEY_LENGTH = 256

  /**
   * Encrypt backup data with current active key
   */
  static async encryptBackupData(data: string, orgId: string): Promise<EncryptionResult> {
    try {
      // Get or create current encryption key
      const keyInfo = await this.getCurrentEncryptionKey(orgId)
      
      // Check if key needs rotation
      if (await this.shouldRotateKey(keyInfo)) {
        console.log(`Key rotation needed for org ${orgId}`)
        const newKeyInfo = await this.rotateEncryptionKey(orgId)
        return await this.performEncryption(data, newKeyInfo)
      }

      return await this.performEncryption(data, keyInfo)
    } catch (error) {
      await auditLogger.logActivity({
        entity_type: 'backup_encryption',
        entity_id: orgId,
        action: 'encryption_failed',
        description: 'Backup data encryption failed',
        severity: 'error',
        category: 'security',
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      })
      throw error
    }
  }

  /**
   * Decrypt backup data using specified key
   */
  static async decryptBackupData(context: DecryptionContext): Promise<string> {
    try {
      // Get the specific key used for encryption
      const { data: keyRecord, error } = await supabase
        .from('backup_encryption_keys')
        .select('*')
        .eq('id', context.keyId)
        .single()

      if (error || !keyRecord) {
        throw new Error('Encryption key not found')
      }

      // Import the key
      const cryptoKey = await this.importKey(keyRecord.key_data)

      // Decrypt the data
      const decryptedData = await this.performDecryption(context, cryptoKey)

      await auditLogger.logActivity({
        entity_type: 'backup_encryption',
        entity_id: keyRecord.org_id,
        action: 'decryption_success',
        description: 'Backup data decrypted successfully',
        severity: 'info',
        category: 'security',
        metadata: { key_id: context.keyId, algorithm: context.algorithm }
      })

      return decryptedData
    } catch (error) {
      await auditLogger.logActivity({
        entity_type: 'backup_encryption',
        entity_id: context.keyId,
        action: 'decryption_failed',
        description: 'Backup data decryption failed',
        severity: 'error',
        category: 'security',
        metadata: { 
          key_id: context.keyId,
          error: error instanceof Error ? error.message : 'Unknown error' 
        }
      })
      throw error
    }
  }

  /**
   * Rotate encryption key for organization
   */
  static async rotateEncryptionKey(orgId: string, rotatedBy?: string): Promise<EncryptionKey> {
    try {
      console.log(`Starting key rotation for org ${orgId}`)

      // Deactivate current key
      await supabase
        .from('backup_encryption_keys')
        .update({ 
          is_active: false,
          rotated_at: new Date().toISOString(),
          rotated_by: rotatedBy
        })
        .eq('org_id', orgId)
        .eq('is_active', true)

      // Generate new key
      const newKey = await this.generateNewEncryptionKey(orgId)

      await auditLogger.logActivity({
        entity_type: 'backup_encryption',
        entity_id: orgId,
        action: 'key_rotated',
        description: 'Encryption key rotated successfully',
        severity: 'info',
        category: 'security',
        metadata: { 
          new_key_id: newKey.id,
          rotated_by: rotatedBy || 'system'
        }
      })

      console.log(`Key rotation completed for org ${orgId}`)
      return newKey
    } catch (error) {
      await auditLogger.logActivity({
        entity_type: 'backup_encryption',
        entity_id: orgId,
        action: 'key_rotation_failed',
        description: 'Encryption key rotation failed',
        severity: 'error',
        category: 'security',
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      })
      throw error
    }
  }

  /**
   * Get current active encryption key for organization
   */
  private static async getCurrentEncryptionKey(orgId: string): Promise<EncryptionKey> {
    const { data: existingKey, error } = await supabase
      .from('backup_encryption_keys')
      .select('*')
      .eq('org_id', orgId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (error || !existingKey) {
      // Generate new key if none exists
      return await this.generateNewEncryptionKey(orgId)
    }

    return existingKey
  }

  /**
   * Generate new encryption key
   */
  private static async generateNewEncryptionKey(orgId: string): Promise<EncryptionKey> {
    // Generate crypto key
    const cryptoKey = await crypto.subtle.generateKey(
      { name: this.KEY_ALGORITHM, length: this.KEY_LENGTH },
      true,
      ['encrypt', 'decrypt']
    )

    // Export key data
    const keyData = await this.exportKey(cryptoKey)

    // Get current version number
    const { data: versionData } = await supabase
      .from('backup_encryption_keys')
      .select('version')
      .eq('org_id', orgId)
      .order('version', { ascending: false })
      .limit(1)
      .single()

    const version = (versionData?.version || 0) + 1

    // Store in database
    const { data: newKey, error } = await supabase
      .from('backup_encryption_keys')
      .insert({
        org_id: orgId,
        key_data: keyData,
        algorithm: `${this.KEY_ALGORITHM}-${this.KEY_LENGTH}`,
        version,
        is_active: true,
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + this.KEY_ROTATION_DAYS * 24 * 60 * 60 * 1000).toISOString()
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to store encryption key: ${error.message}`)
    }

    return newKey
  }

  /**
   * Check if key should be rotated
   */
  private static async shouldRotateKey(key: EncryptionKey): Promise<boolean> {
    if (!key.expires_at) {
      return false
    }

    const expiryDate = new Date(key.expires_at)
    const now = new Date()
    
    // Rotate if key expires within 7 days
    const rotationThreshold = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    
    return expiryDate <= rotationThreshold
  }

  /**
   * Perform encryption with given key
   */
  private static async performEncryption(data: string, keyInfo: EncryptionKey): Promise<EncryptionResult> {
    // Import the key
    const cryptoKey = await this.importKey(keyInfo.key_data)

    // Generate random IV
    const iv = crypto.getRandomValues(new Uint8Array(12))

    // Encrypt data
    const encodedData = new TextEncoder().encode(data)
    const encryptedBuffer = await crypto.subtle.encrypt(
      { name: this.KEY_ALGORITHM, iv },
      cryptoKey,
      encodedData
    )

    return {
      encryptedData: btoa(String.fromCharCode(...new Uint8Array(encryptedBuffer))),
      keyId: keyInfo.id,
      iv: btoa(String.fromCharCode(...iv)),
      algorithm: keyInfo.algorithm,
      version: keyInfo.version
    }
  }

  /**
   * Perform decryption with given key
   */
  private static async performDecryption(context: DecryptionContext, cryptoKey: CryptoKey): Promise<string> {
    // Decode IV and encrypted data
    const iv = Uint8Array.from(atob(context.iv), c => c.charCodeAt(0))
    const encryptedBuffer = Uint8Array.from(atob(context.encryptedData), c => c.charCodeAt(0))

    // Decrypt data
    const decryptedBuffer = await crypto.subtle.decrypt(
      { name: this.KEY_ALGORITHM, iv },
      cryptoKey,
      encryptedBuffer
    )

    return new TextDecoder().decode(decryptedBuffer)
  }

  /**
   * Export crypto key to storable format
   */
  private static async exportKey(cryptoKey: CryptoKey): Promise<string> {
    const exportedKey = await crypto.subtle.exportKey('raw', cryptoKey)
    return btoa(String.fromCharCode(...new Uint8Array(exportedKey)))
  }

  /**
   * Import key from stored format
   */
  private static async importKey(keyData: string): Promise<CryptoKey> {
    const keyBuffer = Uint8Array.from(atob(keyData), c => c.charCodeAt(0))
    return await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: this.KEY_ALGORITHM },
      true,
      ['encrypt', 'decrypt']
    )
  }

  /**
   * Get key recovery information for organization
   */
  static async getKeyRecoveryInfo(orgId: string): Promise<{
    activeKey: EncryptionKey | null
    historicalKeys: EncryptionKey[]
    rotationSchedule: Date | null
  }> {
    try {
      // Get active key
      const { data: activeKey } = await supabase
        .from('backup_encryption_keys')
        .select('*')
        .eq('org_id', orgId)
        .eq('is_active', true)
        .single()

      // Get historical keys
      const { data: historicalKeys } = await supabase
        .from('backup_encryption_keys')
        .select('*')
        .eq('org_id', orgId)
        .eq('is_active', false)
        .order('created_at', { ascending: false })

      // Calculate next rotation date
      let rotationSchedule: Date | null = null
      if (activeKey?.expires_at) {
        rotationSchedule = new Date(activeKey.expires_at)
      }

      return {
        activeKey: activeKey || null,
        historicalKeys: historicalKeys || [],
        rotationSchedule
      }
    } catch (error) {
      console.error('Failed to get key recovery info:', error)
      return {
        activeKey: null,
        historicalKeys: [],
        rotationSchedule: null
      }
    }
  }

  /**
   * Schedule automatic key rotation
   */
  static async scheduleKeyRotation(orgId: string): Promise<void> {
    // In a real implementation, this would integrate with a job scheduler
    // For now, we'll just log the scheduling
    console.log(`Scheduling key rotation for org ${orgId}`)
    
    await auditLogger.logActivity({
      entity_type: 'backup_encryption',
      entity_id: orgId,
      action: 'key_rotation_scheduled',
      description: 'Automatic key rotation scheduled',
      severity: 'info',
      category: 'security',
      metadata: { 
        rotation_days: this.KEY_ROTATION_DAYS,
        scheduled_at: new Date().toISOString()
      }
    })
  }
}
