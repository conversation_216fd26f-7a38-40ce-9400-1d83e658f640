
import React, { useState, useEffect, useCallback } from 'react'
import { Plus, Users, UserCheck, UserX, Edit, Trash2, Mail, Clock, CheckCircle, XCircle, RotateCcw, Copy, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/hooks/useAuthHook'
import type { Profile, UserRole } from '@/types/database'
import { useForm } from 'react-hook-form'
import { createUserInvitation, getOrganizationInvitations, cancelInvitation, resendInvitation, copyInvitationUrl, generateInvitationUrl, type UserInvitation } from '@/lib/invitationService'
import { validatePhoneNumber } from '@/lib/validators'

interface UserFormData {
  email: string
  role: UserRole
  phone?: string
}

export default function UserManagement() {
  const [users, setUsers] = useState<Profile[]>([])
  const [invitations, setInvitations] = useState<UserInvitation[]>([])
  const [loading, setLoading] = useState(true)
  const [invitationsLoading, setInvitationsLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [inviteLoading, setInviteLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('users')
  const { toast } = useToast()
  const { profile } = useAuth()

  const { register, handleSubmit, setValue, reset, formState: { errors } } = useForm<UserFormData>()

  const fetchUsers = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('Error fetching users:', error)
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, toast])

  const fetchInvitations = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      const invitationData = await getOrganizationInvitations(profile.org_id)
      setInvitations(invitationData)
    } catch (error) {
      console.error('Error fetching invitations:', error)
      toast({
        title: "Error",
        description: "Failed to load invitations",
        variant: "destructive"
      })
    } finally {
      setInvitationsLoading(false)
    }
  }, [profile?.org_id, toast])

  useEffect(() => {
    if (profile?.role === 'admin') {
      fetchUsers()
      fetchInvitations()
    }
  }, [profile, fetchUsers, fetchInvitations])

  const onSubmit = async (data: UserFormData) => {
    if (!profile?.org_id || !profile?.email) return

    // Validate phone number if provided
    if (data.phone) {
      const phoneValidation = validatePhoneNumber(data.phone)
      if (!phoneValidation.isValid) {
        toast({
          title: "Error",
          description: phoneValidation.message,
          variant: "destructive"
        })
        return
      }
    }

    setInviteLoading(true)
    try {
      // Get organization name
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', profile.org_id)
        .single()

      if (orgError) throw orgError

      // Create and send invitation
      const result = await createUserInvitation({
        email: data.email,
        role: data.role,
        phone: data.phone,
        orgId: profile.org_id,
        inviterName: profile.email, // You might want to use full_name if available
        organizationName: orgData.name
      })

      if (result.success) {
        toast({
          title: "Success",
          description: "User invitation sent successfully"
        })

        setIsDialogOpen(false)
        reset()
        fetchInvitations() // Refresh invitations list
        setActiveTab('invitations') // Switch to invitations tab
      } else {
        throw new Error(result.error || 'Failed to send invitation')
      }
    } catch (error) {
      console.error('Error inviting user:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send user invitation",
        variant: "destructive"
      })
    } finally {
      setInviteLoading(false)
    }
  }

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      const result = await cancelInvitation(invitationId)
      if (result.success) {
        toast({
          title: "Success",
          description: "Invitation cancelled successfully"
        })
        fetchInvitations()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel invitation",
        variant: "destructive"
      })
    }
  }

  const handleResendInvitation = async (invitationId: string) => {
    if (!profile?.email) return

    try {
      // Get organization name
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', profile.org_id)
        .single()

      if (orgError) throw orgError

      const result = await resendInvitation(
        invitationId,
        profile.email,
        orgData.name
      )

      if (result.success) {
        toast({
          title: "Success",
          description: "Invitation resent successfully"
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to resend invitation",
        variant: "destructive"
      })
    }
  }

  const handleCopyInvitationUrl = async (token: string) => {
    try {
      const success = await copyInvitationUrl(token)
      if (success) {
        toast({
          title: "Success",
          description: "Invitation link copied to clipboard"
        })
      } else {
        // Fallback: show the URL in a prompt
        const url = generateInvitationUrl(token)
        prompt('Copy this invitation URL:', url)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy invitation link",
        variant: "destructive"
      })
    }
  }

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'accountant': return 'bg-blue-100 text-blue-800'
      case 'viewer': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getInvitationStatusBadge = (status: string, expiresAt: string) => {
    const isExpired = new Date(expiresAt) < new Date()

    switch (status) {
      case 'pending':
        return isExpired ? (
          <Badge className="bg-orange-100 text-orange-800">
            <Clock className="h-3 w-3 mr-1" />
            Expired
          </Badge>
        ) : (
          <Badge className="bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        )
      case 'accepted':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Accepted
          </Badge>
        )
      case 'cancelled':
        return (
          <Badge className="bg-gray-100 text-gray-800">
            <XCircle className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        )
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800">
            {status}
          </Badge>
        )
    }
  }

  if (profile?.role !== 'admin') {
    return (
      <div className="p-6">
        <div className="text-center py-8">
          <UserX className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-600">You need admin privileges to access user management.</p>
        </div>
      </div>
    )
  }

  if (loading && invitationsLoading) {
    return <div className="p-6">Loading...</div>
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            User Management
          </h1>
          <p className="text-gray-600">Manage organization users and their roles</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Invite User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Invite New User</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  {...register('email', { required: 'Email is required' })}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="role">Role</Label>
                <Select onValueChange={(value) => setValue('role', value as UserRole)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="accountant">Accountant</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="phone">Phone Number (Optional)</Label>
                <Input
                  id="phone"
                  type="tel"
                  {...register('phone')}
                  placeholder="*********"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Enter phone number without leading 0. We'll automatically add +256 prefix.
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800">
                <div className="flex items-start gap-2">
                  <Mail className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Email Service Status</p>
                    <p className="text-blue-700">
                      Invitations will be created successfully. If email service is not configured,
                      you can copy the invitation link manually from the Invitations tab.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={inviteLoading}>
                  {inviteLoading ? 'Creating Invitation...' : 'Create Invitation'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">
            Active Users ({users.length})
          </TabsTrigger>
          <TabsTrigger value="invitations">
            Invitations ({invitations.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>Active Users</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">Loading users...</div>
              ) : users.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No users found</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Phone</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Joined</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.email}</TableCell>
                        <TableCell>
                          <Badge className={getRoleBadgeColor(user.role)}>
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>{user.phone || '-'}</TableCell>
                        <TableCell>
                          <Badge className="bg-green-100 text-green-800">
                            <UserCheck className="h-3 w-3 mr-1" />
                            Active
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(user.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                            {user.id !== profile?.id && (
                              <Button size="sm" variant="outline" className="text-red-600">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invitations">
          <Card>
            <CardHeader>
              <CardTitle>Pending Invitations</CardTitle>
            </CardHeader>
            <CardContent>
              {invitationsLoading ? (
                <div className="text-center py-8">Loading invitations...</div>
              ) : invitations.length === 0 ? (
                <div className="text-center py-8">
                  <Mail className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No invitations found</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Click "Invite User" to send your first invitation
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Invited</TableHead>
                      <TableHead>Expires</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invitations.map((invitation) => (
                      <TableRow key={invitation.id}>
                        <TableCell className="font-medium">{invitation.email}</TableCell>
                        <TableCell>
                          <Badge className={getRoleBadgeColor(invitation.role)}>
                            {invitation.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {getInvitationStatusBadge(invitation.status, invitation.expires_at)}
                        </TableCell>
                        <TableCell>
                          {new Date(invitation.invited_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          {new Date(invitation.expires_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {invitation.status === 'pending' && (
                              <>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleCopyInvitationUrl(invitation.token)}
                                  title="Copy invitation link"
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleResendInvitation(invitation.id)}
                                  title="Resend invitation email"
                                >
                                  <RotateCcw className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-red-600"
                                  onClick={() => handleCancelInvitation(invitation.id)}
                                  title="Cancel invitation"
                                >
                                  <XCircle className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
