-- =====================================================
-- INVENTORY MANAGEMENT - PERFORMANCE INDEXES
-- =====================================================
-- Date: 2025-01-16
-- Purpose: Create performance indexes for inventory tables
-- Dependencies: 20250116_01_inventory_management_foundation.sql
-- Rollback: See 20250116_03_inventory_indexes_rollback.sql

-- =====================================================
-- PRODUCT INDEXES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📊 Creating performance indexes for inventory tables...';
END $$;

-- Core product lookup indexes
CREATE INDEX IF NOT EXISTS idx_products_org_sku ON products(org_id, sku);
CREATE INDEX IF NOT EXISTS idx_products_org_name ON products(org_id, name);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id) WHERE category_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode) WHERE barcode IS NOT NULL;

-- Product status indexes
CREATE INDEX IF NOT EXISTS idx_products_active ON products(org_id, is_active);
CREATE INDEX IF NOT EXISTS idx_products_sellable ON products(org_id, is_sellable) WHERE is_sellable = true;
CREATE INDEX IF NOT EXISTS idx_products_purchasable ON products(org_id, is_purchasable) WHERE is_purchasable = true;

-- Product search and filtering
CREATE INDEX IF NOT EXISTS idx_products_name_search ON products USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_products_description_search ON products USING gin(to_tsvector('english', description)) WHERE description IS NOT NULL;

-- =====================================================
-- PRODUCT CATEGORIES INDEXES
-- =====================================================

-- Hierarchical structure indexes
CREATE INDEX IF NOT EXISTS idx_product_categories_parent ON product_categories(parent_id) WHERE parent_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_product_categories_org ON product_categories(org_id, is_active);
CREATE INDEX IF NOT EXISTS idx_product_categories_sort ON product_categories(parent_id, sort_order);

-- Category lookup indexes
CREATE INDEX IF NOT EXISTS idx_product_categories_name ON product_categories(org_id, name);
CREATE INDEX IF NOT EXISTS idx_product_categories_code ON product_categories(org_id, code) WHERE code IS NOT NULL;

-- =====================================================
-- INVENTORY LOCATIONS INDEXES
-- =====================================================

-- Location lookup indexes
CREATE INDEX IF NOT EXISTS idx_inventory_locations_org ON inventory_locations(org_id, is_active);
CREATE INDEX IF NOT EXISTS idx_inventory_locations_default ON inventory_locations(org_id, is_default) WHERE is_default = true;
CREATE INDEX IF NOT EXISTS idx_inventory_locations_code ON inventory_locations(org_id, code);
CREATE INDEX IF NOT EXISTS idx_inventory_locations_name ON inventory_locations(org_id, name);

-- =====================================================
-- STOCK LEVELS INDEXES
-- =====================================================

-- Core stock level indexes
CREATE INDEX IF NOT EXISTS idx_stock_levels_product ON stock_levels(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_levels_location ON stock_levels(location_id);
CREATE INDEX IF NOT EXISTS idx_stock_levels_org_product ON stock_levels(org_id, product_id);

-- Stock availability indexes
CREATE INDEX IF NOT EXISTS idx_stock_levels_available ON stock_levels(org_id, quantity_available) WHERE quantity_available > 0;
CREATE INDEX IF NOT EXISTS idx_stock_levels_on_hand ON stock_levels(org_id, quantity_on_hand) WHERE quantity_on_hand > 0;
CREATE INDEX IF NOT EXISTS idx_stock_levels_reserved ON stock_levels(org_id, quantity_reserved) WHERE quantity_reserved > 0;

-- Low stock monitoring
CREATE INDEX IF NOT EXISTS idx_stock_levels_low_stock ON stock_levels(org_id, product_id, quantity_available);

-- Cost tracking indexes
CREATE INDEX IF NOT EXISTS idx_stock_levels_avg_cost ON stock_levels(org_id, average_cost) WHERE average_cost > 0;
CREATE INDEX IF NOT EXISTS idx_stock_levels_last_updated ON stock_levels(last_updated);

-- =====================================================
-- INVENTORY TRANSACTIONS INDEXES
-- =====================================================

-- Core transaction indexes
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product ON inventory_transactions(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_location ON inventory_transactions(location_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_org ON inventory_transactions(org_id);

-- Transaction date and type indexes
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_date ON inventory_transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_type ON inventory_transactions(org_id, transaction_type);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_org_date ON inventory_transactions(org_id, transaction_date);

-- Reference tracking indexes
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_reference ON inventory_transactions(reference_type, reference_id) WHERE reference_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_ref_number ON inventory_transactions(reference_number) WHERE reference_number IS NOT NULL;

-- Audit and reporting indexes
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_created_by ON inventory_transactions(created_by) WHERE created_by IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_batch ON inventory_transactions(batch_number) WHERE batch_number IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_expiry ON inventory_transactions(expiry_date) WHERE expiry_date IS NOT NULL;

-- Cost analysis indexes
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_cost ON inventory_transactions(org_id, unit_cost) WHERE unit_cost IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_total_cost ON inventory_transactions(org_id, total_cost) WHERE total_cost IS NOT NULL;

-- =====================================================
-- LINE ITEMS INDEXES (for product references)
-- =====================================================

-- Invoice line items with products
CREATE INDEX IF NOT EXISTS idx_invoice_lines_product_id ON invoice_lines(product_id) WHERE product_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_invoice_lines_product_invoice ON invoice_lines(invoice_id, product_id) WHERE product_id IS NOT NULL;

-- Bill line items with products
CREATE INDEX IF NOT EXISTS idx_bill_lines_product_id ON bill_lines(product_id) WHERE product_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_bill_lines_product_bill ON bill_lines(bill_id, product_id) WHERE product_id IS NOT NULL;

-- =====================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- Product availability across locations
CREATE INDEX IF NOT EXISTS idx_product_stock_summary ON stock_levels(org_id, product_id, location_id, quantity_available);

-- Transaction history by product and date
CREATE INDEX IF NOT EXISTS idx_transaction_history ON inventory_transactions(org_id, product_id, transaction_date, transaction_type);

-- Cost tracking by product and location
CREATE INDEX IF NOT EXISTS idx_cost_tracking ON stock_levels(org_id, product_id, location_id, average_cost, last_cost);

-- Low stock alerts with product details
CREATE INDEX IF NOT EXISTS idx_low_stock_alerts ON stock_levels(org_id, quantity_available, product_id) WHERE quantity_available >= 0;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    index_count INTEGER;
BEGIN
    -- Count indexes created for inventory tables
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE schemaname = 'public'
    AND tablename IN ('products', 'product_categories', 'inventory_locations', 'stock_levels', 'inventory_transactions')
    AND indexname LIKE 'idx_%';
    
    RAISE NOTICE '✅ Created % performance indexes for inventory tables', index_count;
    RAISE NOTICE '📊 Database optimized for inventory operations';
END $$;
