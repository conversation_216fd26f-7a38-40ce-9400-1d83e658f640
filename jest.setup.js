/**
 * Jest Setup for Notification System Testing
 * Global test configuration and mocks
 */

import '@testing-library/jest-dom'
import 'jest-canvas-mock'

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
    }
  },
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock Supabase client
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      neq: jest.fn().mockReturnThis(),
      gt: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lt: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      like: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      contains: jest.fn().mockReturnThis(),
      containedBy: jest.fn().mockReturnThis(),
      rangeGt: jest.fn().mockReturnThis(),
      rangeGte: jest.fn().mockReturnThis(),
      rangeLt: jest.fn().mockReturnThis(),
      rangeLte: jest.fn().mockReturnThis(),
      rangeAdjacent: jest.fn().mockReturnThis(),
      overlaps: jest.fn().mockReturnThis(),
      textSearch: jest.fn().mockReturnThis(),
      match: jest.fn().mockReturnThis(),
      not: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      filter: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockReturnThis(),
      csv: jest.fn().mockReturnThis(),
      upsert: jest.fn().mockReturnThis(),
    })),
    auth: {
      getUser: jest.fn(),
      getSession: jest.fn(),
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
      onAuthStateChange: jest.fn(),
    },
    channel: jest.fn(() => ({
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    })),
    removeChannel: jest.fn(),
    rpc: jest.fn(),
    functions: {
      invoke: jest.fn(),
    },
  },
}))

// Mock toast notifications
jest.mock('@/components/ui/toast-utils', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
  },
}))

// Mock real-time hooks
jest.mock('@/hooks/useNotificationRealtime', () => ({
  useNotificationRealtime: () => ({
    isConnected: true,
    connectionError: null,
    reconnect: jest.fn(),
    isUsingPolling: false,
  }),
}))

// Mock push notification service
jest.mock('@/lib/pushNotificationService', () => ({
  PushNotificationService: jest.fn().mockImplementation(() => ({
    initialize: jest.fn().mockResolvedValue(true),
    requestPermission: jest.fn().mockResolvedValue('granted'),
    subscribe: jest.fn().mockResolvedValue({}),
    unsubscribe: jest.fn().mockResolvedValue(true),
    sendTestNotification: jest.fn().mockResolvedValue(true),
  })),
  initializePushNotifications: jest.fn().mockResolvedValue({}),
  getPushNotificationService: jest.fn().mockReturnValue({}),
  isPushNotificationSupported: jest.fn().mockReturnValue(true),
}))

// Mock external integrations
jest.mock('@/lib/externalIntegrations', () => ({
  ExternalIntegrationService: {
    sendToSlack: jest.fn().mockResolvedValue(true),
    sendToTeams: jest.fn().mockResolvedValue(true),
    sendToWebhook: jest.fn().mockResolvedValue(true),
    sendSMS: jest.fn().mockResolvedValue(true),
  },
  IntegrationAPI: {
    getIntegrations: jest.fn().mockResolvedValue([]),
    createIntegration: jest.fn().mockResolvedValue({}),
    updateIntegration: jest.fn().mockResolvedValue({}),
    deleteIntegration: jest.fn().mockResolvedValue(true),
    testIntegration: jest.fn().mockResolvedValue(true),
  },
  sendToIntegrations: jest.fn().mockResolvedValue(undefined),
}))

// Mock email service
jest.mock('@/lib/emailService', () => ({
  sendNotificationEmail: jest.fn().mockResolvedValue({
    success: true,
    email_id: 'test-email-id',
  }),
  sendEmail: jest.fn().mockResolvedValue({
    success: true,
    email_id: 'test-email-id',
  }),
}))

// Mock enhanced email service
jest.mock('@/lib/enhancedEmailService', () => ({
  EnhancedEmailService: jest.fn().mockImplementation(() => ({
    sendNotificationEmail: jest.fn().mockResolvedValue({
      success: true,
      deliveryId: 'test-delivery-id',
    }),
    trackEmailOpen: jest.fn().mockResolvedValue(undefined),
    trackEmailClick: jest.fn().mockResolvedValue(undefined),
    getDeliveryAnalytics: jest.fn().mockResolvedValue({}),
  })),
  createEnhancedEmailService: jest.fn().mockReturnValue({}),
  sendRichNotificationEmail: jest.fn().mockResolvedValue({
    success: true,
    deliveryId: 'test-delivery-id',
  }),
}))

// Mock Web APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock Service Worker
Object.defineProperty(navigator, 'serviceWorker', {
  value: {
    register: jest.fn().mockResolvedValue({
      installing: null,
      waiting: null,
      active: null,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      pushManager: {
        subscribe: jest.fn().mockResolvedValue({
          endpoint: 'test-endpoint',
          getKey: jest.fn().mockReturnValue(new ArrayBuffer(8)),
        }),
      },
    }),
    ready: Promise.resolve({
      showNotification: jest.fn().mockResolvedValue(undefined),
      pushManager: {
        subscribe: jest.fn().mockResolvedValue({
          endpoint: 'test-endpoint',
          getKey: jest.fn().mockReturnValue(new ArrayBuffer(8)),
        }),
      },
    }),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  },
  writable: true,
})

// Mock Notification API
Object.defineProperty(window, 'Notification', {
  value: {
    permission: 'granted',
    requestPermission: jest.fn().mockResolvedValue('granted'),
  },
  writable: true,
})

// Mock PushManager
Object.defineProperty(window, 'PushManager', {
  value: jest.fn(),
  writable: true,
})

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  })
)

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.sessionStorage = sessionStorageMock

// Mock console methods for cleaner test output
const originalError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})

// Global test utilities
global.testUtils = {
  // Helper to create mock notification data
  createMockNotification: (overrides = {}) => ({
    id: 'test-notification-id',
    type: 'payment_pending_approval',
    category: 'financial',
    priority: 'high',
    title: 'Test Notification',
    message: 'This is a test notification',
    is_read: false,
    is_archived: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    user_id: 'test-user-id',
    org_id: 'test-org-id',
    entity_type: 'payment',
    entity_id: 'test-entity-id',
    read_at: null,
    data: {},
    ...overrides,
  }),

  // Helper to create mock user profile
  createMockProfile: (overrides = {}) => ({
    id: 'test-user-id',
    org_id: 'test-org-id',
    email: '<EMAIL>',
    full_name: 'Test User',
    ...overrides,
  }),

  // Helper to wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
}

// Increase timeout for async operations
jest.setTimeout(10000)
