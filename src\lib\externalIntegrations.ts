/**
 * External Integrations for Notifications
 * Slack, Microsoft Teams, Webhooks, and SMS integrations
 */

import { supabase } from '@/integrations/supabase/client'
import type { NotificationWithMeta, NotificationType } from '@/types/notifications'

// Define specific config types for different integrations
type SlackConfig = {
  webhook_url: string
  channel?: string
  username?: string
  icon_emoji?: string
  auth_type?: 'bearer' | 'basic' | 'api_key'
  auth_config?: {
    token?: string
    username?: string
    password?: string
    api_key?: string
    header_name?: string
  }
}

type TeamsConfig = {
  webhook_url: string
  auth_type?: 'bearer' | 'basic' | 'api_key'
  auth_config?: {
    token?: string
    username?: string
    password?: string
    api_key?: string
    header_name?: string
  }
}

type WebhookConfig = {
  url: string
  method?: 'POST' | 'PUT'
  headers?: Record<string, string>
  auth_type?: 'bearer' | 'basic' | 'api_key'
  auth_config?: {
    token?: string
    username?: string
    password?: string
    api_key?: string
    header_name?: string
  }
}

type SMSConfig = {
  provider: 'twilio' | 'aws_sns' | 'custom'
  account_sid?: string
  auth_token?: string
  from_number?: string
  region?: string
  access_key_id?: string
  secret_access_key?: string
  api_url?: string
  api_key?: string
}

type IntegrationConfigData = SlackConfig | TeamsConfig | WebhookConfig | SMSConfig

// Define action types
type SlackAction = {
  type: 'button'
  text: {
    type: 'plain_text'
    text: string
  }
  url: string
  style?: 'primary' | 'danger'
}

type TeamsAction = {
  '@type': 'OpenUri'
  name: string
  targets: Array<{
    os: 'default'
    uri: string
  }>
}

export interface IntegrationConfig {
  id: string
  org_id: string
  type: 'slack' | 'teams' | 'webhook' | 'sms'
  name: string
  config: IntegrationConfigData
  is_active: boolean
  notification_types: NotificationType[]
  created_at: string
  updated_at: string
}

export interface SlackConfig {
  webhook_url: string
  channel?: string
  username?: string
  icon_emoji?: string
}

export interface TeamsConfig {
  webhook_url: string
  title_color?: string
}

export interface WebhookConfig {
  url: string
  method: 'POST' | 'PUT' | 'PATCH'
  headers?: Record<string, string>
  auth_type?: 'none' | 'bearer' | 'basic' | 'api_key'
  auth_config?: Record<string, string>
}

export interface SMSConfig {
  provider: 'twilio' | 'aws_sns' | 'custom'
  api_key: string
  api_secret?: string
  from_number: string
  region?: string
}

/**
 * External Integration Service
 */
export class ExternalIntegrationService {
  /**
   * Send notification to Slack
   */
  static async sendToSlack(
    config: SlackConfig,
    notification: NotificationWithMeta
  ): Promise<boolean> {
    try {
      const payload = {
        channel: config.channel,
        username: config.username || 'Kaya Finance',
        icon_emoji: config.icon_emoji || ':bell:',
        attachments: [
          {
            color: this.getPriorityColor(notification.priority),
            title: notification.title,
            text: notification.message,
            fields: [
              {
                title: 'Type',
                value: notification.type,
                short: true
              },
              {
                title: 'Priority',
                value: notification.priority,
                short: true
              },
              {
                title: 'Category',
                value: notification.category,
                short: true
              },
              {
                title: 'Created',
                value: new Date(notification.created_at).toLocaleString(),
                short: true
              }
            ],
            actions: this.getSlackActions(notification),
            footer: 'Kaya Finance',
            ts: Math.floor(new Date(notification.created_at).getTime() / 1000)
          }
        ]
      }

      const response = await fetch(config.webhook_url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      return response.ok
    } catch (error) {
      console.error('Error sending to Slack:', error)
      return false
    }
  }

  /**
   * Send notification to Microsoft Teams
   */
  static async sendToTeams(
    config: TeamsConfig,
    notification: NotificationWithMeta
  ): Promise<boolean> {
    try {
      const payload = {
        '@type': 'MessageCard',
        '@context': 'http://schema.org/extensions',
        themeColor: config.title_color || this.getPriorityColor(notification.priority),
        summary: notification.title,
        sections: [
          {
            activityTitle: notification.title,
            activitySubtitle: notification.message,
            activityImage: 'https://app.kayafinance.com/icons/icon-192x192.png',
            facts: [
              {
                name: 'Type',
                value: notification.type
              },
              {
                name: 'Priority',
                value: notification.priority
              },
              {
                name: 'Category',
                value: notification.category
              },
              {
                name: 'Created',
                value: new Date(notification.created_at).toLocaleString()
              }
            ],
            markdown: true
          }
        ],
        potentialAction: this.getTeamsActions(notification)
      }

      const response = await fetch(config.webhook_url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      return response.ok
    } catch (error) {
      console.error('Error sending to Teams:', error)
      return false
    }
  }

  /**
   * Send notification via webhook
   */
  static async sendToWebhook(
    config: WebhookConfig,
    notification: NotificationWithMeta
  ): Promise<boolean> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...config.headers
      }

      // Add authentication headers
      if (config.auth_type && config.auth_config) {
        switch (config.auth_type) {
          case 'bearer': {
            headers['Authorization'] = `Bearer ${config.auth_config?.token}`
            break
          }
          case 'basic': {
            const credentials = btoa(`${config.auth_config?.username}:${config.auth_config?.password}`)
            headers['Authorization'] = `Basic ${credentials}`
            break
          }
          case 'api_key': {
            headers[config.auth_config?.header_name || 'X-API-Key'] = config.auth_config?.api_key
            break
          }
        }
      }

      const payload = {
        notification: {
          id: notification.id,
          type: notification.type,
          category: notification.category,
          priority: notification.priority,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          created_at: notification.created_at,
          entity_type: notification.entity_type,
          entity_id: notification.entity_id
        },
        timestamp: new Date().toISOString(),
        source: 'kaya-finance'
      }

      const response = await fetch(config.url, {
        method: config.method,
        headers,
        body: JSON.stringify(payload)
      })

      return response.ok
    } catch (error) {
      console.error('Error sending to webhook:', error)
      return false
    }
  }

  /**
   * Send SMS notification
   */
  static async sendSMS(
    config: SMSConfig,
    notification: NotificationWithMeta,
    phoneNumber: string
  ): Promise<boolean> {
    try {
      const message = `${notification.title}\n\n${notification.message}\n\n- Kaya Finance`

      switch (config.provider) {
        case 'twilio':
          return await this.sendTwilioSMS(config, phoneNumber, message)
        case 'aws_sns':
          return await this.sendAWSSMS(config, phoneNumber, message)
        default:
          return await this.sendCustomSMS(config, phoneNumber, message)
      }
    } catch (error) {
      console.error('Error sending SMS:', error)
      return false
    }
  }

  /**
   * Send SMS via Twilio
   */
  private static async sendTwilioSMS(
    config: SMSConfig,
    to: string,
    message: string
  ): Promise<boolean> {
    try {
      const credentials = btoa(`${config.api_key}:${config.api_secret}`)
      
      const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${config.api_key}/Messages.json`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          From: config.from_number,
          To: to,
          Body: message
        })
      })

      return response.ok
    } catch (error) {
      console.error('Error sending Twilio SMS:', error)
      return false
    }
  }

  /**
   * Send SMS via AWS SNS
   */
  private static async sendAWSSMS(
    config: SMSConfig,
    to: string,
    message: string
  ): Promise<boolean> {
    try {
      // This would require AWS SDK integration
      // For now, return false as placeholder
      console.log('AWS SNS SMS not implemented yet')
      return false
    } catch (error) {
      console.error('Error sending AWS SMS:', error)
      return false
    }
  }

  /**
   * Send SMS via custom provider
   */
  private static async sendCustomSMS(
    config: SMSConfig,
    to: string,
    message: string
  ): Promise<boolean> {
    try {
      // Custom SMS provider implementation
      console.log('Custom SMS provider not implemented yet')
      return false
    } catch (error) {
      console.error('Error sending custom SMS:', error)
      return false
    }
  }

  /**
   * Get priority color for integrations
   */
  private static getPriorityColor(priority: string): string {
    switch (priority) {
      case 'urgent':
        return '#e53e3e'
      case 'high':
        return '#dd6b20'
      case 'normal':
        return '#3182ce'
      case 'low':
        return '#38a169'
      default:
        return '#3182ce'
    }
  }

  /**
   * Get Slack actions for notification
   */
  private static getSlackActions(notification: NotificationWithMeta): SlackAction[] {
    const actions = []

    if (notification.entity_type && notification.entity_id) {
      actions.push({
        type: 'button',
        text: 'View Details',
        url: `https://app.kayafinance.com/${notification.entity_type}s/${notification.entity_id}`
      })
    }

    actions.push({
      type: 'button',
      text: 'Mark as Read',
      url: `https://app.kayafinance.com/api/notifications/${notification.id}/read`
    })

    return actions
  }

  /**
   * Get Teams actions for notification
   */
  private static getTeamsActions(notification: NotificationWithMeta): TeamsAction[] {
    const actions = []

    if (notification.entity_type && notification.entity_id) {
      actions.push({
        '@type': 'OpenUri',
        name: 'View Details',
        targets: [
          {
            os: 'default',
            uri: `https://app.kayafinance.com/${notification.entity_type}s/${notification.entity_id}`
          }
        ]
      })
    }

    actions.push({
      '@type': 'OpenUri',
      name: 'Open Notifications',
      targets: [
        {
          os: 'default',
          uri: 'https://app.kayafinance.com/notifications'
        }
      ]
    })

    return actions
  }
}

/**
 * Integration Management API
 */
export class IntegrationAPI {
  /**
   * Get organization integrations
   */
  static async getIntegrations(orgId: string): Promise<IntegrationConfig[]> {
    try {
      const { data, error } = await supabase
        .from('notification_integrations')
        .select('*')
        .eq('org_id', orgId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) throw error

      return data || []
    } catch (error) {
      console.error('Error fetching integrations:', error)
      return []
    }
  }

  /**
   * Create integration
   */
  static async createIntegration(
    orgId: string,
    integration: Omit<IntegrationConfig, 'id' | 'org_id' | 'created_at' | 'updated_at'>
  ): Promise<IntegrationConfig | null> {
    try {
      const { data, error } = await supabase
        .from('notification_integrations')
        .insert({
          org_id: orgId,
          ...integration
        })
        .select()
        .single()

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error creating integration:', error)
      return null
    }
  }

  /**
   * Update integration
   */
  static async updateIntegration(
    integrationId: string,
    updates: Partial<IntegrationConfig>
  ): Promise<IntegrationConfig | null> {
    try {
      const { data, error } = await supabase
        .from('notification_integrations')
        .update(updates)
        .eq('id', integrationId)
        .select()
        .single()

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error updating integration:', error)
      return null
    }
  }

  /**
   * Delete integration
   */
  static async deleteIntegration(integrationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notification_integrations')
        .delete()
        .eq('id', integrationId)

      return !error
    } catch (error) {
      console.error('Error deleting integration:', error)
      return false
    }
  }

  /**
   * Test integration
   */
  static async testIntegration(
    integration: IntegrationConfig
  ): Promise<boolean> {
    const testNotification: NotificationWithMeta = {
      id: 'test-notification',
      type: 'system_maintenance',
      category: 'system',
      priority: 'normal',
      title: 'Test Notification',
      message: 'This is a test notification from Kaya Finance to verify your integration is working correctly.',
      data: {},
      is_read: false,
      is_archived: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      user_id: null,
      org_id: integration.org_id,
      entity_type: null,
      entity_id: null,
      read_at: null
    }

    switch (integration.type) {
      case 'slack':
        return await ExternalIntegrationService.sendToSlack(
          integration.config as SlackConfig,
          testNotification
        )
      case 'teams':
        return await ExternalIntegrationService.sendToTeams(
          integration.config as TeamsConfig,
          testNotification
        )
      case 'webhook':
        return await ExternalIntegrationService.sendToWebhook(
          integration.config as WebhookConfig,
          testNotification
        )
      default:
        return false
    }
  }
}

/**
 * Send notification to all configured integrations
 */
export async function sendToIntegrations(
  notification: NotificationWithMeta,
  orgId: string
): Promise<void> {
  try {
    const integrations = await IntegrationAPI.getIntegrations(orgId)
    
    const promises = integrations
      .filter(integration => 
        integration.is_active && 
        integration.notification_types.includes(notification.type)
      )
      .map(async (integration) => {
        try {
          switch (integration.type) {
            case 'slack':
              return await ExternalIntegrationService.sendToSlack(
                integration.config as SlackConfig,
                notification
              )
            case 'teams':
              return await ExternalIntegrationService.sendToTeams(
                integration.config as TeamsConfig,
                notification
              )
            case 'webhook':
              return await ExternalIntegrationService.sendToWebhook(
                integration.config as WebhookConfig,
                notification
              )
            default:
              return false
          }
        } catch (error) {
          console.error(`Error sending to ${integration.type} integration:`, error)
          return false
        }
      })

    await Promise.allSettled(promises)
  } catch (error) {
    console.error('Error sending to integrations:', error)
  }
}
