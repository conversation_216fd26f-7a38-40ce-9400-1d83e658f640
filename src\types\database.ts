
// Import the generated Supabase types
import type { Database } from '@/generated/db'

// Re-export the Database type
export type { Database } from '@/generated/db'

// Helper types for easier usage using the Database type structure
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Organization = Database['public']['Tables']['organizations']['Row']
export type Account = Database['public']['Tables']['accounts']['Row']
export type JournalEntry = Database['public']['Tables']['journal_entries']['Row']
export type TransactionLine = Database['public']['Tables']['transaction_lines']['Row']
export type Customer = Database['public']['Tables']['customers']['Row']
export type Vendor = Database['public']['Tables']['vendors']['Row']
export type Invoice = Database['public']['Tables']['invoices']['Row']
export type InvoiceLine = Database['public']['Tables']['invoice_lines']['Row']
export type Bill = Database['public']['Tables']['bills']['Row']
export type BillLine = Database['public']['Tables']['bill_lines']['Row']
export type Payment = Database['public']['Tables']['payments']['Row']
export type PaymentApproval = Database['public']['Tables']['payment_approvals']['Row']
export type BankAccount = Database['public']['Tables']['bank_accounts']['Row']
export type MobileMoney = Database['public']['Tables']['mobile_money_accounts']['Row']
export type Budget = Database['public']['Tables']['budgets']['Row']
export type Currency = Database['public']['Tables']['currencies']['Row']
export type Attachment = Database['public']['Tables']['attachments']['Row']
export type AuditLog = Database['public']['Tables']['audit_logs']['Row']
export type TaxRate = Database['public']['Tables']['tax_rates']['Row']
export type WithholdingTaxRate = Database['public']['Tables']['withholding_tax_rates']['Row']
export type Notification = Database['public']['Tables']['notifications']['Row']
export type NotificationPreference = Database['public']['Tables']['notification_preferences']['Row']
export type NotificationTemplate = Database['public']['Tables']['notification_templates']['Row']

// Inventory Management types - temporarily using extended types until generated types are updated
// These will be replaced with proper generated types once the database schema is updated
export type {
  Product,
  ProductCategory,
  InventoryLocation,
  StockLevel,
  InventoryTransaction,
  ProductInsert,
  ProductCategoryInsert,
  InventoryLocationInsert,
  StockLevelInsert,
  InventoryTransactionInsert,
  ProductUpdate,
  ProductCategoryUpdate,
  InventoryLocationUpdate,
  StockLevelUpdate,
  InventoryTransactionUpdate,
  InvoiceLineWithProduct,
  BillLineWithProduct
} from './extended-database'

// Backup & Restoration types
export type BackupMetadata = Database['public']['Tables']['backup_metadata']['Row']
export type BackupSettings = Database['public']['Tables']['backup_settings']['Row']
export type BackupRestoration = Database['public']['Tables']['backup_restorations']['Row']
export type BackupError = Database['public']['Tables']['backup_errors']['Row']

// Audit log related types
export interface AuditLogEntry {
  id?: string
  entity_type: string
  entity_id: string
  action: AuditAction
  changed_data?: Record<string, unknown>
  ip_address?: string
  user_agent?: string
  session_id?: string
  description?: string
  severity: AuditSeverity
  category: AuditCategory
  metadata?: Record<string, unknown>
  // Note: old_values and new_values are stored within changed_data for compatibility
  old_values?: Record<string, unknown> // For internal use only, stored in changed_data
  new_values?: Record<string, unknown> // For internal use only, stored in changed_data
}

export type AuditAction =
  | 'create' | 'update' | 'delete' | 'view' | 'export' | 'import'
  | 'login' | 'logout' | 'password_change' | 'permission_change'
  | 'approve' | 'reject' | 'reconcile' | 'send' | 'cancel'
  | 'backup' | 'restore' | 'archive' | 'purge'

export type AuditSeverity = 'low' | 'medium' | 'high' | 'critical'

export type AuditCategory =
  | 'authentication' | 'authorization' | 'data_modification' | 'data_access'
  | 'financial_transaction' | 'system_configuration' | 'user_management'
  | 'compliance' | 'security' | 'backup_restore'

// Enum types
export type UserRole = Database['public']['Enums']['user_role']
export type AccountType = Database['public']['Enums']['account_type']
export type InvoiceStatus = Database['public']['Enums']['invoice_status']
export type BillStatus = Database['public']['Enums']['bill_status']
export type BudgetStatus = Database['public']['Enums']['budget_status']
export type PaymentChannel = Database['public']['Enums']['payment_channel']
export type PaymentStatus = Database['public']['Enums']['payment_status']
