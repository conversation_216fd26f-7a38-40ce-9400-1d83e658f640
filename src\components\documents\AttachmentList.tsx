
import React, { useState, useEffect, useCallback } from 'react'
import { File, Download, Trash2, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/hooks/useAuthHook'
import type { Attachment } from '@/types/database'

interface AttachmentListProps {
  attachedToType: string
  attachedToId: string
  refreshTrigger?: number
}

export function AttachmentList({ attachedToType, attachedToId, refreshTrigger }: AttachmentListProps) {
  const [attachments, setAttachments] = useState<Attachment[]>([])
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()
  const { user } = useAuth()

  const fetchAttachments = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('attachments')
        .select('*')
        .eq('attached_to_type', attachedToType)
        .eq('attached_to_id', attachedToId)
        .order('created_at', { ascending: false })

      if (error) throw error
      setAttachments(data || [])
    } catch (error) {
      console.error('Error fetching attachments:', error)
      toast({
        title: "Error",
        description: "Failed to load attachments",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [attachedToType, attachedToId, toast])

  useEffect(() => {
    fetchAttachments()
  }, [attachedToType, attachedToId, refreshTrigger, fetchAttachments])

  const handleDelete = async (attachment: Attachment) => {
    try {
      // Delete from storage
      const filePath = attachment.url.split('/').pop()
      if (filePath) {
        await supabase.storage
          .from('documents')
          .remove([`attachments/${filePath}`])
      }

      // Delete from database
      const { error } = await supabase
        .from('attachments')
        .delete()
        .eq('id', attachment.id)

      if (error) throw error

      setAttachments(prev => prev.filter(a => a.id !== attachment.id))
      toast({
        title: "Success",
        description: "Attachment deleted successfully"
      })
    } catch (error) {
      console.error('Delete error:', error)
      toast({
        title: "Error",
        description: "Failed to delete attachment",
        variant: "destructive"
      })
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return <div className="text-center py-4">Loading attachments...</div>
  }

  if (attachments.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-gray-500">
          No attachments yet
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <File className="h-5 w-5" />
          Attachments ({attachments.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {attachments.map((attachment) => (
            <div key={attachment.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <File className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="font-medium text-sm">{attachment.file_name}</p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(attachment.file_size)} • {new Date(attachment.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open(attachment.url, '_blank')}
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    const link = document.createElement('a')
                    link.href = attachment.url
                    link.download = attachment.file_name
                    link.click()
                  }}
                >
                  <Download className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDelete(attachment)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
