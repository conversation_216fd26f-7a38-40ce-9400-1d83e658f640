-- =====================================================
-- JOURNAL ENTRY AUTOMATION - SEGMENT 2: INVOICE AUTOMATION
-- =====================================================
-- This migration implements automated journal entries for invoices
-- Date: 2025-06-29
-- Purpose: Create invoice journal entry automation and account mapping setup
-- Dependencies: 20250629_01_journal_automation_foundation.sql

-- =====================================================
-- STEP 1: ACCOUNT MAPPING SETUP FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating account mapping setup functions...';
END $$;

-- Function to setup default account mappings for an organization
CREATE OR REPLACE FUNCTION setup_default_account_mappings(org_id_param UUID)
RETURNS TABLE (
    mapping_type TEXT,
    account_id UUID,
    account_name TEXT,
    account_code TEXT,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    account_record RECORD;
    mapping_types TEXT[] := ARRAY[
        'accounts_receivable', 'accounts_payable', 'revenue', 'expense',
        'vat_payable', 'vat_receivable', 'cash', 'bank'
    ];
    mapping_type_item TEXT;
BEGIN
    -- Loop through each mapping type and try to find suitable accounts
    FOREACH mapping_type_item IN ARRAY mapping_types
    LOOP
        -- Skip if mapping already exists
        IF EXISTS (
            SELECT 1 FROM account_mappings
            WHERE org_id = org_id_param AND mapping_type = mapping_type_item
        ) THEN
            -- Return existing mapping
            SELECT am.mapping_type, am.account_id, a.name, a.code, 'existing'
            INTO account_record
            FROM account_mappings am
            JOIN accounts a ON am.account_id = a.id
            WHERE am.org_id = org_id_param
            AND am.mapping_type = mapping_type_item
            AND am.is_default = true;

            RETURN QUERY SELECT
                account_record.mapping_type,
                account_record.account_id,
                account_record.name,
                account_record.code,
                account_record.status;
            CONTINUE;
        END IF;

        -- Try to find suitable account based on account type and name patterns
        CASE mapping_type_item
            WHEN 'accounts_receivable' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND (a.account_type = 'asset' OR a.account_type = 'current_asset')
                AND (LOWER(a.name) LIKE '%receivable%' OR LOWER(a.name) LIKE '%debtors%')
                ORDER BY a.code
                LIMIT 1;

            WHEN 'revenue' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND a.account_type = 'revenue'
                AND (LOWER(a.name) LIKE '%sales%' OR LOWER(a.name) LIKE '%revenue%' OR LOWER(a.name) LIKE '%income%')
                ORDER BY a.code
                LIMIT 1;

            WHEN 'vat_payable' THEN
                SELECT a.id, a.name, a.code
                INTO account_record
                FROM accounts a
                WHERE a.org_id = org_id_param
                AND (a.account_type = 'liability' OR a.account_type = 'current_liability')
                AND (LOWER(a.name) LIKE '%vat%' OR LOWER(a.name) LIKE '%tax%')
                AND LOWER(a.name) LIKE '%payable%'
                ORDER BY a.code
                LIMIT 1;
        END CASE;

        -- If account found, create the mapping
        IF account_record.id IS NOT NULL THEN
            INSERT INTO account_mappings (
                org_id, mapping_type, account_id, is_default
            )
            VALUES (
                org_id_param, mapping_type_item, account_record.id, true
            );

            RETURN QUERY SELECT
                mapping_type_item,
                account_record.id,
                account_record.name,
                account_record.code,
                'created'::TEXT;
        ELSE
            RETURN QUERY SELECT
                mapping_type_item,
                NULL::UUID,
                NULL::TEXT,
                NULL::TEXT,
                'not_found'::TEXT;
        END IF;
    END LOOP;
END;
$$;

-- Function to get account mapping for a specific type
CREATE OR REPLACE FUNCTION get_account_mapping(
    org_id_param UUID,
    mapping_type_param TEXT
)
RETURNS TABLE (
    account_id UUID,
    account_name TEXT,
    account_code TEXT,
    account_type TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id,
        a.name,
        a.code,
        a.account_type
    FROM account_mappings am
    JOIN accounts a ON am.account_id = a.id
    WHERE am.org_id = org_id_param
    AND am.mapping_type = mapping_type_param
    AND am.is_default = true;
END;
$$;

-- =====================================================
-- STEP 2: INVOICE JOURNAL ENTRY AUTOMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 Creating invoice journal entry automation...';
END $$;

-- Function to create journal entry for invoice
CREATE OR REPLACE FUNCTION create_invoice_journal_entry()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_journal_id UUID;
    ar_account_id UUID;
    revenue_account_id UUID;
    vat_account_id UUID;
    validation_result RECORD;
    required_mappings TEXT[] := ARRAY['accounts_receivable', 'revenue', 'vat_payable'];
    subtotal DECIMAL(15,2);
    automation_enabled BOOLEAN := true;
BEGIN
    -- Only process when invoice status changes to 'sent' or when new invoice is created with 'sent' status
    IF (TG_OP = 'INSERT' AND NEW.status = 'sent') OR
       (TG_OP = 'UPDATE' AND NEW.status = 'sent' AND (OLD.status IS NULL OR OLD.status != 'sent')) THEN

        -- Check if automation is enabled for this organization
        SELECT setting_value->>'enabled' = 'true'
        INTO automation_enabled
        FROM automation_settings
        WHERE org_id = NEW.org_id AND setting_key = 'invoice_journal_automation';

        -- Default to enabled if no setting exists
        IF automation_enabled IS NULL THEN
            automation_enabled := true;
        END IF;

        IF NOT automation_enabled THEN
            RETURN NEW;
        END IF;

        BEGIN
            -- Validate required account mappings
            FOR validation_result IN
                SELECT * FROM validate_account_mappings(NEW.org_id, required_mappings)
            LOOP
                IF NOT validation_result.is_valid THEN
                    PERFORM handle_journal_entry_error(
                        NEW.org_id,
                        'invoice',
                        NEW.id,
                        'missing_account_mapping',
                        validation_result.error_message,
                        jsonb_build_object('mapping_type', validation_result.mapping_type)
                    );
                    RETURN NEW;
                END IF;

                -- Store account IDs based on mapping type
                CASE validation_result.mapping_type
                    WHEN 'accounts_receivable' THEN ar_account_id := validation_result.account_id;
                    WHEN 'revenue' THEN revenue_account_id := validation_result.account_id;
                    WHEN 'vat_payable' THEN vat_account_id := validation_result.account_id;
                END CASE;
            END LOOP;

            -- Calculate subtotal (total - tax)
            subtotal := NEW.total_amount - NEW.tax_amount;

            -- Create journal entry
            INSERT INTO journal_entries (
                org_id, date, description, reference,
                source_id, source_type, created_by, is_posted
            )
            VALUES (
                NEW.org_id,
                NEW.date_issued,
                'Invoice: ' || NEW.invoice_number,
                NEW.invoice_number,
                NEW.id,
                'invoice',
                NEW.created_by,
                false
            )
            RETURNING id INTO new_journal_id;

            -- Create transaction lines for invoice (DR AR, CR Revenue, CR VAT)

            -- Debit: Accounts Receivable (total amount)
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                NEW.org_id, new_journal_id, ar_account_id,
                NEW.total_amount, 0, 'Accounts Receivable - ' || NEW.invoice_number
            );

            -- Credit: Revenue (subtotal)
            IF subtotal > 0 THEN
                INSERT INTO transaction_lines (
                    org_id, journal_entry_id, account_id,
                    debit, credit, description
                )
                VALUES (
                    NEW.org_id, new_journal_id, revenue_account_id,
                    0, subtotal, 'Sales Revenue - ' || NEW.invoice_number
                );
            END IF;

            -- Credit: VAT Payable (tax amount)
            IF NEW.tax_amount > 0 THEN
                INSERT INTO transaction_lines (
                    org_id, journal_entry_id, account_id,
                    debit, credit, description
                )
                VALUES (
                    NEW.org_id, new_journal_id, vat_account_id,
                    0, NEW.tax_amount, 'VAT Payable - ' || NEW.invoice_number
                );
            END IF;

            -- Validate the journal entry is balanced
            DECLARE
                balance_check RECORD;
            BEGIN
                SELECT * INTO balance_check
                FROM validate_journal_entry_balance(new_journal_id);

                IF NOT balance_check.is_balanced THEN
                    RAISE EXCEPTION 'Journal entry is not balanced. Debits: %, Credits: %, Difference: %',
                        balance_check.total_debits, balance_check.total_credits, balance_check.difference;
                END IF;
            END;

        EXCEPTION WHEN OTHERS THEN
            -- Log the error
            PERFORM handle_journal_entry_error(
                NEW.org_id,
                'invoice',
                NEW.id,
                'database_error',
                SQLERRM,
                jsonb_build_object(
                    'invoice_number', NEW.invoice_number,
                    'total_amount', NEW.total_amount,
                    'tax_amount', NEW.tax_amount
                )
            );
        END;
    END IF;

    RETURN NEW;
END;
$$;

-- Create the trigger for invoice journal entries
DROP TRIGGER IF EXISTS invoice_journal_entry_trigger ON invoices;
CREATE TRIGGER invoice_journal_entry_trigger
    AFTER INSERT OR UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION create_invoice_journal_entry();

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ INVOICE JOURNAL AUTOMATION COMPLETE!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '';
    RAISE NOTICE '📋 CREATED:';
    RAISE NOTICE '  • setup_default_account_mappings() function';
    RAISE NOTICE '  • get_account_mapping() function';
    RAISE NOTICE '  • create_invoice_journal_entry() function';
    RAISE NOTICE '  • invoice_journal_entry_trigger trigger';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 NEXT: Run segment 3 for payment automation';
    RAISE NOTICE '';
END $$;

        -- Check if journal entry already exists for this invoice
        IF EXISTS (
            SELECT 1 FROM journal_entries
            WHERE source_id = NEW.id AND source_type = 'invoice'
        ) THEN
            RETURN NEW;
        END IF;