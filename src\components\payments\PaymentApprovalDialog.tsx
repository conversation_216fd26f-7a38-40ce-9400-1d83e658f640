import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'
import { formatCurrency } from '@/lib/utils'
import type { Payment } from '@/types/database'

const approvalSchema = z.object({
  comments: z.string().optional(),
  rejection_reason: z.string().optional(),
})

interface PaymentApprovalDialogProps {
  payment: Payment & { payee_name?: string }
  open: boolean
  onClose: () => void
  onApprovalComplete: () => void
}

export function PaymentApprovalDialog({ 
  payment, 
  open, 
  onClose, 
  onApprovalComplete 
}: PaymentApprovalDialogProps) {
  const { profile } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [action, setAction] = useState<'approve' | 'reject' | null>(null)

  const form = useForm<z.infer<typeof approvalSchema>>({
    resolver: zodResolver(approvalSchema),
    defaultValues: {
      comments: '',
      rejection_reason: '',
    },
  })

  const onSubmit = async (values: z.infer<typeof approvalSchema>) => {
    if (!profile?.id || !action) {
      toast.error('Unable to process approval')
      return
    }

    setIsSubmitting(true)

    try {
      // Create approval record
      const { error: approvalError } = await supabase
        .from('payment_approvals')
        .insert({
          payment_id: payment.id,
          approver_id: profile.id,
          status: action === 'approve' ? 'approved' : 'rejected',
          comments: values.comments || null,
          rejection_reason: action === 'reject' ? values.rejection_reason || null : null,
          org_id: profile.org_id, // Explicitly include org_id for RLS
        })

      if (approvalError) {
        console.error('Approval error:', approvalError)
        throw new Error(`Failed to create approval record: ${approvalError.message}`)
      }

      // Update payment status and reconciliation
      const newStatus = action === 'approve' ? 'approved' : 'rejected'
      const isReconciled = action === 'approve' ? true : payment.is_reconciled

      const { error: paymentError } = await supabase
        .from('payments')
        .update({
          status: newStatus,
          is_reconciled: isReconciled
        })
        .eq('id', payment.id)

      if (paymentError) {
        console.error('Payment update error:', paymentError)
        throw new Error(`Failed to update payment: ${paymentError.message}`)
      }

      toast.success(`Payment ${action === 'approve' ? 'approved and marked as paid' : 'rejected'} successfully`)
      onApprovalComplete()
      onClose()
    } catch (error: unknown) {
      console.error('Error processing approval:', error)
      const errorMessage = error instanceof Error ? error.message : (typeof error === 'string' ? error : 'Failed to process approval')
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
      setAction(null)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Payment Approval - {payment.payee_name || 'Unknown Payee'}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Payment Details */}
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">Payment Date:</span>
              <span>{new Date(payment.payment_date).toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Amount:</span>
              <span className="font-semibold">{formatCurrency(payment.amount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Channel:</span>
              <Badge variant="outline">{payment.channel}</Badge>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Current Status:</span>
              <Badge variant={payment.status === 'pending' ? 'secondary' : 'default'}>
                {payment.status}
              </Badge>
            </div>
            {payment.notes && (
              <div className="flex justify-between">
                <span className="font-medium">Notes:</span>
                <span className="text-sm text-gray-600">{payment.notes}</span>
              </div>
            )}
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="comments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comments (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any comments about this approval..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {action === 'reject' && (
                <FormField
                  control={form.control}
                  name="rejection_reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rejection Reason</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Please provide a reason for rejection..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setAction('reject')}
                  disabled={isSubmitting}
                >
                  Reject
                </Button>
                <Button
                  type="submit"
                  onClick={() => setAction('approve')}
                  disabled={isSubmitting}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSubmitting ? 'Processing...' : 'Approve & Reconcile'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
