import type { 
  WorkflowTemplate, 
  ApprovalRule, 
  ApprovalStep, 
  RoleApprovalLimit, 
  ApprovalInstance, 
  ApprovalAction,
  ApprovalStatus,
  WorkflowStepType,
  DocumentType,
  EscalationType,
  NotificationChannel,
  UserRole
} from './database'

// =====================================================
// WORKFLOW TEMPLATE TYPES
// =====================================================

export interface WorkflowTemplateWithSteps extends WorkflowTemplate {
  approval_steps: ApprovalStep[]
  approval_rules: ApprovalRule[]
}

export interface CreateWorkflowTemplatePayload {
  name: string
  description?: string
  document_type: DocumentType
  is_active?: boolean
  is_default?: boolean
  steps: CreateApprovalStepPayload[]
  rules?: CreateApprovalRulePayload[]
}

export interface CreateApprovalStepPayload {
  step_order: number
  name: string
  description?: string
  step_type?: WorkflowStepType
  required_role: UserRole[]
  required_approvers?: number
  allow_self_approval?: boolean
  escalation_enabled?: boolean
  escalation_timeout_hours?: number
  escalation_type?: EscalationType
  escalation_to_role?: UserRole
  allow_delegation?: boolean
}

export interface CreateApprovalRulePayload {
  name: string
  description?: string
  priority?: number
  is_active?: boolean
  conditions: ApprovalRuleConditions
}

// =====================================================
// APPROVAL RULE CONDITIONS
// =====================================================

export interface ApprovalRuleConditions {
  amount_min?: number
  amount_max?: number
  currency_code?: string
  vendor_type?: string
  customer_type?: string
  department?: string
  project_id?: string
  account_id?: string
  created_by_role?: UserRole[]
  tags?: string[]
  custom_fields?: Record<string, unknown>
}

// =====================================================
// APPROVAL INSTANCE TYPES
// =====================================================

export interface ApprovalInstanceWithDetails extends ApprovalInstance {
  workflow_template: WorkflowTemplate
  current_step?: ApprovalStep
  approval_actions: ApprovalActionWithDetails[]
  submitted_by_profile?: {
    id: string
    full_name: string
    email: string
    role: UserRole
  }
  document_details?: DocumentDetails
}

export interface ApprovalActionWithDetails extends ApprovalAction {
  approver_profile: {
    id: string
    full_name: string
    email: string
    role: UserRole
  }
  delegated_to_profile?: {
    id: string
    full_name: string
    email: string
    role: UserRole
  }
}

export interface DocumentDetails {
  id: string
  type: DocumentType
  number: string
  amount: number
  currency_code: string
  date: string
  status: string
  title: string
  description?: string
}

// =====================================================
// APPROVAL ACTION TYPES
// =====================================================

export interface CreateApprovalActionPayload {
  approval_instance_id: string
  action: ApprovalStatus
  comments?: string
  rejection_reason?: string
  delegated_to?: string
  delegation_reason?: string
  delegation_expires_at?: string
}

export interface ApprovalActionRequest {
  action: 'approve' | 'reject' | 'delegate'
  comments?: string
  rejection_reason?: string
  delegated_to?: string
  delegation_reason?: string
  delegation_expires_at?: string
}

// =====================================================
// APPROVAL DASHBOARD TYPES
// =====================================================

export interface ApprovalDashboardData {
  pending_approvals: ApprovalInstanceWithDetails[]
  recent_actions: ApprovalActionWithDetails[]
  approval_stats: ApprovalStats
  my_delegations: ApprovalDelegation[]
}

export interface ApprovalStats {
  total_pending: number
  pending_by_type: Record<DocumentType, number>
  overdue_count: number
  approved_today: number
  rejected_today: number
  average_approval_time_hours: number
  my_pending_count: number
}

export interface ApprovalDelegation {
  id: string
  delegated_to: string
  delegated_to_profile: {
    full_name: string
    email: string
  }
  delegation_reason: string
  expires_at: string
  is_active: boolean
  created_at: string
}

// =====================================================
// APPROVAL FILTERS AND QUERIES
// =====================================================

export interface ApprovalFilters {
  status?: ApprovalStatus[]
  document_type?: DocumentType[]
  submitted_by?: string[]
  date_range?: {
    start: string
    end: string
  }
  amount_range?: {
    min: number
    max: number
  }
  current_approver?: string
  overdue_only?: boolean
  my_approvals_only?: boolean
}

export interface ApprovalSortOptions {
  field: 'submitted_at' | 'document_amount' | 'current_step_order' | 'status'
  direction: 'asc' | 'desc'
}

// =====================================================
// WORKFLOW CONFIGURATION TYPES
// =====================================================

export interface WorkflowConfigurationData {
  templates: WorkflowTemplateWithSteps[]
  role_limits: RoleApprovalLimit[]
  available_roles: UserRole[]
  document_types: DocumentType[]
}

export interface BulkApprovalRequest {
  approval_instance_ids: string[]
  action: 'approve' | 'reject'
  comments?: string
  rejection_reason?: string
}

export interface BulkApprovalResult {
  successful: string[]
  failed: Array<{
    id: string
    error: string
  }>
  total_processed: number
}

// =====================================================
// NOTIFICATION TYPES
// =====================================================

export interface ApprovalNotificationData {
  type: 'approval_pending' | 'approval_approved' | 'approval_rejected' | 'approval_escalated' | 'approval_delegated'
  approval_instance: ApprovalInstanceWithDetails
  recipient_id: string
  channels: NotificationChannel[]
  template_data: Record<string, unknown>
}

// =====================================================
// ESCALATION TYPES
// =====================================================

export interface EscalationRule {
  id: string
  step_id: string
  timeout_hours: number
  escalation_type: EscalationType
  escalate_to_role: UserRole
  notification_intervals: number[] // Hours before escalation to send reminders
}

export interface EscalationEvent {
  approval_instance_id: string
  step_order: number
  escalated_from: string
  escalated_to: string
  escalation_reason: string
  escalated_at: string
}

// =====================================================
// AUTHORIZATION TYPES
// =====================================================

export interface ApprovalAuthority {
  can_approve: boolean
  can_reject: boolean
  can_delegate: boolean
  approval_limit?: number
  daily_limit_remaining?: number
  monthly_limit_remaining?: number
  reasons?: string[]
}

export interface ApprovalLimitCheck {
  within_limit: boolean
  limit_type: 'amount' | 'daily' | 'monthly' | 'role'
  current_amount: number
  limit_amount?: number
  remaining_amount?: number
}
