
import { useState } from 'react'
import { useActiveAccounts, useDeleteAccount } from '@/hooks/queries'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Dialog, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { Plus } from 'lucide-react'
import { AccountForm } from '@/components/accounts/AccountForm'
import { AccountTable } from '@/components/accounts/AccountTable'
import type { Account } from '@/types/database'

export const Accounts = () => {
  const { toast } = useToast()
  const deleteAccount = useDeleteAccount()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingAccount, setEditingAccount] = useState<Account | null>(null)

  // Use React Query hook for active accounts
  const { data: accounts = [], isLoading, error } = useActiveAccounts()

  const handleEdit = (account: Account) => {
    setEditingAccount(account)
    setIsDialogOpen(true)
  }

  const handleDelete = async (account: Account) => {
    if (!confirm(`Are you sure you want to delete the account "${account.name}"? This action cannot be undone.`)) return

    try {
      await deleteAccount.mutateAsync(account.id)
    } catch (error) {
      console.error('Error deleting account:', error)
      // Error handling is done by the React Query hook
    }
  }

  const handleCreateNew = () => {
    setEditingAccount(null)
    setIsDialogOpen(true)
  }

  const handleFormSuccess = () => {
    // React Query will automatically refetch and update the cache
    setIsDialogOpen(false)
    setEditingAccount(null)
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">Loading accounts...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">
          <p>Failed to load accounts</p>
          <Button onClick={() => window.location.reload()} className="mt-2">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Chart of Accounts</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleCreateNew}>
              <Plus className="h-4 w-4 mr-2" />
              Add Account
            </Button>
          </DialogTrigger>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Accounts ({accounts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <AccountTable
            accounts={accounts}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onCreateNew={handleCreateNew}
          />
        </CardContent>
      </Card>

      <AccountForm
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        editingAccount={editingAccount}
        accounts={accounts}
        onSuccess={handleFormSuccess}
      />
    </div>
  )
}
