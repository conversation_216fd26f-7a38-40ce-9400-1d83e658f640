import { useQuery, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'

export interface BudgetAnalysisItem {
  account: {
    id: string
    name: string
    code: string
    type: string
  }
  budget: {
    id: string
    name: string
    startDate: string
    endDate: string
    status: string
  }
  budgetAmount: number
  actualAmount: number
  variance: number
  variancePercent: number
  utilizationPercent: number
  isOverBudget: boolean
  alertLevel: 'normal' | 'warning' | 'critical' | 'exceeded'
}

export interface BudgetAnalysisSummary {
  totalBudget: number
  totalActual: number
  totalVariance: number
  totalVariancePercent: number
  accountsAnalyzed: number
  overBudgetAccounts: number
  criticalAccounts: number
  warningAccounts: number
  items: BudgetAnalysisItem[]
}

/**
 * Hook to get comprehensive budget analysis for a specific budget
 */
export const useBudgetAnalysis = (budgetId: string | null, enabled = true) => {
  const { profile } = useAuth()

  return useQuery({
    queryKey: queryKeys.budgets.analysis(profile?.org_id || '', budgetId || ''),
    queryFn: async (): Promise<BudgetAnalysisSummary> => {
      if (!profile?.org_id || !budgetId) {
        return {
          totalBudget: 0,
          totalActual: 0,
          totalVariance: 0,
          totalVariancePercent: 0,
          accountsAnalyzed: 0,
          overBudgetAccounts: 0,
          criticalAccounts: 0,
          warningAccounts: 0,
          items: []
        }
      }

      // Get budget details
      const { data: budget, error: budgetError } = await supabase
        .from('budgets')
        .select('*')
        .eq('id', budgetId)
        .single()

      if (budgetError) throw budgetError

      // Get budget lines with account details
      const { data: budgetLines, error: linesError } = await supabase
        .from('budget_lines')
        .select(`
          amount,
          account_id,
          accounts(*)
        `)
        .eq('budget_id', budgetId)

      if (linesError) throw linesError

      const analysisItems: BudgetAnalysisItem[] = []

      for (const line of budgetLines || []) {
        if (!line.accounts) continue

        // Get actual amounts for the budget period
        const { data: transactions, error: transError } = await supabase
          .from('transaction_lines')
          .select('debit, credit')
          .eq('org_id', profile.org_id)
          .eq('account_id', line.account_id)
          .gte('created_at', `${budget.start_date}T00:00:00`)
          .lte('created_at', `${budget.end_date}T23:59:59`)

        if (transError) continue // Skip this line if error

        const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
        const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0

        // Calculate actual amount based on account type
        let actualAmount = 0
        if (line.accounts.type === 'expense') {
          actualAmount = debitTotal - creditTotal
        } else if (line.accounts.type === 'income') {
          actualAmount = creditTotal - debitTotal
        } else if (line.accounts.type === 'asset') {
          actualAmount = debitTotal - creditTotal
        } else {
          actualAmount = creditTotal - debitTotal
        }

        const budgetAmount = line.amount || 0
        const variance = actualAmount - budgetAmount
        const variancePercent = budgetAmount !== 0 ? (variance / budgetAmount) * 100 : 0
        const utilizationPercent = budgetAmount !== 0 ? (actualAmount / budgetAmount) * 100 : 0

        // Determine alert level
        let alertLevel: 'normal' | 'warning' | 'critical' | 'exceeded' = 'normal'
        if (utilizationPercent >= 100) {
          alertLevel = 'exceeded'
        } else if (utilizationPercent >= 90) {
          alertLevel = 'critical'
        } else if (utilizationPercent >= 75) {
          alertLevel = 'warning'
        }

        analysisItems.push({
          account: {
            id: line.account_id,
            name: line.accounts.name,
            code: line.accounts.code,
            type: line.accounts.type
          },
          budget: {
            id: budget.id,
            name: budget.name,
            startDate: budget.start_date,
            endDate: budget.end_date,
            status: budget.status
          },
          budgetAmount,
          actualAmount,
          variance,
          variancePercent,
          utilizationPercent,
          isOverBudget: actualAmount > budgetAmount,
          alertLevel
        })
      }

      // Calculate summary
      const totalBudget = analysisItems.reduce((sum, item) => sum + item.budgetAmount, 0)
      const totalActual = analysisItems.reduce((sum, item) => sum + item.actualAmount, 0)
      const totalVariance = totalActual - totalBudget
      const totalVariancePercent = totalBudget !== 0 ? (totalVariance / totalBudget) * 100 : 0

      const overBudgetAccounts = analysisItems.filter(item => item.isOverBudget).length
      const criticalAccounts = analysisItems.filter(item => item.alertLevel === 'critical' || item.alertLevel === 'exceeded').length
      const warningAccounts = analysisItems.filter(item => item.alertLevel === 'warning').length

      return {
        totalBudget,
        totalActual,
        totalVariance,
        totalVariancePercent,
        accountsAnalyzed: analysisItems.length,
        overBudgetAccounts,
        criticalAccounts,
        warningAccounts,
        items: analysisItems.sort((a, b) => {
          // Sort by alert level (exceeded > critical > warning > normal) then by variance
          const alertOrder = { exceeded: 4, critical: 3, warning: 2, normal: 1 }
          if (alertOrder[a.alertLevel] !== alertOrder[b.alertLevel]) {
            return alertOrder[b.alertLevel] - alertOrder[a.alertLevel]
          }
          return Math.abs(b.variance) - Math.abs(a.variance)
        })
      }
    },
    enabled: enabled && !!profile?.org_id && !!budgetId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes for live updates
  })
}

/**
 * Hook to get budget analysis for all active budgets
 */
export const useAllBudgetsAnalysis = (enabled = true) => {
  const { profile } = useAuth()

  return useQuery({
    queryKey: queryKeys.budgets.allAnalysis(profile?.org_id || ''),
    queryFn: async (): Promise<BudgetAnalysisSummary[]> => {
      if (!profile?.org_id) return []

      // Get all active budgets
      const currentDate = new Date().toISOString().split('T')[0]
      
      const { data: budgets, error } = await supabase
        .from('budgets')
        .select('id, name, start_date, end_date, status')
        .eq('org_id', profile.org_id)
        .eq('status', 'approved')
        .lte('start_date', currentDate)
        .gte('end_date', currentDate)

      if (error) throw error

      // Get analysis for each budget
      const analyses: BudgetAnalysisSummary[] = []
      
      for (const budget of budgets || []) {
        // This would ideally use the individual budget analysis hook
        // For now, we'll implement a simplified version
        const { data: budgetLines } = await supabase
          .from('budget_lines')
          .select(`
            amount,
            account_id,
            accounts(*)
          `)
          .eq('budget_id', budget.id)

        const items: BudgetAnalysisItem[] = []
        
        for (const line of budgetLines || []) {
          if (!line.accounts) continue

          const { data: transactions } = await supabase
            .from('transaction_lines')
            .select('debit, credit')
            .eq('org_id', profile.org_id)
            .eq('account_id', line.account_id)
            .gte('created_at', `${budget.start_date}T00:00:00`)
            .lte('created_at', `${budget.end_date}T23:59:59`)

          const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
          const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0

          let actualAmount = 0
          if (line.accounts.type === 'expense') {
            actualAmount = debitTotal - creditTotal
          } else if (line.accounts.type === 'income') {
            actualAmount = creditTotal - debitTotal
          } else if (line.accounts.type === 'asset') {
            actualAmount = debitTotal - creditTotal
          } else {
            actualAmount = creditTotal - debitTotal
          }

          const budgetAmount = line.amount || 0
          const variance = actualAmount - budgetAmount
          const variancePercent = budgetAmount !== 0 ? (variance / budgetAmount) * 100 : 0
          const utilizationPercent = budgetAmount !== 0 ? (actualAmount / budgetAmount) * 100 : 0

          let alertLevel: 'normal' | 'warning' | 'critical' | 'exceeded' = 'normal'
          if (utilizationPercent >= 100) {
            alertLevel = 'exceeded'
          } else if (utilizationPercent >= 90) {
            alertLevel = 'critical'
          } else if (utilizationPercent >= 75) {
            alertLevel = 'warning'
          }

          items.push({
            account: {
              id: line.account_id,
              name: line.accounts.name,
              code: line.accounts.code,
              type: line.accounts.type
            },
            budget: {
              id: budget.id,
              name: budget.name,
              startDate: budget.start_date,
              endDate: budget.end_date,
              status: budget.status
            },
            budgetAmount,
            actualAmount,
            variance,
            variancePercent,
            utilizationPercent,
            isOverBudget: actualAmount > budgetAmount,
            alertLevel
          })
        }

        const totalBudget = items.reduce((sum, item) => sum + item.budgetAmount, 0)
        const totalActual = items.reduce((sum, item) => sum + item.actualAmount, 0)
        const totalVariance = totalActual - totalBudget
        const totalVariancePercent = totalBudget !== 0 ? (totalVariance / totalBudget) * 100 : 0

        analyses.push({
          totalBudget,
          totalActual,
          totalVariance,
          totalVariancePercent,
          accountsAnalyzed: items.length,
          overBudgetAccounts: items.filter(item => item.isOverBudget).length,
          criticalAccounts: items.filter(item => item.alertLevel === 'critical' || item.alertLevel === 'exceeded').length,
          warningAccounts: items.filter(item => item.alertLevel === 'warning').length,
          items
        })
      }

      return analyses
    },
    enabled: enabled && !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })
}

/**
 * Hook to invalidate budget analysis when bills/transactions change
 */
export const useInvalidateBudgetAnalysis = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return {
    invalidateAll: () => {
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.budgets.analysis(profile?.org_id || '', '') 
      })
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.budgets.allAnalysis(profile?.org_id || '') 
      })
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.budgets.alerts(profile?.org_id || '') 
      })
    },
    invalidateBudget: (budgetId: string) => {
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.budgets.analysis(profile?.org_id || '', budgetId) 
      })
    }
  }
}
