
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus } from 'lucide-react'
import { BudgetsTable } from '@/components/budgets/BudgetsTable'
import { BudgetForm } from '@/components/budgets/BudgetForm'
import { BudgetApprovalDialog } from '@/components/budgets/BudgetApprovalDialog'
import { LoadingPage } from '@/components/ui/loading'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import type { Budget } from '@/types/database'

export default function Budgets() {
  const { profile } = useAuth()
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [selectedBudget, setSelectedBudget] = useState<Budget | null>(null)
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false)

  // Use the standardized query key pattern
  const { data: budgets = [], isLoading, refetch } = useQuery({
    queryKey: ['budgets', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('budgets')
        .select(`
          *,
          accounts (name, code),
          budget_lines (
            id,
            account_id,
            amount,
            notes,
            accounts (name, code)
          ),
          budget_approvals (
            id,
            status,
            approver_id,
            comments,
            rejection_reason,
            actioned_at
          )
        `)
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  const handleEdit = (budget: Budget) => {
    setSelectedBudget(budget)
    setIsFormOpen(true)
  }

  const handleApproval = (budget: Budget) => {
    setSelectedBudget(budget)
    setIsApprovalDialogOpen(true)
  }

  const handleFormClose = () => {
    setIsFormOpen(false)
    setSelectedBudget(null)
    refetch()
  }

  const handleApprovalClose = () => {
    setIsApprovalDialogOpen(false)
    setSelectedBudget(null)
    refetch()
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <LoadingPage text="Loading budgets..." fullScreen={false} />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Budget Management</h1>
          <p className="text-muted-foreground">
            Create, manage, and approve organizational budgets
          </p>
        </div>
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setSelectedBudget(null)}>
              <Plus className="mr-2 h-4 w-4" />
              New Budget
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedBudget ? 'Edit Budget' : 'Create New Budget'}
              </DialogTitle>
            </DialogHeader>
            <BudgetForm 
              budget={selectedBudget} 
              onClose={handleFormClose}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="draft" className="space-y-4">
        <TabsList>
          <TabsTrigger value="draft">Draft Budgets</TabsTrigger>
          <TabsTrigger value="pending_approval">Pending Approval</TabsTrigger>
          <TabsTrigger value="approved">Approved Budgets</TabsTrigger>
          <TabsTrigger value="rejected">Rejected Budgets</TabsTrigger>
        </TabsList>

        <TabsContent value="draft" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Draft Budgets</CardTitle>
              <CardDescription>
                Budgets that are still being prepared
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BudgetsTable
                budgets={budgets.filter(b => b.status === 'draft')}
                onEdit={handleEdit}
                onApproval={handleApproval}
                onRefresh={refetch}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending_approval" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Approval</CardTitle>
              <CardDescription>
                Budgets waiting for approval review
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BudgetsTable
                budgets={budgets.filter(b => b.status === 'pending_approval')}
                onEdit={handleEdit}
                onApproval={handleApproval}
                onRefresh={refetch}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approved" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Approved Budgets</CardTitle>
              <CardDescription>
                Budgets that have been approved for execution
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BudgetsTable
                budgets={budgets.filter(b => b.status === 'approved')}
                onEdit={handleEdit}
                onApproval={handleApproval}
                onRefresh={refetch}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rejected" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Rejected Budgets</CardTitle>
              <CardDescription>
                Budgets that have been rejected and need revision
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BudgetsTable
                budgets={budgets.filter(b => b.status === 'rejected')}
                onEdit={handleEdit}
                onApproval={handleApproval}
                onRefresh={refetch}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {selectedBudget && (
        <BudgetApprovalDialog
          budget={selectedBudget}
          open={isApprovalDialogOpen}
          onClose={handleApprovalClose}
        />
      )}
    </div>
  )
}
