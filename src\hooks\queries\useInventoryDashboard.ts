import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import type { InventoryDashboardData } from '@/types/inventory'

/**
 * Hook to fetch inventory dashboard summary data
 */
export function useInventoryDashboardSummary() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryDashboard.summary(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return null

      const { data, error } = await supabase.rpc('get_inventory_dashboard_summary', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data as InventoryDashboardData
    },
    enabled: !!profile?.org_id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to fetch inventory alerts (low stock, out of stock, etc.)
 */
export function useInventoryAlerts() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryDashboard.alerts(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_inventory_alerts', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

/**
 * Hook to get top selling products
 */
export function useTopSellingProducts(limit: number = 10, dateFrom?: string, dateTo?: string) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['top-selling-products', profile?.org_id, limit, dateFrom, dateTo],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_top_selling_products', {
        org_id_param: profile.org_id,
        limit_param: limit,
        date_from_param: dateFrom || null,
        date_to_param: dateTo || null
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get inventory turnover analysis
 */
export function useInventoryTurnover(dateFrom?: string, dateTo?: string) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['inventory-turnover', profile?.org_id, dateFrom, dateTo],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_inventory_turnover', {
        org_id_param: profile.org_id,
        date_from_param: dateFrom || null,
        date_to_param: dateTo || null
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to get slow moving products
 */
export function useSlowMovingProducts(days: number = 90) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['slow-moving-products', profile?.org_id, days],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_slow_moving_products', {
        org_id_param: profile.org_id,
        days_param: days
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook to get products requiring reorder
 */
export function useProductsRequiringReorder() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['products-requiring-reorder', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_products_requiring_reorder', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

/**
 * Hook to get inventory aging report
 */
export function useInventoryAging() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['inventory-aging', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_inventory_aging', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 30 * 60 * 1000, // 30 minutes
  })
}

/**
 * Hook to get stock level trends
 */
export function useStockLevelTrends(productId?: string, days: number = 30) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['stock-level-trends', profile?.org_id, productId, days],
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase.rpc('get_stock_level_trends', {
        org_id_param: profile.org_id,
        product_id_param: productId || null,
        days_param: days
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
