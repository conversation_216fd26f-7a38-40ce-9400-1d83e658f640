/**
 * Real-time notification subscription hook
 * Replaces polling with Supabase real-time subscriptions
 * Uses singleton pattern to prevent multiple subscriptions
 */

import { useEffect, useRef, useState, useCallback } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import { toast } from '@/components/ui/toast-utils'
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import type { Notification } from '@/types/notifications'

// Global state for singleton pattern
let globalChannel: RealtimeChannel | null = null
let globalPollingInterval: NodeJS.Timeout | null = null
let globalReconnectTimeout: NodeJS.Timeout | null = null
let globalIsConnected = false
let globalIsSubscribing = false
let globalReconnectAttempts = 0
const globalSubscribers = new Set<() => void>()
let globalConfig: RealtimeConfig | null = null

interface RealtimeConfig {
  enableRealtime: boolean
  fallbackToPolling: boolean
  pollingInterval: number
  reconnectAttempts: number
  reconnectDelay: number
}

const DEFAULT_CONFIG: RealtimeConfig = {
  enableRealtime: true,
  fallbackToPolling: true,
  pollingInterval: 30000, // 30 seconds
  reconnectAttempts: 3,
  reconnectDelay: 1000 // 1 second
}

// Global cleanup function
const globalCleanup = () => {
  if (globalChannel) {
    supabase.removeChannel(globalChannel)
    globalChannel = null
  }
  if (globalPollingInterval) {
    clearInterval(globalPollingInterval)
    globalPollingInterval = null
  }
  if (globalReconnectTimeout) {
    clearTimeout(globalReconnectTimeout)
    globalReconnectTimeout = null
  }
  globalIsConnected = false
  globalIsSubscribing = false
  globalReconnectAttempts = 0
  // Notify all subscribers
  globalSubscribers.forEach(callback => callback())
}

export function useNotificationRealtime(config: Partial<RealtimeConfig> = {}) {
  const { profile } = useAuth()
  const queryClient = useQueryClient()
  const [isConnected, setIsConnected] = useState(globalIsConnected)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const [reconnectAttempts, setReconnectAttempts] = useState(globalReconnectAttempts)
  const [isSubscribing, setIsSubscribing] = useState(globalIsSubscribing)

  const finalConfig = { ...DEFAULT_CONFIG, ...config }

  // Update global config if this is the first subscriber or config changed
  if (!globalConfig || JSON.stringify(globalConfig) !== JSON.stringify(finalConfig)) {
    globalConfig = finalConfig
  }

  // Subscribe to global state changes
  useEffect(() => {
    const updateLocalState = () => {
      setIsConnected(globalIsConnected)
      setReconnectAttempts(globalReconnectAttempts)
      setIsSubscribing(globalIsSubscribing)
    }

    globalSubscribers.add(updateLocalState)

    return () => {
      globalSubscribers.delete(updateLocalState)
    }
  }, [])

  // Invalidate notification queries
  const invalidateNotificationQueries = useCallback(() => {
    if (!profile?.id) return

    queryClient.invalidateQueries({
      queryKey: queryKeys.notifications.all(profile.id)
    })
    queryClient.invalidateQueries({
      queryKey: queryKeys.notifications.count(profile.id)
    })
    queryClient.invalidateQueries({
      queryKey: queryKeys.notifications.unread(profile.id)
    })
  }, [profile?.id, queryClient])

  // Handle real-time notification changes
  const handleNotificationChange = useCallback((
    payload: RealtimePostgresChangesPayload<Notification>
  ) => {
    console.log('Real-time notification change:', payload)

    // Invalidate queries for all subscribers
    globalSubscribers.forEach(() => {
      // This will trigger query invalidation in all components using the hook
    })

    switch (payload.eventType) {
      case 'INSERT':
        // New notification created
        invalidateNotificationQueries()

        // Show toast for high priority notifications
        if (payload.new.priority === 'urgent' || payload.new.priority === 'high') {
          toast.info(payload.new.title, {
            description: payload.new.message,
            duration: 5000
          })
        }
        break

      case 'UPDATE':
        // Notification updated (e.g., marked as read)
        invalidateNotificationQueries()
        break

      case 'DELETE':
        // Notification deleted
        invalidateNotificationQueries()
        break
    }
  }, [invalidateNotificationQueries])

  // Setup polling fallback (global)
  const setupPolling = useCallback(() => {
    // Don't setup polling if already polling
    if (globalPollingInterval) {
      return
    }

    globalPollingInterval = setInterval(() => {
      // Invalidate queries for all subscribers
      globalSubscribers.forEach(() => {
        invalidateNotificationQueries()
      })
    }, globalConfig?.pollingInterval || DEFAULT_CONFIG.pollingInterval)

    console.log('Notification polling enabled as fallback')
  }, [invalidateNotificationQueries])

  // Setup real-time subscription (global)
  const setupRealtimeSubscription = useCallback(() => {
    if (!profile?.id || !profile?.org_id || !globalConfig?.enableRealtime || globalIsSubscribing) {
      return
    }

    globalIsSubscribing = true
    globalSubscribers.forEach(callback => callback()) // Notify all subscribers

    // Clean up existing subscription first
    if (globalChannel) {
      supabase.removeChannel(globalChannel)
      globalChannel = null
    }

    // Clear any existing reconnect timeout
    if (globalReconnectTimeout) {
      clearTimeout(globalReconnectTimeout)
      globalReconnectTimeout = null
    }

    try {
      // Create channel for user's notifications
      const channel = supabase
        .channel(`notifications:${profile.id}`) // Single channel per user
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${profile.id}`
          },
          handleNotificationChange
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=is.null,org_id=eq.${profile.org_id}`
          },
          handleNotificationChange
        )
        .subscribe((status) => {
          console.log('Notification subscription status:', status)

          switch (status) {
            case 'SUBSCRIBED':
              globalIsConnected = true
              globalReconnectAttempts = 0
              globalIsSubscribing = false
              setConnectionError(null)
              // Stop polling if real-time is working
              if (globalPollingInterval) {
                clearInterval(globalPollingInterval)
                globalPollingInterval = null
              }
              // Notify all subscribers
              globalSubscribers.forEach(callback => callback())
              break

            case 'CHANNEL_ERROR':
            case 'TIMED_OUT':
            case 'CLOSED':
              globalIsConnected = false
              globalIsSubscribing = false
              setConnectionError(`Connection ${status.toLowerCase()}`)

              // Only attempt reconnection if we haven't exceeded max attempts
              if (globalReconnectAttempts < (globalConfig?.reconnectAttempts || DEFAULT_CONFIG.reconnectAttempts)) {
                globalReconnectAttempts++
                console.log(`Reconnection attempt ${globalReconnectAttempts}/${globalConfig?.reconnectAttempts || DEFAULT_CONFIG.reconnectAttempts}`)
                globalReconnectTimeout = setTimeout(() => {
                  setupRealtimeSubscription()
                }, (globalConfig?.reconnectDelay || DEFAULT_CONFIG.reconnectDelay) * Math.pow(2, globalReconnectAttempts - 1))
              } else {
                console.log('Max reconnection attempts reached, falling back to polling')
                if (globalConfig?.fallbackToPolling) {
                  setupPolling()
                }
              }
              // Notify all subscribers
              globalSubscribers.forEach(callback => callback())
              break
          }
        })

      globalChannel = channel
    } catch (error) {
      console.error('Error setting up real-time subscription:', error)
      setConnectionError('Failed to setup real-time connection')
      globalIsSubscribing = false
      globalSubscribers.forEach(callback => callback())

      if (globalConfig?.fallbackToPolling) {
        setupPolling()
      }
    }
  }, [profile?.id, profile?.org_id, handleNotificationChange, setupPolling])

  // Force reconnection
  const reconnect = useCallback(() => {
    globalCleanup()
    if (profile?.id && globalConfig?.enableRealtime) {
      setupRealtimeSubscription()
    } else if (profile?.id && globalConfig?.fallbackToPolling) {
      setupPolling()
    }
  }, [profile?.id, setupRealtimeSubscription, setupPolling])

  // Setup subscription on mount and profile change
  useEffect(() => {
    // Only setup if no global subscription exists
    if (profile?.id && !globalChannel && !globalPollingInterval) {
      if (globalConfig?.enableRealtime) {
        setupRealtimeSubscription()
      } else if (globalConfig?.fallbackToPolling) {
        setupPolling()
      }
    }

    // Cleanup only when component unmounts (not on every render)
    return () => {
      // Only cleanup if this is the last subscriber
      globalSubscribers.delete(() => {})
      if (globalSubscribers.size === 0) {
        globalCleanup()
      }
    }
  }, [profile?.id, setupPolling, setupRealtimeSubscription]) // Include function dependencies

  return {
    isConnected,
    connectionError,
    reconnectAttempts,
    reconnect,
    isUsingPolling: !isConnected && globalPollingInterval !== null
  }
}

/**
 * Hook for managing notification real-time connection globally
 */
export function useNotificationRealtimeManager() {
  const [globalConfig, setGlobalConfig] = useState<Partial<RealtimeConfig>>({})
  
  const updateConfig = useCallback((newConfig: Partial<RealtimeConfig>) => {
    setGlobalConfig(prev => ({ ...prev, ...newConfig }))
  }, [])

  const enableRealtime = useCallback(() => {
    updateConfig({ enableRealtime: true })
  }, [updateConfig])

  const disableRealtime = useCallback(() => {
    updateConfig({ enableRealtime: false })
  }, [updateConfig])

  const enablePollingFallback = useCallback(() => {
    updateConfig({ fallbackToPolling: true })
  }, [updateConfig])

  const disablePollingFallback = useCallback(() => {
    updateConfig({ fallbackToPolling: false })
  }, [updateConfig])

  const setPollingInterval = useCallback((interval: number) => {
    updateConfig({ pollingInterval: interval })
  }, [updateConfig])

  return {
    config: globalConfig,
    updateConfig,
    enableRealtime,
    disableRealtime,
    enablePollingFallback,
    disablePollingFallback,
    setPollingInterval
  }
}

/**
 * Hook for notification connection status
 */
export function useNotificationConnectionStatus() {
  const [status, setStatus] = useState<{
    isOnline: boolean
    lastSeen: Date | null
    connectionType: 'realtime' | 'polling' | 'offline'
  }>({
    isOnline: navigator.onLine,
    lastSeen: null,
    connectionType: 'offline'
  })

  useEffect(() => {
    const handleOnline = () => {
      setStatus(prev => ({ ...prev, isOnline: true, lastSeen: new Date() }))
    }

    const handleOffline = () => {
      setStatus(prev => ({ ...prev, isOnline: false }))
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const updateConnectionType = useCallback((type: 'realtime' | 'polling' | 'offline') => {
    setStatus(prev => ({ 
      ...prev, 
      connectionType: type,
      lastSeen: type !== 'offline' ? new Date() : prev.lastSeen
    }))
  }, [])

  return {
    ...status,
    updateConnectionType
  }
}
