
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Check, X } from 'lucide-react'
import { toast } from 'sonner'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import type { Budget } from '@/types/database'

const approvalSchema = z.object({
  comments: z.string().optional(),
  rejection_reason: z.string().optional(),
})

interface BudgetApprovalDialogProps {
  budget: Budget
  open: boolean
  onClose: () => void
}

export function BudgetApprovalDialog({ budget, open, onClose }: BudgetApprovalDialogProps) {
  const { profile } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [action, setAction] = useState<'approve' | 'reject' | null>(null)

  const form = useForm<z.infer<typeof approvalSchema>>({
    resolver: zodResolver(approvalSchema),
    defaultValues: {
      comments: '',
      rejection_reason: '',
    },
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const onSubmit = async (values: z.infer<typeof approvalSchema>) => {
    if (!profile?.id || !action) {
      toast.error('Unable to process approval')
      return
    }

    setIsSubmitting(true)

    try {
      // Create approval record
      const { error: approvalError } = await supabase
        .from('budget_approvals')
        .insert({
          budget_id: budget.id,
          approver_id: profile.id,
          status: action === 'approve' ? 'approved' : 'rejected',
          comments: values.comments,
          rejection_reason: action === 'reject' ? values.rejection_reason : null,
        })

      if (approvalError) throw approvalError

      // Update budget status
      const newStatus = action === 'approve' ? 'approved' : 'rejected'
      const { error: budgetError } = await supabase
        .from('budgets')
        .update({ status: newStatus })
        .eq('id', budget.id)

      if (budgetError) throw budgetError

      toast.success(`Budget ${action === 'approve' ? 'approved' : 'rejected'} successfully`)
      onClose()
    } catch (error) {
      console.error('Error processing approval:', error)
      toast.error('Failed to process approval')
    } finally {
      setIsSubmitting(false)
      setAction(null)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Budget Approval - {budget.name}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Budget Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Account:</span>{' '}
                  {budget.accounts ? `${budget.accounts.code} - ${budget.accounts.name}` : 'Not set'}
                </div>
                <div>
                  <span className="font-medium">Period:</span> {budget.period}
                </div>
                <div>
                  <span className="font-medium">Status:</span>{' '}
                  <Badge variant="secondary">{budget.status.toUpperCase()}</Badge>
                </div>
                <div>
                  <span className="font-medium">Start Date:</span>{' '}
                  {new Date(budget.start_date).toLocaleDateString()}
                </div>
                <div>
                  <span className="font-medium">End Date:</span>{' '}
                  {new Date(budget.end_date).toLocaleDateString()}
                </div>
                <div>
                  <span className="font-medium">Total Amount:</span>{' '}
                  {formatCurrency(budget.total_amount)}
                </div>
                <div>
                  <span className="font-medium">Approval Deadline:</span>{' '}
                  {budget.approval_deadline
                    ? new Date(budget.approval_deadline).toLocaleDateString()
                    : 'Not set'
                  }
                </div>
              </div>
              {budget.notes && (
                <div>
                  <span className="font-medium">Notes:</span>
                  <p className="text-sm text-muted-foreground mt-1">{budget.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Budget Lines */}
          {budget.budget_lines && budget.budget_lines.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Budget Line Items</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {budget.budget_lines.map((line) => {
                      // Parse the notes to extract item name and additional notes
                      const notes = line.notes || 'Budget Item'
                      const [itemName, ...additionalNotes] = notes.split(' - ')

                      return (
                        <TableRow key={line.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{itemName}</p>
                              {additionalNotes.length > 0 && (
                                <p className="text-sm text-muted-foreground">{additionalNotes.join(' - ')}</p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(line.amount)}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
                <div className="flex justify-end mt-4 pt-4 border-t">
                  <div className="text-lg font-semibold">
                    Total: {formatCurrency(budget.total_amount)}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="comments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comments</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Add any comments about this budget..."
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {action === 'reject' && (
                <FormField
                  control={form.control}
                  name="rejection_reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rejection Reason *</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Please provide a reason for rejecting this budget..."
                          rows={3}
                          required
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <div className="flex justify-end gap-4 pt-4">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="destructive"
                  disabled={isSubmitting}
                  onClick={() => setAction('reject')}
                >
                  <X className="mr-2 h-4 w-4" />
                  {isSubmitting && action === 'reject' ? 'Rejecting...' : 'Reject'}
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  onClick={() => setAction('approve')}
                >
                  <Check className="mr-2 h-4 w-4" />
                  {isSubmitting && action === 'approve' ? 'Approving...' : 'Approve'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
