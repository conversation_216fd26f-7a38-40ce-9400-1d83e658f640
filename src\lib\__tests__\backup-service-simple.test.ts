import { calculateChecksum, formatBackupSize } from '../backup-service'

// Mock crypto.subtle for testing
const mockCrypto = {
  subtle: {
    digest: jest.fn()
  }
}

Object.defineProperty(global, 'crypto', {
  value: mockCrypto
})

describe('Backup Service Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('calculateChecksum', () => {
    it('should calculate SHA-256 checksum', async () => {
      // Mock the digest function to return a consistent hash
      const mockHash = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8])
      mockCrypto.subtle.digest.mockResolvedValue(mockHash.buffer)

      const result = await calculateChecksum('test data')

      expect(mockCrypto.subtle.digest).toHaveBeenCalledWith(
        'SHA-256',
        expect.any(ArrayBuffer)
      )
      expect(typeof result).toBe('string')
      expect(result).toBe('0102030405060708')
    })

    it('should produce consistent checksums for same data', async () => {
      const mockHash = new Uint8Array([1, 2, 3, 4])
      mockCrypto.subtle.digest.mockResolvedValue(mockHash.buffer)

      const result1 = await calculateChecksum('same data')
      const result2 = await calculateChecksum('same data')

      expect(result1).toBe(result2)
    })

    it('should handle empty string', async () => {
      const mockHash = new Uint8Array([0, 0, 0, 0])
      mockCrypto.subtle.digest.mockResolvedValue(mockHash.buffer)

      const result = await calculateChecksum('')

      expect(typeof result).toBe('string')
      expect(result).toBe('00000000')
    })
  })

  describe('formatBackupSize', () => {
    it('should format bytes correctly', () => {
      expect(formatBackupSize(0)).toBe('0.0 B')
      expect(formatBackupSize(512)).toBe('512.0 B')
      expect(formatBackupSize(1023)).toBe('1023.0 B')
    })

    it('should format kilobytes correctly', () => {
      expect(formatBackupSize(1024)).toBe('1.0 KB')
      expect(formatBackupSize(1536)).toBe('1.5 KB')
      expect(formatBackupSize(2048)).toBe('2.0 KB')
    })

    it('should format megabytes correctly', () => {
      expect(formatBackupSize(1024 * 1024)).toBe('1.0 MB')
      expect(formatBackupSize(1024 * 1024 * 1.5)).toBe('1.5 MB')
      expect(formatBackupSize(1024 * 1024 * 10)).toBe('10.0 MB')
    })

    it('should format gigabytes correctly', () => {
      expect(formatBackupSize(1024 * 1024 * 1024)).toBe('1.0 GB')
      expect(formatBackupSize(1024 * 1024 * 1024 * 2.5)).toBe('2.5 GB')
    })

    it('should format terabytes correctly', () => {
      expect(formatBackupSize(1024 * 1024 * 1024 * 1024)).toBe('1.0 TB')
      expect(formatBackupSize(1024 * 1024 * 1024 * 1024 * 1.2)).toBe('1.2 TB')
    })

    it('should handle very large numbers', () => {
      const veryLarge = 1024 * 1024 * 1024 * 1024 * 1024
      const result = formatBackupSize(veryLarge)
      expect(result).toContain('TB')
      expect(typeof result).toBe('string')
    })
  })
})

describe('Backup Service Configuration', () => {
  it('should have correct environment variables for development', () => {
    // Test that environment variables are properly configured
    expect(process.env.NODE_ENV).toBe('test')
    expect(process.env.VITE_SUPABASE_URL).toBe('http://localhost:54321')
  })

  it('should handle missing environment variables gracefully', () => {
    // This test ensures our service can handle missing env vars
    const originalEnv = process.env.VITE_SUPABASE_URL
    delete process.env.VITE_SUPABASE_URL

    // Test that the service doesn't crash without env vars
    expect(() => {
      // This would be called in the service
      const url = process.env.VITE_SUPABASE_URL || 'fallback-url'
      expect(url).toBe('fallback-url')
    }).not.toThrow()

    // Restore original env
    process.env.VITE_SUPABASE_URL = originalEnv
  })
})

describe('Backup Data Validation', () => {
  it('should validate backup data structure', () => {
    const validBackupData = {
      customers: [{ id: 1, name: 'Test Customer' }],
      invoices: [{ id: 1, amount: 100 }],
      bills: [],
      payments: [],
      accounts: []
    }

    // Test that backup data has required structure
    expect(validBackupData).toHaveProperty('customers')
    expect(validBackupData).toHaveProperty('invoices')
    expect(validBackupData).toHaveProperty('bills')
    expect(validBackupData).toHaveProperty('payments')
    expect(validBackupData).toHaveProperty('accounts')

    expect(Array.isArray(validBackupData.customers)).toBe(true)
    expect(Array.isArray(validBackupData.invoices)).toBe(true)
  })

  it('should handle empty backup data', () => {
    const emptyBackupData = {
      customers: [],
      invoices: [],
      bills: [],
      payments: [],
      accounts: []
    }

    // Test that empty arrays are valid
    Object.values(emptyBackupData).forEach(table => {
      expect(Array.isArray(table)).toBe(true)
      expect(table.length).toBe(0)
    })
  })

  it('should calculate total records correctly', () => {
    const backupData = {
      customers: [{ id: 1 }, { id: 2 }],
      invoices: [{ id: 1 }, { id: 2 }, { id: 3 }],
      bills: [{ id: 1 }],
      payments: [],
      accounts: [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }]
    }

    const totalRecords = Object.values(backupData).reduce(
      (total, table) => total + table.length,
      0
    )

    expect(totalRecords).toBe(10) // 2 + 3 + 1 + 0 + 4
  })
})

describe('Error Handling', () => {
  it('should handle crypto API errors', async () => {
    mockCrypto.subtle.digest.mockRejectedValue(new Error('Crypto API not available'))

    await expect(calculateChecksum('test')).rejects.toThrow('Crypto API not available')
  })

  it('should handle invalid input gracefully', () => {
    // Test formatBackupSize with invalid inputs
    expect(formatBackupSize(-1)).toBe('-1.0 B')
    expect(formatBackupSize(NaN)).toBe('NaN B')
    expect(formatBackupSize(Infinity)).toBe('Infinity B')
  })
})
