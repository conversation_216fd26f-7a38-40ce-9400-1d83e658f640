
import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash } from 'lucide-react'
import { toast } from 'sonner'
import type { TaxRate } from '@/types/database'

interface TaxRatesTableProps {
  taxRates: TaxRate[]
  onEdit: (taxRate: TaxRate) => void
  onRefresh: () => void
}

export function TaxRatesTable({ taxRates, onEdit, onRefresh }: TaxRatesTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (id: string) => {
    setDeletingId(id)
    try {
      const { error } = await supabase
        .from('tax_rates')
        .delete()
        .eq('id', id)

      if (error) throw error
      
      toast.success('Tax rate deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting tax rate:', error)
      toast.error('Failed to delete tax rate')
    } finally {
      setDeletingId(null)
    }
  }

  const formatPercentage = (rate: number) => {
    return `${rate}%`
  }

  if (taxRates.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No tax rates found. Create your first tax rate to get started.
      </div>
    )
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Rate</TableHead>
          <TableHead>URA Code</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Created</TableHead>
          <TableHead className="w-[50px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {taxRates.map((taxRate) => (
          <TableRow key={taxRate.id}>
            <TableCell className="font-medium">{taxRate.name}</TableCell>
            <TableCell>{formatPercentage(taxRate.rate_pct)}</TableCell>
            <TableCell>{taxRate.ura_code || '-'}</TableCell>
            <TableCell>
              <Badge variant={taxRate.is_active ? 'default' : 'secondary'}>
                {taxRate.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </TableCell>
            <TableCell>
              {new Date(taxRate.created_at).toLocaleDateString()}
            </TableCell>
            <TableCell>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(taxRate)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDelete(taxRate.id)}
                    disabled={deletingId === taxRate.id}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
