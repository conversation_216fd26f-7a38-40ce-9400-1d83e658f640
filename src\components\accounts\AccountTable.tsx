
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Plus } from 'lucide-react'
import { AccountRow } from './AccountRow'
import type { Account } from '@/types/database'

interface AccountTableProps {
  accounts: Account[]
  onEdit: (account: Account) => void
  onDelete: (account: Account) => void
  onCreateNew: () => void
}

export const AccountTable = ({ accounts, onEdit, onDelete, onCreateNew }: AccountTableProps) => {
  if (accounts.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground mb-4">No accounts found</p>
        <Button onClick={onCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Create your first account
        </Button>
      </div>
    )
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Code</TableHead>
          <TableHead>Name</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Parent</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {accounts.map((account) => {
          const parentAccount = accounts.find(a => a.id === account.parent_id)
          return (
            <AccountRow
              key={account.id}
              account={account}
              parentAccount={parentAccount}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          )
        })}
      </TableBody>
    </Table>
  )
}
