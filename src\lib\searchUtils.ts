import type { RecentSearch } from '@/types/search'

const RECENT_SEARCHES_KEY = 'kaya-recent-searches'
const MAX_RECENT_SEARCHES = 10
const RECENT_SEARCH_EXPIRY_DAYS = 30

export function getRecentSearches(): RecentSearch[] {
  try {
    const stored = localStorage.getItem(RECENT_SEARCHES_KEY)
    if (!stored) return []
    
    const searches: RecentSearch[] = JSON.parse(stored)
    const now = Date.now()
    const expiryTime = RECENT_SEARCH_EXPIRY_DAYS * 24 * 60 * 60 * 1000
    
    // Filter out expired searches
    const validSearches = searches.filter(search => 
      now - search.timestamp < expiryTime
    )
    
    // Update localStorage if we filtered out any expired searches
    if (validSearches.length !== searches.length) {
      localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(validSearches))
    }
    
    return validSearches.sort((a, b) => b.timestamp - a.timestamp)
  } catch (error) {
    console.error('Error loading recent searches:', error)
    return []
  }
}

export function addRecentSearch(query: string, resultCount: number): void {
  try {
    const searches = getRecentSearches()
    const existingIndex = searches.findIndex(search => search.query === query)
    
    const newSearch: RecentSearch = {
      id: Date.now().toString(),
      query,
      timestamp: Date.now(),
      resultCount
    }
    
    // Remove existing search if found
    if (existingIndex >= 0) {
      searches.splice(existingIndex, 1)
    }
    
    // Add new search at the beginning
    searches.unshift(newSearch)
    
    // Keep only the most recent searches
    const trimmedSearches = searches.slice(0, MAX_RECENT_SEARCHES)
    
    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(trimmedSearches))
  } catch (error) {
    console.error('Error saving recent search:', error)
  }
}

export function removeRecentSearch(id: string): void {
  try {
    const searches = getRecentSearches()
    const filteredSearches = searches.filter(search => search.id !== id)
    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(filteredSearches))
  } catch (error) {
    console.error('Error removing recent search:', error)
  }
}

export function clearRecentSearches(): void {
  try {
    localStorage.removeItem(RECENT_SEARCHES_KEY)
  } catch (error) {
    console.error('Error clearing recent searches:', error)
  }
}

export function formatSearchResultTitle(type: string, name: string): string {
  const typeLabels = {
    customer: 'Customer',
    vendor: 'Vendor', 
    invoice: 'Invoice',
    bill: 'Bill',
    payment: 'Payment'
  }
  
  return `${typeLabels[type as keyof typeof typeLabels] || type} • ${name}`
}

export function getSearchResultIcon(type: string): string {
  const icons = {
    customer: '👤',
    vendor: '🏢',
    invoice: '📄',
    bill: '📋',
    payment: '💰'
  }
  
  return icons[type as keyof typeof icons] || '📄'
}

export function formatCurrency(amount: number, currency: string = 'UGX'): string {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount)
}

export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-UG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
