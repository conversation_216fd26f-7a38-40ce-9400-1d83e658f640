// Comprehensive Error Handling for Backup Operations
// Provides structured error handling, logging, and user notifications

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'
import { toast } from 'sonner'

export enum BackupErrorType {
  VALIDATION_ERROR = 'validation_error',
  PERMISSION_ERROR = 'permission_error',
  NETWORK_ERROR = 'network_error',
  STORAGE_ERROR = 'storage_error',
  ENCRYPTION_ERROR = 'encryption_error',
  DATABASE_ERROR = 'database_error',
  TIMEOUT_ERROR = 'timeout_error',
  QUOTA_ERROR = 'quota_error',
  CORRUPTION_ERROR = 'corruption_error',
  SYSTEM_ERROR = 'system_error'
}

export enum BackupErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface BackupError {
  id: string
  type: BackupErrorType
  severity: BackupErrorSeverity
  message: string
  details?: Record<string, unknown>
  context?: {
    operation: string
    orgId: string
    userId?: string
    backupId?: string
    timestamp: string
  }
  stack?: string
  recoverable: boolean
  retryable: boolean
  userMessage: string
}

export interface ErrorRecoveryAction {
  type: 'retry' | 'rollback' | 'manual_intervention' | 'ignore'
  description: string
  automated: boolean
  execute?: () => Promise<void>
}

/**
 * Comprehensive Backup Error Handler
 */
export class BackupErrorHandler {
  private static readonly ERROR_PATTERNS = {
    [BackupErrorType.NETWORK_ERROR]: [
      /network/i,
      /connection/i,
      /timeout/i,
      /fetch.*failed/i,
      /ECONNREFUSED/i,
      /ENOTFOUND/i
    ],
    [BackupErrorType.STORAGE_ERROR]: [
      /storage/i,
      /bucket/i,
      /upload.*failed/i,
      /download.*failed/i,
      /file.*not.*found/i
    ],
    [BackupErrorType.ENCRYPTION_ERROR]: [
      /encryption/i,
      /decrypt/i,
      /key.*not.*found/i,
      /invalid.*key/i,
      /cipher/i
    ],
    [BackupErrorType.DATABASE_ERROR]: [
      /database/i,
      /sql/i,
      /constraint/i,
      /foreign.*key/i,
      /duplicate.*key/i
    ],
    [BackupErrorType.PERMISSION_ERROR]: [
      /permission/i,
      /unauthorized/i,
      /access.*denied/i,
      /forbidden/i,
      /role.*not.*allowed/i
    ],
    [BackupErrorType.QUOTA_ERROR]: [
      /quota/i,
      /limit.*exceeded/i,
      /storage.*full/i,
      /insufficient.*space/i
    ]
  }

  /**
   * Handle and classify backup errors
   */
  static async handleError(
    error: Error | unknown,
    context: {
      operation: string
      orgId: string
      userId?: string
      backupId?: string
    }
  ): Promise<BackupError> {
    const backupError = this.classifyError(error, context)
    
    // Log the error
    await this.logError(backupError)
    
    // Store error in database for tracking
    await this.storeError(backupError)
    
    // Notify user appropriately
    await this.notifyUser(backupError)
    
    // Attempt automatic recovery if possible
    if (backupError.recoverable) {
      await this.attemptRecovery(backupError)
    }
    
    return backupError
  }

  /**
   * Classify error type and severity
   */
  private static classifyError(
    error: Error | unknown,
    context: {
      operation: string
      orgId: string
      userId?: string
      backupId?: string
    }
  ): BackupError {
    const errorMessage = error instanceof Error ? error.message : String(error)
    const errorStack = error instanceof Error ? error.stack : undefined
    
    // Determine error type
    let errorType = BackupErrorType.SYSTEM_ERROR
    for (const [type, patterns] of Object.entries(this.ERROR_PATTERNS)) {
      if (patterns.some(pattern => pattern.test(errorMessage))) {
        errorType = type as BackupErrorType
        break
      }
    }
    
    // Determine severity
    const severity = this.determineSeverity(errorType, errorMessage, context)
    
    // Determine if recoverable/retryable
    const { recoverable, retryable } = this.determineRecoverability(errorType, errorMessage)
    
    // Generate user-friendly message
    const userMessage = this.generateUserMessage(errorType, errorMessage, context)
    
    return {
      id: crypto.randomUUID(),
      type: errorType,
      severity,
      message: errorMessage,
      details: {
        original_error: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error
      },
      context: {
        ...context,
        timestamp: new Date().toISOString()
      },
      stack: errorStack,
      recoverable,
      retryable,
      userMessage
    }
  }

  /**
   * Determine error severity
   */
  private static determineSeverity(
    errorType: BackupErrorType,
    message: string,
    context: { operation: string }
  ): BackupErrorSeverity {
    // Critical errors that could cause data loss
    if (errorType === BackupErrorType.CORRUPTION_ERROR ||
        (errorType === BackupErrorType.STORAGE_ERROR && context.operation === 'restore')) {
      return BackupErrorSeverity.CRITICAL
    }
    
    // High severity errors that prevent core functionality
    if (errorType === BackupErrorType.ENCRYPTION_ERROR ||
        errorType === BackupErrorType.DATABASE_ERROR ||
        (errorType === BackupErrorType.STORAGE_ERROR && context.operation === 'create')) {
      return BackupErrorSeverity.HIGH
    }
    
    // Medium severity for permission and quota issues
    if (errorType === BackupErrorType.PERMISSION_ERROR ||
        errorType === BackupErrorType.QUOTA_ERROR) {
      return BackupErrorSeverity.MEDIUM
    }
    
    // Low severity for network and timeout issues (usually temporary)
    return BackupErrorSeverity.LOW
  }

  /**
   * Determine if error is recoverable/retryable
   */
  private static determineRecoverability(
    errorType: BackupErrorType,
    message: string
  ): { recoverable: boolean; retryable: boolean } {
    switch (errorType) {
      case BackupErrorType.NETWORK_ERROR:
      case BackupErrorType.TIMEOUT_ERROR:
        return { recoverable: true, retryable: true }
      
      case BackupErrorType.STORAGE_ERROR:
        // Some storage errors are retryable (temporary issues)
        if (message.includes('timeout') || message.includes('temporary')) {
          return { recoverable: true, retryable: true }
        }
        return { recoverable: false, retryable: false }
      
      case BackupErrorType.PERMISSION_ERROR:
        return { recoverable: false, retryable: false }
      
      case BackupErrorType.QUOTA_ERROR:
        return { recoverable: false, retryable: false }
      
      case BackupErrorType.ENCRYPTION_ERROR:
        return { recoverable: false, retryable: false }
      
      case BackupErrorType.CORRUPTION_ERROR:
        return { recoverable: false, retryable: false }
      
      default:
        return { recoverable: false, retryable: true }
    }
  }

  /**
   * Generate user-friendly error message
   */
  private static generateUserMessage(
    errorType: BackupErrorType,
    message: string,
    context: { operation: string }
  ): string {
    const operation = context.operation
    
    switch (errorType) {
      case BackupErrorType.NETWORK_ERROR:
        return `Network connection issue during ${operation}. Please check your internet connection and try again.`
      
      case BackupErrorType.STORAGE_ERROR:
        return `Storage error during ${operation}. The backup files may be temporarily unavailable.`
      
      case BackupErrorType.ENCRYPTION_ERROR:
        return `Encryption error during ${operation}. Please contact your administrator.`
      
      case BackupErrorType.PERMISSION_ERROR:
        return `You don't have permission to perform this ${operation} operation. Please contact your administrator.`
      
      case BackupErrorType.QUOTA_ERROR:
        return `Storage quota exceeded during ${operation}. Please free up space or contact your administrator.`
      
      case BackupErrorType.DATABASE_ERROR:
        return `Database error during ${operation}. Please try again or contact support if the issue persists.`
      
      case BackupErrorType.TIMEOUT_ERROR:
        return `The ${operation} operation timed out. This may be due to a large amount of data. Please try again.`
      
      case BackupErrorType.CORRUPTION_ERROR:
        return `Data corruption detected during ${operation}. Please contact support immediately.`
      
      default:
        return `An unexpected error occurred during ${operation}. Please try again or contact support.`
    }
  }

  /**
   * Log error to audit system
   */
  private static async logError(backupError: BackupError): Promise<void> {
    try {
      await auditLogger.logActivity({
        entity_type: 'backup_error',
        entity_id: backupError.context?.backupId || 'system',
        action: 'error_occurred',
        description: `Backup error: ${backupError.type}`,
        severity: backupError.severity === BackupErrorSeverity.CRITICAL ? 'error' : 'warning',
        category: 'system',
        metadata: {
          error_id: backupError.id,
          error_type: backupError.type,
          error_message: backupError.message,
          operation: backupError.context?.operation,
          recoverable: backupError.recoverable,
          retryable: backupError.retryable
        }
      })
    } catch (error) {
      console.error('Failed to log backup error:', error)
    }
  }

  /**
   * Store error in database for tracking
   */
  private static async storeError(backupError: BackupError): Promise<void> {
    try {
      await supabase
        .from('backup_errors')
        .insert({
          id: backupError.id,
          org_id: backupError.context?.orgId,
          backup_id: backupError.context?.backupId,
          error_type: backupError.type,
          error_message: backupError.message,
          error_details: backupError.details,
          severity: backupError.severity,
          operation: backupError.context?.operation,
          user_id: backupError.context?.userId,
          recoverable: backupError.recoverable,
          retryable: backupError.retryable,
          stack_trace: backupError.stack,
          created_at: backupError.context?.timestamp
        })
    } catch (error) {
      console.error('Failed to store backup error:', error)
    }
  }

  /**
   * Notify user about the error
   */
  private static async notifyUser(backupError: BackupError): Promise<void> {
    try {
      // Show toast notification
      switch (backupError.severity) {
        case BackupErrorSeverity.CRITICAL:
          toast.error(backupError.userMessage, {
            duration: 10000,
            action: {
              label: 'Contact Support',
              onClick: () => window.open('/support', '_blank')
            }
          })
          break
        
        case BackupErrorSeverity.HIGH:
          toast.error(backupError.userMessage, { duration: 8000 })
          break
        
        case BackupErrorSeverity.MEDIUM:
          toast.warning(backupError.userMessage, { duration: 6000 })
          break
        
        case BackupErrorSeverity.LOW:
          toast.info(backupError.userMessage, { duration: 4000 })
          break
      }

      // Create in-app notification for high/critical errors
      if (backupError.severity === BackupErrorSeverity.HIGH || 
          backupError.severity === BackupErrorSeverity.CRITICAL) {
        
        if (backupError.context?.orgId && backupError.context?.userId) {
          await supabase.rpc('create_notification_from_template', {
            template_type: 'backup_error',
            org_id: backupError.context.orgId,
            user_id: backupError.context.userId,
            template_data: {
              error_type: backupError.type,
              error_message: backupError.userMessage,
              operation: backupError.context.operation,
              error_id: backupError.id
            }
          })
        }
      }
    } catch (error) {
      console.error('Failed to notify user about backup error:', error)
    }
  }

  /**
   * Attempt automatic recovery
   */
  private static async attemptRecovery(backupError: BackupError): Promise<void> {
    try {
      if (!backupError.retryable) return

      // Implement retry logic for retryable errors
      console.log(`Attempting recovery for error: ${backupError.id}`)
      
      // This would integrate with the specific operation retry mechanisms
      // For now, just log the attempt
      await auditLogger.logActivity({
        entity_type: 'backup_error',
        entity_id: backupError.id,
        action: 'recovery_attempted',
        description: 'Automatic recovery attempted for backup error',
        severity: 'info',
        category: 'system',
        metadata: {
          error_type: backupError.type,
          operation: backupError.context?.operation
        }
      })
    } catch (error) {
      console.error('Failed to attempt recovery:', error)
    }
  }

  /**
   * Get error statistics for monitoring
   */
  static async getErrorStatistics(orgId: string, timeRange: string = '24h'): Promise<{
    totalErrors: number
    errorsByType: Record<string, number>
    errorsBySeverity: Record<string, number>
    recentErrors: BackupError[]
  }> {
    try {
      const timeFilter = new Date()
      switch (timeRange) {
        case '1h':
          timeFilter.setHours(timeFilter.getHours() - 1)
          break
        case '24h':
          timeFilter.setHours(timeFilter.getHours() - 24)
          break
        case '7d':
          timeFilter.setDate(timeFilter.getDate() - 7)
          break
        default:
          timeFilter.setHours(timeFilter.getHours() - 24)
      }

      const { data: errors, error } = await supabase
        .from('backup_errors')
        .select('*')
        .eq('org_id', orgId)
        .gte('created_at', timeFilter.toISOString())
        .order('created_at', { ascending: false })

      if (error) throw error

      const errorsByType: Record<string, number> = {}
      const errorsBySeverity: Record<string, number> = {}

      errors?.forEach(err => {
        errorsByType[err.error_type] = (errorsByType[err.error_type] || 0) + 1
        errorsBySeverity[err.severity] = (errorsBySeverity[err.severity] || 0) + 1
      })

      return {
        totalErrors: errors?.length || 0,
        errorsByType,
        errorsBySeverity,
        recentErrors: (errors || []).slice(0, 10) // Last 10 errors
      }
    } catch (error) {
      console.error('Failed to get error statistics:', error)
      return {
        totalErrors: 0,
        errorsByType: {},
        errorsBySeverity: {},
        recentErrors: []
      }
    }
  }
}
