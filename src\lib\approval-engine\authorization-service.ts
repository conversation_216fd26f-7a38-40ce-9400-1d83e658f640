import { supabase } from '@/lib/supabase'
import type { 
  RoleApprovalLimit,
  DocumentType,
  UserRole
} from '@/types/database'
import type {
  ApprovalAuthority,
  ApprovalLimitCheck
} from '@/types/approval-workflow'

/**
 * Role-Based Authorization Service
 * Validates user approval authority based on role limits and requirements
 */
export class AuthorizationService {

  /**
   * Check if user has approval authority for a specific amount and document type
   */
  static async checkApprovalAuthority(
    userId: string,
    documentType: DocumentType,
    amount: number,
    currencyCode: string = 'UGX'
  ): Promise<ApprovalAuthority> {
    try {
      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, org_id')
        .eq('id', userId)
        .single()

      if (profileError) throw profileError

      // Get role approval limits
      const limits = await this.getRoleApprovalLimits(
        profile.role,
        documentType,
        currencyCode,
        profile.org_id
      )

      if (!limits) {
        return {
          can_approve: false,
          can_reject: false,
          can_delegate: false,
          reasons: ['No approval limits configured for this role']
        }
      }

      // Check amount limits
      const limitCheck = await this.checkAmountLimits(
        userId,
        profile.role,
        documentType,
        amount,
        currencyCode,
        profile.org_id
      )

      // Check daily and monthly limits
      const dailyCheck = await this.checkDailyLimits(
        userId,
        profile.role,
        documentType,
        amount,
        currencyCode,
        profile.org_id
      )

      const monthlyCheck = await this.checkMonthlyLimits(
        userId,
        profile.role,
        documentType,
        amount,
        currencyCode,
        profile.org_id
      )

      const canApprove = limitCheck.within_limit && dailyCheck.within_limit && monthlyCheck.within_limit
      const reasons: string[] = []

      if (!limitCheck.within_limit) {
        reasons.push(`Amount ${amount} exceeds approval limit of ${limitCheck.limit_amount}`)
      }

      if (!dailyCheck.within_limit) {
        reasons.push(`Amount would exceed daily limit. Remaining: ${dailyCheck.remaining_amount}`)
      }

      if (!monthlyCheck.within_limit) {
        reasons.push(`Amount would exceed monthly limit. Remaining: ${monthlyCheck.remaining_amount}`)
      }

      return {
        can_approve: canApprove,
        can_reject: canApprove, // Can reject if can approve
        can_delegate: canApprove, // Can delegate if can approve
        approval_limit: limitCheck.limit_amount,
        daily_limit_remaining: dailyCheck.remaining_amount,
        monthly_limit_remaining: monthlyCheck.remaining_amount,
        reasons: reasons.length > 0 ? reasons : undefined
      }
    } catch (error) {
      console.error('Error checking approval authority:', error)
      return {
        can_approve: false,
        can_reject: false,
        can_delegate: false,
        reasons: ['Error checking approval authority']
      }
    }
  }

  /**
   * Get role approval limits for specific role and document type
   */
  static async getRoleApprovalLimits(
    role: UserRole,
    documentType: DocumentType,
    currencyCode: string,
    orgId: string
  ): Promise<RoleApprovalLimit | null> {
    try {
      const today = new Date().toISOString().split('T')[0]

      const { data, error } = await supabase
        .from('role_approval_limits')
        .select('*')
        .eq('role', role)
        .eq('document_type', documentType)
        .eq('currency_code', currencyCode)
        .eq('org_id', orgId)
        .eq('is_active', true)
        .lte('effective_from', today)
        .or(`effective_to.is.null,effective_to.gte.${today}`)
        .order('effective_from', { ascending: false })
        .limit(1)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          // No matching record found
          return null
        }
        throw error
      }

      return data
    } catch (error) {
      console.error('Error fetching role approval limits:', error)
      return null
    }
  }

  /**
   * Check amount limits
   */
  private static async checkAmountLimits(
    userId: string,
    role: UserRole,
    documentType: DocumentType,
    amount: number,
    currencyCode: string,
    orgId: string
  ): Promise<ApprovalLimitCheck> {
    try {
      const limits = await this.getRoleApprovalLimits(role, documentType, currencyCode, orgId)

      if (!limits) {
        return {
          within_limit: false,
          limit_type: 'role',
          current_amount: amount
        }
      }

      // If amount_limit is null, it means unlimited
      if (limits.amount_limit === null) {
        return {
          within_limit: true,
          limit_type: 'amount',
          current_amount: amount
        }
      }

      const withinLimit = amount <= limits.amount_limit

      return {
        within_limit: withinLimit,
        limit_type: 'amount',
        current_amount: amount,
        limit_amount: limits.amount_limit,
        remaining_amount: withinLimit ? limits.amount_limit - amount : 0
      }
    } catch (error) {
      console.error('Error checking amount limits:', error)
      return {
        within_limit: false,
        limit_type: 'amount',
        current_amount: amount
      }
    }
  }

  /**
   * Check daily limits
   */
  private static async checkDailyLimits(
    userId: string,
    role: UserRole,
    documentType: DocumentType,
    amount: number,
    currencyCode: string,
    orgId: string
  ): Promise<ApprovalLimitCheck> {
    try {
      const limits = await this.getRoleApprovalLimits(role, documentType, currencyCode, orgId)

      if (!limits || limits.daily_limit === null) {
        return {
          within_limit: true,
          limit_type: 'daily',
          current_amount: amount
        }
      }

      // Get today's approved amounts by this user
      const today = new Date().toISOString().split('T')[0]
      
      const { data: todayApprovals, error } = await supabase
        .from('approval_actions')
        .select(`
          approval_instance:approval_instances!inner(document_amount, currency_code)
        `)
        .eq('approver_id', userId)
        .eq('action', 'approved')
        .gte('action_taken_at', `${today}T00:00:00.000Z`)
        .lt('action_taken_at', `${today}T23:59:59.999Z`)

      if (error) throw error

      const todayTotal = (todayApprovals || []).reduce((sum, approval) => {
        if (approval.approval_instance.currency_code === currencyCode) {
          return sum + (approval.approval_instance.document_amount || 0)
        }
        return sum
      }, 0)

      const totalWithNewAmount = todayTotal + amount
      const withinLimit = totalWithNewAmount <= limits.daily_limit

      return {
        within_limit: withinLimit,
        limit_type: 'daily',
        current_amount: totalWithNewAmount,
        limit_amount: limits.daily_limit,
        remaining_amount: withinLimit ? limits.daily_limit - totalWithNewAmount : 0
      }
    } catch (error) {
      console.error('Error checking daily limits:', error)
      return {
        within_limit: false,
        limit_type: 'daily',
        current_amount: amount
      }
    }
  }

  /**
   * Check monthly limits
   */
  private static async checkMonthlyLimits(
    userId: string,
    role: UserRole,
    documentType: DocumentType,
    amount: number,
    currencyCode: string,
    orgId: string
  ): Promise<ApprovalLimitCheck> {
    try {
      const limits = await this.getRoleApprovalLimits(role, documentType, currencyCode, orgId)

      if (!limits || limits.monthly_limit === null) {
        return {
          within_limit: true,
          limit_type: 'monthly',
          current_amount: amount
        }
      }

      // Get this month's approved amounts by this user
      const now = new Date()
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString()
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString()
      
      const { data: monthApprovals, error } = await supabase
        .from('approval_actions')
        .select(`
          approval_instance:approval_instances!inner(document_amount, currency_code)
        `)
        .eq('approver_id', userId)
        .eq('action', 'approved')
        .gte('action_taken_at', monthStart)
        .lte('action_taken_at', monthEnd)

      if (error) throw error

      const monthTotal = (monthApprovals || []).reduce((sum, approval) => {
        if (approval.approval_instance.currency_code === currencyCode) {
          return sum + (approval.approval_instance.document_amount || 0)
        }
        return sum
      }, 0)

      const totalWithNewAmount = monthTotal + amount
      const withinLimit = totalWithNewAmount <= limits.monthly_limit

      return {
        within_limit: withinLimit,
        limit_type: 'monthly',
        current_amount: totalWithNewAmount,
        limit_amount: limits.monthly_limit,
        remaining_amount: withinLimit ? limits.monthly_limit - totalWithNewAmount : 0
      }
    } catch (error) {
      console.error('Error checking monthly limits:', error)
      return {
        within_limit: false,
        limit_type: 'monthly',
        current_amount: amount
      }
    }
  }

  /**
   * Create or update role approval limits
   */
  static async setRoleApprovalLimits(
    orgId: string,
    role: UserRole,
    documentType: DocumentType,
    limits: {
      amount_limit?: number | null
      daily_limit?: number | null
      monthly_limit?: number | null
      currency_code?: string
      effective_from?: string
      effective_to?: string | null
    },
    createdBy: string
  ): Promise<RoleApprovalLimit> {
    try {
      const { data, error } = await supabase
        .from('role_approval_limits')
        .insert({
          org_id: orgId,
          role: role,
          document_type: documentType,
          currency_code: limits.currency_code || 'UGX',
          amount_limit: limits.amount_limit,
          daily_limit: limits.daily_limit,
          monthly_limit: limits.monthly_limit,
          effective_from: limits.effective_from || new Date().toISOString().split('T')[0],
          effective_to: limits.effective_to,
          is_active: true,
          created_by: createdBy
        })
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error setting role approval limits:', error)
      throw error
    }
  }

  /**
   * Get all role approval limits for an organization
   */
  static async getAllRoleApprovalLimits(orgId: string): Promise<RoleApprovalLimit[]> {
    try {
      const { data, error } = await supabase
        .from('role_approval_limits')
        .select('*')
        .eq('org_id', orgId)
        .eq('is_active', true)
        .order('role')
        .order('document_type')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching role approval limits:', error)
      return []
    }
  }

  /**
   * Check if user can approve a specific step in a workflow
   */
  static async canApproveStep(
    userId: string,
    stepRequiredRoles: UserRole[],
    allowSelfApproval: boolean,
    submittedBy: string
  ): Promise<boolean> {
    try {
      // Check if self-approval is allowed
      if (!allowSelfApproval && userId === submittedBy) {
        return false
      }

      // Get user role
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single()

      if (error) throw error

      // Check if user's role is in required roles
      return stepRequiredRoles.includes(profile.role)
    } catch (error) {
      console.error('Error checking step approval authority:', error)
      return false
    }
  }
}
