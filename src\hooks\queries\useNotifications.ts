import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import { toast } from '@/components/ui/toast-utils'
import { useNotificationRealtime } from '@/hooks/useNotificationRealtime'
import type {
  Notification,
  NotificationPreference,
  NotificationTemplate,
  CreateNotificationPayload,
  UpdateNotificationPayload,
  NotificationFilters,
  NotificationPreferencePayload,
  NotificationWithMeta,
  NotificationStats
} from '@/types/notifications'
import { getNotificationTimeAgo, isNotificationExpired, NOTIFICATION_CONFIG } from '@/types/notifications'

/**
 * Hook to fetch user notifications with real-time updates
 */
export const useNotifications = (filters?: NotificationFilters) => {
  const { profile } = useAuth()

  // Setup real-time subscription
  const { isConnected, isUsingPolling } = useNotificationRealtime({
    enableRealtime: true,
    fallbackToPolling: true
  })

  return useQuery({
    queryKey: filters
      ? queryKeys.notifications.filtered(profile?.id || '', filters)
      : queryKeys.notifications.all(profile?.id || ''),
    queryFn: async (): Promise<NotificationWithMeta[]> => {
      if (!profile?.id) return []

      try {
        let query = supabase
          .from('notifications')
          .select('*')
          .or(`user_id.eq.${profile.id},and(user_id.is.null,org_id.eq.${profile.org_id})`)
          .order('created_at', { ascending: false })
          .limit(NOTIFICATION_CONFIG.MAX_NOTIFICATIONS_DISPLAY)

      // Apply filters
      if (filters) {
        if (filters.category) {
          query = query.eq('category', filters.category)
        }
        if (filters.priority) {
          query = query.eq('priority', filters.priority)
        }
        if (filters.is_read !== undefined) {
          query = query.eq('is_read', filters.is_read)
        }
        if (filters.is_archived !== undefined) {
          query = query.eq('is_archived', filters.is_archived)
        }
        if (filters.entity_type) {
          query = query.eq('entity_type', filters.entity_type)
        }
        if (filters.date_from) {
          query = query.gte('created_at', filters.date_from)
        }
        if (filters.date_to) {
          query = query.lte('created_at', filters.date_to)
        }
      }

        const { data, error } = await query

        if (error) {
          // If table doesn't exist, return empty array
          if (error.message?.includes('relation "notifications" does not exist')) {
            console.warn('Notifications table does not exist yet. Please run the migration.')
            return []
          }
          throw error
        }

        // Add computed properties
        return (data || []).map(notification => ({
          ...notification,
          isExpired: isNotificationExpired(notification),
          timeAgo: getNotificationTimeAgo(notification.created_at),
        }))
      } catch (error) {
        console.error('Error fetching notifications:', error)
        return []
      }
    },
    enabled: !!profile?.id,
    staleTime: isConnected ? 5 * 60 * 1000 : 30 * 1000, // 5 minutes if real-time, 30 seconds if polling
    refetchInterval: isConnected ? false : NOTIFICATION_CONFIG.POLLING_INTERVAL, // Disable polling if real-time is connected
  })
}

/**
 * Hook to fetch unread notifications count with real-time updates
 */
export const useNotificationCount = () => {
  const { profile } = useAuth()

  // Setup real-time subscription
  const { isConnected } = useNotificationRealtime({
    enableRealtime: true,
    fallbackToPolling: true
  })

  return useQuery({
    queryKey: queryKeys.notifications.count(profile?.id || ''),
    queryFn: async (): Promise<number> => {
      if (!profile?.id) return 0

      try {
        const { count, error } = await supabase
          .from('notifications')
          .select('*', { count: 'exact', head: true })
          .or(`user_id.eq.${profile.id},and(user_id.is.null,org_id.eq.${profile.org_id})`)
          .eq('is_read', false)
          .eq('is_archived', false)

        if (error) {
          // If table doesn't exist, return 0
          if (error.message?.includes('relation "notifications" does not exist')) {
            return 0
          }
          throw error
        }
        return count || 0
      } catch (error) {
        console.error('Error fetching notification count:', error)
        return 0
      }
    },
    enabled: !!profile?.id,
    staleTime: isConnected ? 2 * 60 * 1000 : 10 * 1000, // 2 minutes if real-time, 10 seconds if polling
    refetchInterval: isConnected ? false : NOTIFICATION_CONFIG.POLLING_INTERVAL, // Disable polling if real-time is connected
  })
}

/**
 * Hook to fetch notification statistics
 */
export const useNotificationStats = () => {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['notification-stats', profile?.id],
    queryFn: async (): Promise<NotificationStats> => {
      if (!profile?.id) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('notifications')
        .select('category, priority, is_read')
        .or(`user_id.eq.${profile.id},and(user_id.is.null,org_id.eq.${profile.org_id})`)
        .eq('is_archived', false)

      if (error) throw error

      const notifications = data || []
      const total = notifications.length
      const unread = notifications.filter(n => !n.is_read).length

      const by_category = notifications.reduce((acc, n) => {
        acc[n.category as keyof typeof acc] = (acc[n.category as keyof typeof acc] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const by_priority = notifications.reduce((acc, n) => {
        acc[n.priority as keyof typeof acc] = (acc[n.priority as keyof typeof acc] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      return {
        total,
        unread,
        by_category: by_category as Record<string, number>,
        by_priority: by_priority as Record<string, number>,
      }
    },
    enabled: !!profile?.id,
    staleTime: 60 * 1000, // 1 minute
  })
}

/**
 * Hook to mark notification as read
 */
export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await supabase
        .from('notifications')
        .update({ 
          is_read: true, 
          read_at: new Date().toISOString() 
        })
        .eq('id', notificationId)

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate notification queries
      if (profile?.id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.notifications.all(profile.id) 
        })
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.notifications.count(profile.id) 
        })
      }
    },
    onError: (error) => {
      console.error('Error marking notification as read:', error)
      toast.error('Failed to mark notification as read')
    },
  })
}

/**
 * Hook to mark all notifications as read
 */
export const useMarkAllNotificationsAsRead = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async (notificationIds?: string[]) => {
      if (!profile?.id) throw new Error('User not authenticated')

      let query = supabase
        .from('notifications')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })

      if (notificationIds && notificationIds.length > 0) {
        // Mark specific notifications as read
        query = query.in('id', notificationIds)
      } else {
        // Mark all unread notifications as read
        query = query
          .or(`user_id.eq.${profile.id},and(user_id.is.null,org_id.eq.${profile.org_id})`)
          .eq('is_read', false)
      }

      const { error } = await query

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate notification queries
      if (profile?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.all(profile.id)
        })
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.count(profile.id)
        })
      }
      toast.success('Notifications marked as read')
    },
    onError: (error) => {
      console.error('Error marking notifications as read:', error)
      toast.error('Failed to mark notifications as read')
    },
  })
}

/**
 * Hook to archive notification
 */
export const useArchiveNotification = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await supabase
        .from('notifications')
        .update({ is_archived: true })
        .eq('id', notificationId)

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate notification queries
      if (profile?.id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.notifications.all(profile.id) 
        })
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.notifications.count(profile.id) 
        })
      }
      toast.success('Notification archived')
    },
    onError: (error) => {
      console.error('Error archiving notification:', error)
      toast.error('Failed to archive notification')
    },
  })
}

/**
 * Hook to create a new notification
 */
export const useCreateNotification = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: CreateNotificationPayload) => {
      try {
        const { data, error } = await supabase
          .from('notifications')
          .insert(payload)
          .select()
          .single()

        if (error) {
          // If table doesn't exist, provide helpful error message
          if (error.message?.includes('relation "notifications" does not exist')) {
            throw new Error('Notifications table does not exist. Please run the database migration first.')
          }
          throw error
        }
        return data
      } catch (error) {
        console.error('Error creating notification:', error)
        throw error
      }
    },
    onSuccess: (data) => {
      // Invalidate notification queries for the target user
      if (data.user_id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.all(data.user_id)
        })
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.count(data.user_id)
        })
      }
    },
    onError: (error: Error) => {
      console.error('Error creating notification:', error)
      const message = error.message?.includes('does not exist')
        ? 'Notification system not set up. Please contact administrator.'
        : 'Failed to create notification'
      toast.error(message)
    },
  })
}

/**
 * Hook to fetch user notification preferences
 */
export const useNotificationPreferences = () => {
  const { profile } = useAuth()

  return useQuery({
    queryKey: queryKeys.notifications.preferences(profile?.id || ''),
    queryFn: async (): Promise<NotificationPreference[]> => {
      if (!profile?.id) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', profile.id)

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to update single notification preference
 */
export const useUpdateNotificationPreference = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async (payload: NotificationPreferencePayload) => {
      const { data, error } = await supabase
        .from('notification_preferences')
        .upsert(payload, { onConflict: 'user_id,notification_type' })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      // Invalidate preferences query
      if (profile?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.preferences(profile.id)
        })
      }
      toast.success('Notification preference updated')
    },
    onError: (error) => {
      console.error('Error updating notification preference:', error)
      toast.error('Failed to update notification preference')
    },
  })
}

/**
 * Hook to bulk update notification preferences
 */
export const useBulkUpdateNotificationPreferences = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async (preferences: Array<{
      notification_type: string
      enabled?: boolean
      email_enabled?: boolean
      in_app_enabled?: boolean
    }>) => {
      const updates = preferences.map(pref => ({
        user_id: profile?.id,
        ...pref
      }))

      const { data, error } = await supabase
        .from('notification_preferences')
        .upsert(updates, { onConflict: 'user_id,notification_type' })
        .select()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      // Invalidate preferences query
      if (profile?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.preferences(profile.id)
        })
      }
    },
    onError: (error) => {
      console.error('Error bulk updating notification preferences:', error)
      throw error
    },
  })
}

/**
 * Hook to fetch notification templates
 */
export const useNotificationTemplates = () => {
  return useQuery({
    queryKey: queryKeys.notifications.templates(),
    queryFn: async (): Promise<NotificationTemplate[]> => {
      const { data, error } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('is_active', true)
        .order('category', { ascending: true })

      if (error) throw error
      return data || []
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to bulk archive notifications
 */
export const useBulkArchiveNotifications = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async (notificationIds: string[]) => {
      if (!profile?.id) throw new Error('User not authenticated')
      if (notificationIds.length === 0) throw new Error('No notifications selected')

      const { error } = await supabase
        .from('notifications')
        .update({ is_archived: true })
        .in('id', notificationIds)
        .or(`user_id.eq.${profile.id},and(user_id.is.null,org_id.eq.${profile.org_id})`)

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate all notification queries
      if (profile?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.all(profile.id)
        })
      }
    },
    onError: (error) => {
      console.error('Error archiving notifications:', error)
      throw error
    },
  })
}

/**
 * Hook to delete a single notification
 */
export const useDeleteNotification = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async (notificationId: string) => {
      if (!profile?.id) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', profile.id) // Only allow deleting own notifications

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate notification queries
      if (profile?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.all(profile.id)
        })
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.count(profile.id)
        })
      }
      toast.success('Notification deleted')
    },
    onError: (error) => {
      console.error('Error deleting notification:', error)
      toast.error('Failed to delete notification')
    },
  })
}

/**
 * Hook to bulk delete notifications
 */
export const useBulkDeleteNotifications = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async (notificationIds: string[]) => {
      if (!profile?.id) throw new Error('User not authenticated')
      if (notificationIds.length === 0) throw new Error('No notifications selected')

      const { error } = await supabase
        .from('notifications')
        .delete()
        .in('id', notificationIds)
        .eq('user_id', profile.id) // Only allow deleting own notifications

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate all notification queries
      if (profile?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.all(profile.id)
        })
      }
    },
    onError: (error) => {
      console.error('Error deleting notifications:', error)
      throw error
    },
  })
}

/**
 * Hook to snooze notification
 */
export const useSnoozeNotification = () => {
  const queryClient = useQueryClient()
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async ({
      notificationId,
      duration,
      unit
    }: {
      notificationId: string
      duration: number
      unit: 'minutes' | 'hours' | 'days'
    }) => {
      if (!profile?.id) throw new Error('User not authenticated')

      // Calculate snooze until time
      const now = new Date()
      const snoozeUntil = new Date(now)

      switch (unit) {
        case 'minutes':
          snoozeUntil.setMinutes(now.getMinutes() + duration)
          break
        case 'hours':
          snoozeUntil.setHours(now.getHours() + duration)
          break
        case 'days':
          snoozeUntil.setDate(now.getDate() + duration)
          break
      }

      // Update notification with snooze data
      const { error } = await supabase
        .from('notifications')
        .update({
          data: {
            snoozed_until: snoozeUntil.toISOString(),
            is_snoozed: true
          },
          is_read: false // Unread when snoozed
        })
        .eq('id', notificationId)
        .or(`user_id.eq.${profile.id},and(user_id.is.null,org_id.eq.${profile.org_id})`)

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate notification queries
      if (profile?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.notifications.all(profile.id)
        })
      }
    },
    onError: (error) => {
      console.error('Error snoozing notification:', error)
      throw error
    },
  })
}

/**
 * Hook to forward notification
 */
export const useForwardNotification = () => {
  const { profile } = useAuth()

  return useMutation({
    mutationFn: async ({
      notificationId,
      email,
      message
    }: {
      notificationId: string
      email: string
      message?: string
    }) => {
      if (!profile?.id) throw new Error('User not authenticated')

      // Get notification details
      const { data: notification, error: fetchError } = await supabase
        .from('notifications')
        .select('*')
        .eq('id', notificationId)
        .single()

      if (fetchError) throw fetchError

      // Send email via email service
      const emailData = {
        to: email,
        subject: `Forwarded Notification: ${notification.title}`,
        html: `
          <h2>Forwarded Notification</h2>
          <p><strong>From:</strong> ${profile.full_name || profile.email}</p>
          ${message ? `<p><strong>Message:</strong> ${message}</p>` : ''}
          <hr>
          <h3>${notification.title}</h3>
          <p>${notification.message}</p>
          <p><small>Sent on: ${new Date(notification.created_at).toLocaleString()}</small></p>
        `
      }

      // For now, just log the email data (implement actual email sending)
      console.log('Would send email:', emailData)

      // In a real implementation, you would call your email service here
      // const response = await emailService.send(emailData)
    },
    onError: (error) => {
      console.error('Error forwarding notification:', error)
      throw error
    },
  })
}
