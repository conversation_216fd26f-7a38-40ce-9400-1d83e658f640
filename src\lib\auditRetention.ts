import { supabase } from '@/lib/supabase'
import type { AuditLogRetentionPolicy } from '@/types/audit'

export class AuditRetentionManager {
  private static instance: AuditRetentionManager
  private retentionPolicies: Map<string, AuditLogRetentionPolicy> = new Map()

  static getInstance(): AuditRetentionManager {
    if (!AuditRetentionManager.instance) {
      AuditRetentionManager.instance = new AuditRetentionManager()
    }
    return AuditRetentionManager.instance
  }

  // Default retention policies by entity type
  private getDefaultRetentionPolicy(entityType: string): AuditLogRetentionPolicy {
    const financialEntities = ['payments', 'invoices', 'bills', 'journal_entries', 'budgets']
    const securityEntities = ['authentication', 'authorization', 'security']
    
    if (financialEntities.includes(entityType)) {
      return {
        retention_days: 2555, // 7 years for financial records
        archive_after_days: 1095, // 3 years
        auto_purge_enabled: false,
        compliance_requirements: ['SOX', 'Tax Regulations']
      }
    }
    
    if (securityEntities.includes(entityType)) {
      return {
        retention_days: 1095, // 3 years for security logs
        archive_after_days: 365, // 1 year
        auto_purge_enabled: false,
        compliance_requirements: ['Security Standards', 'GDPR']
      }
    }
    
    // Default for other entities
    return {
      retention_days: 365, // 1 year
      archive_after_days: 90, // 3 months
      auto_purge_enabled: true,
      compliance_requirements: []
    }
  }

  setRetentionPolicy(entityType: string, policy: AuditLogRetentionPolicy) {
    this.retentionPolicies.set(entityType, policy)
  }

  getRetentionPolicy(entityType: string): AuditLogRetentionPolicy {
    return this.retentionPolicies.get(entityType) || this.getDefaultRetentionPolicy(entityType)
  }

  async archiveOldLogs(orgId: string, entityType?: string): Promise<{
    archived: number
    errors: string[]
  }> {
    const results = { archived: 0, errors: [] as string[] }

    try {
      let query = supabase
        .from('audit_logs')
        .select('id, entity_type, created_at')
        .eq('org_id', orgId)

      if (entityType) {
        query = query.eq('entity_type', entityType)
      }

      const { data: logs, error } = await query

      if (error) {
        results.errors.push(`Failed to fetch logs: ${error.message}`)
        return results
      }

      if (!logs || logs.length === 0) {
        return results
      }

      // Group logs by entity type
      const logsByType = logs.reduce((acc, log) => {
        if (!acc[log.entity_type]) {
          acc[log.entity_type] = []
        }
        acc[log.entity_type].push(log)
        return acc
      }, {} as Record<string, typeof logs>)

      // Process each entity type
      for (const [type, typeLogs] of Object.entries(logsByType)) {
        const policy = this.getRetentionPolicy(type)
        const archiveDate = new Date()
        archiveDate.setDate(archiveDate.getDate() - policy.archive_after_days)

        const logsToArchive = typeLogs.filter(log => 
          new Date(log.created_at) < archiveDate
        )

        if (logsToArchive.length > 0) {
          // In a real implementation, you would move these to an archive table
          // For now, we'll just mark them as archived
          const { error: archiveError } = await supabase
            .from('audit_logs')
            .update({ 
              // Add an archived flag if it exists in your schema
              // archived: true,
              // archived_at: new Date().toISOString()
            })
            .in('id', logsToArchive.map(log => log.id))

          if (archiveError) {
            results.errors.push(`Failed to archive ${type} logs: ${archiveError.message}`)
          } else {
            results.archived += logsToArchive.length
          }
        }
      }

    } catch (error) {
      results.errors.push(`Archive operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    return results
  }

  async purgeExpiredLogs(orgId: string, entityType?: string): Promise<{
    purged: number
    errors: string[]
  }> {
    const results = { purged: 0, errors: [] as string[] }

    try {
      let query = supabase
        .from('audit_logs')
        .select('id, entity_type, created_at')
        .eq('org_id', orgId)

      if (entityType) {
        query = query.eq('entity_type', entityType)
      }

      const { data: logs, error } = await query

      if (error) {
        results.errors.push(`Failed to fetch logs: ${error.message}`)
        return results
      }

      if (!logs || logs.length === 0) {
        return results
      }

      // Group logs by entity type
      const logsByType = logs.reduce((acc, log) => {
        if (!acc[log.entity_type]) {
          acc[log.entity_type] = []
        }
        acc[log.entity_type].push(log)
        return acc
      }, {} as Record<string, typeof logs>)

      // Process each entity type
      for (const [type, typeLogs] of Object.entries(logsByType)) {
        const policy = this.getRetentionPolicy(type)
        
        // Only purge if auto-purge is enabled
        if (!policy.auto_purge_enabled) {
          continue
        }

        const purgeDate = new Date()
        purgeDate.setDate(purgeDate.getDate() - policy.retention_days)

        const logsToPurge = typeLogs.filter(log => 
          new Date(log.created_at) < purgeDate
        )

        if (logsToPurge.length > 0) {
          const { error: purgeError } = await supabase
            .from('audit_logs')
            .delete()
            .in('id', logsToPurge.map(log => log.id))

          if (purgeError) {
            results.errors.push(`Failed to purge ${type} logs: ${purgeError.message}`)
          } else {
            results.purged += logsToPurge.length
          }
        }
      }

    } catch (error) {
      results.errors.push(`Purge operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    return results
  }

  async getRetentionStatus(orgId: string): Promise<{
    total_logs: number
    logs_by_age: {
      current: number // < 30 days
      recent: number // 30-90 days
      old: number // 90-365 days
      very_old: number // > 365 days
    }
    logs_by_type: Record<string, {
      count: number
      oldest: string
      newest: string
      can_archive: number
      can_purge: number
    }>
  }> {
    const { data: logs, error } = await supabase
      .from('audit_logs')
      .select('entity_type, created_at')
      .eq('org_id', orgId)

    if (error || !logs) {
      throw new Error(`Failed to fetch retention status: ${error?.message}`)
    }

    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)

    const logs_by_age = {
      current: 0,
      recent: 0,
      old: 0,
      very_old: 0
    }

    const logs_by_type: Record<string, {
      count: number;
      oldest: string;
      newest: string;
      can_archive: number;
      can_purge: number;
      dates: Date[];
    }> = {}

    logs.forEach(log => {
      const logDate = new Date(log.created_at)
      
      // Age categorization
      if (logDate > thirtyDaysAgo) {
        logs_by_age.current++
      } else if (logDate > ninetyDaysAgo) {
        logs_by_age.recent++
      } else if (logDate > oneYearAgo) {
        logs_by_age.old++
      } else {
        logs_by_age.very_old++
      }

      // Type categorization
      if (!logs_by_type[log.entity_type]) {
        logs_by_type[log.entity_type] = {
          count: 0,
          oldest: log.created_at,
          newest: log.created_at,
          can_archive: 0,
          can_purge: 0,
          dates: []
        }
      }

      logs_by_type[log.entity_type].count++
      logs_by_type[log.entity_type].dates.push(logDate)
      
      if (logDate < new Date(logs_by_type[log.entity_type].oldest)) {
        logs_by_type[log.entity_type].oldest = log.created_at
      }
      if (logDate > new Date(logs_by_type[log.entity_type].newest)) {
        logs_by_type[log.entity_type].newest = log.created_at
      }
    })

    // Calculate archive and purge counts
    Object.keys(logs_by_type).forEach(entityType => {
      const policy = this.getRetentionPolicy(entityType)
      const archiveDate = new Date(now.getTime() - policy.archive_after_days * 24 * 60 * 60 * 1000)
      const purgeDate = new Date(now.getTime() - policy.retention_days * 24 * 60 * 60 * 1000)

      logs_by_type[entityType].can_archive = logs_by_type[entityType].dates.filter(
        (date: Date) => date < archiveDate
      ).length

      logs_by_type[entityType].can_purge = policy.auto_purge_enabled 
        ? logs_by_type[entityType].dates.filter((date: Date) => date < purgeDate).length
        : 0

      // Clean up temporary dates array
      delete logs_by_type[entityType].dates
    })

    return {
      total_logs: logs.length,
      logs_by_age,
      logs_by_type
    }
  }

  async scheduleRetentionJob(orgId: string): Promise<void> {
    // In a real implementation, this would schedule a background job
    // For now, we'll just run the retention process immediately
    console.log(`Scheduling retention job for organization ${orgId}`)
    
    try {
      const archiveResults = await this.archiveOldLogs(orgId)
      const purgeResults = await this.purgeExpiredLogs(orgId)
      
      console.log(`Retention job completed:`, {
        archived: archiveResults.archived,
        purged: purgeResults.purged,
        errors: [...archiveResults.errors, ...purgeResults.errors]
      })
    } catch (error) {
      console.error('Retention job failed:', error)
    }
  }
}

// Export singleton instance
export const auditRetentionManager = AuditRetentionManager.getInstance()
