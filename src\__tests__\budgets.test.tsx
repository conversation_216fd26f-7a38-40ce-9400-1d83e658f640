import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BudgetForm } from '@/components/budgets/BudgetForm'
import { useBudgetAnalysis, useBudgetAlerts } from '@/hooks/queries'
import type { Budget, Account } from '@/types/database'

// Mock the hooks
jest.mock('@/hooks/queries', () => ({
  useBudgetAnalysis: jest.fn(),
  useBudgetAlerts: jest.fn(),
  useCreateBudget: jest.fn(),
  useUpdateBudget: jest.fn(),
  useActiveAccounts: jest.fn(),
}))

jest.mock('@/hooks/useAuthHook', () => ({
  useAuth: () => ({
    profile: { org_id: 'test-org-id' }
  })
}))

jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}))

const mockUseBudgetAnalysis = useBudgetAnalysis as jest.MockedFunction<typeof useBudgetAnalysis>
const mockUseBudgetAlerts = useBudgetAlerts as jest.MockedFunction<typeof useBudgetAlerts>

const mockAccounts: Account[] = [
  {
    id: 'account-1',
    name: 'Office Supplies',
    code: '5100',
    type: 'expense',
    org_id: 'test-org-id',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    description: null,
    parent_id: null
  },
  {
    id: 'account-2',
    name: 'Marketing Expenses',
    code: '5200',
    type: 'expense',
    org_id: 'test-org-id',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    description: null,
    parent_id: null
  }
]

describe('Budget Management', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    mockUseBudgetAnalysis.mockReturnValue({
      data: {
        totalBudget: 10000,
        totalActual: 7500,
        totalVariance: -2500,
        totalVariancePercent: -25,
        accountsAnalyzed: 2,
        overBudgetAccounts: 0,
        criticalAccounts: 0,
        warningAccounts: 1,
        items: []
      },
      isLoading: false,
      error: null,
    })

    mockUseBudgetAlerts.mockReturnValue({
      data: {
        totalAlerts: 1,
        warningAlerts: 1,
        criticalAlerts: 0,
        exceededAlerts: 0,
        alerts: []
      },
      isLoading: false,
      error: null,
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    )
  }

  describe('BudgetForm', () => {
    it('renders create budget form correctly', () => {
      renderWithProviders(
        <BudgetForm
          budget={null}
          onClose={jest.fn()}
        />
      )

      expect(screen.getByText('Budget Name')).toBeInTheDocument()
      expect(screen.getByText('Account')).toBeInTheDocument()
      expect(screen.getByText('Period')).toBeInTheDocument()
      expect(screen.getByText('Start Date')).toBeInTheDocument()
      expect(screen.getByText('End Date')).toBeInTheDocument()
    })

    it('validates required fields', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <BudgetForm
          budget={null}
          onClose={jest.fn()}
        />
      )

      const submitButton = screen.getByRole('button', { name: /create budget/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/budget name is required/i)).toBeInTheDocument()
      })
    })

    it('validates date range', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <BudgetForm
          budget={null}
          onClose={jest.fn()}
        />
      )

      const startDateInput = screen.getByLabelText(/start date/i)
      await user.type(startDateInput, '2024-12-31')

      const endDateInput = screen.getByLabelText(/end date/i)
      await user.type(endDateInput, '2024-01-01')

      const submitButton = screen.getByRole('button', { name: /create budget/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/end date must be after start date/i)).toBeInTheDocument()
      })
    })

    it('adds and removes budget line items', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <BudgetForm
          budget={null}
          onClose={jest.fn()}
        />
      )

      // Initially should have one line item
      expect(screen.getAllByLabelText(/item/i)).toHaveLength(1)

      // Add another line item
      const addLineButton = screen.getByRole('button', { name: /add line/i })
      await user.click(addLineButton)

      expect(screen.getAllByLabelText(/item/i)).toHaveLength(2)

      // Remove a line item
      const removeButtons = screen.getAllByRole('button', { name: /remove/i })
      await user.click(removeButtons[0])

      expect(screen.getAllByLabelText(/item/i)).toHaveLength(1)
    })

    it('calculates total budget amount correctly', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <BudgetForm
          budget={null}
          onClose={jest.fn()}
        />
      )

      // Fill in first line item
      const amountInputs = screen.getAllByLabelText(/amount/i)
      await user.clear(amountInputs[0])
      await user.type(amountInputs[0], '5000')

      // Add another line item
      const addLineButton = screen.getByRole('button', { name: /add line/i })
      await user.click(addLineButton)

      // Fill in second line item
      const updatedAmountInputs = screen.getAllByLabelText(/amount/i)
      await user.clear(updatedAmountInputs[1])
      await user.type(updatedAmountInputs[1], '3000')

      // Check if total is calculated correctly
      await waitFor(() => {
        expect(screen.getByText('8,000')).toBeInTheDocument()
      })
    })
  })

  describe('Budget Analysis', () => {
    it('calculates variance correctly', () => {
      const budgetAmount = 10000
      const actualAmount = 7500
      const variance = actualAmount - budgetAmount
      const variancePercent = (variance / budgetAmount) * 100

      expect(variance).toBe(-2500)
      expect(variancePercent).toBe(-25)
    })

    it('identifies over-budget scenarios', () => {
      const budgetAmount = 5000
      const actualAmount = 6000
      const isOverBudget = actualAmount > budgetAmount

      expect(isOverBudget).toBe(true)
    })

    it('calculates utilization percentage', () => {
      const budgetAmount = 10000
      const actualAmount = 7500
      const utilizationPercent = (actualAmount / budgetAmount) * 100

      expect(utilizationPercent).toBe(75)
    })

    it('determines alert levels correctly', () => {
      const budgetAmount = 1000
      
      // Warning level (75-90%)
      const warningAmount = 800
      const warningPercent = (warningAmount / budgetAmount) * 100
      const isWarning = warningPercent >= 75 && warningPercent < 90
      
      // Critical level (90-100%)
      const criticalAmount = 950
      const criticalPercent = (criticalAmount / budgetAmount) * 100
      const isCritical = criticalPercent >= 90 && criticalPercent < 100
      
      // Exceeded level (>100%)
      const exceededAmount = 1100
      const exceededPercent = (exceededAmount / budgetAmount) * 100
      const isExceeded = exceededPercent >= 100

      expect(isWarning).toBe(true)
      expect(isCritical).toBe(true)
      expect(isExceeded).toBe(true)
    })
  })

  describe('Budget Enforcement', () => {
    it('validates bill amount against budget', () => {
      const budgetAmount = 5000
      const currentSpent = 4000
      const billAmount = 1500
      const remainingBudget = budgetAmount - currentSpent
      const wouldExceedBudget = billAmount > remainingBudget

      expect(remainingBudget).toBe(1000)
      expect(wouldExceedBudget).toBe(true)
    })

    it('calculates exceedance amount', () => {
      const budgetAmount = 5000
      const currentSpent = 4000
      const billAmount = 1500
      const remainingBudget = budgetAmount - currentSpent
      const exceedanceAmount = billAmount - remainingBudget

      expect(exceedanceAmount).toBe(500)
    })

    it('allows bills within budget', () => {
      const budgetAmount = 5000
      const currentSpent = 3000
      const billAmount = 1000
      const remainingBudget = budgetAmount - currentSpent
      const isWithinBudget = billAmount <= remainingBudget

      expect(isWithinBudget).toBe(true)
    })
  })

  describe('Budget Periods', () => {
    it('validates monthly budget period', () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      const isMonthlyPeriod = daysDifference >= 28 && daysDifference <= 31

      expect(isMonthlyPeriod).toBe(true)
    })

    it('validates quarterly budget period', () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-03-31')
      const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      const isQuarterlyPeriod = daysDifference >= 89 && daysDifference <= 92

      expect(isQuarterlyPeriod).toBe(true)
    })

    it('validates yearly budget period', () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-12-31')
      const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      const isYearlyPeriod = daysDifference >= 365 && daysDifference <= 366

      expect(isYearlyPeriod).toBe(true)
    })
  })

  describe('Budget Status Management', () => {
    it('handles budget approval workflow', () => {
      const budgetStatuses = ['draft', 'pending_approval', 'approved', 'rejected']
      const currentStatus = 'draft'
      const nextStatus = 'pending_approval'

      expect(budgetStatuses.includes(currentStatus)).toBe(true)
      expect(budgetStatuses.includes(nextStatus)).toBe(true)
    })

    it('validates budget approval transitions', () => {
      const validTransitions = {
        'draft': ['pending_approval'],
        'pending_approval': ['approved', 'rejected'],
        'approved': [],
        'rejected': ['draft']
      }

      const currentStatus = 'pending_approval'
      const targetStatus = 'approved'
      const isValidTransition = validTransitions[currentStatus]?.includes(targetStatus)

      expect(isValidTransition).toBe(true)
    })
  })
})
