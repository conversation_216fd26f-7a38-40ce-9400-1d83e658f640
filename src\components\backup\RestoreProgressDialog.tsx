import React from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Database,
  FileText,
  Upload,
  Download,
  Shield
} from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { RestoreJob, RestoreLog } from '@/hooks/useRestoration'

interface RestoreProgressDialogProps {
  restoreJob: RestoreJob | null
  restoreLogs: RestoreLog[]
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel?: () => void
}

export function RestoreProgressDialog({
  restoreJob,
  restoreLogs,
  open,
  onOpenChange,
  onCancel
}: RestoreProgressDialogProps) {
  if (!restoreJob) return null

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-gray-500" />
      case 'pending':
      case 'validating':
      case 'downloading':
      case 'restoring':
        return <Clock className="h-5 w-5 text-blue-500 animate-spin" />
      default:
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      pending: 'outline',
      validating: 'secondary',
      downloading: 'secondary',
      restoring: 'secondary',
      completed: 'default',
      failed: 'destructive',
      cancelled: 'outline'
    }

    return (
      <Badge variant={variants[status] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getOperationIcon = (operation?: string) => {
    switch (operation) {
      case 'validate':
        return <Shield className="h-4 w-4" />
      case 'download':
        return <Download className="h-4 w-4" />
      case 'restore_table':
        return <Database className="h-4 w-4" />
      case 'cleanup':
        return <FileText className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-red-600'
      case 'warning':
        return 'text-yellow-600'
      case 'info':
        return 'text-blue-600'
      case 'debug':
        return 'text-gray-500'
      default:
        return 'text-gray-600'
    }
  }

  const isInProgress = ['pending', 'validating', 'downloading', 'restoring'].includes(restoreJob.status)
  const canCancel = isInProgress && onCancel

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Restoration Progress
          </DialogTitle>
          <DialogDescription>
            Tracking restoration job started on {formatDate(restoreJob.requested_at)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Overview */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                {getStatusIcon(restoreJob.status)}
                <div>
                  <h3 className="font-medium">
                    {restoreJob.restore_type.charAt(0).toUpperCase() + restoreJob.restore_type.slice(1)} Restoration
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Mode: {restoreJob.restore_mode} • Job ID: {restoreJob.id.slice(0, 8)}
                  </p>
                </div>
              </div>
              {getStatusBadge(restoreJob.status)}
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Overall Progress</span>
                <span>{restoreJob.progress_percentage}%</span>
              </div>
              <Progress value={restoreJob.progress_percentage} className="w-full" />
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 text-sm">
              <div>
                <span className="text-muted-foreground">Tables:</span>
                <span className="ml-2 font-medium">
                  {restoreJob.completed_tables} / {restoreJob.total_tables}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Records:</span>
                <span className="ml-2 font-medium">
                  {restoreJob.restored_records.toLocaleString()} / {restoreJob.total_records.toLocaleString()}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Started:</span>
                <span className="ml-2 font-medium">
                  {restoreJob.started_at ? formatDate(restoreJob.started_at) : 'Not started'}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">ETA:</span>
                <span className="ml-2 font-medium">
                  {restoreJob.estimated_completion ? formatDate(restoreJob.estimated_completion) : 'Calculating...'}
                </span>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {restoreJob.error_message && (
            <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <XCircle className="h-4 w-4 text-red-500" />
                <span className="font-medium text-red-700">Error</span>
              </div>
              <p className="text-red-600 text-sm">{restoreJob.error_message}</p>
            </div>
          )}

          {/* Warnings */}
          {restoreJob.warnings && restoreJob.warnings.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <span className="font-medium text-yellow-700">Warnings</span>
              </div>
              <ul className="text-yellow-600 text-sm space-y-1">
                {restoreJob.warnings.map((warning, index) => (
                  <li key={index}>• {warning}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Restoration Notes */}
          {restoreJob.restore_notes && (
            <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="h-4 w-4 text-blue-500" />
                <span className="font-medium text-blue-700">Notes</span>
              </div>
              <p className="text-blue-600 text-sm">{restoreJob.restore_notes}</p>
            </div>
          )}

          {/* Activity Logs */}
          <div className="space-y-3">
            <h3 className="font-medium">Activity Log</h3>
            <ScrollArea className="h-64 border rounded-lg p-4">
              {restoreLogs.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No activity logs yet</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {restoreLogs.map((log) => (
                    <div key={log.id} className="flex items-start gap-3 text-sm">
                      <div className="flex-shrink-0 mt-0.5">
                        {getOperationIcon(log.operation)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className={`font-medium ${getLogLevelColor(log.log_level)}`}>
                            {log.log_level.toUpperCase()}
                          </span>
                          {log.table_name && (
                            <Badge variant="outline" className="text-xs">
                              {log.table_name}
                            </Badge>
                          )}
                          <span className="text-xs text-muted-foreground">
                            {formatDate(log.created_at)}
                          </span>
                        </div>
                        <p className="text-gray-700">{log.message}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex justify-between">
          <div>
            {canCancel && (
              <Button variant="outline" onClick={onCancel} className="text-red-600 hover:text-red-700">
                Cancel Restoration
              </Button>
            )}
          </div>
          <Button onClick={() => onOpenChange(false)}>
            {isInProgress ? 'Close' : 'Done'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
