import { supabase } from '@/lib/supabase'
import type { 
  ApprovalRule, 
  WorkflowTemplate,
  DocumentType,
  UserRole
} from '@/types/database'
import type {
  ApprovalRuleConditions,
  WorkflowTemplateWithSteps
} from '@/types/approval-workflow'

/**
 * Document context for rule evaluation
 */
export interface DocumentContext {
  type: DocumentType
  amount: number
  currency_code: string
  vendor_id?: string
  customer_id?: string
  account_id?: string
  created_by: string
  created_by_role: UserRole
  department?: string
  project_id?: string
  tags?: string[]
  custom_fields?: Record<string, string | number | boolean | null>
  metadata?: Record<string, string | number | boolean | null>
}

/**
 * Rule evaluation result
 */
export interface RuleEvaluationResult {
  rule_id: string
  rule_name: string
  matched: boolean
  priority: number
  workflow_template_id: string
  conditions_met: string[]
  conditions_failed: string[]
}

/**
 * Workflow selection result
 */
export interface WorkflowSelectionResult {
  workflow_template: WorkflowTemplateWithSteps
  matched_rules: RuleEvaluationResult[]
  selection_reason: string
  confidence_score: number
}

/**
 * Approval Rules Engine
 * Evaluates document properties against approval rules to determine workflow
 */
export class ApprovalRulesEngine {

  /**
   * Select the appropriate workflow template for a document
   */
  static async selectWorkflowTemplate(
    documentContext: DocumentContext,
    orgId: string
  ): Promise<WorkflowSelectionResult | null> {
    try {
      // Get all active workflow templates for this document type
      const templates = await this.getActiveWorkflowTemplates(
        documentContext.type, 
        orgId
      )

      if (!templates || templates.length === 0) {
        return null
      }

      // Evaluate rules for each template
      const evaluationResults: Array<{
        template: WorkflowTemplateWithSteps
        ruleResults: RuleEvaluationResult[]
        score: number
      }> = []

      for (const template of templates) {
        const ruleResults = await this.evaluateTemplateRules(
          template, 
          documentContext
        )
        
        const score = this.calculateTemplateScore(template, ruleResults, documentContext)
        
        evaluationResults.push({
          template,
          ruleResults,
          score
        })
      }

      // Sort by score (highest first)
      evaluationResults.sort((a, b) => b.score - a.score)

      const bestMatch = evaluationResults[0]
      
      if (bestMatch.score === 0) {
        // No rules matched, use default template
        const defaultTemplate = templates.find(t => t.is_default)
        if (defaultTemplate) {
          return {
            workflow_template: defaultTemplate,
            matched_rules: [],
            selection_reason: 'Default template - no rules matched',
            confidence_score: 0.5
          }
        }
        return null
      }

      return {
        workflow_template: bestMatch.template,
        matched_rules: bestMatch.ruleResults.filter(r => r.matched),
        selection_reason: this.generateSelectionReason(bestMatch.ruleResults),
        confidence_score: Math.min(bestMatch.score / 100, 1.0)
      }
    } catch (error) {
      console.error('Error selecting workflow template:', error)
      return null
    }
  }

  /**
   * Evaluate all rules for a specific template
   */
  static async evaluateTemplateRules(
    template: WorkflowTemplateWithSteps,
    documentContext: DocumentContext
  ): Promise<RuleEvaluationResult[]> {
    const results: RuleEvaluationResult[] = []

    for (const rule of template.approval_rules) {
      if (!rule.is_active) continue

      const evaluation = await this.evaluateRule(rule, documentContext)
      results.push(evaluation)
    }

    return results
  }

  /**
   * Evaluate a single approval rule
   */
  static async evaluateRule(
    rule: ApprovalRule,
    documentContext: DocumentContext
  ): Promise<RuleEvaluationResult> {
    const conditions = rule.conditions as ApprovalRuleConditions
    const conditionsMet: string[] = []
    const conditionsFailed: string[] = []

    // Amount range evaluation
    if (conditions.amount_min !== undefined) {
      if (documentContext.amount >= conditions.amount_min) {
        conditionsMet.push(`Amount >= ${conditions.amount_min}`)
      } else {
        conditionsFailed.push(`Amount < ${conditions.amount_min}`)
      }
    }

    if (conditions.amount_max !== undefined) {
      if (documentContext.amount <= conditions.amount_max) {
        conditionsMet.push(`Amount <= ${conditions.amount_max}`)
      } else {
        conditionsFailed.push(`Amount > ${conditions.amount_max}`)
      }
    }

    // Currency evaluation
    if (conditions.currency_code) {
      if (documentContext.currency_code === conditions.currency_code) {
        conditionsMet.push(`Currency matches ${conditions.currency_code}`)
      } else {
        conditionsFailed.push(`Currency mismatch: expected ${conditions.currency_code}, got ${documentContext.currency_code}`)
      }
    }

    // Role evaluation
    if (conditions.created_by_role && conditions.created_by_role.length > 0) {
      if (conditions.created_by_role.includes(documentContext.created_by_role)) {
        conditionsMet.push(`Creator role matches: ${documentContext.created_by_role}`)
      } else {
        conditionsFailed.push(`Creator role mismatch: ${documentContext.created_by_role} not in ${conditions.created_by_role.join(', ')}`)
      }
    }

    // Vendor type evaluation
    if (conditions.vendor_type && documentContext.vendor_id) {
      const vendorTypeMatch = await this.checkVendorType(
        documentContext.vendor_id, 
        conditions.vendor_type
      )
      if (vendorTypeMatch) {
        conditionsMet.push(`Vendor type matches: ${conditions.vendor_type}`)
      } else {
        conditionsFailed.push(`Vendor type mismatch: ${conditions.vendor_type}`)
      }
    }

    // Customer type evaluation
    if (conditions.customer_type && documentContext.customer_id) {
      const customerTypeMatch = await this.checkCustomerType(
        documentContext.customer_id, 
        conditions.customer_type
      )
      if (customerTypeMatch) {
        conditionsMet.push(`Customer type matches: ${conditions.customer_type}`)
      } else {
        conditionsFailed.push(`Customer type mismatch: ${conditions.customer_type}`)
      }
    }

    // Department evaluation
    if (conditions.department) {
      if (documentContext.department === conditions.department) {
        conditionsMet.push(`Department matches: ${conditions.department}`)
      } else {
        conditionsFailed.push(`Department mismatch: expected ${conditions.department}, got ${documentContext.department}`)
      }
    }

    // Account evaluation
    if (conditions.account_id) {
      if (documentContext.account_id === conditions.account_id) {
        conditionsMet.push(`Account matches: ${conditions.account_id}`)
      } else {
        conditionsFailed.push(`Account mismatch: expected ${conditions.account_id}, got ${documentContext.account_id}`)
      }
    }

    // Tags evaluation
    if (conditions.tags && conditions.tags.length > 0) {
      const documentTags = documentContext.tags || []
      const matchingTags = conditions.tags.filter(tag => documentTags.includes(tag))
      
      if (matchingTags.length > 0) {
        conditionsMet.push(`Tags match: ${matchingTags.join(', ')}`)
      } else {
        conditionsFailed.push(`No matching tags: required ${conditions.tags.join(', ')}`)
      }
    }

    // Custom fields evaluation
    if (conditions.custom_fields) {
      for (const [key, expectedValue] of Object.entries(conditions.custom_fields)) {
        const actualValue = documentContext.custom_fields?.[key]
        
        if (this.compareValues(actualValue, expectedValue)) {
          conditionsMet.push(`Custom field ${key} matches: ${expectedValue}`)
        } else {
          conditionsFailed.push(`Custom field ${key} mismatch: expected ${expectedValue}, got ${actualValue}`)
        }
      }
    }

    const matched = conditionsFailed.length === 0 && conditionsMet.length > 0

    return {
      rule_id: rule.id,
      rule_name: rule.name,
      matched,
      priority: rule.priority,
      workflow_template_id: rule.workflow_template_id,
      conditions_met: conditionsMet,
      conditions_failed: conditionsFailed
    }
  }

  /**
   * Get active workflow templates for document type
   */
  private static async getActiveWorkflowTemplates(
    documentType: DocumentType,
    orgId: string
  ): Promise<WorkflowTemplateWithSteps[]> {
    try {
      const { data, error } = await supabase
        .from('workflow_templates')
        .select(`
          *,
          approval_steps(*),
          approval_rules(*)
        `)
        .eq('document_type', documentType)
        .eq('org_id', orgId)
        .eq('is_active', true)
        .order('version', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching workflow templates:', error)
      return []
    }
  }

  /**
   * Calculate template score based on rule matches
   */
  private static calculateTemplateScore(
    template: WorkflowTemplateWithSteps,
    ruleResults: RuleEvaluationResult[],
    documentContext: DocumentContext
  ): number {
    let score = 0

    // Base score for having rules
    if (ruleResults.length > 0) {
      score += 10
    }

    // Score for matched rules (weighted by priority)
    for (const result of ruleResults) {
      if (result.matched) {
        score += result.priority * 10
        score += result.conditions_met.length * 5
      }
    }

    // Bonus for default template if no rules matched
    if (score === 0 && template.is_default) {
      score = 25
    }

    return score
  }

  /**
   * Generate human-readable selection reason
   */
  private static generateSelectionReason(ruleResults: RuleEvaluationResult[]): string {
    const matchedRules = ruleResults.filter(r => r.matched)
    
    if (matchedRules.length === 0) {
      return 'No rules matched'
    }

    if (matchedRules.length === 1) {
      return `Matched rule: ${matchedRules[0].rule_name}`
    }

    const highestPriority = Math.max(...matchedRules.map(r => r.priority))
    const topRules = matchedRules.filter(r => r.priority === highestPriority)
    
    return `Matched ${matchedRules.length} rules, selected highest priority: ${topRules[0].rule_name}`
  }

  /**
   * Check vendor type (placeholder - would integrate with vendor data)
   */
  private static async checkVendorType(vendorId: string, expectedType: string): Promise<boolean> {
    // Implementation would check vendor metadata or categories
    return true
  }

  /**
   * Check customer type (placeholder - would integrate with customer data)
   */
  private static async checkCustomerType(customerId: string, expectedType: string): Promise<boolean> {
    // Implementation would check customer metadata or categories
    return true
  }

  /**
   * Compare values with type coercion
   */
  private static compareValues(actual: unknown, expected: unknown): boolean {
    if (actual === expected) return true
    
    // Handle string/number comparisons
    if (typeof actual === 'string' && typeof expected === 'number') {
      return parseFloat(actual) === expected
    }
    
    if (typeof actual === 'number' && typeof expected === 'string') {
      return actual === parseFloat(expected)
    }
    
    // Handle boolean comparisons
    if (typeof expected === 'boolean') {
      return Boolean(actual) === expected
    }
    
    return false
  }
}
