import React, { useState, useEffect, useCallback } from 'react'
import { Activity, BarChart3, List, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/hooks/useAuthHook'
import { AuditDashboard } from '@/components/audit/AuditDashboard'
import { AuditLogList } from '@/components/audit/AuditLogList'
import { AuditSettings } from '@/components/audit/AuditSettings'
import { auditLogger } from '@/lib/auditLogger'
import type { AuditLogWithProfile, AuditLogFilters } from '@/types/audit'

export default function AuditLogs() {
  const [auditLogs, setAuditLogs] = useState<AuditLogWithProfile[]>([])
  const [filteredLogs, setFilteredLogs] = useState<AuditLogWithProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<AuditLogFilters>({})
  const [activeTab, setActiveTab] = useState('dashboard')
  const { toast } = useToast()
  const { profile } = useAuth()

  const fetchAuditLogs = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('audit_logs')
        .select(`
          *,
          profiles:profile_id(email)
        `)
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })
        .limit(500) // Increased limit for better analytics

      if (error) throw error

      setAuditLogs(data || [])

      // Log the audit log view action
      await auditLogger.logView('audit_logs', 'system', 'Viewed audit logs dashboard')
    } catch (error: unknown) {
      console.error('Error fetching audit logs:', error)
      const errorMessage = error instanceof Error ? error.message :
                          typeof error === 'string' ? error : 'Failed to load audit logs'
      toast({
        title: "Error",
        description: `Failed to load audit logs: ${errorMessage}`,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, toast])

  const applyFilters = useCallback(() => {
    let filtered = [...auditLogs]

    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(log =>
        log.entity_type.toLowerCase().includes(searchLower) ||
        log.action.toLowerCase().includes(searchLower) ||
        (log.profiles?.email || '').toLowerCase().includes(searchLower) ||
        log.entity_id.toLowerCase().includes(searchLower)
      )
    }

    if (filters.entity_type) {
      filtered = filtered.filter(log => log.entity_type === filters.entity_type)
    }

    if (filters.action) {
      filtered = filtered.filter(log => log.action === filters.action)
    }

    if (filters.user_id) {
      filtered = filtered.filter(log => log.profile_id === filters.user_id)
    }

    if (filters.date_from) {
      filtered = filtered.filter(log => log.created_at >= filters.date_from!)
    }

    if (filters.date_to) {
      const dateTo = new Date(filters.date_to)
      dateTo.setHours(23, 59, 59, 999) // End of day
      filtered = filtered.filter(log => new Date(log.created_at) <= dateTo)
    }

    if (filters.ip_address) {
      filtered = filtered.filter(log => log.ip_address?.includes(filters.ip_address!))
    }

    setFilteredLogs(filtered)
  }, [auditLogs, filters])

  useEffect(() => {
    if (profile?.org_id) {
      fetchAuditLogs()
      // Initialize audit logger context
      auditLogger.setContext({
        userId: profile.id,
        orgId: profile.org_id
      })
    }
  }, [profile, fetchAuditLogs])

  useEffect(() => {
    applyFilters()
  }, [applyFilters])

  const handleFiltersChange = (newFilters: AuditLogFilters) => {
    setFilters(newFilters)
  }

  const handleExport = async () => {
    try {
      // Log the export action
      await auditLogger.logExport(
        'audit_logs',
        filteredLogs.map(log => log.id),
        'csv',
        `Exported ${filteredLogs.length} audit log entries`
      )

      // Create CSV content
      const headers = ['Date', 'Action', 'Entity Type', 'Entity ID', 'User', 'IP Address', 'Details']
      const csvContent = [
        headers.join(','),
        ...filteredLogs.map(log => [
          new Date(log.created_at).toISOString(),
          log.action,
          log.entity_type,
          log.entity_id,
          log.profiles?.email || 'Unknown',
          log.ip_address || 'N/A',
          JSON.stringify(log.changed_data || {}).replace(/"/g, '""')
        ].map(field => `"${field}"`).join(','))
      ].join('\n')

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)

      toast({
        title: "Success",
        description: "Audit logs exported successfully",
      })
    } catch (error) {
      console.error('Error exporting audit logs:', error)
      toast({
        title: "Error",
        description: "Failed to export audit logs",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Activity className="h-8 w-8" />
            Audit Logs
          </h1>
          <p className="text-muted-foreground">
            Comprehensive activity tracking and security monitoring
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 max-w-lg">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="logs" className="flex items-center gap-2">
            <List className="h-4 w-4" />
            Audit Logs
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          <AuditDashboard />
        </TabsContent>

        <TabsContent value="logs" className="space-y-6">
          <AuditLogList
            logs={filteredLogs}
            loading={loading}
            onFiltersChange={handleFiltersChange}
            onRefresh={fetchAuditLogs}
            onExport={handleExport}
          />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <AuditSettings />
        </TabsContent>
      </Tabs>
    </div>
  )
}
