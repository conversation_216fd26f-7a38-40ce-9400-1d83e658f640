import { Link } from 'react-router-dom'
import { ArrowLeft, MessageCircle, Mail, Clock, FileText, HelpCircle, Phone } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const Support = () => {
  const whatsappNumber = "256777959328"
  const whatsappUrl = `https://wa.me/${whatsappNumber}`
  const emailAddress = "<EMAIL>"

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <Link to="/" className="flex items-center">
              <span className="text-xl font-bold text-primary">KAYA<span className="text-green-600"> Finance</span></span>
            </Link>
            <Link to="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="space-y-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight mb-4">Support Center</h1>
            <p className="text-muted-foreground text-lg">
              Get help with KAYA Finance - we're here to support your business
            </p>
            <p className="text-muted-foreground text-sm mt-2">
              Powered by Tom's Cyber Lab (U) Ltd - Your ICT Solutions Partner
            </p>
          </div>

          {/* Contact Options */}
          <div className="grid md:grid-cols-2 gap-6">
            <Card className="border-green-200 bg-green-50/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5 text-green-600" />
                  WhatsApp Support
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Get instant help via WhatsApp. Our team responds quickly to your questions.
                </p>
                <div className="space-y-2">
                  <p className="font-semibold">+256 777 959 328</p>
                  <p className="text-sm text-muted-foreground">Available: Monday - Friday, 8:00 AM - 6:00 PM EAT</p>
                </div>
                <Button asChild className="w-full bg-green-600 hover:bg-green-700">
                  <a href={whatsappUrl} target="_blank" rel="noopener noreferrer">
                    <MessageCircle className="mr-2 h-4 w-4" />
                    Chat on WhatsApp
                  </a>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5 text-blue-600" />
                  Email Support
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Send us detailed questions or technical issues via email.
                </p>
                <div className="space-y-2">
                  <p className="font-semibold">{emailAddress}</p>
                  <p className="text-sm text-muted-foreground">Response time: Within 24 hours</p>
                </div>
                <Button asChild variant="outline" className="w-full">
                  <a href={`mailto:${emailAddress}`}>
                    <Mail className="mr-2 h-4 w-4" />
                    Send Email
                  </a>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Support Hours */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Support Hours
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Regular Support</h4>
                  <ul className="space-y-1 text-sm">
                    <li>Monday - Friday: 8:00 AM - 6:00 PM</li>
                    <li>Saturday: 9:00 AM - 2:00 PM</li>
                    <li>Sunday: Closed</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Emergency Support</h4>
                  <p className="text-sm text-muted-foreground">
                    Critical system issues are handled 24/7. Contact us via WhatsApp for urgent matters.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Common Issues */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5" />
                Common Questions & Solutions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-semibold mb-2">Getting Started</h4>
                <ul className="space-y-2 text-sm">
                  <li>• <strong>Account Setup:</strong> Follow the onboarding wizard to set up your organization</li>
                  <li>• <strong>Chart of Accounts:</strong> Use our pre-configured Ugandan chart or customize your own</li>
                  <li>• <strong>User Management:</strong> Add team members and set appropriate permissions</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">URA Integration</h4>
                <ul className="space-y-2 text-sm">
                  <li>• <strong>Tax Filing:</strong> Ensure your TIN is correctly entered in organization settings</li>
                  <li>• <strong>VAT Returns:</strong> Review transactions before submitting to URA</li>
                  <li>• <strong>Compliance:</strong> Keep all supporting documents for audit purposes</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Mobile Money & Banking</h4>
                <ul className="space-y-2 text-sm">
                  <li>• <strong>MTN/Airtel Setup:</strong> Contact support for integration assistance</li>
                  <li>• <strong>Bank Reconciliation:</strong> Import statements and match transactions</li>
                  <li>• <strong>Payment Tracking:</strong> Use payment references for easy reconciliation</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Offline Mode</h4>
                <ul className="space-y-2 text-sm">
                  <li>• <strong>Data Sync:</strong> Changes sync automatically when internet returns</li>
                  <li>• <strong>Backup:</strong> Critical data is stored locally for offline access</li>
                  <li>• <strong>Limitations:</strong> Some features require internet connectivity</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Resources */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Additional Resources
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Documentation</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• User Guide & Tutorials</li>
                    <li>• API Documentation</li>
                    <li>• Best Practices Guide</li>
                    <li>• Troubleshooting Tips</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Training</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• Video Tutorials</li>
                    <li>• Webinar Sessions</li>
                    <li>• One-on-One Training</li>
                    <li>• Team Workshops</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Enterprise Support */}
          <Card className="border-blue-200 bg-blue-50/50">
            <CardHeader>
              <CardTitle>Enterprise Support</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                Need dedicated support for your organization? Our Enterprise support includes:
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Priority support with faster response times</li>
                <li>Dedicated account manager</li>
                <li>Custom integrations and features</li>
                <li>On-site training and setup</li>
                <li>24/7 emergency support</li>
              </ul>
              <Button asChild>
                <a href={`${whatsappUrl}?text=I'm interested in Enterprise Support for KAYA Finance`}>
                  <Phone className="mr-2 h-4 w-4" />
                  Contact Sales
                </a>
              </Button>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm">All systems operational</span>
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                For real-time system status updates, follow us on our social media channels or contact support.
              </p>
            </CardContent>
          </Card>

          {/* Contact Summary */}
          <Card className="bg-muted/50">
            <CardHeader>
              <CardTitle>Quick Contact</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <MessageCircle className="h-4 w-4 text-green-600" />
                <span>WhatsApp: +256 777 959 328</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-blue-600" />
                <span>Email: {emailAddress}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4" />
                <span>Hours: Mon-Fri 8AM-6PM, Sat 9AM-2PM EAT</span>
              </div>
              <div className="mt-3 pt-2 border-t border-border">
                <p className="text-xs text-muted-foreground">
                  <strong>Tom's Cyber Lab (U) Ltd</strong> - Registration: 80020002602390
                </p>
                <p className="text-xs text-muted-foreground">
                  ICT Solutions | Digital Platforms | Network Security | Computer Maintenance
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-background mt-16">
        <div className="container mx-auto px-4">
          <div className="flex h-14 items-center justify-center">
            <div className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} KAYA Finance by Tom's Cyber Lab (U) Ltd. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Support
