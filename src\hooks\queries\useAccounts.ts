import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys, type QueryFilters } from '@/lib/queryKeys'
import { toast } from '@/hooks/use-toast'
import type { Account } from '@/types/database'

/**
 * Hook to fetch all accounts for the organization
 */
export function useAccounts(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: filters 
      ? queryKeys.accounts.all(profile?.org_id || '') // Use filtered key when we add it
      : queryKeys.accounts.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('accounts')
        .select('*')
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,code.ilike.%${filters.search}%`)
      }
      
      if (filters?.active !== undefined) {
        query = query.eq('is_active', filters.active)
      }

      // Apply ordering
      query = query.order('code')

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit)
      }
      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes - accounts change less frequently
  })
}

/**
 * Hook to fetch active accounts only
 */
export function useActiveAccounts() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.accounts.active(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('code')

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 15 * 60 * 1000, // 15 minutes - active accounts are very stable
  })
}

/**
 * Hook to fetch accounts by type
 */
export function useAccountsByType(accountType: string) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.accounts.byType(profile?.org_id || '', accountType),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('type', accountType)
        .eq('is_active', true)
        .order('code')

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id && !!accountType,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook to fetch a single account by ID
 */
export function useAccount(accountId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.accounts.detail(profile?.org_id || '', accountId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !accountId) return null

      const { data, error } = await supabase
        .from('accounts')
        .select('*')
        .eq('id', accountId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id && !!accountId,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook to fetch account balance
 */
export function useAccountBalance(accountId: string | undefined, date?: string) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.accounts.balance(profile?.org_id || '', accountId || '', date),
    queryFn: async () => {
      if (!profile?.org_id || !accountId) return null

      // This would typically call a stored procedure or view that calculates the balance
      // For now, we'll return a placeholder
      const { data, error } = await supabase
        .rpc('get_account_balance', {
          account_id: accountId,
          as_of_date: date || new Date().toISOString().split('T')[0]
        })

      if (error) {
        // Fallback if the RPC doesn't exist yet
        console.warn('Account balance RPC not available, returning 0')
        return { balance: 0, currency: 'UGX' }
      }
      
      return data
    },
    enabled: !!profile?.org_id && !!accountId,
    staleTime: 2 * 60 * 1000, // 2 minutes - balances change more frequently
  })
}

/**
 * Hook to create a new account
 */
export function useCreateAccount() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (accountData: Omit<Account, 'id' | 'org_id' | 'created_at' | 'updated_at'>) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('accounts')
        .insert({
          ...accountData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (newAccount) => {
      // Invalidate all account-related queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounts.all(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounts.active(profile?.org_id || '')
      })

      // Invalidate any filtered queries
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === 'accounts' &&
          query.queryKey[1] === profile?.org_id
      })

      // Add the new account to the cache
      queryClient.setQueryData(
        queryKeys.accounts.detail(profile?.org_id || '', newAccount.id),
        newAccount
      )

      // Invalidate accounts by type if applicable
      if (newAccount.type) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.accounts.byType(profile?.org_id || '', newAccount.type)
        })
      }

      toast({
        title: 'Success',
        description: 'Account created successfully',
      })
    },
    onError: (error) => {
      console.error('Error creating account:', error)
      toast({
        title: 'Error',
        description: 'Failed to create account',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update an existing account
 */
export function useUpdateAccount() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      accountId, 
      accountData 
    }: { 
      accountId: string
      accountData: Partial<Omit<Account, 'id' | 'org_id' | 'created_at'>>
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('accounts')
        .update({
          ...accountData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', accountId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (updatedAccount) => {
      // Update the account in the cache
      queryClient.setQueryData(
        queryKeys.accounts.detail(profile?.org_id || '', updatedAccount.id),
        updatedAccount
      )

      // Invalidate all account-related queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounts.all(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounts.active(profile?.org_id || '')
      })

      // Invalidate any filtered queries
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === 'accounts' &&
          query.queryKey[1] === profile?.org_id
      })

      // Invalidate accounts by type if type changed
      if (updatedAccount.type) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.accounts.byType(profile?.org_id || '', updatedAccount.type)
        })
      }

      toast({
        title: 'Success',
        description: 'Account updated successfully',
      })
    },
    onError: (error) => {
      console.error('Error updating account:', error)
      toast({
        title: 'Error',
        description: 'Failed to update account',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete an account
 */
export function useDeleteAccount() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (accountId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { error } = await supabase
        .from('accounts')
        .delete()
        .eq('id', accountId)
        .eq('org_id', profile.org_id)

      if (error) throw error
      return accountId
    },
    onSuccess: (deletedAccountId) => {
      // Remove the account from the cache
      queryClient.removeQueries({ 
        queryKey: queryKeys.accounts.detail(profile?.org_id || '', deletedAccountId) 
      })

      // Invalidate accounts lists
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.accounts.all(profile?.org_id || '') 
      })
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.accounts.active(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: 'Account deleted successfully',
      })
    },
    onError: (error) => {
      console.error('Error deleting account:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete account',
        variant: 'destructive',
      })
    },
  })
}
