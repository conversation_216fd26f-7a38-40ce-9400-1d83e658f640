import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { formatCurrency } from '@/lib/utils'
import { Edit, Trash2, Plus, Building2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/lib/supabase'

interface BankAccount {
  id: string
  name: string
  bank_name: string
  account_no: string
  branch_name?: string
  currency: string
  opening_balance: number
  opening_date: string
  is_active: boolean
  created_at: string
}

interface BankAccountListProps {
  accounts: BankAccount[]
  onEdit: (account: BankAccount) => void
  onAdd: () => void
  onRefresh: () => void
}

export function BankAccountList({
  accounts,
  onEdit,
  onAdd,
  onRefresh
}: BankAccountListProps) {
  const { toast } = useToast()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [accountToDelete, setAccountToDelete] = useState<BankAccount | null>(null)
  const [deleting, setDeleting] = useState(false)

  const handleDeleteClick = (account: BankAccount) => {
    setAccountToDelete(account)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!accountToDelete) return

    setDeleting(true)
    try {
      const { error } = await supabase
        .from('bank_accounts')
        .delete()
        .eq('id', accountToDelete.id)

      if (error) throw error

      toast({
        title: 'Success',
        description: 'Bank account deleted successfully'
      })
      
      onRefresh()
    } catch (error) {
      console.error('Error deleting bank account:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete bank account',
        variant: 'destructive'
      })
    } finally {
      setDeleting(false)
      setDeleteDialogOpen(false)
      setAccountToDelete(null)
    }
  }

  if (accounts.length === 0) {
    return (
      <div className="text-center py-8">
        <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground mb-4">No bank accounts configured</p>
        <Button onClick={onAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Your First Bank Account
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <p className="text-sm text-muted-foreground">
            {accounts.length} bank account{accounts.length !== 1 ? 's' : ''} configured
          </p>
          <Button onClick={onAdd} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Account
          </Button>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Account Name</TableHead>
              <TableHead>Bank</TableHead>
              <TableHead>Account Number</TableHead>
              <TableHead>Currency</TableHead>
              <TableHead>Opening Balance</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {accounts.map((account) => (
              <TableRow key={account.id}>
                <TableCell>
                  <div>
                    <p className="font-medium">{account.name}</p>
                    {account.branch_name && (
                      <p className="text-sm text-muted-foreground">{account.branch_name}</p>
                    )}
                  </div>
                </TableCell>
                <TableCell>{account.bank_name}</TableCell>
                <TableCell className="font-mono text-sm">{account.account_no}</TableCell>
                <TableCell>{account.currency}</TableCell>
                <TableCell>{formatCurrency(account.opening_balance)}</TableCell>
                <TableCell>
                  <Badge variant={account.is_active ? 'default' : 'secondary'}>
                    {account.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onEdit(account)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteClick(account)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Bank Account</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the bank account "{accountToDelete?.name}"? 
              This action cannot be undone and may affect existing payment records.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={deleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
