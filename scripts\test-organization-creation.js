#!/usr/bin/env node

/**
 * Test script to verify organization creation RLS fix
 * This script tests the organization creation functionality after the RLS fix
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testUnauthenticatedAccess() {
  console.log('\n🔒 Testing Unauthenticated Access...')
  console.log('=' .repeat(60))

  // Test 1: Try to create organization without authentication
  console.log('\n1. Testing direct organization creation (should fail)...')
  const { data: orgData, error: orgError } = await supabase
    .from('organizations')
    .insert({
      name: 'Test Org Unauthorized',
      currency_code: 'UGX',
      timezone: 'Africa/Kampala'
    })
    .select()

  if (orgError) {
    if (orgError.message.includes('new row violates row-level security') || 
        orgError.message.includes('permission denied') ||
        orgError.code === '42501') {
      console.log('✅ RLS is working - unauthenticated users cannot create organizations')
      console.log('   Error:', orgError.message)
    } else {
      console.log('❌ Unexpected error:', orgError.message)
    }
  } else {
    console.log('❌ WARNING: Unauthenticated user was able to create organization!')
    console.log('   This indicates RLS policies may not be working correctly')
  }

  // Test 2: Try to use the helper function without authentication
  console.log('\n2. Testing helper function without auth (should fail)...')
  const { data: functionData, error: functionError } = await supabase
    .rpc('create_organization_with_profile', {
      org_name: 'Test Org Function Unauthorized',
      tin_number: null,
      business_reg_number: null,
      ura_tax_office: null,
      user_phone: null,
      user_role: 'accountant'
    })

  if (functionError) {
    console.log('✅ Function properly rejects unauthenticated users')
    console.log('   Error:', functionError.message)
  } else if (functionData?.[0]?.success === false) {
    console.log('✅ Function properly rejects unauthenticated users')
    console.log('   Error:', functionData[0].error_message)
  } else {
    console.log('❌ WARNING: Function allowed unauthenticated access!')
  }
}

async function testAuthenticatedAccess() {
  console.log('\n🔐 Testing Authenticated Access...')
  console.log('=' .repeat(60))

  // For this test, we need actual user credentials
  console.log('ℹ️  To test with authentication, you need to:')
  console.log('1. Sign up a test user in the application')
  console.log('2. Get their JWT token')
  console.log('3. Use supabase.auth.setSession() with the token')
  console.log('4. Then test organization creation')
  
  console.log('\nExample authenticated test:')
  console.log(`
// After authentication with a valid session
const { data, error } = await supabase
  .rpc('create_organization_with_profile', {
    org_name: 'My Test Company',
    tin_number: '**********',
    business_reg_number: 'REG123',
    ura_tax_office: 'Kampala',
    user_phone: '+************',
    user_role: 'accountant'
  })

if (error) {
  console.log('Error:', error.message)
} else if (data?.[0]?.success) {
  console.log('Success! Organization ID:', data[0].organization_id)
} else {
  console.log('Function error:', data?.[0]?.error_message)
}
`)
}

async function testRLSPolicies() {
  console.log('\n📋 Testing RLS Policies...')
  console.log('=' .repeat(60))

  // Test table access permissions
  const tables = ['organizations', 'profiles']
  
  for (const table of tables) {
    const { data, error } = await supabase
      .from(table)
      .select('count')
      .limit(1)

    if (error) {
      if (error.message.includes('permission denied') || 
          error.message.includes('row-level security')) {
        console.log(`✅ ${table}: Properly protected (no read access without auth)`)
      } else {
        console.log(`❌ ${table}: Unexpected error - ${error.message}`)
      }
    } else {
      console.log(`⚠️  ${table}: Readable without authentication (may be intentional)`)
    }
  }
}

async function checkDatabaseFunction() {
  console.log('\n🔧 Checking Database Function...')
  console.log('=' .repeat(60))

  // Check if function exists and its properties
  const { data: functionInfo, error: functionError } = await supabase
    .rpc('debug_auth_context')

  if (functionError) {
    console.log('❌ debug_auth_context function failed:', functionError.message)
  } else {
    console.log('✅ debug_auth_context function works')
    console.log('   Current user ID:', functionInfo?.[0]?.current_user_id || 'null (unauthenticated)')
    console.log('   User role:', functionInfo?.[0]?.user_role || 'null')
    console.log('   Is authenticated:', functionInfo?.[0]?.is_authenticated || false)
  }
}

async function main() {
  console.log('🧪 Organization Creation RLS Test Suite')
  console.log('=' .repeat(60))
  console.log('This script tests the RLS policies and organization creation functionality')
  console.log('after the recent security fixes.')

  try {
    await testUnauthenticatedAccess()
    await testRLSPolicies()
    await checkDatabaseFunction()
    await testAuthenticatedAccess()

    console.log('\n✅ Test suite completed!')
    console.log('\nNext steps:')
    console.log('1. Test the onboarding flow in the application')
    console.log('2. Check the debug panel for permission status')
    console.log('3. Verify organization creation works for authenticated users')

  } catch (error) {
    console.error('\n❌ Test suite failed:', error.message)
    process.exit(1)
  }
}

main()
