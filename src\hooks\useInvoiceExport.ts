/**
 * Hook for invoice export functionality
 */

import { useState } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { useToast } from '@/hooks/use-toast'
import { exportInvoices, type InvoiceExportOptions } from '@/lib/invoiceExport'

export function useInvoiceExport() {
  const { profile } = useAuth()
  const { toast } = useToast()
  const [isExporting, setIsExporting] = useState(false)

  const exportInvoiceData = async (options: InvoiceExportOptions) => {
    if (!profile?.org_id) {
      toast({
        title: "Error",
        description: "Organization not found",
        variant: "destructive"
      })
      return false
    }

    setIsExporting(true)
    try {
      await exportInvoices(profile.org_id, options)
      
      toast({
        title: "Success",
        description: "Invoices exported successfully",
      })
      
      return true
    } catch (error) {
      console.error('Export failed:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to export invoices",
        variant: "destructive"
      })
      return false
    } finally {
      setIsExporting(false)
    }
  }

  return {
    exportInvoiceData,
    isExporting
  }
}
