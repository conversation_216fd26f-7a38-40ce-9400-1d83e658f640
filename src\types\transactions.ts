import type { Customer, Vendor } from './extended-database'
import type { Payment } from './database'

export interface TransactionDetails extends Payment {
  payee_name?: string
  payee_details?: Customer | Vendor
  applications_total?: number
  remaining_balance?: number
  related_documents?: Array<{
    id: string
    type: 'invoice' | 'bill'
    number: string
    amount: number
    amount_applied: number
    date: string
    status: string
  }>
  bank_account_details?: {
    id: string
    account_name: string
    account_number: string
    bank_name: string
  }
}

export interface PaymentApplication {
  id: string
  payment_id: string
  applied_to_type: 'invoice' | 'bill'
  applied_to_id: string
  amount_applied: number
  created_at: string
  document_details?: {
    number: string
    date: string
    total_amount: number
    status: string
  }
}
