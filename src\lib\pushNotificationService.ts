/**
 * Push Notification Service
 * Handles browser push notifications, service worker registration, and subscription management
 */

import { supabase } from '@/integrations/supabase/client'
import type { NotificationType, NotificationWithMeta } from '@/types/notifications'

export interface PushSubscriptionData {
  id?: string
  user_id: string
  org_id: string
  endpoint: string
  p256dh_key: string
  auth_key: string
  user_agent: string
  is_active: boolean
  created_at?: string
  updated_at?: string
}

export interface PushNotificationPayload {
  title: string
  message: string
  type: NotificationType
  notificationId: string
  entityType?: string
  entityId?: string
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  icon?: string
  image?: string
  url?: string
  data?: Record<string, unknown>
}

/**
 * Push Notification Service Class
 */
export class PushNotificationService {
  private registration: ServiceWorkerRegistration | null = null
  private subscription: PushSubscription | null = null
  private vapidPublicKey: string

  constructor(vapidPublicKey: string) {
    this.vapidPublicKey = vapidPublicKey
  }

  /**
   * Initialize push notification service
   */
  async initialize(): Promise<boolean> {
    try {
      // Check if service workers are supported
      if (!('serviceWorker' in navigator)) {
        console.warn('Service workers not supported')
        return false
      }

      // Check if push notifications are supported
      if (!('PushManager' in window)) {
        console.warn('Push notifications not supported')
        return false
      }

      // Register service worker
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })

      console.log('Service Worker registered:', this.registration)

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready

      // Set up message listener
      this.setupMessageListener()

      return true
    } catch (error) {
      console.error('Error initializing push notification service:', error)
      return false
    }
  }

  /**
   * Request notification permission
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('Notifications not supported')
    }

    const permission = await Notification.requestPermission()
    console.log('Notification permission:', permission)
    return permission
  }

  /**
   * Check current notification permission
   */
  getPermission(): NotificationPermission {
    if (!('Notification' in window)) {
      return 'denied'
    }
    return Notification.permission
  }

  /**
   * Subscribe to push notifications
   */
  async subscribe(userId: string, orgId: string): Promise<PushSubscriptionData | null> {
    try {
      if (!this.registration) {
        throw new Error('Service worker not registered')
      }

      // Check permission
      const permission = await this.requestPermission()
      if (permission !== 'granted') {
        throw new Error('Notification permission denied')
      }

      // Subscribe to push notifications
      this.subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
      })

      console.log('Push subscription created:', this.subscription)

      // Save subscription to database
      const subscriptionData: Omit<PushSubscriptionData, 'id' | 'created_at' | 'updated_at'> = {
        user_id: userId,
        org_id: orgId,
        endpoint: this.subscription.endpoint,
        p256dh_key: this.arrayBufferToBase64(this.subscription.getKey('p256dh')!),
        auth_key: this.arrayBufferToBase64(this.subscription.getKey('auth')!),
        user_agent: navigator.userAgent,
        is_active: true
      }

      const { data, error } = await supabase
        .from('push_subscriptions')
        .upsert(subscriptionData, { 
          onConflict: 'user_id,endpoint',
          ignoreDuplicates: false 
        })
        .select()
        .single()

      if (error) {
        console.error('Error saving push subscription:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('Error subscribing to push notifications:', error)
      return null
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(userId: string): Promise<boolean> {
    try {
      if (this.subscription) {
        await this.subscription.unsubscribe()
        this.subscription = null
      }

      // Remove subscription from database
      const { error } = await supabase
        .from('push_subscriptions')
        .update({ is_active: false })
        .eq('user_id', userId)

      if (error) {
        console.error('Error removing push subscription:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error)
      return false
    }
  }

  /**
   * Get current subscription status
   */
  async getSubscriptionStatus(userId: string): Promise<{
    isSubscribed: boolean
    subscription: PushSubscriptionData | null
  }> {
    try {
      const { data, error } = await supabase
        .from('push_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single()

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error getting subscription status:', error)
        return { isSubscribed: false, subscription: null }
      }

      return {
        isSubscribed: !!data,
        subscription: data
      }
    } catch (error) {
      console.error('Error getting subscription status:', error)
      return { isSubscribed: false, subscription: null }
    }
  }

  /**
   * Send test notification
   */
  async sendTestNotification(): Promise<boolean> {
    try {
      if (!this.registration) {
        throw new Error('Service worker not registered')
      }

      await this.registration.showNotification('Test Notification', {
        body: 'This is a test notification from Kaya Finance',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        tag: 'test',
        data: {
          url: '/notifications',
          timestamp: Date.now()
        }
      })

      return true
    } catch (error) {
      console.error('Error sending test notification:', error)
      return false
    }
  }

  /**
   * Setup message listener for service worker communication
   */
  private setupMessageListener(): void {
    navigator.serviceWorker.addEventListener('message', (event) => {
      const { type, payload } = event.data

      switch (type) {
        case 'NOTIFICATION_CLICKED':
          this.handleNotificationClick(payload)
          break
        default:
          console.log('Unknown message from service worker:', type)
      }
    })
  }

  /**
   * Handle notification click from service worker
   */
  private handleNotificationClick(payload: NotificationPayload): void {
    console.log('Notification clicked:', payload)
    
    // Navigate to the appropriate page
    if (payload.url) {
      window.location.href = payload.url
    }

    // Dispatch custom event for app to handle
    window.dispatchEvent(new CustomEvent('notificationClick', {
      detail: payload
    }))
  }

  /**
   * Convert VAPID key to Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  /**
   * Convert ArrayBuffer to base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return window.btoa(binary)
  }
}

/**
 * Global push notification service instance
 */
let pushService: PushNotificationService | null = null

/**
 * Initialize push notification service
 */
export async function initializePushNotifications(vapidPublicKey: string): Promise<PushNotificationService | null> {
  try {
    pushService = new PushNotificationService(vapidPublicKey)
    const initialized = await pushService.initialize()
    
    if (!initialized) {
      console.warn('Push notifications could not be initialized')
      return null
    }

    return pushService
  } catch (error) {
    console.error('Error initializing push notifications:', error)
    return null
  }
}

/**
 * Get push notification service instance
 */
export function getPushNotificationService(): PushNotificationService | null {
  return pushService
}

/**
 * Check if push notifications are supported
 */
export function isPushNotificationSupported(): boolean {
  return (
    'serviceWorker' in navigator &&
    'PushManager' in window &&
    'Notification' in window
  )
}

/**
 * Send push notification to user (server-side function)
 */
export async function sendPushNotification(
  userId: string,
  payload: PushNotificationPayload
): Promise<boolean> {
  try {
    const { data, error } = await supabase.functions.invoke('send-push-notification', {
      body: {
        userId,
        payload
      }
    })

    if (error) {
      console.error('Error sending push notification:', error)
      return false
    }

    return data.success || false
  } catch (error) {
    console.error('Error sending push notification:', error)
    return false
  }
}

/**
 * Send push notification to multiple users
 */
export async function sendBulkPushNotifications(
  userIds: string[],
  payload: PushNotificationPayload
): Promise<{ success: number; failed: number }> {
  try {
    const { data, error } = await supabase.functions.invoke('send-bulk-push-notifications', {
      body: {
        userIds,
        payload
      }
    })

    if (error) {
      console.error('Error sending bulk push notifications:', error)
      return { success: 0, failed: userIds.length }
    }

    return data || { success: 0, failed: userIds.length }
  } catch (error) {
    console.error('Error sending bulk push notifications:', error)
    return { success: 0, failed: userIds.length }
  }
}
