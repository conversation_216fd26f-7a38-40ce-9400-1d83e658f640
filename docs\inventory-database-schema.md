# Inventory Management Database Schema Design

## Overview

This document outlines the comprehensive database schema design for adding inventory management capabilities to KAYA Finance. The design maintains backward compatibility while introducing robust inventory tracking features.

## Core Design Principles

1. **Organization Isolation**: All tables include `org_id` for multi-tenant security
2. **Backward Compatibility**: Existing invoice/bill line items remain functional
3. **Audit Trail**: Complete tracking of all inventory movements
4. **Scalability**: Designed to handle multi-location inventory
5. **Flexibility**: Support for various business models and product types

## New Tables

### 1. product_categories

Hierarchical product categorization system.

```sql
CREATE TABLE product_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES product_categories(id) ON DELETE SET NULL,
    code VARCHAR(50), -- Optional category code
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_category_name_per_org UNIQUE(org_id, name),
    CONSTRAINT unique_category_code_per_org UNIQUE(org_id, code)
);
```

### 2. inventory_locations

Physical locations where inventory is stored.

```sql
CREATE TABLE inventory_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_location_name_per_org UNIQUE(org_id, name),
    CONSTRAINT unique_location_code_per_org UNIQUE(org_id, code)
);
```

### 3. products

Core product master data.

```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    sku VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES product_categories(id) ON DELETE SET NULL,
    unit_of_measure VARCHAR(50) NOT NULL DEFAULT 'each',
    
    -- Pricing
    cost_price DECIMAL(15,2) DEFAULT 0,
    selling_price DECIMAL(15,2) DEFAULT 0,
    
    -- Inventory Control
    track_inventory BOOLEAN DEFAULT true,
    reorder_level DECIMAL(15,3) DEFAULT 0,
    reorder_quantity DECIMAL(15,3) DEFAULT 0,
    
    -- Product Attributes
    barcode VARCHAR(255),
    weight DECIMAL(10,3),
    dimensions VARCHAR(100), -- e.g., "10x5x3 cm"
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_sellable BOOLEAN DEFAULT true,
    is_purchasable BOOLEAN DEFAULT true,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    CONSTRAINT unique_sku_per_org UNIQUE(org_id, sku),
    CONSTRAINT unique_barcode_per_org UNIQUE(org_id, barcode)
);
```

### 4. stock_levels

Current inventory quantities per product per location.

```sql
CREATE TABLE stock_levels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES inventory_locations(id) ON DELETE CASCADE,
    
    quantity_on_hand DECIMAL(15,3) DEFAULT 0,
    quantity_reserved DECIMAL(15,3) DEFAULT 0, -- Reserved for pending orders
    quantity_available DECIMAL(15,3) GENERATED ALWAYS AS (quantity_on_hand - quantity_reserved) STORED,
    
    -- Cost tracking for valuation
    average_cost DECIMAL(15,4) DEFAULT 0,
    last_cost DECIMAL(15,4) DEFAULT 0,
    
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_product_location UNIQUE(org_id, product_id, location_id),
    CONSTRAINT non_negative_quantities CHECK (
        quantity_on_hand >= 0 AND 
        quantity_reserved >= 0 AND 
        quantity_reserved <= quantity_on_hand
    )
);
```

### 5. inventory_transactions

Complete audit trail of all inventory movements.

```sql
CREATE TABLE inventory_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES inventory_locations(id) ON DELETE CASCADE,
    
    -- Transaction Details
    transaction_type VARCHAR(50) NOT NULL, -- 'purchase', 'sale', 'adjustment', 'transfer_in', 'transfer_out'
    quantity DECIMAL(15,3) NOT NULL, -- Positive for increases, negative for decreases
    unit_cost DECIMAL(15,4),
    total_cost DECIMAL(15,2),
    
    -- Reference Information
    reference_type VARCHAR(50), -- 'invoice', 'bill', 'adjustment', 'transfer'
    reference_id UUID, -- ID of the source document
    reference_number VARCHAR(255), -- Human-readable reference
    
    -- Additional Details
    reason_code VARCHAR(50),
    notes TEXT,
    batch_number VARCHAR(100),
    expiry_date DATE,
    
    -- Audit Information
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    CONSTRAINT valid_transaction_type CHECK (
        transaction_type IN ('purchase', 'sale', 'adjustment', 'transfer_in', 'transfer_out', 'opening_balance')
    )
);
```

## Modified Tables

### invoice_lines (Add product reference)

```sql
-- Add optional product reference to existing invoice_lines table
ALTER TABLE invoice_lines 
ADD COLUMN product_id UUID REFERENCES products(id) ON DELETE SET NULL;

-- Add index for performance
CREATE INDEX idx_invoice_lines_product_id ON invoice_lines(product_id);
```

### bill_lines (Add product reference)

```sql
-- Add optional product reference to existing bill_lines table
ALTER TABLE bill_lines 
ADD COLUMN product_id UUID REFERENCES products(id) ON DELETE SET NULL;

-- Add index for performance
CREATE INDEX idx_bill_lines_product_id ON bill_lines(bill_id);
```

## Indexes for Performance

```sql
-- Product indexes
CREATE INDEX idx_products_org_sku ON products(org_id, sku);
CREATE INDEX idx_products_org_name ON products(org_id, name);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_barcode ON products(barcode) WHERE barcode IS NOT NULL;

-- Stock level indexes
CREATE INDEX idx_stock_levels_product ON stock_levels(product_id);
CREATE INDEX idx_stock_levels_location ON stock_levels(location_id);
CREATE INDEX idx_stock_levels_low_stock ON stock_levels(org_id, product_id) 
    WHERE quantity_available <= (SELECT reorder_level FROM products WHERE id = product_id);

-- Transaction indexes
CREATE INDEX idx_inventory_transactions_product ON inventory_transactions(product_id);
CREATE INDEX idx_inventory_transactions_location ON inventory_transactions(location_id);
CREATE INDEX idx_inventory_transactions_date ON inventory_transactions(transaction_date);
CREATE INDEX idx_inventory_transactions_reference ON inventory_transactions(reference_type, reference_id);

-- Category indexes
CREATE INDEX idx_product_categories_parent ON product_categories(parent_id);
CREATE INDEX idx_product_categories_org ON product_categories(org_id);
```

## Constraints and Business Rules

### Data Integrity
- All tables enforce organization-level isolation
- Product SKUs must be unique within organization
- Stock levels cannot be negative
- Reserved quantities cannot exceed on-hand quantities

### Referential Integrity
- Products can exist without categories (category_id nullable)
- Stock levels require valid product and location
- Transactions require valid product and location
- Line items can optionally reference products

## Next Steps

1. Create migration scripts for each table
2. Implement Row Level Security (RLS) policies
3. Create database functions for common operations
4. Set up triggers for automatic stock level updates
5. Generate TypeScript types from schema

## Migration Strategy

The schema will be implemented in phases:
1. Core tables (categories, locations, products)
2. Stock tracking (stock_levels, inventory_transactions)
3. Integration (modify line item tables)
4. Indexes and constraints
5. RLS policies and functions
