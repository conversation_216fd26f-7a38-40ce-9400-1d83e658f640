/**
 * API Interceptor
 * Automatically logs API calls, handles errors, and provides monitoring
 */

import { supabase } from '@/lib/supabase'
import { logger } from '@/lib/logger'
import { errorHandler, handleNetworkError } from '@/lib/errorHandler'

// Define types for API interceptor
type AuthMethod = (...args: unknown[]) => Promise<unknown>
type QueryMethod = (...args: unknown[]) => unknown
type SupabaseQueryBuilder = Record<string, unknown> & {
  [key: string]: QueryMethod | unknown
}

export interface ApiCallMetrics {
  method: string
  url: string
  status: number
  duration: number
  timestamp: Date
  userId?: string
  orgId?: string
  error?: Error
}

class ApiInterceptor {
  private static instance: ApiInterceptor
  private metrics: ApiCallMetrics[] = []
  private maxMetrics = 1000

  private constructor() {
    this.setupSupabaseInterceptors()
  }

  public static getInstance(): ApiInterceptor {
    if (!ApiInterceptor.instance) {
      ApiInterceptor.instance = new ApiInterceptor()
    }
    return ApiInterceptor.instance
  }

  private setupSupabaseInterceptors(): void {
    // Temporary disable for debugging - remove this when issue is resolved
    if (import.meta.env.VITE_DISABLE_API_INTERCEPTOR === 'true') {
      console.log('API interceptor disabled via environment variable')
      return
    }

    // Validate Supabase client before intercepting
    if (!supabase || typeof supabase.from !== 'function') {
      console.warn('Supabase client not properly initialized, skipping API interceptor setup')
      return
    }

    try {
      // Intercept Supabase REST API calls
      const originalFrom = supabase.from.bind(supabase)
      supabase.from = (table: string) => {
        try {
          const query = originalFrom(table)
          return this.wrapQueryBuilder(query, table)
        } catch (error) {
          console.error('Error in Supabase from() interceptor:', error)
          // Fallback to original method
          return originalFrom(table)
        }
      }
    } catch (error) {
      console.error('Failed to setup Supabase REST interceptors:', error)
    }

    // Intercept Supabase Auth calls
    const originalAuth = supabase.auth

    // Wrap auth methods
    const authMethods = ['signInWithPassword', 'signUp', 'signOut', 'resetPasswordForEmail']
    authMethods.forEach(method => {
      const originalMethod = (originalAuth as Record<string, AuthMethod>)[method]
      if (originalMethod) {
        (originalAuth as Record<string, AuthMethod>)[method] = async (...args: unknown[]) => {
          const startTime = Date.now()
          try {
            const result = await originalMethod.apply(originalAuth, args)
            const duration = Date.now() - startTime

            this.logApiCall(
              'POST',
              `/auth/${method}`,
              result.error ? 400 : 200,
              duration,
              undefined,
              undefined,
              result.error
            )
            
            return result
          } catch (error) {
            const duration = Date.now() - startTime
            this.logApiCall(
              'POST',
              `/auth/${method}`,
              500,
              duration,
              undefined,
              undefined,
              error as Error
            )
            throw error
          }
        }
      }
    })
  }

  private wrapQueryBuilder(query: SupabaseQueryBuilder, table: string): SupabaseQueryBuilder {
    // Validate query object
    if (!query || typeof query !== 'object') {
      console.warn('Invalid query object passed to wrapQueryBuilder')
      return query
    }

    try {
      // Wrap the execute methods
      const executeMethods = ['select', 'insert', 'update', 'delete', 'upsert']

      executeMethods.forEach(method => {
        const originalMethod = query[method]
        if (originalMethod && typeof originalMethod === 'function') {
          query[method] = (...args: unknown[]) => {
            try {
              const result = originalMethod.apply(query, args)

              // If this returns a promise (final execution), wrap it
              if (result && typeof result.then === 'function') {
                return this.wrapPromise(result, method.toUpperCase(), table)
              }

              // Otherwise, continue chaining
              return interceptorInstance.wrapQueryBuilder(result, table)
            } catch (error) {
              console.error(`Error in ${method} interceptor:`, error)
              // Fallback to original method
              return originalMethod.apply(this, args)
            }
          }
        }
      })
    } catch (error) {
      console.error('Error wrapping execute methods:', error)
    }

    try {
      // Wrap filter methods that might be final
      const filterMethods = ['eq', 'neq', 'gt', 'gte', 'lt', 'lte', 'like', 'ilike', 'is', 'in', 'contains', 'containedBy', 'rangeGt', 'rangeGte', 'rangeLt', 'rangeLte', 'rangeAdjacent', 'overlaps', 'textSearch', 'match', 'not', 'or', 'filter']

      filterMethods.forEach(method => {
        const originalMethod = query[method]
        if (originalMethod && typeof originalMethod === 'function') {
          query[method] = function(...args: unknown[]) {
            try {
              const result = originalMethod.apply(this, args)
              return interceptorInstance.wrapQueryBuilder(result, table)
            } catch (error) {
              console.error(`Error in ${method} filter interceptor:`, error)
              // Fallback to original method
              return originalMethod.apply(this, args)
            }
          }
        }
      })
    } catch (error) {
      console.error('Error wrapping filter methods:', error)
    }

    try {
      // Wrap modifier methods
      const modifierMethods = ['order', 'limit', 'range', 'single', 'maybeSingle', 'csv', 'geojson', 'explain']

      modifierMethods.forEach(method => {
        const originalMethod = query[method]
        if (originalMethod && typeof originalMethod === 'function') {
          query[method] = function(...args: unknown[]) {
            try {
              const result = originalMethod.apply(this, args)

              // If this returns a promise (final execution), wrap it
              if (result && typeof result.then === 'function') {
                return interceptorInstance.wrapPromise(result, 'SELECT', table)
              }

              return interceptorInstance.wrapQueryBuilder(result, table)
            } catch (error) {
              console.error(`Error in ${method} modifier interceptor:`, error)
              // Fallback to original method
              return originalMethod.apply(this, args)
            }
          }
        }
      })
    } catch (error) {
      console.error('Error wrapping modifier methods:', error)
    }

    return query
  }

  private async wrapPromise(promise: Promise<unknown>, method: string, table: string): Promise<unknown> {
    const startTime = Date.now()
    const url = `/rest/v1/${table}`
    
    try {
      const result = await promise
      const duration = Date.now() - startTime
      const status = result.error ? 400 : 200
      
      this.logApiCall(method, url, status, duration, undefined, undefined, result.error)
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      this.logApiCall(method, url, 500, duration, undefined, undefined, error as Error)
      throw error
    }
  }

  private logApiCall(
    method: string,
    url: string,
    status: number,
    duration: number,
    userId?: string,
    orgId?: string,
    error?: Error
  ): void {
    const timestamp = new Date()
    
    // Create metrics entry
    const metrics: ApiCallMetrics = {
      method,
      url,
      status,
      duration,
      timestamp,
      userId,
      orgId,
      error
    }

    // Store metrics
    this.metrics.push(metrics)
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // Log to our logging system
    const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'info'
    logger[level](`API ${method} ${url} - ${status}`, {
      component: 'API',
      action: `${method} ${url}`,
      duration,
      userId,
      orgId,
      metadata: {
        status,
        method,
        url,
        apiCall: true
      }
    })

    // Handle errors
    if (error) {
      handleNetworkError(error, {
        component: 'API',
        action: `${method} ${url}`,
        userId,
        orgId,
        metadata: {
          status,
          method,
          url,
          duration
        }
      })
    }

    // Log performance warnings
    if (duration > 5000) {
      logger.warn(`Slow API call detected: ${method} ${url}`, {
        component: 'Performance',
        action: `${method} ${url}`,
        duration,
        metadata: {
          slowApiCall: true,
          threshold: 5000
        }
      })
    }
  }

  public getMetrics(filters?: {
    method?: string
    status?: number
    since?: Date
    limit?: number
  }): ApiCallMetrics[] {
    let metrics = [...this.metrics]

    if (filters) {
      if (filters.method) {
        metrics = metrics.filter(m => m.method === filters.method)
      }
      if (filters.status) {
        metrics = metrics.filter(m => m.status === filters.status)
      }
      if (filters.since) {
        metrics = metrics.filter(m => m.timestamp >= filters.since!)
      }
      if (filters.limit) {
        metrics = metrics.slice(-filters.limit)
      }
    }

    return metrics.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  public getApiStats(): {
    total: number
    byMethod: Record<string, number>
    byStatus: Record<string, number>
    averageDuration: number
    errorRate: number
    slowCalls: number
  } {
    const total = this.metrics.length
    const byMethod: Record<string, number> = {}
    const byStatus: Record<string, number> = {}
    let totalDuration = 0
    let errorCount = 0
    let slowCalls = 0

    this.metrics.forEach(metric => {
      byMethod[metric.method] = (byMethod[metric.method] || 0) + 1
      byStatus[metric.status.toString()] = (byStatus[metric.status.toString()] || 0) + 1
      totalDuration += metric.duration
      
      if (metric.status >= 400) {
        errorCount++
      }
      
      if (metric.duration > 5000) {
        slowCalls++
      }
    })

    return {
      total,
      byMethod,
      byStatus,
      averageDuration: total > 0 ? totalDuration / total : 0,
      errorRate: total > 0 ? (errorCount / total) * 100 : 0,
      slowCalls
    }
  }

  public clearMetrics(): void {
    this.metrics = []
  }
}

// Initialize the interceptor
export const apiInterceptor = ApiInterceptor.getInstance()

// Utility function to manually log external API calls
export const logExternalApiCall = (
  method: string,
  url: string,
  status: number,
  duration: number,
  userId?: string,
  orgId?: string,
  error?: Error
) => {
  apiInterceptor['logApiCall'](method, url, status, duration, userId, orgId, error)
}

// Performance monitoring utilities
export const withApiMonitoring = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  context?: { userId?: string; orgId?: string }
): Promise<T> => {
  const startTime = Date.now()
  
  try {
    const result = await operation()
    const duration = Date.now() - startTime
    
    logger.logPerformance(operationName, duration, {
      ...context,
      component: 'API',
      action: operationName
    })
    
    return result
  } catch (error) {
    const duration = Date.now() - startTime
    
    logger.error(`Operation failed: ${operationName}`, {
      ...context,
      component: 'API',
      action: operationName,
      duration,
      metadata: { error: (error as Error).message }
    })
    
    throw error
  }
}
