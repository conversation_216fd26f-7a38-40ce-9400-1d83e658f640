-- =====================================================
-- NOTIFICATION SYSTEM RLS SECURITY POLICIES
-- =====================================================
-- Migration: 20250628_notification_rls_policies.sql
-- Description: Row Level Security policies for notification system
-- Author: Kaya Finance Team
-- Date: 2025-06-28

-- =====================================================
-- STEP 1: ENABLE RLS ON NOTIFICATION TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Enabling RLS on notification tables...';
END $$;

-- Enable RLS on all notification tables
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 2: NOTIFICATION TEMPLATES RLS POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📝 Creating notification templates RLS policies...';
END $$;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "notification_templates_select_policy" ON notification_templates;
DROP POLICY IF EXISTS "notification_templates_insert_policy" ON notification_templates;
DROP POLICY IF EXISTS "notification_templates_update_policy" ON notification_templates;
DROP POLICY IF EXISTS "notification_templates_delete_policy" ON notification_templates;

-- Templates are global and can be read by authenticated users
CREATE POLICY "notification_templates_select_policy" ON notification_templates
    FOR SELECT USING (auth.role() = 'authenticated');

-- Only service role can manage templates (for system administration)
CREATE POLICY "notification_templates_insert_policy" ON notification_templates
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "notification_templates_update_policy" ON notification_templates
    FOR UPDATE USING (auth.role() = 'service_role');

CREATE POLICY "notification_templates_delete_policy" ON notification_templates
    FOR DELETE USING (auth.role() = 'service_role');

-- =====================================================
-- STEP 3: NOTIFICATIONS RLS POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔔 Creating notifications RLS policies...';
END $$;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "notifications_select_policy" ON notifications;
DROP POLICY IF EXISTS "notifications_insert_policy" ON notifications;
DROP POLICY IF EXISTS "notifications_update_policy" ON notifications;
DROP POLICY IF EXISTS "notifications_delete_policy" ON notifications;

-- Users can view notifications that are either:
-- 1. Specifically for them (user_id matches)
-- 2. Organization-wide notifications (user_id is NULL) for their org
CREATE POLICY "notifications_select_policy" ON notifications
    FOR SELECT USING (
        -- User-specific notifications
        (user_id = auth.uid()) OR
        -- Organization-wide notifications for user's org
        (user_id IS NULL AND org_id IN (
            SELECT p.org_id FROM profiles p WHERE p.id = auth.uid()
        ))
    );

-- Users can create notifications for their organization
-- Service role can create any notification
CREATE POLICY "notifications_insert_policy" ON notifications
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role' OR
        (
            auth.role() = 'authenticated' AND
            org_id IN (
                SELECT p.org_id FROM profiles p WHERE p.id = auth.uid()
            )
        )
    );

-- Users can update notifications that belong to them or their org
-- Only specific fields can be updated by regular users
CREATE POLICY "notifications_update_policy" ON notifications
    FOR UPDATE USING (
        auth.role() = 'service_role' OR
        (
            auth.role() = 'authenticated' AND
            (
                -- User-specific notifications
                (user_id = auth.uid()) OR
                -- Organization-wide notifications for user's org
                (user_id IS NULL AND org_id IN (
                    SELECT p.org_id FROM profiles p WHERE p.id = auth.uid()
                ))
            )
        )
    );

-- Users can delete notifications that belong to them
-- Service role can delete any notification
CREATE POLICY "notifications_delete_policy" ON notifications
    FOR DELETE USING (
        auth.role() = 'service_role' OR
        (
            auth.role() = 'authenticated' AND
            user_id = auth.uid()
        )
    );

-- =====================================================
-- STEP 4: NOTIFICATION PREFERENCES RLS POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating notification preferences RLS policies...';
END $$;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "notification_preferences_select_policy" ON notification_preferences;
DROP POLICY IF EXISTS "notification_preferences_insert_policy" ON notification_preferences;
DROP POLICY IF EXISTS "notification_preferences_update_policy" ON notification_preferences;
DROP POLICY IF EXISTS "notification_preferences_delete_policy" ON notification_preferences;

-- Users can only view their own preferences
CREATE POLICY "notification_preferences_select_policy" ON notification_preferences
    FOR SELECT USING (user_id = auth.uid());

-- Users can only create their own preferences
CREATE POLICY "notification_preferences_insert_policy" ON notification_preferences
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can only update their own preferences
CREATE POLICY "notification_preferences_update_policy" ON notification_preferences
    FOR UPDATE USING (user_id = auth.uid());

-- Users can only delete their own preferences
CREATE POLICY "notification_preferences_delete_policy" ON notification_preferences
    FOR DELETE USING (user_id = auth.uid());

-- =====================================================
-- STEP 5: CREATE SECURITY HELPER FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🛡️ Creating security helper functions...';
END $$;

-- Function to check if user can access notification
CREATE OR REPLACE FUNCTION can_access_notification(notification_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    notification_record RECORD;
    user_org_id UUID;
BEGIN
    -- Get user's organization
    SELECT org_id INTO user_org_id
    FROM profiles
    WHERE id = auth.uid();
    
    IF user_org_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- Get notification details
    SELECT user_id, org_id INTO notification_record
    FROM notifications
    WHERE id = notification_id;
    
    IF NOT FOUND THEN
        RETURN false;
    END IF;
    
    -- Check access rights
    RETURN (
        -- User-specific notification
        notification_record.user_id = auth.uid() OR
        -- Organization-wide notification for user's org
        (notification_record.user_id IS NULL AND notification_record.org_id = user_org_id)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate notification creation
CREATE OR REPLACE FUNCTION validate_notification_creation(
    org_id_param UUID,
    user_id_param UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    user_org_id UUID;
BEGIN
    -- Service role can create any notification
    IF auth.role() = 'service_role' THEN
        RETURN true;
    END IF;
    
    -- Get user's organization
    SELECT org_id INTO user_org_id
    FROM profiles
    WHERE id = auth.uid();
    
    -- User must belong to the organization
    IF user_org_id != org_id_param THEN
        RETURN false;
    END IF;
    
    -- If user_id is specified, it must be within the same organization
    IF user_id_param IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM profiles
            WHERE id = user_id_param AND org_id = org_id_param
        ) THEN
            RETURN false;
        END IF;
    END IF;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 6: CREATE NOTIFICATION SECURITY VIEWS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '👁️ Creating security views...';
END $$;

-- View for user's accessible notifications
CREATE OR REPLACE VIEW user_notifications AS
SELECT 
    n.*,
    CASE 
        WHEN n.expires_at IS NOT NULL AND n.expires_at < NOW() THEN true
        ELSE false
    END as is_expired
FROM notifications n
WHERE (
    -- User-specific notifications
    n.user_id = auth.uid() OR
    -- Organization-wide notifications for user's org
    (n.user_id IS NULL AND n.org_id IN (
        SELECT p.org_id FROM profiles p WHERE p.id = auth.uid()
    ))
);

-- View for user's notification statistics
CREATE OR REPLACE VIEW user_notification_stats AS
SELECT 
    COUNT(*) as total_notifications,
    COUNT(*) FILTER (WHERE is_read = false) as unread_notifications,
    COUNT(*) FILTER (WHERE is_archived = true) as archived_notifications,
    COUNT(*) FILTER (WHERE priority = 'urgent') as urgent_notifications,
    COUNT(*) FILTER (WHERE priority = 'high') as high_priority_notifications,
    jsonb_object_agg(category, category_count) as by_category,
    jsonb_object_agg(priority, priority_count) as by_priority
FROM (
    SELECT 
        category,
        priority,
        is_read,
        is_archived,
        COUNT(*) OVER (PARTITION BY category) as category_count,
        COUNT(*) OVER (PARTITION BY priority) as priority_count
    FROM user_notifications
    WHERE is_archived = false
) stats;

-- =====================================================
-- STEP 7: VERIFICATION AND TESTING
-- =====================================================

DO $$
DECLARE
    policy_count INTEGER;
    table_count INTEGER;
BEGIN
    RAISE NOTICE '🔍 Verifying RLS policies...';
    
    -- Count RLS-enabled tables
    SELECT COUNT(*) INTO table_count
    FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename IN ('notification_templates', 'notifications', 'notification_preferences')
    AND rowsecurity = true;
    
    -- Count policies
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename IN ('notification_templates', 'notifications', 'notification_preferences');
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ RLS SECURITY POLICIES COMPLETED!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 VERIFICATION RESULTS:';
    RAISE NOTICE '  • Tables with RLS enabled: %', table_count;
    RAISE NOTICE '  • Security policies created: %', policy_count;
    RAISE NOTICE '';
    RAISE NOTICE '🔒 SECURITY FEATURES:';
    RAISE NOTICE '  • Organization-based isolation';
    RAISE NOTICE '  • User-specific notification access';
    RAISE NOTICE '  • Service role administrative access';
    RAISE NOTICE '  • Preference privacy protection';
    RAISE NOTICE '';
    RAISE NOTICE '🛡️ HELPER FUNCTIONS:';
    RAISE NOTICE '  • can_access_notification()';
    RAISE NOTICE '  • validate_notification_creation()';
    RAISE NOTICE '';
    RAISE NOTICE '👁️ SECURITY VIEWS:';
    RAISE NOTICE '  • user_notifications';
    RAISE NOTICE '  • user_notification_stats';
    RAISE NOTICE '';
    
    IF table_count = 3 AND policy_count >= 12 THEN
        RAISE NOTICE '🎉 All security policies successfully implemented!';
    ELSE
        RAISE WARNING '⚠️ Some policies may be missing. Expected 3 tables and 12+ policies.';
    END IF;
    
    RAISE NOTICE '';
END $$;
