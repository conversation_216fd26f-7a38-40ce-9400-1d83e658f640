import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { InvoiceForm } from '@/components/invoices/InvoiceForm'
import { useCreateInvoice, useUpdateInvoice } from '@/hooks/queries'
import type { Customer, Account } from '@/types/database'
import type { InvoiceFormData, InvoiceLineData } from '@/types/invoices'

// Mock the hooks
jest.mock('@/hooks/queries', () => ({
  useCreateInvoice: jest.fn(),
  useUpdateInvoice: jest.fn(),
}))

jest.mock('@/hooks/useAuthHook', () => ({
  useAuth: () => ({
    profile: { org_id: 'test-org-id' }
  })
}))

jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}))

const mockCreateInvoice = useCreateInvoice as jest.MockedFunction<typeof useCreateInvoice>
const mockUpdateInvoice = useUpdateInvoice as jest.MockedFunction<typeof useUpdateInvoice>

const mockCustomers: Customer[] = [
  {
    id: 'customer-1',
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '+************',
    tin_number: '**********',
    org_id: 'test-org-id',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    address: null,
    notes: null,
    payment_terms: 30
  }
]

const mockAccounts: Account[] = [
  {
    id: 'account-1',
    name: 'Accounts Receivable',
    code: '1200',
    type: 'asset',
    org_id: 'test-org-id',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    description: null,
    parent_id: null
  }
]

describe('Invoice Management', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    mockCreateInvoice.mockReturnValue({
      mutate: jest.fn(),
      isPending: false,
      error: null,
    })

    mockUpdateInvoice.mockReturnValue({
      mutate: jest.fn(),
      isPending: false,
      error: null,
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    )
  }

  describe('InvoiceForm', () => {
    it('renders create invoice form correctly', () => {
      renderWithProviders(
        <InvoiceForm
          open={true}
          onOpenChange={jest.fn()}
          editingInvoice={null}
          customers={mockCustomers}
          accounts={mockAccounts}
          onSubmit={jest.fn()}
        />
      )

      expect(screen.getByText('Create New Invoice')).toBeInTheDocument()
      expect(screen.getByText('Customer *')).toBeInTheDocument()
      expect(screen.getByText('Account *')).toBeInTheDocument()
      expect(screen.getByText('Invoice Number')).toBeInTheDocument()
      expect(screen.getByText('Date Issued')).toBeInTheDocument()
      expect(screen.getByText('Due Date')).toBeInTheDocument()
    })

    it('validates required fields', async () => {
      const user = userEvent.setup()
      const onSubmit = jest.fn()
      
      renderWithProviders(
        <InvoiceForm
          open={true}
          onOpenChange={jest.fn()}
          editingInvoice={null}
          customers={mockCustomers}
          accounts={mockAccounts}
          onSubmit={onSubmit}
        />
      )

      const submitButton = screen.getByRole('button', { name: /create invoice/i })
      await user.click(submitButton)

      // Should not call onSubmit if validation fails
      expect(onSubmit).not.toHaveBeenCalled()
    })

    it('calculates totals correctly', async () => {
      const user = userEvent.setup()
      const onSubmit = jest.fn()
      
      renderWithProviders(
        <InvoiceForm
          open={true}
          onOpenChange={jest.fn()}
          editingInvoice={null}
          customers={mockCustomers}
          accounts={mockAccounts}
          onSubmit={onSubmit}
        />
      )

      // Fill in required fields
      const customerSelect = screen.getByRole('combobox', { name: /customer/i })
      await user.click(customerSelect)
      await user.click(screen.getByText('Test Customer'))

      const accountSelect = screen.getByRole('combobox', { name: /account/i })
      await user.click(accountSelect)
      await user.click(screen.getByText('Accounts Receivable'))

      // Fill in line item
      const itemInput = screen.getByLabelText(/item/i)
      await user.type(itemInput, 'Test Item')

      const quantityInput = screen.getByLabelText(/quantity/i)
      await user.clear(quantityInput)
      await user.type(quantityInput, '2')

      const unitPriceInput = screen.getByLabelText(/unit price/i)
      await user.clear(unitPriceInput)
      await user.type(unitPriceInput, '1000')

      const taxRateInput = screen.getByLabelText(/tax rate/i)
      await user.clear(taxRateInput)
      await user.type(taxRateInput, '18')

      // Check if totals are calculated correctly
      // Subtotal: 2 * 1000 = 2000
      // Tax: 2000 * 0.18 = 360
      // Total: 2000 + 360 = 2360
      await waitFor(() => {
        expect(screen.getByText('2,000')).toBeInTheDocument() // Subtotal
        expect(screen.getByText('360')).toBeInTheDocument() // Tax
        expect(screen.getByText('2,360')).toBeInTheDocument() // Total
      })
    })

    it('adds and removes line items', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(
        <InvoiceForm
          open={true}
          onOpenChange={jest.fn()}
          editingInvoice={null}
          customers={mockCustomers}
          accounts={mockAccounts}
          onSubmit={jest.fn()}
        />
      )

      // Initially should have one line item
      expect(screen.getAllByLabelText(/item/i)).toHaveLength(1)

      // Add another line item
      const addLineButton = screen.getByRole('button', { name: /add line/i })
      await user.click(addLineButton)

      expect(screen.getAllByLabelText(/item/i)).toHaveLength(2)

      // Remove a line item
      const removeButtons = screen.getAllByRole('button', { name: /remove/i })
      await user.click(removeButtons[0])

      expect(screen.getAllByLabelText(/item/i)).toHaveLength(1)
    })

    it('submits invoice with correct data', async () => {
      const user = userEvent.setup()
      const onSubmit = jest.fn()
      
      renderWithProviders(
        <InvoiceForm
          open={true}
          onOpenChange={jest.fn()}
          editingInvoice={null}
          customers={mockCustomers}
          accounts={mockAccounts}
          onSubmit={onSubmit}
        />
      )

      // Fill in all required fields
      const customerSelect = screen.getByRole('combobox', { name: /customer/i })
      await user.click(customerSelect)
      await user.click(screen.getByText('Test Customer'))

      const accountSelect = screen.getByRole('combobox', { name: /account/i })
      await user.click(accountSelect)
      await user.click(screen.getByText('Accounts Receivable'))

      const invoiceNumberInput = screen.getByLabelText(/invoice number/i)
      await user.type(invoiceNumberInput, 'INV-001')

      const dueDateInput = screen.getByLabelText(/due date/i)
      await user.type(dueDateInput, '2024-12-31')

      // Fill in line item
      const itemInput = screen.getByLabelText(/item/i)
      await user.type(itemInput, 'Test Item')

      const descriptionInput = screen.getByLabelText(/description/i)
      await user.type(descriptionInput, 'Test Description')

      const quantityInput = screen.getByLabelText(/quantity/i)
      await user.clear(quantityInput)
      await user.type(quantityInput, '1')

      const unitPriceInput = screen.getByLabelText(/unit price/i)
      await user.clear(unitPriceInput)
      await user.type(unitPriceInput, '1000')

      const submitButton = screen.getByRole('button', { name: /create invoice/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(onSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            customer_id: 'customer-1',
            account_id: 'account-1',
            invoice_number: 'INV-001',
            due_date: '2024-12-31',
            status: 'draft'
          }),
          expect.arrayContaining([
            expect.objectContaining({
              item: 'Test Item',
              description: 'Test Description',
              quantity: 1,
              unit_price: 1000,
              tax_rate_pct: 0
            })
          ])
        )
      })
    })
  })

  describe('Invoice Calculations', () => {
    it('calculates line totals correctly', () => {
      const lineData: InvoiceLineData = {
        item: 'Test Item',
        description: 'Test Description',
        quantity: 2,
        unit_price: 1000,
        tax_rate_pct: 18
      }

      const lineTotal = lineData.quantity * lineData.unit_price
      const lineTax = lineTotal * (lineData.tax_rate_pct / 100)
      const lineGrandTotal = lineTotal + lineTax

      expect(lineTotal).toBe(2000)
      expect(lineTax).toBe(360)
      expect(lineGrandTotal).toBe(2360)
    })

    it('handles zero tax rate', () => {
      const lineData: InvoiceLineData = {
        item: 'Test Item',
        description: 'Test Description',
        quantity: 1,
        unit_price: 1000,
        tax_rate_pct: 0
      }

      const lineTotal = lineData.quantity * lineData.unit_price
      const lineTax = lineTotal * (lineData.tax_rate_pct / 100)

      expect(lineTotal).toBe(1000)
      expect(lineTax).toBe(0)
    })

    it('handles decimal quantities and prices', () => {
      const lineData: InvoiceLineData = {
        item: 'Test Item',
        description: 'Test Description',
        quantity: 2.5,
        unit_price: 999.99,
        tax_rate_pct: 18
      }

      const lineTotal = lineData.quantity * lineData.unit_price
      const lineTax = lineTotal * (lineData.tax_rate_pct / 100)

      expect(lineTotal).toBeCloseTo(2499.975, 2)
      expect(lineTax).toBeCloseTo(449.9955, 2)
    })
  })
})
