// Backup Security Configuration and Validation
// Provides additional security layers for backup operations

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'
import { BackupRBACManager } from './backup-rbac'

export interface SecurityValidationResult {
  allowed: boolean
  reason?: string
  requiresApproval?: boolean
}

export interface BackupSecurityContext {
  userId: string
  orgId: string
  userRole: string
  operation: 'create' | 'restore' | 'delete' | 'download'
  backupId?: string
  restoreMode?: 'replace' | 'merge' | 'preview'
}

/**
 * Backup Security Manager
 * Handles security validation, access control, and audit logging for backup operations
 */
export class BackupSecurityManager {
  /**
   * Validate if user can perform backup operation
   */
  static async validateOperation(context: BackupSecurityContext): Promise<SecurityValidationResult> {
    try {
      // Log security check attempt
      await auditLogger.logActivity({
        entity_type: 'backup_security',
        entity_id: context.backupId || 'system',
        action: `validate_${context.operation}`,
        description: `Security validation for ${context.operation} operation`,
        severity: 'info',
        category: 'security',
        metadata: {
          user_role: context.userRole,
          operation: context.operation,
          restore_mode: context.restoreMode
        }
      })

      // Check RBAC permissions first
      const rbacContext = {
        mode: context.restoreMode,
        backup_id: context.backupId
      }

      const rbacResult = await BackupRBACManager.hasPermission(
        context.userId,
        context.orgId,
        context.operation,
        'backup',
        rbacContext
      )

      if (!rbacResult.allowed) {
        return {
          allowed: false,
          reason: rbacResult.reason || 'Access denied by role-based access control',
          requiresApproval: rbacResult.requiresApproval
        }
      }

      // If RBAC requires approval, return that requirement
      if (rbacResult.requiresApproval) {
        return {
          allowed: false,
          requiresApproval: true,
          reason: 'This operation requires approval from an administrator'
        }
      }

      // Check operation-specific security rules
      switch (context.operation) {
        case 'create':
          return await this.validateBackupCreation(context)
        case 'restore':
          return await this.validateBackupRestore(context)
        case 'delete':
          return await this.validateBackupDeletion(context)
        case 'download':
          return await this.validateBackupDownload(context)
        default:
          return { allowed: false, reason: 'Unknown operation' }
      }
    } catch (error) {
      console.error('Security validation error:', error)
      return { allowed: false, reason: 'Security validation failed' }
    }
  }

  /**
   * Validate role-based permissions
   */
  private static validateRolePermissions(context: BackupSecurityContext): SecurityValidationResult {
    const { userRole, operation } = context

    // Define role-based permissions
    const permissions = {
      owner: ['create', 'restore', 'delete', 'download'],
      admin: ['create', 'restore', 'delete', 'download'],
      accountant: ['create', 'download'],
      viewer: ['download']
    }

    const allowedOperations = permissions[userRole as keyof typeof permissions] || []

    if (!allowedOperations.includes(operation)) {
      return {
        allowed: false,
        reason: `Role '${userRole}' is not permitted to perform '${operation}' operation`
      }
    }

    return { allowed: true }
  }

  /**
   * Validate backup creation security
   */
  private static async validateBackupCreation(context: BackupSecurityContext): Promise<SecurityValidationResult> {
    try {
      // Check if there's already a backup in progress
      const { data: activeBackups, error } = await supabase
        .from('backup_metadata')
        .select('id, status')
        .eq('org_id', context.orgId)
        .in('status', ['pending', 'in_progress'])

      if (error) {
        return { allowed: false, reason: 'Failed to check active backups' }
      }

      if (activeBackups && activeBackups.length > 0) {
        return {
          allowed: false,
          reason: 'Another backup operation is already in progress'
        }
      }

      // Check backup frequency limits (prevent abuse)
      const { data: recentBackups, error: recentError } = await supabase
        .from('backup_metadata')
        .select('id')
        .eq('org_id', context.orgId)
        .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour

      if (recentError) {
        return { allowed: false, reason: 'Failed to check recent backups' }
      }

      if (recentBackups && recentBackups.length >= 5) {
        return {
          allowed: false,
          reason: 'Too many backup attempts in the last hour. Please wait before creating another backup.'
        }
      }

      return { allowed: true }
    } catch (error) {
      return { allowed: false, reason: 'Backup creation validation failed' }
    }
  }

  /**
   * Validate backup restoration security
   */
  private static async validateBackupRestore(context: BackupSecurityContext): Promise<SecurityValidationResult> {
    try {
      if (!context.backupId) {
        return { allowed: false, reason: 'Backup ID is required for restore operation' }
      }

      // Verify backup exists and belongs to organization
      const { data: backup, error } = await supabase
        .from('backup_metadata')
        .select('*')
        .eq('id', context.backupId)
        .eq('org_id', context.orgId)
        .single()

      if (error || !backup) {
        return { allowed: false, reason: 'Backup not found or access denied' }
      }

      // Check backup status
      if (backup.status !== 'completed') {
        return {
          allowed: false,
          reason: `Cannot restore backup with status: ${backup.status}`
        }
      }

      // Check if restore mode requires approval
      if (context.restoreMode === 'replace') {
        // Replace mode is dangerous and requires owner/admin role
        if (!['owner', 'admin'].includes(context.userRole)) {
          return {
            allowed: false,
            reason: 'Replace mode restoration requires admin or owner privileges'
          }
        }

        // For replace mode, require additional approval for non-owners
        if (context.userRole === 'admin') {
          return {
            allowed: true,
            requiresApproval: true,
            reason: 'Replace mode restoration requires owner approval'
          }
        }
      }

      // Check if there's already a restoration in progress
      const { data: activeRestorations, error: restoreError } = await supabase
        .from('backup_restorations')
        .select('id')
        .eq('org_id', context.orgId)
        .in('status', ['pending', 'in_progress'])

      if (restoreError) {
        return { allowed: false, reason: 'Failed to check active restorations' }
      }

      if (activeRestorations && activeRestorations.length > 0) {
        return {
          allowed: false,
          reason: 'Another restoration operation is already in progress'
        }
      }

      return { allowed: true }
    } catch (error) {
      return { allowed: false, reason: 'Backup restore validation failed' }
    }
  }

  /**
   * Validate backup deletion security
   */
  private static async validateBackupDeletion(context: BackupSecurityContext): Promise<SecurityValidationResult> {
    try {
      if (!context.backupId) {
        return { allowed: false, reason: 'Backup ID is required for delete operation' }
      }

      // Only owners and admins can delete backups
      if (!['owner', 'admin'].includes(context.userRole)) {
        return {
          allowed: false,
          reason: 'Only owners and admins can delete backups'
        }
      }

      // Verify backup exists and belongs to organization
      const { data: backup, error } = await supabase
        .from('backup_metadata')
        .select('*')
        .eq('id', context.backupId)
        .eq('org_id', context.orgId)
        .single()

      if (error || !backup) {
        return { allowed: false, reason: 'Backup not found or access denied' }
      }

      // Don't allow deletion of the most recent successful backup
      const { data: latestBackup, error: latestError } = await supabase
        .from('backup_metadata')
        .select('id')
        .eq('org_id', context.orgId)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (!latestError && latestBackup && latestBackup.id === context.backupId) {
        return {
          allowed: false,
          reason: 'Cannot delete the most recent successful backup'
        }
      }

      // Check if backup is currently being used for restoration
      const { data: activeRestorations, error: restoreError } = await supabase
        .from('backup_restorations')
        .select('id')
        .eq('backup_id', context.backupId)
        .in('status', ['pending', 'in_progress'])

      if (restoreError) {
        return { allowed: false, reason: 'Failed to check backup usage' }
      }

      if (activeRestorations && activeRestorations.length > 0) {
        return {
          allowed: false,
          reason: 'Cannot delete backup that is currently being used for restoration'
        }
      }

      return { allowed: true }
    } catch (error) {
      return { allowed: false, reason: 'Backup deletion validation failed' }
    }
  }

  /**
   * Validate backup download security
   */
  private static async validateBackupDownload(context: BackupSecurityContext): Promise<SecurityValidationResult> {
    try {
      if (!context.backupId) {
        return { allowed: false, reason: 'Backup ID is required for download operation' }
      }

      // Verify backup exists and belongs to organization
      const { data: backup, error } = await supabase
        .from('backup_metadata')
        .select('status, org_id')
        .eq('id', context.backupId)
        .eq('org_id', context.orgId)
        .single()

      if (error || !backup) {
        return { allowed: false, reason: 'Backup not found or access denied' }
      }

      // Only allow download of completed backups
      if (backup.status !== 'completed') {
        return {
          allowed: false,
          reason: `Cannot download backup with status: ${backup.status}`
        }
      }

      return { allowed: true }
    } catch (error) {
      return { allowed: false, reason: 'Backup download validation failed' }
    }
  }

  /**
   * Log security event
   */
  static async logSecurityEvent(
    context: BackupSecurityContext,
    result: SecurityValidationResult,
    additionalData?: Record<string, unknown>
  ): Promise<void> {
    try {
      await auditLogger.logActivity({
        entity_type: 'backup_security',
        entity_id: context.backupId || 'system',
        action: `security_${result.allowed ? 'allowed' : 'denied'}`,
        description: `Security check for ${context.operation}: ${result.allowed ? 'ALLOWED' : 'DENIED'}`,
        severity: result.allowed ? 'info' : 'warning',
        category: 'security',
        metadata: {
          ...context,
          result: result.allowed,
          reason: result.reason,
          requires_approval: result.requiresApproval,
          ...additionalData
        }
      })
    } catch (error) {
      console.error('Failed to log security event:', error)
    }
  }
}
