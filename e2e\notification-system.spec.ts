/**
 * End-to-End Tests for Notification System
 * Tests complete user journeys and system interactions
 */

import { test, expect, Page } from '@playwright/test'

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'testpassword123',
  name: 'Test User'
}

const testOrganization = {
  name: 'Test Organization',
  id: 'test-org-123'
}

// Helper functions
async function loginUser(page: Page) {
  await page.goto('/login')
  await page.fill('[data-testid="email-input"]', testUser.email)
  await page.fill('[data-testid="password-input"]', testUser.password)
  await page.click('[data-testid="login-button"]')
  await page.waitForURL('/dashboard')
}

async function createTestNotification(page: Page) {
  // Navigate to a page that would trigger a notification
  await page.goto('/payments/new')
  await page.fill('[data-testid="amount-input"]', '1000')
  await page.fill('[data-testid="payee-input"]', 'Test Vendor')
  await page.fill('[data-testid="description-input"]', 'Test payment for approval')
  await page.click('[data-testid="submit-payment"]')
  
  // This should create a payment_pending_approval notification
  await page.waitForSelector('[data-testid="success-message"]')
}

test.describe('Notification System E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/')
  })

  test.describe('Notification Dropdown', () => {
    test('displays notification count and opens dropdown', async ({ page }) => {
      await loginUser(page)
      
      // Check notification bell is visible
      const notificationBell = page.locator('[data-testid="notification-bell"]')
      await expect(notificationBell).toBeVisible()
      
      // Check for notification count badge
      const countBadge = page.locator('[data-testid="notification-count"]')
      if (await countBadge.isVisible()) {
        const count = await countBadge.textContent()
        expect(parseInt(count || '0')).toBeGreaterThanOrEqual(0)
      }
      
      // Click to open dropdown
      await notificationBell.click()
      
      // Verify dropdown is open
      const dropdown = page.locator('[data-testid="notification-dropdown"]')
      await expect(dropdown).toBeVisible()
    })

    test('shows notifications in dropdown', async ({ page }) => {
      await loginUser(page)
      
      // Create a test notification
      await createTestNotification(page)
      
      // Open notification dropdown
      await page.click('[data-testid="notification-bell"]')
      
      // Check for notifications
      const notifications = page.locator('[data-testid="notification-item"]')
      await expect(notifications.first()).toBeVisible()
      
      // Verify notification content
      const firstNotification = notifications.first()
      await expect(firstNotification.locator('[data-testid="notification-title"]')).toContainText('Payment')
      await expect(firstNotification.locator('[data-testid="notification-message"]')).toBeVisible()
      await expect(firstNotification.locator('[data-testid="notification-time"]')).toBeVisible()
    })

    test('marks notification as read when clicked', async ({ page }) => {
      await loginUser(page)
      await createTestNotification(page)
      
      // Open dropdown and get initial count
      await page.click('[data-testid="notification-bell"]')
      const initialCount = await page.locator('[data-testid="notification-count"]').textContent()
      
      // Click on first notification
      const firstNotification = page.locator('[data-testid="notification-item"]').first()
      await firstNotification.click()
      
      // Verify notification is marked as read (visual indicator change)
      await expect(firstNotification).toHaveClass(/read/)
      
      // Check if count decreased
      const newCount = await page.locator('[data-testid="notification-count"]').textContent()
      if (initialCount && newCount) {
        expect(parseInt(newCount)).toBeLessThanOrEqual(parseInt(initialCount))
      }
    })

    test('marks all notifications as read', async ({ page }) => {
      await loginUser(page)
      await createTestNotification(page)
      
      // Open dropdown
      await page.click('[data-testid="notification-bell"]')
      
      // Click mark all as read
      await page.click('[data-testid="mark-all-read"]')
      
      // Verify all notifications are marked as read
      const notifications = page.locator('[data-testid="notification-item"]')
      const count = await notifications.count()
      
      for (let i = 0; i < count; i++) {
        await expect(notifications.nth(i)).toHaveClass(/read/)
      }
      
      // Verify count badge is hidden or shows 0
      const countBadge = page.locator('[data-testid="notification-count"]')
      if (await countBadge.isVisible()) {
        await expect(countBadge).toHaveText('0')
      }
    })
  })

  test.describe('Notification Center', () => {
    test('navigates to notification center and displays all notifications', async ({ page }) => {
      await loginUser(page)
      
      // Navigate to notification center
      await page.goto('/notifications')
      
      // Verify page loaded
      await expect(page.locator('h1')).toContainText('Notification Center')
      
      // Check for notification list
      const notificationList = page.locator('[data-testid="notification-list"]')
      await expect(notificationList).toBeVisible()
      
      // Verify tabs are present
      await expect(page.locator('[data-testid="tab-all"]')).toBeVisible()
      await expect(page.locator('[data-testid="tab-unread"]')).toBeVisible()
      await expect(page.locator('[data-testid="tab-archived"]')).toBeVisible()
    })

    test('filters notifications by type', async ({ page }) => {
      await loginUser(page)
      await page.goto('/notifications')
      
      // Click on unread tab
      await page.click('[data-testid="tab-unread"]')
      
      // Verify only unread notifications are shown
      const notifications = page.locator('[data-testid="notification-item"]')
      const count = await notifications.count()
      
      for (let i = 0; i < count; i++) {
        await expect(notifications.nth(i)).not.toHaveClass(/read/)
      }
    })

    test('searches notifications', async ({ page }) => {
      await loginUser(page)
      await page.goto('/notifications')
      
      // Enter search term
      await page.fill('[data-testid="notification-search"]', 'payment')
      
      // Wait for search results
      await page.waitForTimeout(500)
      
      // Verify filtered results
      const notifications = page.locator('[data-testid="notification-item"]')
      const count = await notifications.count()
      
      if (count > 0) {
        for (let i = 0; i < count; i++) {
          const notificationText = await notifications.nth(i).textContent()
          expect(notificationText?.toLowerCase()).toContain('payment')
        }
      }
    })

    test('performs bulk operations', async ({ page }) => {
      await loginUser(page)
      await createTestNotification(page)
      await page.goto('/notifications')
      
      // Select multiple notifications
      const checkboxes = page.locator('[data-testid="notification-checkbox"]')
      const count = Math.min(await checkboxes.count(), 3)
      
      for (let i = 0; i < count; i++) {
        await checkboxes.nth(i).check()
      }
      
      // Verify bulk actions are available
      await expect(page.locator('[data-testid="bulk-actions"]')).toBeVisible()
      
      // Perform bulk mark as read
      await page.click('[data-testid="bulk-mark-read"]')
      
      // Verify success message
      await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    })
  })

  test.describe('Notification Settings', () => {
    test('navigates to settings and updates preferences', async ({ page }) => {
      await loginUser(page)
      await page.goto('/notifications/settings')
      
      // Verify settings page loaded
      await expect(page.locator('h3')).toContainText('Notification Preferences')
      
      // Find a notification type toggle
      const paymentToggle = page.locator('[data-testid="toggle-payment_pending_approval"]')
      await expect(paymentToggle).toBeVisible()
      
      // Toggle the setting
      const initialState = await paymentToggle.isChecked()
      await paymentToggle.click()
      
      // Save changes
      await page.click('[data-testid="save-preferences"]')
      
      // Verify success message
      await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
      
      // Refresh page and verify setting persisted
      await page.reload()
      await expect(paymentToggle).toBeChecked({ checked: !initialState })
    })

    test('configures push notifications', async ({ page }) => {
      await loginUser(page)
      await page.goto('/notifications/settings')
      
      // Check if push notifications are supported
      const pushSection = page.locator('[data-testid="push-notifications-section"]')
      
      if (await pushSection.isVisible()) {
        // Try to enable push notifications
        const pushToggle = page.locator('[data-testid="push-notifications-toggle"]')
        
        if (await pushToggle.isVisible() && !await pushToggle.isChecked()) {
          await pushToggle.click()
          
          // Handle browser permission dialog (if any)
          // Note: This might require special browser permissions in test environment
        }
      }
    })
  })

  test.describe('External Integrations', () => {
    test('manages Slack integration', async ({ page }) => {
      await loginUser(page)
      await page.goto('/notifications/integrations')
      
      // Verify integrations page
      await expect(page.locator('h3')).toContainText('External Integrations')
      
      // Add new Slack integration
      await page.click('[data-testid="add-integration"]')
      
      // Fill integration form
      await page.selectOption('[data-testid="integration-type"]', 'slack')
      await page.fill('[data-testid="integration-name"]', 'Test Slack Integration')
      await page.fill('[data-testid="webhook-url"]', 'https://hooks.slack.com/services/test/test/test')
      
      // Select notification types
      await page.check('[data-testid="notification-type-payment_pending_approval"]')
      await page.check('[data-testid="notification-type-invoice_overdue"]')
      
      // Save integration
      await page.click('[data-testid="save-integration"]')
      
      // Verify integration was created
      await expect(page.locator('[data-testid="integration-item"]')).toContainText('Test Slack Integration')
    })

    test('tests integration connectivity', async ({ page }) => {
      await loginUser(page)
      await page.goto('/notifications/integrations')
      
      // Assuming there's an existing integration
      const testButton = page.locator('[data-testid="test-integration"]').first()
      
      if (await testButton.isVisible()) {
        await testButton.click()
        
        // Wait for test result
        await page.waitForTimeout(2000)
        
        // Check for success or error message
        const result = page.locator('[data-testid="test-result"]')
        await expect(result).toBeVisible()
      }
    })
  })

  test.describe('Real-time Updates', () => {
    test('receives real-time notification updates', async ({ page, context }) => {
      await loginUser(page)
      
      // Open a second page to simulate another user action
      const secondPage = await context.newPage()
      await loginUser(secondPage)
      
      // Create notification from second page
      await createTestNotification(secondPage)
      
      // Check if first page receives the update
      await page.waitForTimeout(2000)
      
      // Verify notification appears in real-time
      await page.click('[data-testid="notification-bell"]')
      const notifications = page.locator('[data-testid="notification-item"]')
      await expect(notifications.first()).toBeVisible()
      
      await secondPage.close()
    })

    test('shows connection status indicator', async ({ page }) => {
      await loginUser(page)
      
      // Check for real-time connection indicator
      const connectionStatus = page.locator('[data-testid="connection-status"]')
      
      if (await connectionStatus.isVisible()) {
        // Should show connected status
        await expect(connectionStatus).toContainText(/connected|real-time/i)
      }
    })
  })

  test.describe('Performance Tests', () => {
    test('loads notification dropdown quickly', async ({ page }) => {
      await loginUser(page)
      
      const startTime = Date.now()
      await page.click('[data-testid="notification-bell"]')
      await page.waitForSelector('[data-testid="notification-dropdown"]')
      const endTime = Date.now()
      
      const loadTime = endTime - startTime
      expect(loadTime).toBeLessThan(1000) // Should load within 1 second
    })

    test('handles large notification lists efficiently', async ({ page }) => {
      await loginUser(page)
      await page.goto('/notifications')
      
      // Scroll through notifications to test virtual scrolling
      const notificationList = page.locator('[data-testid="notification-list"]')
      
      for (let i = 0; i < 5; i++) {
        await notificationList.evaluate(el => el.scrollTop += 500)
        await page.waitForTimeout(100)
      }
      
      // Verify page remains responsive
      await expect(page.locator('[data-testid="notification-item"]').first()).toBeVisible()
    })
  })

  test.describe('Accessibility', () => {
    test('supports keyboard navigation', async ({ page }) => {
      await loginUser(page)
      
      // Focus on notification bell
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab') // Navigate to notification bell
      
      // Open with Enter key
      await page.keyboard.press('Enter')
      
      // Verify dropdown opened
      await expect(page.locator('[data-testid="notification-dropdown"]')).toBeVisible()
      
      // Navigate through notifications with arrow keys
      await page.keyboard.press('ArrowDown')
      await page.keyboard.press('ArrowDown')
      
      // Close with Escape
      await page.keyboard.press('Escape')
      await expect(page.locator('[data-testid="notification-dropdown"]')).not.toBeVisible()
    })

    test('has proper ARIA labels and roles', async ({ page }) => {
      await loginUser(page)
      
      // Check notification bell accessibility
      const notificationBell = page.locator('[data-testid="notification-bell"]')
      await expect(notificationBell).toHaveAttribute('role', 'button')
      await expect(notificationBell).toHaveAttribute('aria-label')
      
      // Open dropdown and check accessibility
      await notificationBell.click()
      
      const dropdown = page.locator('[data-testid="notification-dropdown"]')
      await expect(dropdown).toHaveAttribute('role', 'menu')
      
      // Check notification items
      const notifications = page.locator('[data-testid="notification-item"]')
      if (await notifications.count() > 0) {
        await expect(notifications.first()).toHaveAttribute('role', 'menuitem')
      }
    })
  })
})
