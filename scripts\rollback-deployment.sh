#!/bin/bash

# =====================================================
# KAYA FINANCE ROLLBACK SCRIPT
# Emergency rollback for failed deployments
# =====================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Default values
ENVIRONMENT=""
TARGET_VERSION=""
FORCE=false
DRY_RUN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -e|--environment)
      ENVIRONMENT="$2"
      shift 2
      ;;
    -v|--version)
      TARGET_VERSION="$2"
      shift 2
      ;;
    --force)
      FORCE=true
      shift
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    -h|--help)
      echo "Usage: $0 -e <environment> [options]"
      echo "Options:"
      echo "  -e, --environment    Target environment (staging|production)"
      echo "  -v, --version        Specific version to rollback to"
      echo "  --force             Skip confirmation prompts"
      echo "  --dry-run           Show what would be done without executing"
      echo "  -h, --help          Show this help message"
      exit 0
      ;;
    *)
      print_error "Unknown option $1"
      exit 1
      ;;
  esac
done

# Validate environment
if [[ -z "$ENVIRONMENT" ]]; then
  print_error "Environment is required. Use -e staging or -e production"
  exit 1
fi

if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
  print_error "Environment must be 'staging' or 'production'"
  exit 1
fi

print_warning "🔄 EMERGENCY ROLLBACK INITIATED"
print_status "Environment: $ENVIRONMENT"
print_status "Target Version: ${TARGET_VERSION:-'Previous stable version'}"

# Function to get deployment history
get_deployment_history() {
  print_status "Fetching deployment history..."
  
  # This would typically query your deployment service
  # For now, we'll simulate with git tags
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would fetch deployment history from deployment service"
    return
  fi
  
  # Get recent git tags (representing deployments)
  RECENT_DEPLOYMENTS=$(git tag --sort=-version:refname | head -10)
  
  if [[ -z "$RECENT_DEPLOYMENTS" ]]; then
    print_error "No deployment history found"
    exit 1
  fi
  
  print_status "Recent deployments:"
  echo "$RECENT_DEPLOYMENTS" | nl
}

# Function to select rollback target
select_rollback_target() {
  if [[ -n "$TARGET_VERSION" ]]; then
    print_status "Using specified target version: $TARGET_VERSION"
    return
  fi
  
  if [[ "$FORCE" == true ]] || [[ "$DRY_RUN" == true ]]; then
    # Use the previous version automatically
    TARGET_VERSION=$(git tag --sort=-version:refname | sed -n '2p')
    print_status "Auto-selected rollback target: $TARGET_VERSION"
    return
  fi
  
  # Interactive selection
  print_status "Select rollback target:"
  DEPLOYMENTS_ARRAY=($(git tag --sort=-version:refname | head -10))
  
  for i in "${!DEPLOYMENTS_ARRAY[@]}"; do
    echo "$((i+1)). ${DEPLOYMENTS_ARRAY[$i]}"
  done
  
  read -p "Enter selection (1-${#DEPLOYMENTS_ARRAY[@]}): " selection
  
  if [[ "$selection" -ge 1 && "$selection" -le "${#DEPLOYMENTS_ARRAY[@]}" ]]; then
    TARGET_VERSION="${DEPLOYMENTS_ARRAY[$((selection-1))]}"
    print_status "Selected rollback target: $TARGET_VERSION"
  else
    print_error "Invalid selection"
    exit 1
  fi
}

# Function to validate rollback target
validate_rollback_target() {
  print_status "Validating rollback target: $TARGET_VERSION"
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would validate rollback target"
    return
  fi
  
  # Check if target version exists
  if ! git tag | grep -q "^$TARGET_VERSION$"; then
    print_error "Target version $TARGET_VERSION not found"
    exit 1
  fi
  
  # Check if target is different from current
  CURRENT_VERSION=$(git describe --tags --exact-match HEAD 2>/dev/null || echo "unknown")
  if [[ "$TARGET_VERSION" == "$CURRENT_VERSION" ]]; then
    print_error "Target version is the same as current version"
    exit 1
  fi
  
  print_success "Rollback target validated"
}

# Function to create rollback backup
create_rollback_backup() {
  print_status "Creating backup of current deployment..."
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would create backup of current deployment"
    return
  fi
  
  BACKUP_NAME="rollback-backup-$(date +%Y%m%d-%H%M%S)"
  
  # Create backup tag
  git tag "$BACKUP_NAME" HEAD
  
  print_success "Backup created: $BACKUP_NAME"
}

# Function to perform rollback
perform_rollback() {
  print_status "Performing rollback to $TARGET_VERSION..."
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would rollback to $TARGET_VERSION"
    print_status "[DRY RUN] Would checkout target version"
    print_status "[DRY RUN] Would rebuild application"
    print_status "[DRY RUN] Would deploy to $ENVIRONMENT"
    return
  fi
  
  # Checkout target version
  git checkout "$TARGET_VERSION"
  
  # Rebuild application
  print_status "Rebuilding application..."
  npm ci
  npm run "build:$ENVIRONMENT"
  
  # Deploy to environment
  case "$ENVIRONMENT" in
    "staging")
      rollback_staging
      ;;
    "production")
      rollback_production
      ;;
  esac
}

# Function to rollback staging
rollback_staging() {
  print_status "Rolling back staging environment..."
  
  # Add staging-specific rollback logic
  # This would typically involve your deployment service API
  
  print_success "Staging rollback completed"
}

# Function to rollback production
rollback_production() {
  print_status "Rolling back production environment..."
  
  # Production rollback requires extra confirmation
  if [[ "$FORCE" != true ]]; then
    print_warning "⚠️  PRODUCTION ROLLBACK CONFIRMATION ⚠️"
    print_warning "This will rollback the production environment!"
    read -p "Type 'ROLLBACK PRODUCTION' to confirm: " confirmation
    
    if [[ "$confirmation" != "ROLLBACK PRODUCTION" ]]; then
      print_error "Rollback cancelled"
      exit 1
    fi
  fi
  
  # Add production-specific rollback logic
  
  print_success "Production rollback completed"
}

# Function to verify rollback
verify_rollback() {
  print_status "Verifying rollback..."
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would verify rollback success"
    return
  fi
  
  # Add verification logic
  # - Health checks
  # - Smoke tests
  # - Version verification
  
  print_success "Rollback verification completed"
}

# Function to send rollback notifications
send_rollback_notifications() {
  print_status "Sending rollback notifications..."
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would send rollback notifications"
    return
  fi
  
  # Add notification logic
  # - Slack alerts
  # - Email notifications
  # - Incident management systems
  
  print_success "Rollback notifications sent"
}

# Function to cleanup
cleanup_rollback() {
  print_status "Cleaning up rollback artifacts..."
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would cleanup rollback artifacts"
    return
  fi
  
  # Cleanup temporary files, caches, etc.
  
  print_success "Cleanup completed"
}

# Main execution flow
main() {
  print_warning "🚨 KAYA FINANCE EMERGENCY ROLLBACK 🚨"
  
  # Confirmation for production
  if [[ "$ENVIRONMENT" == "production" && "$FORCE" != true && "$DRY_RUN" != true ]]; then
    print_warning "⚠️  You are about to rollback PRODUCTION! ⚠️"
    read -p "Are you sure you want to continue? (yes/no): " -r
    if [[ ! $REPLY =~ ^yes$ ]]; then
      print_warning "Rollback cancelled"
      exit 0
    fi
  fi
  
  get_deployment_history
  select_rollback_target
  validate_rollback_target
  create_rollback_backup
  perform_rollback
  verify_rollback
  send_rollback_notifications
  cleanup_rollback
  
  print_success "🎉 Rollback to $TARGET_VERSION completed successfully!"
  print_warning "📋 Please update incident documentation and review what caused the need for rollback"
}

# Run main function
main "$@"
