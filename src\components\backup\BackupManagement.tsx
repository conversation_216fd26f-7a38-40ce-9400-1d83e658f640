import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import {
  Download,
  Upload,
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Database,
  Calendar,
  FileText,
  History
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuthHook'
import { useBackupManagement, type BackupMetadata } from '@/hooks/useBackupManagement'
import { useRestoration } from '@/hooks/useRestoration'
import { RestoreBackupDialog, type RestoreOptions } from './RestoreBackupDialog'
import { RestoreProgressDialog } from './RestoreProgressDialog'
import { formatBytes, formatDate } from '@/lib/utils'

export function BackupManagement() {
  const { profile } = useAuth()
  const [selectedBackup, setSelectedBackup] = useState<BackupMetadata | null>(null)
  const [isCreatingBackup, setIsCreatingBackup] = useState(false)
  const [backupProgress, setBackupProgress] = useState(0)
  const [showRestoreDialog, setShowRestoreDialog] = useState(false)
  const [showProgressDialog, setShowProgressDialog] = useState(false)
  const [activeRestoreJob, setActiveRestoreJob] = useState<string | null>(null)

  const {
    backups,
    settings,
    statistics,
    isLoading,
    createBackup,
    verifyBackup,
    deleteBackup,
    isCreating,
    isVerifying,
    error
  } = useBackupManagement()

  const {
    restoreJobs,
    createRestoreJob,
    cancelRestoreJob,
    useRestoreLogs,
    isCreating: isCreatingRestore
  } = useRestoration()

  // Get restore logs for the active restore job
  const restoreLogsQuery = useRestoreLogs(activeRestoreJob)

  const handleCreateBackup = async (type: 'full' | 'incremental' = 'full') => {
    if (!profile?.org_id) return

    setIsCreatingBackup(true)
    setBackupProgress(0)

    try {
      // Simulate progress for UI feedback
      const progressInterval = setInterval(() => {
        setBackupProgress(prev => Math.min(prev + 10, 90))
      }, 500)

      await createBackup(type)
      
      clearInterval(progressInterval)
      setBackupProgress(100)
      
      setTimeout(() => {
        setIsCreatingBackup(false)
        setBackupProgress(0)
      }, 1000)
    } catch (error) {
      setIsCreatingBackup(false)
      setBackupProgress(0)
    }
  }

  const handleRestoreBackup = (backup: BackupMetadata) => {
    setSelectedBackup(backup)
    setShowRestoreDialog(true)
  }

  const handleRestoreSubmit = async (options: RestoreOptions) => {
    try {
      const restorationId = await createRestoreJob(options)
      setShowRestoreDialog(false)
      setActiveRestoreJob(restorationId)
      setShowProgressDialog(true)
    } catch (error) {
      console.error('Failed to start restoration:', error)
    }
  }

  const handleCancelRestore = async () => {
    if (activeRestoreJob) {
      try {
        await cancelRestoreJob(activeRestoreJob)
        setShowProgressDialog(false)
        setActiveRestoreJob(null)
      } catch (error) {
        console.error('Failed to cancel restoration:', error)
      }
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'default',
      failed: 'destructive',
      pending: 'secondary'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Database className="h-8 w-8 animate-pulse mx-auto mb-2" />
            <p>Loading backup information...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Backup Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Data Backup & Recovery
          </CardTitle>
          <CardDescription>
            Create and manage data backups for your organization. Regular backups ensure your data is safe and recoverable.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isCreatingBackup && (
            <Alert>
              <Database className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>Creating backup... Please do not close this page.</p>
                  <Progress value={backupProgress} className="w-full" />
                  <p className="text-sm text-muted-foreground">{backupProgress}% complete</p>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button
              onClick={() => handleCreateBackup('full')}
              disabled={isCreating || isCreatingBackup}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Create Full Backup
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleCreateBackup('incremental')}
              disabled={isCreating || isCreatingBackup}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Create Incremental Backup
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
            <div className="text-center p-4 border rounded-lg">
              <Database className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <h3 className="font-medium">Full Backup</h3>
              <p className="text-sm text-muted-foreground">
                Complete backup of all organization data
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <Clock className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <h3 className="font-medium">Incremental Backup</h3>
              <p className="text-sm text-muted-foreground">
                Backup only changes since last backup
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <Shield className="h-8 w-8 mx-auto mb-2 text-purple-500" />
              <h3 className="font-medium">Automatic Retention</h3>
              <p className="text-sm text-muted-foreground">
                Backups kept for 90 days, then automatically cleaned
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Backup History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Backup History
          </CardTitle>
          <CardDescription>
            View and manage your organization's backup history
          </CardDescription>
        </CardHeader>
        <CardContent>
          {backups.length === 0 ? (
            <div className="text-center py-8">
              <Database className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No backups found</h3>
              <p className="text-muted-foreground mb-4">
                Create your first backup to ensure your data is protected
              </p>
              <Button onClick={() => handleCreateBackup('full')}>
                Create First Backup
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {backups.map((backup: BackupMetadata) => (
                <div
                  key={backup.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center gap-4">
                    {getStatusIcon(backup.status)}
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">
                          {backup.backup_type.charAt(0).toUpperCase() + backup.backup_type.slice(1)} Backup
                        </h4>
                        {getStatusBadge(backup.status)}
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(backup.created_at)}
                          </span>
                          <span className="flex items-center gap-1">
                            <Database className="h-3 w-3" />
                            {backup.table_count} tables, {backup.record_count.toLocaleString()} records
                          </span>
                          <span>{formatBytes(backup.size_bytes)}</span>
                        </div>
                        {backup.error_message && (
                          <p className="text-red-500 text-xs">{backup.error_message}</p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {backup.status === 'completed' && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => verifyBackup(backup.id)}
                          className="flex items-center gap-1"
                        >
                          <CheckCircle className="h-3 w-3" />
                          Verify
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRestoreBackup(backup)}
                          disabled={isCreatingRestore}
                          className="flex items-center gap-1"
                        >
                          <Upload className="h-3 w-3" />
                          Restore
                        </Button>
                      </>
                    )}
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteBackup(backup.id)}
                      className="flex items-center gap-1 text-red-600 hover:text-red-700"
                    >
                      <XCircle className="h-3 w-3" />
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Backup Information */}
      <Card>
        <CardHeader>
          <CardTitle>Backup Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">What's Included in Backups</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Organization settings and configuration</li>
                <li>• Customer and vendor information</li>
                <li>• All invoices, bills, and payments</li>
                <li>• Chart of accounts and journal entries</li>
                <li>• User profiles and permissions</li>
                <li>• Audit logs and activity history</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Backup Security</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Data encrypted in transit and at rest</li>
                <li>• Checksums verify data integrity</li>
                <li>• Access restricted to organization members</li>
                <li>• Automatic cleanup after 90 days</li>
                <li>• Compliant with Uganda data protection laws</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Restoration History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Restoration History
          </CardTitle>
          <CardDescription>
            View and track your organization's data restoration activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          {restoreJobs.length === 0 ? (
            <div className="text-center py-8">
              <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No restoration history</h3>
              <p className="text-muted-foreground">
                Restoration jobs will appear here when you restore data from backups
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {restoreJobs.slice(0, 5).map((job) => (
                <div
                  key={job.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center gap-4">
                    {getStatusIcon(job.status)}
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">
                          {job.restore_type.charAt(0).toUpperCase() + job.restore_type.slice(1)} Restoration
                        </h4>
                        {getStatusBadge(job.status)}
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(job.requested_at)}
                          </span>
                          <span className="flex items-center gap-1">
                            <Database className="h-3 w-3" />
                            Mode: {job.restore_mode}
                          </span>
                          {job.progress_percentage > 0 && (
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {job.progress_percentage}% complete
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {['pending', 'validating', 'downloading', 'restoring'].includes(job.status) && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setActiveRestoreJob(job.id)
                          setShowProgressDialog(true)
                        }}
                        className="flex items-center gap-1"
                      >
                        <Clock className="h-3 w-3" />
                        View Progress
                      </Button>
                    )}

                    {job.status === 'completed' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setActiveRestoreJob(job.id)
                          setShowProgressDialog(true)
                        }}
                        className="flex items-center gap-1"
                      >
                        <CheckCircle className="h-3 w-3" />
                        View Details
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Restore Backup Dialog */}
      <RestoreBackupDialog
        backup={selectedBackup}
        open={showRestoreDialog}
        onOpenChange={setShowRestoreDialog}
        onRestore={handleRestoreSubmit}
        isRestoring={isCreatingRestore}
      />

      {/* Restore Progress Dialog */}
      {activeRestoreJob && (
        <RestoreProgressDialog
          restoreJob={restoreJobs.find(job => job.id === activeRestoreJob) || null}
          restoreLogs={restoreLogsQuery.data || []}
          open={showProgressDialog}
          onOpenChange={setShowProgressDialog}
          onCancel={handleCancelRestore}
        />
      )}
    </div>
  )
}
