-- =====================================================
-- EMAIL DELIVERY TRACKING MIGRATION
-- =====================================================
-- Migration: 20250628_email_delivery_tracking.sql
-- Description: Email delivery tracking and analytics tables
-- Author: Kaya Finance Team
-- Date: 2025-06-28

-- =====================================================
-- STEP 1: CREATE EMAIL DELIVERY TRACKING TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📧 Creating email delivery tracking tables...';
END $$;

-- Email deliveries table
CREATE TABLE IF NOT EXISTS email_deliveries (
    id TEXT PRIMARY KEY,
    email TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    bounced_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    message_id TEXT,
    template_type TEXT,
    recipient_data JSONB DEFAULT '{}',
    tracking_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email clicks table for detailed click tracking
CREATE TABLE IF NOT EXISTS email_clicks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    delivery_id TEXT NOT NULL REFERENCES email_deliveries(id) ON DELETE CASCADE,
    clicked_url TEXT NOT NULL,
    user_agent TEXT,
    ip_address INET,
    clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email templates customization table
CREATE TABLE IF NOT EXISTS email_template_customizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    template_type TEXT NOT NULL,
    custom_subject TEXT,
    custom_html TEXT,
    custom_variables JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(org_id, template_type)
);

-- Email preferences table (extends notification preferences)
CREATE TABLE IF NOT EXISTS email_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email_frequency TEXT DEFAULT 'immediate' CHECK (email_frequency IN ('immediate', 'daily', 'weekly', 'never')),
    digest_time TIME DEFAULT '09:00:00',
    digest_timezone TEXT DEFAULT 'UTC',
    unsubscribed BOOLEAN DEFAULT false,
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    bounce_count INTEGER DEFAULT 0,
    last_bounce_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, org_id)
);

-- =====================================================
-- STEP 2: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📊 Creating email tracking indexes...';
END $$;

-- Email deliveries indexes
CREATE INDEX IF NOT EXISTS idx_email_deliveries_org_id ON email_deliveries(org_id);
CREATE INDEX IF NOT EXISTS idx_email_deliveries_email ON email_deliveries(email);
CREATE INDEX IF NOT EXISTS idx_email_deliveries_status ON email_deliveries(status);
CREATE INDEX IF NOT EXISTS idx_email_deliveries_notification_id ON email_deliveries(notification_id);
CREATE INDEX IF NOT EXISTS idx_email_deliveries_template_type ON email_deliveries(template_type);
CREATE INDEX IF NOT EXISTS idx_email_deliveries_created_at ON email_deliveries(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_email_deliveries_sent_at ON email_deliveries(sent_at DESC);

-- Email clicks indexes
CREATE INDEX IF NOT EXISTS idx_email_clicks_delivery_id ON email_clicks(delivery_id);
CREATE INDEX IF NOT EXISTS idx_email_clicks_clicked_at ON email_clicks(clicked_at DESC);

-- Email template customizations indexes
CREATE INDEX IF NOT EXISTS idx_email_template_customizations_org_id ON email_template_customizations(org_id);
CREATE INDEX IF NOT EXISTS idx_email_template_customizations_type ON email_template_customizations(template_type);
CREATE INDEX IF NOT EXISTS idx_email_template_customizations_active ON email_template_customizations(is_active) WHERE is_active = true;

-- Email preferences indexes
CREATE INDEX IF NOT EXISTS idx_email_preferences_user_id ON email_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_email_preferences_org_id ON email_preferences(org_id);
CREATE INDEX IF NOT EXISTS idx_email_preferences_unsubscribed ON email_preferences(unsubscribed) WHERE unsubscribed = true;

-- =====================================================
-- STEP 3: CREATE EMAIL TRACKING FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating email tracking functions...';
END $$;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_email_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_email_deliveries_updated_at ON email_deliveries;
CREATE TRIGGER update_email_deliveries_updated_at 
    BEFORE UPDATE ON email_deliveries
    FOR EACH ROW EXECUTE FUNCTION update_email_updated_at_column();

DROP TRIGGER IF EXISTS update_email_template_customizations_updated_at ON email_template_customizations;
CREATE TRIGGER update_email_template_customizations_updated_at 
    BEFORE UPDATE ON email_template_customizations
    FOR EACH ROW EXECUTE FUNCTION update_email_updated_at_column();

DROP TRIGGER IF EXISTS update_email_preferences_updated_at ON email_preferences;
CREATE TRIGGER update_email_preferences_updated_at 
    BEFORE UPDATE ON email_preferences
    FOR EACH ROW EXECUTE FUNCTION update_email_updated_at_column();

-- Function to get email analytics
CREATE OR REPLACE FUNCTION get_email_analytics(
    org_id_param UUID,
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS JSONB AS $$
DECLARE
    analytics JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_sent', COUNT(*),
        'delivered', COUNT(*) FILTER (WHERE status IN ('delivered', 'opened', 'clicked')),
        'opened', COUNT(*) FILTER (WHERE status IN ('opened', 'clicked')),
        'clicked', COUNT(*) FILTER (WHERE status = 'clicked'),
        'bounced', COUNT(*) FILTER (WHERE status = 'bounced'),
        'failed', COUNT(*) FILTER (WHERE status = 'failed'),
        'delivery_rate', ROUND((COUNT(*) FILTER (WHERE status IN ('delivered', 'opened', 'clicked'))::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 2),
        'open_rate', ROUND((COUNT(*) FILTER (WHERE status IN ('opened', 'clicked'))::DECIMAL / NULLIF(COUNT(*) FILTER (WHERE status IN ('delivered', 'opened', 'clicked')), 0)) * 100, 2),
        'click_rate', ROUND((COUNT(*) FILTER (WHERE status = 'clicked')::DECIMAL / NULLIF(COUNT(*) FILTER (WHERE status IN ('opened', 'clicked')), 0)) * 100, 2),
        'bounce_rate', ROUND((COUNT(*) FILTER (WHERE status = 'bounced')::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 2),
        'by_template', template_stats,
        'daily_volume', daily_stats
    ) INTO analytics
    FROM (
        SELECT 
            status,
            template_type,
            DATE(created_at) as date_created,
            jsonb_object_agg(DISTINCT template_type, template_count) as template_stats,
            jsonb_object_agg(DISTINCT date_created, daily_count) as daily_stats
        FROM (
            SELECT 
                status,
                template_type,
                created_at,
                DATE(created_at) as date_created,
                COUNT(*) OVER (PARTITION BY template_type) as template_count,
                COUNT(*) OVER (PARTITION BY DATE(created_at)) as daily_count
            FROM email_deliveries
            WHERE org_id = org_id_param
            AND DATE(created_at) BETWEEN start_date AND end_date
        ) subq
        GROUP BY status, template_type, date_created
    ) analytics_data;
    
    RETURN COALESCE(analytics, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old email delivery records
CREATE OR REPLACE FUNCTION cleanup_email_deliveries(
    org_id_param UUID DEFAULT NULL,
    days_old INTEGER DEFAULT 365
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    IF org_id_param IS NOT NULL THEN
        DELETE FROM email_deliveries 
        WHERE org_id = org_id_param 
        AND created_at < NOW() - INTERVAL '1 day' * days_old;
    ELSE
        DELETE FROM email_deliveries 
        WHERE created_at < NOW() - INTERVAL '1 day' * days_old;
    END IF;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get email delivery status for notification
CREATE OR REPLACE FUNCTION get_notification_email_status(notification_id_param UUID)
RETURNS JSONB AS $$
DECLARE
    status_info JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_recipients', COUNT(*),
        'sent', COUNT(*) FILTER (WHERE status != 'pending' AND status != 'failed'),
        'delivered', COUNT(*) FILTER (WHERE delivered_at IS NOT NULL),
        'opened', COUNT(*) FILTER (WHERE opened_at IS NOT NULL),
        'clicked', COUNT(*) FILTER (WHERE clicked_at IS NOT NULL),
        'failed', COUNT(*) FILTER (WHERE status = 'failed'),
        'deliveries', jsonb_agg(
            jsonb_build_object(
                'email', email,
                'status', status,
                'sent_at', sent_at,
                'delivered_at', delivered_at,
                'opened_at', opened_at,
                'clicked_at', clicked_at,
                'error_message', error_message
            )
        )
    ) INTO status_info
    FROM email_deliveries
    WHERE notification_id = notification_id_param;
    
    RETURN COALESCE(status_info, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 4: CREATE RLS POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Creating email tracking RLS policies...';
END $$;

-- Enable RLS on email tables
ALTER TABLE email_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_template_customizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_preferences ENABLE ROW LEVEL SECURITY;

-- Email deliveries policies
CREATE POLICY "email_deliveries_select_policy" ON email_deliveries
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_deliveries_insert_policy" ON email_deliveries
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role' OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_deliveries_update_policy" ON email_deliveries
    FOR UPDATE USING (
        auth.role() = 'service_role' OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

-- Email clicks policies
CREATE POLICY "email_clicks_select_policy" ON email_clicks
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        delivery_id IN (
            SELECT ed.id FROM email_deliveries ed
            JOIN profiles p ON ed.org_id = p.org_id
            WHERE p.id = auth.uid()
        )
    );

CREATE POLICY "email_clicks_insert_policy" ON email_clicks
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Email template customizations policies
CREATE POLICY "email_template_customizations_select_policy" ON email_template_customizations
    FOR SELECT USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_template_customizations_insert_policy" ON email_template_customizations
    FOR INSERT WITH CHECK (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_template_customizations_update_policy" ON email_template_customizations
    FOR UPDATE USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_template_customizations_delete_policy" ON email_template_customizations
    FOR DELETE USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

-- Email preferences policies
CREATE POLICY "email_preferences_select_policy" ON email_preferences
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "email_preferences_insert_policy" ON email_preferences
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "email_preferences_update_policy" ON email_preferences
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "email_preferences_delete_policy" ON email_preferences
    FOR DELETE USING (user_id = auth.uid());

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ EMAIL DELIVERY TRACKING MIGRATION COMPLETED!';
    RAISE NOTICE '====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '📧 CREATED TABLES:';
    RAISE NOTICE '  • email_deliveries - Delivery tracking';
    RAISE NOTICE '  • email_clicks - Click tracking';
    RAISE NOTICE '  • email_template_customizations - Custom templates';
    RAISE NOTICE '  • email_preferences - User email preferences';
    RAISE NOTICE '';
    RAISE NOTICE '📊 CREATED INDEXES:';
    RAISE NOTICE '  • Performance indexes for all email tables';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ CREATED FUNCTIONS:';
    RAISE NOTICE '  • get_email_analytics()';
    RAISE NOTICE '  • cleanup_email_deliveries()';
    RAISE NOTICE '  • get_notification_email_status()';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 CREATED RLS POLICIES:';
    RAISE NOTICE '  • Organization-based access control';
    RAISE NOTICE '  • User-specific preferences protection';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Email delivery tracking is ready!';
    RAISE NOTICE '';
END $$;
