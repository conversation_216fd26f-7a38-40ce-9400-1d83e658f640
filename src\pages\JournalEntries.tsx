import { useEffect, useState, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { useToast } from '@/hooks/use-toast'
import { Plus, Trash2, CheckCircle, XCircle, Eye } from 'lucide-react'
import type { JournalEntry, Account, TransactionLine } from '@/types/database'

interface TransactionLineInput {
  account_id: string
  debit: number
  credit: number
  description: string
}

interface TransactionLineWithAccount extends TransactionLine {
  accounts?: {
    code: string
    name: string
  }
}

interface JournalEntryWithLines extends JournalEntry {
  transaction_lines?: TransactionLineWithAccount[]
}

export const JournalEntries = () => {
  const { profile } = useAuth()
  const { toast } = useToast()
  const [journalEntries, setJournalEntries] = useState<JournalEntryWithLines[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [viewingEntry, setViewingEntry] = useState<JournalEntryWithLines | null>(null)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
  })
  const [transactionLines, setTransactionLines] = useState<TransactionLineInput[]>([
    { account_id: '', debit: 0, credit: 0, description: '' },
    { account_id: '', debit: 0, credit: 0, description: '' },
  ])

  const fetchJournalEntries = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('journal_entries')
        .select(`
          *,
          transaction_lines (
            id,
            account_id,
            debit,
            credit,
            description,
            created_at,
            journal_entry_id,
            org_id,
            tax_amount,
            tax_rate_id,
            accounts (
              code,
              name
            )
          )
        `)
        .eq('org_id', profile.org_id)
        .order('date', { ascending: false })

      if (error) throw error
      setJournalEntries(data || [])
    } catch (error) {
      console.error('Error fetching journal entries:', error)
      toast({
        title: 'Error',
        description: 'Failed to load journal entries',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, toast])

  const fetchAccounts = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      const { data, error } = await supabase
        .from('accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('code')

      if (error) throw error
      setAccounts(data || [])
    } catch (error) {
      console.error('Error fetching accounts:', error)
    }
  }, [profile?.org_id])

  useEffect(() => {
    if (profile?.org_id) {
      fetchJournalEntries()
      fetchAccounts()
    }
  }, [profile?.org_id, fetchJournalEntries, fetchAccounts])

  const addTransactionLine = () => {
    setTransactionLines([
      ...transactionLines,
      { account_id: '', debit: 0, credit: 0, description: '' }
    ])
  }

  const removeTransactionLine = (index: number) => {
    if (transactionLines.length > 2) {
      setTransactionLines(transactionLines.filter((_, i) => i !== index))
    }
  }

  const updateTransactionLine = (index: number, field: keyof TransactionLineInput, value: string | number) => {
    const updated = [...transactionLines]
    updated[index] = { ...updated[index], [field]: value }
    setTransactionLines(updated)
  }

  const calculateTotals = () => {
    const totalDebits = transactionLines.reduce((sum, line) => sum + (line.debit || 0), 0)
    const totalCredits = transactionLines.reduce((sum, line) => sum + (line.credit || 0), 0)
    return { totalDebits, totalCredits }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    const { totalDebits, totalCredits } = calculateTotals()
    
    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      toast({
        title: 'Error',
        description: 'Debits and credits must be equal',
        variant: 'destructive',
      })
      return
    }

    const validLines = transactionLines.filter(line => 
      line.account_id && (line.debit > 0 || line.credit > 0)
    )

    if (validLines.length < 2) {
      toast({
        title: 'Error',
        description: 'At least two transaction lines are required',
        variant: 'destructive',
      })
      return
    }

    try {
      // Create journal entry
      const { data: journalEntry, error: journalError } = await supabase
        .from('journal_entries')
        .insert({
          org_id: profile.org_id,
          date: formData.date,
          description: formData.description,
          reference: formData.reference || null,
          created_by: profile.id,
        })
        .select()
        .single()

      if (journalError) throw journalError

      // Create transaction lines
      const lines = validLines.map(line => ({
        org_id: profile.org_id,
        journal_entry_id: journalEntry.id,
        account_id: line.account_id,
        debit: line.debit || 0,
        credit: line.credit || 0,
        description: line.description || null,
      }))

      const { error: linesError } = await supabase
        .from('transaction_lines')
        .insert(lines)

      if (linesError) throw linesError

      toast({ title: 'Success', description: 'Journal entry created successfully' })
      setIsDialogOpen(false)
      resetForm()
      fetchJournalEntries()
    } catch (error) {
      console.error('Error creating journal entry:', error)
      toast({
        title: 'Error',
        description: 'Failed to create journal entry',
        variant: 'destructive',
      })
    }
  }

  const handlePostEntry = async (entryId: string) => {
    try {
      const { error } = await supabase
        .from('journal_entries')
        .update({ 
          is_posted: true, 
          posted_at: new Date().toISOString() 
        })
        .eq('id', entryId)
        .eq('org_id', profile?.org_id)

      if (error) throw error

      toast({ title: 'Success', description: 'Journal entry posted successfully' })
      fetchJournalEntries()
    } catch (error) {
      console.error('Error posting journal entry:', error)
      toast({
        title: 'Error',
        description: 'Failed to post journal entry',
        variant: 'destructive',
      })
    }
  }

  const handleUnpostEntry = async (entryId: string) => {
    try {
      const { error } = await supabase
        .from('journal_entries')
        .update({ 
          is_posted: false, 
          posted_at: null 
        })
        .eq('id', entryId)
        .eq('org_id', profile?.org_id)

      if (error) throw error

      toast({ title: 'Success', description: 'Journal entry unposted successfully' })
      fetchJournalEntries()
    } catch (error) {
      console.error('Error unposting journal entry:', error)
      toast({
        title: 'Error',
        description: 'Failed to unpost journal entry',
        variant: 'destructive',
      })
    }
  }

  const viewEntry = async (entry: JournalEntryWithLines) => {
    try {
      const { data, error } = await supabase
        .from('transaction_lines')
        .select(`
          *,
          accounts (
            code,
            name
          )
        `)
        .eq('journal_entry_id', entry.id)

      if (error) throw error

      setViewingEntry({ ...entry, transaction_lines: data })
      setIsViewDialogOpen(true)
    } catch (error) {
      console.error('Error fetching transaction lines:', error)
      toast({
        title: 'Error',
        description: 'Failed to load transaction details',
        variant: 'destructive',
      })
    }
  }

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
    })
    setTransactionLines([
      { account_id: '', debit: 0, credit: 0, description: '' },
      { account_id: '', debit: 0, credit: 0, description: '' },
    ])
  }

  const { totalDebits, totalCredits } = calculateTotals()
  const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Journal Entries</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              New Journal Entry
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Create New Journal Entry</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="date">Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="reference">Reference</Label>
                  <Input
                    id="reference"
                    value={formData.reference}
                    onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                    placeholder="Optional"
                  />
                </div>
                <div className="col-span-1">
                  <Label>Balance Check</Label>
                  <div className={`p-2 rounded text-sm ${isBalanced ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {isBalanced ? 'Balanced' : 'Out of Balance'}
                  </div>
                </div>
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  required
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-4">
                  <Label>Transaction Lines</Label>
                  <Button type="button" onClick={addTransactionLine} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Line
                  </Button>
                </div>
                
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Debit</TableHead>
                      <TableHead>Credit</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactionLines.map((line, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Select
                            value={line.account_id}
                            onValueChange={(value) => updateTransactionLine(index, 'account_id', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select account" />
                            </SelectTrigger>
                            <SelectContent>
                              {accounts.map((account) => (
                                <SelectItem key={account.id} value={account.id}>
                                  {account.code} - {account.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Input
                            value={line.description}
                            onChange={(e) => updateTransactionLine(index, 'description', e.target.value)}
                            placeholder="Line description"
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            step="0.01"
                            value={line.debit || ''}
                            onChange={(e) => updateTransactionLine(index, 'debit', parseFloat(e.target.value) || 0)}
                            disabled={line.credit > 0}
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            step="0.01"
                            value={line.credit || ''}
                            onChange={(e) => updateTransactionLine(index, 'credit', parseFloat(e.target.value) || 0)}
                            disabled={line.debit > 0}
                          />
                        </TableCell>
                        <TableCell>
                          {transactionLines.length > 2 && (
                            <Button
                              type="button"
                              size="sm"
                              variant="outline"
                              onClick={() => removeTransactionLine(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-bold">
                      <TableCell colSpan={2}>Totals</TableCell>
                      <TableCell>{totalDebits.toFixed(2)}</TableCell>
                      <TableCell>{totalCredits.toFixed(2)}</TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <div className="flex gap-2 pt-4">
                <Button type="submit" className="flex-1" disabled={!isBalanced}>
                  Create Journal Entry
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Journal Entries</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Reference</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {journalEntries.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>{new Date(entry.date).toLocaleDateString()}</TableCell>
                  <TableCell>{entry.reference || '-'}</TableCell>
                  <TableCell>{entry.description}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded text-xs ${
                      entry.is_posted 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {entry.is_posted ? 'Posted' : 'Draft'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => viewEntry(entry)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {!entry.is_posted ? (
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Post Journal Entry</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to post this journal entry? Once posted, it will affect account balances.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handlePostEntry(entry.id)}>
                                Post Entry
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      ) : (
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Unpost Journal Entry</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to unpost this journal entry? This will reverse its effect on account balances.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleUnpostEntry(entry.id)}>
                                Unpost Entry
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Entry Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Journal Entry Details</DialogTitle>
          </DialogHeader>
          {viewingEntry && (
            <div className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>Date</Label>
                  <p className="text-sm">{new Date(viewingEntry.date).toLocaleDateString()}</p>
                </div>
                <div>
                  <Label>Reference</Label>
                  <p className="text-sm">{viewingEntry.reference || '-'}</p>
                </div>
                <div>
                  <Label>Status</Label>
                  <span className={`px-2 py-1 rounded text-xs ${
                    viewingEntry.is_posted 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {viewingEntry.is_posted ? 'Posted' : 'Draft'}
                  </span>
                </div>
              </div>
              
              <div>
                <Label>Description</Label>
                <p className="text-sm">{viewingEntry.description}</p>
              </div>

              <div>
                <Label>Transaction Lines</Label>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Debit</TableHead>
                      <TableHead>Credit</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {viewingEntry.transaction_lines?.map((line) => (
                      <TableRow key={line.id}>
                        <TableCell>
                          {line.accounts?.code} - {line.accounts?.name}
                        </TableCell>
                        <TableCell>{line.description || '-'}</TableCell>
                        <TableCell>{line.debit > 0 ? line.debit.toFixed(2) : '-'}</TableCell>
                        <TableCell>{line.credit > 0 ? line.credit.toFixed(2) : '-'}</TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="font-bold">
                      <TableCell colSpan={2}>Totals</TableCell>
                      <TableCell>
                        {viewingEntry.transaction_lines?.reduce((sum, line) => sum + line.debit, 0).toFixed(2)}
                      </TableCell>
                      <TableCell>
                        {viewingEntry.transaction_lines?.reduce((sum, line) => sum + line.credit, 0).toFixed(2)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <div className="flex justify-end">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
