import { supabase } from '@/lib/supabase'
import { sendToIntegrations } from './externalIntegrations'
import type {
  CreateNotificationPayload,
  NotificationTemplateData,
  NotificationType,
  NotificationCategory,
  NotificationPriority
} from '@/types/notifications'
import type { Notification, NotificationTemplate } from '@/types/database'

// Validation schemas
const NOTIFICATION_VALIDATION = {
  MAX_TITLE_LENGTH: 255,
  MAX_MESSAGE_LENGTH: 2000,
  MAX_BATCH_SIZE: 100,
  REQUIRED_FIELDS: ['org_id', 'type', 'category', 'title', 'message'] as const
}

/**
 * Validate notification payload
 */
function validateNotificationPayload(payload: CreateNotificationPayload): string[] {
  const errors: string[] = []

  // Check required fields
  for (const field of NOTIFICATION_VALIDATION.REQUIRED_FIELDS) {
    if (!payload[field]) {
      errors.push(`Missing required field: ${field}`)
    }
  }

  // Validate field lengths
  if (payload.title && payload.title.length > NOTIFICATION_VALIDATION.MAX_TITLE_LENGTH) {
    errors.push(`Title exceeds maximum length of ${NOTIFICATION_VALIDATION.MAX_TITLE_LENGTH} characters`)
  }

  if (payload.message && payload.message.length > NOTIFICATION_VALIDATION.MAX_MESSAGE_LENGTH) {
    errors.push(`Message exceeds maximum length of ${NOTIFICATION_VALIDATION.MAX_MESSAGE_LENGTH} characters`)
  }

  // Validate UUIDs
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  if (payload.org_id && !uuidRegex.test(payload.org_id)) {
    errors.push('Invalid org_id format')
  }

  if (payload.user_id && !uuidRegex.test(payload.user_id)) {
    errors.push('Invalid user_id format')
  }

  if (payload.entity_id && !uuidRegex.test(payload.entity_id)) {
    errors.push('Invalid entity_id format')
  }

  // Validate enums
  const validCategories: NotificationCategory[] = ['financial', 'system', 'approval', 'reminder', 'security']
  if (payload.category && !validCategories.includes(payload.category)) {
    errors.push(`Invalid category. Must be one of: ${validCategories.join(', ')}`)
  }

  const validPriorities: NotificationPriority[] = ['low', 'normal', 'high', 'urgent']
  if (payload.priority && !validPriorities.includes(payload.priority)) {
    errors.push(`Invalid priority. Must be one of: ${validPriorities.join(', ')}`)
  }

  return errors
}

/**
 * Enhanced helper function to create notifications using templates with validation and error handling
 */
export async function createNotificationFromTemplate(
  templateType: NotificationType,
  orgId: string,
  userId: string | null,
  templateData: NotificationTemplateData,
  entityType?: string,
  entityId?: string
): Promise<Notification> {
  try {
    // Validate input parameters
    if (!templateType) throw new Error('Template type is required')
    if (!orgId) throw new Error('Organization ID is required')

    // Get the template with retry logic
    let template: NotificationTemplate
    let retryCount = 0
    const maxRetries = 3

    while (retryCount < maxRetries) {
      try {
        const { data, error } = await supabase
          .from('notification_templates')
          .select('*')
          .eq('type', templateType)
          .eq('is_active', true)
          .single()

        if (error) throw error
        template = data
        break
      } catch (error: unknown) {
        retryCount++
        if (retryCount >= maxRetries) {
          throw new Error(`Template not found for type: ${templateType} after ${maxRetries} attempts`)
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 100 * retryCount))
      }
    }

    if (!template) {
      throw new Error(`Template not found for type: ${templateType}`)
    }

    // Replace template variables with enhanced error handling
    const title = replaceTemplateVariables(template.title_template, templateData)
    const message = replaceTemplateVariables(template.message_template, templateData)

    // Calculate expiry date
    let expiresAt: string | undefined
    if (template.expires_after_hours) {
      const expiry = new Date()
      expiry.setHours(expiry.getHours() + template.expires_after_hours)
      expiresAt = expiry.toISOString()
    }

    // Create notification payload
    const payload: CreateNotificationPayload = {
      org_id: orgId,
      user_id: userId,
      type: templateType,
      category: template.category as NotificationCategory,
      title,
      message,
      data: templateData,
      entity_type: entityType,
      entity_id: entityId,
      priority: template.default_priority as NotificationPriority,
      expires_at: expiresAt,
    }

    // Validate payload
    const validationErrors = validateNotificationPayload(payload)
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`)
    }

    // Insert notification with retry logic
    retryCount = 0
    while (retryCount < maxRetries) {
      try {
        const { data, error } = await supabase
          .from('notifications')
          .insert(payload)
          .select()
          .single()

        if (error) throw error

        // Send to external integrations asynchronously
        if (data) {
          sendToIntegrations(data, orgId).catch(error => {
            console.error('Error sending to external integrations:', error)
          })
        }

        return data
      } catch (error: unknown) {
        retryCount++
        if (retryCount >= maxRetries) {
          throw new Error(`Failed to create notification after ${maxRetries} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 200 * retryCount))
      }
    }
  } catch (error: unknown) {
    console.error('Error creating notification:', {
      templateType,
      orgId,
      userId,
      entityType,
      entityId,
      error: error.message,
      stack: error.stack
    })
    throw error
  }
}

/**
 * Replace template variables with actual data with enhanced error handling
 */
function replaceTemplateVariables(template: string, data: NotificationTemplateData): string {
  if (!template) return ''

  let result = template
  const missingVariables: string[] = []

  // Find all template variables
  const variablePattern = /{{(\w+)}}/g
  const matches = template.match(variablePattern) || []

  // Replace all {{variable}} patterns
  Object.entries(data).forEach(([key, value]) => {
    const pattern = new RegExp(`{{${key}}}`, 'g')
    const stringValue = value !== null && value !== undefined ? String(value) : ''
    result = result.replace(pattern, stringValue)
  })

  // Check for unreplaced variables
  const remainingVariables = result.match(variablePattern) || []
  if (remainingVariables.length > 0) {
    console.warn('Template variables not replaced:', remainingVariables)
  }

  return result
}

/**
 * Create multiple notifications in batch with transaction support
 */
export async function createBulkNotifications(
  notifications: CreateNotificationPayload[]
): Promise<Notification[]> {
  if (!notifications || notifications.length === 0) {
    throw new Error('No notifications provided')
  }

  if (notifications.length > NOTIFICATION_VALIDATION.MAX_BATCH_SIZE) {
    throw new Error(`Batch size exceeds maximum of ${NOTIFICATION_VALIDATION.MAX_BATCH_SIZE}`)
  }

  try {
    // Validate all notifications
    const allErrors: string[] = []
    notifications.forEach((notification, index) => {
      const errors = validateNotificationPayload(notification)
      if (errors.length > 0) {
        allErrors.push(`Notification ${index}: ${errors.join(', ')}`)
      }
    })

    if (allErrors.length > 0) {
      throw new Error(`Validation failed: ${allErrors.join('; ')}`)
    }

    // Insert all notifications in a single transaction
    const { data, error } = await supabase
      .from('notifications')
      .insert(notifications)
      .select()

    if (error) throw error
    return data || []
  } catch (error: unknown) {
    console.error('Error creating bulk notifications:', {
      count: notifications.length,
      error: error.message
    })
    throw error
  }
}

/**
 * Schedule notification for future delivery
 */
export async function scheduleNotification(
  templateType: NotificationType,
  orgId: string,
  userId: string | null,
  templateData: NotificationTemplateData,
  scheduledFor: Date,
  entityType?: string,
  entityId?: string
): Promise<Notification> {
  try {
    if (scheduledFor <= new Date()) {
      throw new Error('Scheduled time must be in the future')
    }

    // For now, we'll create the notification with a custom scheduled_for field in data
    // In a production system, you'd want a separate scheduled_notifications table
    const enhancedData = {
      ...templateData,
      scheduled_for: scheduledFor.toISOString(),
      is_scheduled: true
    }

    return await createNotificationFromTemplate(
      templateType,
      orgId,
      userId,
      enhancedData,
      entityType,
      entityId
    )
  } catch (error: unknown) {
    console.error('Error scheduling notification:', error)
    throw error
  }
}

/**
 * Create notification with retry and exponential backoff
 */
export async function createNotificationWithRetry(
  payload: CreateNotificationPayload,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<Notification> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Validate payload
      const validationErrors = validateNotificationPayload(payload)
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`)
      }

      const { data, error } = await supabase
        .from('notifications')
        .insert(payload)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error: unknown) {
      lastError = error

      if (attempt === maxRetries) {
        break
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt - 1)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  console.error(`Failed to create notification after ${maxRetries} attempts:`, lastError)
  throw lastError
}

/**
 * Create a payment approval notification
 */
export async function createPaymentApprovalNotification(
  orgId: string,
  approverId: string,
  paymentData: {
    amount: string
    payeeName: string
    paymentId: string
  }
) {
  return createNotificationFromTemplate(
    'payment_pending_approval',
    orgId,
    approverId,
    {
      amount: paymentData.amount,
      payee_name: paymentData.payeeName,
    },
    'payment',
    paymentData.paymentId
  )
}

/**
 * Create an invoice overdue notification
 */
export async function createInvoiceOverdueNotification(
  orgId: string,
  userId: string | null,
  invoiceData: {
    invoiceNumber: string
    customerName: string
    daysOverdue: number
    invoiceId: string
  }
) {
  return createNotificationFromTemplate(
    'invoice_overdue',
    orgId,
    userId,
    {
      invoice_number: invoiceData.invoiceNumber,
      customer_name: invoiceData.customerName,
      days_overdue: invoiceData.daysOverdue,
    },
    'invoice',
    invoiceData.invoiceId
  )
}

/**
 * Create a bill due soon notification
 */
export async function createBillDueSoonNotification(
  orgId: string,
  userId: string | null,
  billData: {
    billNumber: string
    vendorName: string
    daysUntilDue: number
    billId: string
  }
) {
  return createNotificationFromTemplate(
    'bill_due_soon',
    orgId,
    userId,
    {
      bill_number: billData.billNumber,
      vendor_name: billData.vendorName,
      days_until_due: billData.daysUntilDue,
    },
    'bill',
    billData.billId
  )
}

/**
 * Create a user invitation notification
 */
export async function createUserInvitationNotification(
  orgId: string,
  adminUserId: string,
  invitedEmail: string
) {
  return createNotificationFromTemplate(
    'user_invited',
    orgId,
    adminUserId,
    {
      email: invitedEmail,
    }
  )
}

/**
 * Create a system backup notification
 */
export async function createBackupNotification(
  orgId: string,
  success: boolean,
  details?: string
) {
  const templateType = success ? 'backup_completed' : 'backup_failed'
  
  return createNotificationFromTemplate(
    templateType,
    orgId,
    null, // System notifications go to all org users
    {
      details: details || '',
    }
  )
}

/**
 * Enhanced bulk notification creation with organization-wide support
 */
export async function createOrganizationNotification(
  templateType: NotificationType,
  orgId: string,
  templateData: NotificationTemplateData,
  entityType?: string,
  entityId?: string,
  excludeUserIds?: string[]
): Promise<Notification[]> {
  try {
    // Get all users in the organization
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id')
      .eq('org_id', orgId)

    if (usersError) throw usersError

    const userIds = users?.map(u => u.id) || []

    // Filter out excluded users
    const targetUserIds = excludeUserIds
      ? userIds.filter(id => !excludeUserIds.includes(id))
      : userIds

    // Create notifications for all users
    const notifications: CreateNotificationPayload[] = []

    // Add organization-wide notification (user_id = null)
    const orgNotification = await createNotificationFromTemplate(
      templateType,
      orgId,
      null,
      templateData,
      entityType,
      entityId
    )
    notifications.push(orgNotification)

    return notifications
  } catch (error: unknown) {
    console.error('Error creating organization notification:', error)
    throw error
  }
}

/**
 * Create notification with user preference checking
 */
export async function createNotificationWithPreferences(
  templateType: NotificationType,
  orgId: string,
  userId: string,
  templateData: NotificationTemplateData,
  entityType?: string,
  entityId?: string
): Promise<Notification | null> {
  try {
    // Check user preferences
    const { data: preference, error: prefError } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .eq('notification_type', templateType)
      .single()

    // If no preference exists, assume enabled (default behavior)
    const isEnabled = !preference || preference.enabled !== false
    const inAppEnabled = !preference || preference.in_app_enabled !== false

    if (!isEnabled || !inAppEnabled) {
      console.log(`Notification ${templateType} skipped for user ${userId} due to preferences`)
      return null
    }

    return await createNotificationFromTemplate(
      templateType,
      orgId,
      userId,
      templateData,
      entityType,
      entityId
    )
  } catch (error: unknown) {
    console.error('Error creating notification with preferences:', error)
    throw error
  }
}

/**
 * Clean up expired notifications
 */
export async function cleanupExpiredNotifications(orgId: string) {
  try {
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('org_id', orgId)
      .lt('expires_at', new Date().toISOString())

    if (error) throw error
  } catch (error) {
    console.error('Error cleaning up expired notifications:', error)
    throw error
  }
}
