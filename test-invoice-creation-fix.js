/**
 * Test script to verify the invoice creation fix
 * This script tests that invoices can be created without the account_id column error
 */

import { createClient } from '@supabase/supabase-js'

// You'll need to replace these with your actual Supabase credentials
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'your-supabase-url'
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testInvoiceCreation() {
  console.log('🧪 Testing invoice creation fix...')
  
  try {
    // First, let's check if we can query the invoices table structure
    console.log('📋 Checking invoices table structure...')
    
    const { data: invoices, error: queryError } = await supabase
      .from('invoices')
      .select('*')
      .limit(1)
    
    if (queryError) {
      console.error('❌ Error querying invoices:', queryError)
      return
    }
    
    console.log('✅ Successfully queried invoices table')
    
    // Test creating an invoice without account_id (this should work now)
    console.log('📝 Testing invoice creation without account_id...')
    
    const testInvoiceData = {
      customer_id: '********-0000-0000-0000-************', // Dummy UUID
      invoice_number: 'TEST-' + Date.now(),
      date_issued: new Date().toISOString().split('T')[0],
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      total_amount: 100.00,
      tax_amount: 18.00,
      notes: 'Test invoice for account_id fix verification',
      status: 'draft',
      org_id: '********-0000-0000-0000-************' // Dummy UUID
    }
    
    // This should NOT include account_id
    const { data: invoice, error: createError } = await supabase
      .from('invoices')
      .insert(testInvoiceData)
      .select()
      .single()
    
    if (createError) {
      if (createError.code === 'PGRST204' && createError.message.includes('account_id')) {
        console.error('❌ STILL GETTING ACCOUNT_ID ERROR:', createError.message)
        console.error('   This means the fix hasn\'t been applied correctly')
      } else {
        console.log('ℹ️  Got expected error (likely due to dummy data):', createError.message)
        console.log('✅ No account_id column error - fix appears to be working!')
      }
    } else {
      console.log('✅ Invoice created successfully:', invoice)
      
      // Clean up - delete the test invoice
      await supabase
        .from('invoices')
        .delete()
        .eq('id', invoice.id)
      
      console.log('🧹 Cleaned up test invoice')
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

// Run the test
testInvoiceCreation()

console.log(`
📋 INVOICE CREATION FIX SUMMARY:
================================

✅ CHANGES MADE:
  • Updated useCreateInvoice hook to exclude account_id from invoice data
  • Updated useUpdateInvoice hook to exclude account_id from invoice data  
  • Added account_id as optional field to InvoiceLineData interface
  • account_id is now properly handled only for invoice_lines table

🔧 HOW IT WORKS:
  • Form collects account_id from user
  • account_id is used for invoice lines (where it belongs)
  • account_id is excluded from main invoice record (where it doesn't belong)
  • This matches the database schema where invoices table has no account_id column

🚀 DEPLOYMENT:
  • Push these changes to production
  • The PGRST204 error should be resolved
  • Invoice creation should work normally

📝 NOTE:
  • This follows the same pattern used in bills creation
  • The database schema is correct - invoices don't need account_id
  • Only invoice_lines need account_id (which they already have)
`)
