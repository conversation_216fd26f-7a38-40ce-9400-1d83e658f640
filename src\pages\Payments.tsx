
import { useState, useMemo, useCallback, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from '@/components/ui/pagination'
import { Plus, Search, Eye, Edit, Trash, CheckCircle } from 'lucide-react'
import { toast } from 'sonner'
import { PaymentForm } from '@/components/payments/PaymentForm'
import { PaymentApplicationDialog } from '@/components/payments/PaymentApplicationDialog'
import { PaymentApprovalDialog } from '@/components/payments/PaymentApprovalDialog'
import { LoadingPage } from '@/components/ui/loading'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { PaymentsErrorBoundary } from '@/components/payments/PaymentsErrorBoundary'
import { PaymentsLoadingState } from '@/components/payments/PaymentsTableSkeleton'
import { usePayments, useActiveCustomers, useActiveVendors, useDeletePayment } from '@/hooks/queries'
import { formatCurrency } from '@/lib/utils'
import type { Payment, PaymentStatus } from '@/types/database'
import type { PaymentWithDetails } from '@/hooks/queries/usePayments'

export function Payments() {
  const { profile } = useAuth()
  const [showForm, setShowForm] = useState(false)
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null)
  const [showApplicationDialog, setShowApplicationDialog] = useState(false)
  const [showApprovalDialog, setShowApprovalDialog] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20) // Fixed page size for now

  // Debounce search term for better performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
      setCurrentPage(1) // Reset to first page when searching
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // Memoized filters for pagination and search
  const filters = useMemo(() => ({
    search: debouncedSearchTerm,
    limit: pageSize,
    offset: (currentPage - 1) * pageSize
  }), [debouncedSearchTerm, pageSize, currentPage])

  // Use React Query hooks for data fetching
  const { data: payments = [], isLoading: paymentsLoading, error: paymentsError, refetch: refetchPayments } = usePayments(filters)
  const { data: customers = [], isLoading: customersLoading } = useActiveCustomers()
  const { data: vendors = [], isLoading: vendorsLoading } = useActiveVendors()
  const deletePaymentMutation = useDeletePayment()

  // Show granular loading state when any data is loading
  const showLoadingState = paymentsLoading || customersLoading || vendorsLoading

  // Handle errors from React Query
  if (paymentsError) {
    console.error('Error loading payments:', paymentsError)
    toast.error('Failed to load payments')
  }

  const handleDelete = useCallback(async (id: string) => {
    if (!confirm('Are you sure you want to delete this payment?')) return
    deletePaymentMutation.mutate(id)
  }, [deletePaymentMutation])

  // Since we're using server-side filtering, we can use payments directly
  // But keep client-side filtering as fallback for better UX
  const filteredPayments = useMemo(() => {
    // If search is handled server-side, just return payments
    // Otherwise, apply client-side filtering as fallback
    if (!searchTerm) return payments

    const searchLower = searchTerm.toLowerCase()
    return payments.filter(payment =>
      payment.payee_name?.toLowerCase().includes(searchLower) ||
      payment.notes?.toLowerCase().includes(searchLower) ||
      payment.transaction_id?.toLowerCase().includes(searchLower)
    )
  }, [payments, searchTerm])

  // Memoized channel badge function for performance
  const getChannelBadge = useCallback((channel: string) => {
    const channelMap: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' }> = {
      bank: { label: 'Bank Transfer', variant: 'default' },
      mtn_momo: { label: 'MTN MoMo', variant: 'secondary' },
      airtel_money: { label: 'Airtel Money', variant: 'secondary' },
      cash: { label: 'Cash', variant: 'outline' },
      other: { label: 'Other', variant: 'destructive' }
    }

    const config = channelMap[channel] || { label: channel, variant: 'default' as const }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }, [])

  // Memoized status functions for performance
  const getStatusVariant = useCallback((status: PaymentStatus) => {
    switch (status) {
      case 'paid':
        return 'default'
      case 'approved':
        return 'default'
      case 'rejected':
        return 'destructive'
      case 'pending':
      default:
        return 'secondary'
    }
  }, [])

  const getStatusLabel = useCallback((status: PaymentStatus) => {
    switch (status) {
      case 'paid':
        return 'Paid'
      case 'approved':
        return 'Approved'
      case 'rejected':
        return 'Rejected'
      case 'pending':
      default:
        return 'Pending'
    }
  }, [])

  // Simple pagination info component
  const PaginationInfo = ({ currentPage, pageSize, totalItems }: {
    currentPage: number;
    pageSize: number;
    totalItems: number
  }) => {
    const startItem = (currentPage - 1) * pageSize + 1
    const endItem = Math.min(currentPage * pageSize, totalItems)

    return (
      <div className="text-sm text-muted-foreground">
        Showing {startItem} to {endItem} of {totalItems} results
      </div>
    )
  }

  // Simple pagination component
  const PaymentsPagination = ({ currentPage, totalPages, onPageChange }: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  }) => {
    if (totalPages <= 1) return null

    return (
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault()
                if (currentPage > 1) onPageChange(currentPage - 1)
              }}
              className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
            />
          </PaginationItem>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <PaginationItem key={page}>
              <PaginationLink
                href="#"
                onClick={(e) => {
                  e.preventDefault()
                  onPageChange(page)
                }}
                isActive={currentPage === page}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          ))}

          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault()
                if (currentPage < totalPages) onPageChange(currentPage + 1)
              }}
              className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    )
  }

  // Show granular loading state for better UX
  if (showLoadingState) {
    return (
      <PaymentsErrorBoundary>
        <PaymentsLoadingState
          paymentsLoading={paymentsLoading}
          customersLoading={customersLoading}
          vendorsLoading={vendorsLoading}
        />
      </PaymentsErrorBoundary>
    )
  }

  return (
    <PaymentsErrorBoundary>
      <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Payments</h1>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search payments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 w-64"
            />
          </div>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            New Payment
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Payment Records</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Date</th>
                  <th className="text-left p-2">Payee</th>
                  <th className="text-left p-2">Amount</th>
                  <th className="text-left p-2">Related Documents</th>
                  <th className="text-left p-2">Channel</th>
                  <th className="text-left p-2">Applied</th>
                  <th className="text-left p-2">Balance</th>
                  <th className="text-left p-2">Status</th>
                  <th className="text-left p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPayments.map((payment) => {
                  const balance = Number(payment.amount) - (payment.applications_total || 0)
                  return (
                    <tr key={payment.id} className="border-b hover:bg-gray-50">
                      <td className="p-2">{new Date(payment.payment_date).toLocaleDateString()}</td>
                      <td className="p-2">{payment.payee_name}</td>
                      <td className="p-2">{formatCurrency(Number(payment.amount))}</td>
                      <td className="p-2">
                        {payment.related_documents && payment.related_documents.length > 0 ? (
                          <div className="space-y-1">
                            {payment.related_documents.map((doc, index) => (
                              <div key={index} className="text-xs">
                                <Badge variant="outline" className="mr-1">
                                  {doc.type === 'invoice' ? 'INV' : 'BILL'}
                                </Badge>
                                {doc.number} ({formatCurrency(doc.amount)})
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-xs">No documents linked</span>
                        )}
                      </td>
                      <td className="p-2">{getChannelBadge(payment.channel)}</td>
                      <td className="p-2">{formatCurrency(payment.applications_total || 0)}</td>
                      <td className="p-2">{formatCurrency(balance)}</td>
                      <td className="p-2">
                        <Badge variant={getStatusVariant(payment.status || 'pending')}>
                          {getStatusLabel(payment.status || 'pending')}
                        </Badge>
                      </td>
                      <td className="p-2">
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedPayment(payment)
                              setShowApplicationDialog(true)
                            }}
                            title="View payment applications"
                          >
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setEditingPayment(payment)
                              setShowForm(true)
                            }}
                            title="Edit payment"
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          {profile?.role === 'admin' && payment.status === 'pending' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedPayment(payment)
                                setShowApprovalDialog(true)
                              }}
                              title="Approve Payment"
                            >
                              <CheckCircle className="w-3 h-3" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(payment.id)}
                            title="Delete payment"
                          >
                            <Trash className="w-3 h-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination Controls */}
      {filteredPayments.length > 0 && (
        <div className="flex items-center justify-between">
          <PaginationInfo
            currentPage={currentPage}
            pageSize={pageSize}
            totalItems={filteredPayments.length}
          />
          <PaymentsPagination
            currentPage={currentPage}
            totalPages={Math.ceil(filteredPayments.length / pageSize)}
            onPageChange={setCurrentPage}
          />
        </div>
      )}

      <PaymentForm
        payment={editingPayment}
        customers={customers}
        vendors={vendors}
        open={showForm}
        onOpenChange={(open) => {
          setShowForm(open)
          if (!open) {
            setEditingPayment(null)
          }
        }}
        onSuccess={() => {
          refetchPayments()
          setShowForm(false)
          setEditingPayment(null)
        }}
      />

      {showApplicationDialog && selectedPayment && (
        <PaymentApplicationDialog
          payment={selectedPayment}
          onClose={() => {
            setShowApplicationDialog(false)
            setSelectedPayment(null)
          }}
          onSave={() => {
            refetchPayments()
            setShowApplicationDialog(false)
            setSelectedPayment(null)
          }}
        />
      )}

      {showApprovalDialog && selectedPayment && (
        <PaymentApprovalDialog
          payment={selectedPayment}
          open={showApprovalDialog}
          onClose={() => {
            setShowApprovalDialog(false)
            setSelectedPayment(null)
          }}
          onApprovalComplete={() => {
            refetchPayments()
            setShowApprovalDialog(false)
            setSelectedPayment(null)
          }}
        />
      )}
      </div>
    </PaymentsErrorBoundary>
  )
}
