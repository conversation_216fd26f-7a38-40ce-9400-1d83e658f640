import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'

export interface AccountMapping {
  id: string
  org_id: string
  mapping_type: 'sales_revenue' | 'accounts_receivable' | 'accounts_payable' | 'vat_payable' | 'vat_input'
  account_id: string
  is_default: boolean
  category?: string
  created_at: string
  updated_at: string
  account?: {
    id: string
    name: string
    code: string
    type: string
  }
}

export interface AccountMappingValidation {
  mapping_type: string
  account_name: string | null
  account_code: string | null
  status: 'configured' | 'missing'
}

export interface JournalEntryGenerationResult {
  entity_type: string
  entity_id: string
  journal_entry_id: string | null
  status: string
}

export function useAccountMappings() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  const accountMappingsQuery = useQuery({
    queryKey: ['account-mappings', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('account_mappings')
        .select(`
          *,
          account:accounts(id, name, code, type)
        `)
        .eq('org_id', profile.org_id)
        .order('mapping_type')

      if (error) throw error
      return data as AccountMapping[]
    },
    enabled: !!profile?.org_id,
  })

  const validateMappingsMutation = useMutation({
    mutationFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase.rpc('validate_account_mappings', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data as AccountMappingValidation[]
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['account-mappings'] })
    },
  })

  const updateMappingMutation = useMutation({
    mutationFn: async ({ 
      mapping_type, 
      account_id, 
      category 
    }: { 
      mapping_type: string
      account_id: string
      category?: string 
    }) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      // First, unset any existing default for this mapping type
      await supabase
        .from('account_mappings')
        .update({ is_default: false })
        .eq('org_id', profile.org_id)
        .eq('mapping_type', mapping_type)

      // Then set the new mapping as default
      const { data, error } = await supabase
        .from('account_mappings')
        .upsert({
          org_id: profile.org_id,
          mapping_type,
          account_id,
          category,
          is_default: true,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'org_id,mapping_type,category'
        })
        .select()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      toast.success('Account mapping updated successfully')
      queryClient.invalidateQueries({ queryKey: ['account-mappings'] })
    },
    onError: (error) => {
      toast.error('Failed to update account mapping: ' + error.message)
    },
  })

  const generateMissingEntriesMutation = useMutation({
    mutationFn: async () => {
      console.log('Calling generate_missing_journal_entries RPC function...')

      const { data, error } = await supabase.rpc('generate_missing_journal_entries')

      console.log('RPC Response:', { data, error })

      if (error) {
        console.error('RPC Error details:', error)
        throw new Error(`Database error: ${error.message} (Code: ${error.code})`)
      }

      return data
    },
    onSuccess: (data: JournalEntryGenerationResult[]) => {
      console.log('Generate missing entries result:', data)

      if (!data || data.length === 0) {
        toast.info('No missing journal entries found')
        return
      }

      const successEntries = data.filter((item) => item.status === 'success' && item.entity_type !== 'summary')
      const errorEntries = data.filter((item) => item.status.startsWith('error'))
      const summaryEntry = data.find((item) => item.entity_type === 'summary')

      // Show summary message if available
      if (summaryEntry) {
        if (summaryEntry.status.includes('Generated')) {
          toast.success(summaryEntry.status)
        } else {
          toast.info(summaryEntry.status)
        }
      } else {
        // Fallback to count-based messages
        if (successEntries.length > 0) {
          toast.success(`Generated ${successEntries.length} journal entries successfully`)
        }
        if (errorEntries.length > 0) {
          toast.error(`Failed to generate ${errorEntries.length} journal entries`)
        }
        if (successEntries.length === 0 && errorEntries.length === 0) {
          toast.info('No missing journal entries found')
        }
      }

      // Show individual errors if any
      errorEntries.forEach((entry) => {
        if (entry.entity_type !== 'summary') {
          toast.error(`${entry.entity_type} ${entry.entity_id}: ${entry.status}`)
        }
      })

      queryClient.invalidateQueries({ queryKey: ['journal-entries'] })
      queryClient.invalidateQueries({ queryKey: ['audit-missing-entries'] })
    },
    onError: (error) => {
      console.error('Generate missing entries mutation error:', error)
      toast.error('Failed to generate journal entries: ' + error.message)
    },
  })

  return {
    accountMappings: accountMappingsQuery.data || [],
    isLoading: accountMappingsQuery.isLoading,
    error: accountMappingsQuery.error,
    validateMappings: validateMappingsMutation.mutate,
    isValidating: validateMappingsMutation.isPending,
    validationResult: validateMappingsMutation.data,
    updateMapping: updateMappingMutation.mutate,
    isUpdating: updateMappingMutation.isPending,
    generateMissingEntries: generateMissingEntriesMutation.mutate,
    isGenerating: generateMissingEntriesMutation.isPending,
    refetch: accountMappingsQuery.refetch,
  }
}

export function useDocumentJournalEntries(documentType: 'invoice' | 'bill', documentId: string) {
  return useQuery({
    queryKey: ['document-journal-entries', documentType, documentId],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_document_journal_entries', {
        document_type: documentType,
        document_id: documentId
      })

      if (error) throw error
      return data
    },
    enabled: !!documentId,
  })
}

export function useARAPBalances() {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['ar-ap-balances', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase.rpc('get_ar_ap_balances', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
  })
}

export function useAuditMissingEntries() {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['audit-missing-entries', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase.rpc('audit_missing_journal_entries', {
        org_id_param: profile.org_id
      })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
  })
}
