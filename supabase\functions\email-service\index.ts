import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { Resend } from 'https://esm.sh/resend@4.0.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailRequest {
  type: 'user_invitation' | 'password_reset' | 'welcome' | 'notification'
  to: string
  data: Record<string, unknown>
  org_id?: string
}

interface InvitationEmailData {
  inviter_name: string
  organization_name: string
  role: string
  invitation_url: string
  expires_at: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize clients
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const resendApiKey = Deno.env.get('RESEND_API_KEY')!
    
    if (!resendApiKey) {
      throw new Error('RESEND_API_KEY environment variable is required')
    }

    const supabase = createClient(supabaseUrl, supabaseKey)
    const resend = new Resend(resendApiKey)

    // Parse request
    const { type, to, data, org_id }: EmailRequest = await req.json()

    // Validate email address
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(to)) {
      throw new Error('Invalid email address')
    }

    // Get organization settings if org_id provided
    let orgSettings = null
    if (org_id) {
      const { data: org, error: orgError } = await supabase
        .from('organizations')
        .select('name, email_settings')
        .eq('id', org_id)
        .single()

      if (orgError) {
        console.error('Error fetching organization:', orgError)
      } else {
        orgSettings = org
      }
    }

    // Default email settings
    const defaultSettings = {
      from_name: 'Kaya Finance',
      from_email: '<EMAIL>',
      reply_to: '<EMAIL>'
    }

    const emailSettings = orgSettings?.email_settings || defaultSettings
    const fromEmail = `${emailSettings.from_name} <${emailSettings.from_email}>`

    let emailContent: { subject: string; html: string; text: string }

    switch (type) {
      case 'user_invitation':
        emailContent = generateInvitationEmail(data as InvitationEmailData, orgSettings?.name)
        break
      
      case 'password_reset':
        emailContent = generatePasswordResetEmail(data)
        break
      
      case 'welcome':
        emailContent = generateWelcomeEmail(data, orgSettings?.name)
        break
      
      case 'notification':
        emailContent = generateNotificationEmail(data)
        break
      
      default:
        throw new Error(`Unsupported email type: ${type}`)
    }

    // Send email via Resend
    const { data: emailResult, error: emailError } = await resend.emails.send({
      from: fromEmail,
      to: [to],
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
      reply_to: emailSettings.reply_to,
    })

    if (emailError) {
      throw new Error(`Failed to send email: ${emailError.message}`)
    }

    // Log email activity (optional)
    if (org_id) {
      await supabase
        .from('email_logs')
        .insert({
          org_id,
          email_type: type,
          recipient: to,
          subject: emailContent.subject,
          status: 'sent',
          external_id: emailResult?.id,
          sent_at: new Date().toISOString()
        })
        .catch(error => console.error('Failed to log email:', error))
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Email sent successfully',
        email_id: emailResult?.id
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Email service error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

function generateInvitationEmail(data: InvitationEmailData, orgName?: string): { subject: string; html: string; text: string } {
  const organizationName = orgName || 'Kaya Finance'
  
  const subject = `You're invited to join ${organizationName}`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invitation to ${organizationName}</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
            .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .role-badge { background: #dbeafe; color: #1e40af; padding: 4px 12px; border-radius: 20px; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>You're Invited!</h1>
                <p>Join ${organizationName} on Kaya Finance</p>
            </div>
            <div class="content">
                <p>Hello!</p>
                <p><strong>${data.inviter_name}</strong> has invited you to join <strong>${organizationName}</strong> as a <span class="role-badge">${data.role}</span>.</p>
                
                <p>Kaya Finance is a comprehensive financial management platform that helps businesses track invoices, manage expenses, and maintain accurate financial records.</p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="${data.invitation_url}" class="button">Accept Invitation</a>
                </div>
                
                <p><strong>What you can do with your ${data.role} role:</strong></p>
                <ul>
                    ${getRolePermissions(data.role)}
                </ul>
                
                <p><strong>Important:</strong> This invitation expires on ${new Date(data.expires_at).toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                })}.</p>
                
                <p>If you have any questions, please contact your administrator or reach out to our support team.</p>
            </div>
            <div class="footer">
                <p>This invitation was sent by ${organizationName} via Kaya Finance</p>
                <p>If you didn't expect this invitation, you can safely ignore this email.</p>
            </div>
        </div>
    </body>
    </html>
  `
  
  const text = `
You're invited to join ${organizationName}!

${data.inviter_name} has invited you to join ${organizationName} as a ${data.role}.

Accept your invitation: ${data.invitation_url}

This invitation expires on ${new Date(data.expires_at).toLocaleDateString()}.

If you have any questions, please contact your administrator.

---
This invitation was sent by ${organizationName} via Kaya Finance
  `
  
  return { subject, html, text }
}

function getRolePermissions(role: string): string {
  switch (role.toLowerCase()) {
    case 'admin':
      return `
        <li>Full access to all financial data and settings</li>
        <li>Manage users and permissions</li>
        <li>Configure organization settings</li>
        <li>Access all reports and analytics</li>
      `
    case 'accountant':
      return `
        <li>Create and manage invoices and bills</li>
        <li>Record payments and transactions</li>
        <li>Generate financial reports</li>
        <li>Manage customers and vendors</li>
      `
    case 'viewer':
      return `
        <li>View financial reports and dashboards</li>
        <li>Access read-only transaction data</li>
        <li>Export reports for analysis</li>
      `
    default:
      return '<li>Access to assigned features and data</li>'
  }
}

function generatePasswordResetEmail(data: Record<string, unknown>): { subject: string; html: string; text: string } {
  // Implementation for password reset emails
  return {
    subject: 'Reset your Kaya Finance password',
    html: '<p>Password reset functionality coming soon</p>',
    text: 'Password reset functionality coming soon'
  }
}

function generateWelcomeEmail(data: Record<string, unknown>, orgName?: string): { subject: string; html: string; text: string } {
  // Implementation for welcome emails
  return {
    subject: `Welcome to ${orgName || 'Kaya Finance'}!`,
    html: '<p>Welcome email functionality coming soon</p>',
    text: 'Welcome email functionality coming soon'
  }
}

function generateNotificationEmail(data: Record<string, unknown>): { subject: string; html: string; text: string } {
  // Implementation for notification emails
  return {
    subject: 'Kaya Finance Notification',
    html: '<p>Notification email functionality coming soon</p>',
    text: 'Notification email functionality coming soon'
  }
}
