import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate, Outlet } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuthHook'
import { Layout } from '@/components/Layout'
import { Toaster } from '@/components/ui/toaster'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { logger } from '@/lib/logger'
// import { apiInterceptor } from '@/lib/apiInterceptor' // Temporarily disabled - causing Supabase method chaining issues
import { Login } from '@/pages/Login'
import { Signup } from '@/pages/Signup'
import { CompanyOnboarding } from '@/pages/CompanyOnboarding'
import Dashboard from '@/pages/Dashboard'
import { Accounts } from '@/pages/Accounts'
import { JournalEntries } from '@/pages/JournalEntries'
import { RecurringJournals } from '@/pages/RecurringJournals'
import { Customers } from '@/pages/Customers'
import { Vendors } from '@/pages/Vendors'
import { Invoices } from '@/pages/Invoices'
import { Bills } from '@/pages/Bills'
import { Payments } from '@/pages/Payments'
import { Reports } from '@/pages/Reports'
import Budgets from '@/pages/Budgets'
import TaxManagement from '@/pages/TaxManagement'
import Products from '@/pages/Products'
import Inventory from '@/pages/Inventory'
import SettingsOrganization from '@/pages/settings/SettingsOrganization'
import SettingsBackup from '@/pages/settings/SettingsBackup'
import SettingsAccounting from '@/pages/settings/SettingsAccounting'
import SettingsSecurity from '@/pages/settings/SettingsSecurity'
import SettingsNotifications from '@/pages/settings/SettingsNotifications'
import UserManagement from '@/pages/UserManagement'
import AuditLogs from '@/pages/AuditLogs'
import { NotificationTest } from '@/pages/NotificationTest'
import AcceptInvitation from '@/pages/AcceptInvitation'
import NotFound from '@/pages/NotFound'
import { MonitoringDashboard } from '@/components/monitoring/MonitoringDashboard'
import Help from '@/pages/Help'
import Index from '@/pages/Index'
import PrivacyPolicy from '@/pages/PrivacyPolicy'
import TermsOfService from '@/pages/TermsOfService'
import Support from '@/pages/Support'

import { LoadingPage } from '@/components/ui/loading'

function OnboardingRoute({ children }: { children: React.ReactNode }) {
  const { user, loading, needsOnboarding } = useAuth()
  
  if (loading) return <LoadingPage text="Loading..." />
  
  if (!user) {
    return <Navigate to="/login" replace />
  }

  if (!needsOnboarding) {
    return <Navigate to="/dashboard" replace />
  }

  return <>{children}</>
}

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading, needsOnboarding } = useAuth()
  
  if (loading) return <LoadingPage text="Loading..." />
  
  if (!user) {
    return <Navigate to="/" replace />
  }

  if (needsOnboarding) {
    return <Navigate to="/onboarding" replace />
  }

  return <Layout>{children}</Layout>
}

function AdminRoute({ children }: { children: React.ReactNode }) {
  const { user, profile, loading, needsOnboarding } = useAuth()
  
  if (loading) return <LoadingPage text="Loading..." />
  
  if (!user) {
    return <Navigate to="/login" replace />
  }

  if (needsOnboarding) {
    return <Navigate to="/onboarding" replace />
  }

  if (profile?.role !== 'admin') {
    return <Navigate to="/dashboard" replace />
  }
  return <Layout>{children}</Layout>
}

function App() {
  const { user, loading, needsOnboarding } = useAuth()

  // Initialize monitoring systems
  React.useEffect(() => {
    logger.info('Application started', {
      component: 'App',
      action: 'startup',
      metadata: {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
      }
    })

    // API interceptor temporarily disabled due to Supabase method chaining conflicts
    // apiInterceptor // This triggers the singleton initialization
  }, [])

  if (loading) return <LoadingPage text="Loading Kaya Finance..." />

  return (
    <ErrorBoundary component="App" showDetails={import.meta.env.DEV}>
      <Router>
      <Routes>
        {/* Root route - simplified logic */}
        <Route 
          path="/" 
          element={
            user ? (
              needsOnboarding ? (
                <Navigate to="/onboarding" replace />
              ) : (
                <Navigate to="/dashboard" replace />
              )
            ) : (
              <Index />
            )
          } 
        />

        {/* Auth routes - only show to non-authenticated users */}
        <Route 
          path="/login" 
          element={user ? <Navigate to={needsOnboarding ? "/onboarding" : "/dashboard"} replace /> : <Login />} 
        />
        <Route
          path="/signup"
          element={user ? <Navigate to={needsOnboarding ? "/onboarding" : "/dashboard"} replace /> : <Signup />}
        />

        {/* Accept Invitation Route - Public route */}
        <Route path="/accept-invitation" element={<AcceptInvitation />} />

        {/* Onboarding Route */}
        <Route path="/onboarding" element={<OnboardingRoute><CompanyOnboarding /></OnboardingRoute>} />

        {/* Protected Routes */}
        <Route element={<ProtectedRoute><Outlet /></ProtectedRoute>}>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/accounts" element={<Accounts />} />
          <Route path="/journal-entries" element={<JournalEntries />} />
          <Route path="/recurring-journals" element={<RecurringJournals />} />
          <Route path="/customers" element={<Customers />} />
          <Route path="/vendors" element={<Vendors />} />
          <Route path="/invoices" element={<Invoices />} />
          <Route path="/bills" element={<Bills />} />
          <Route path="/payments" element={<Payments />} />
          <Route path="/products" element={<Products />} />
          <Route path="/inventory" element={<Inventory />} />
          <Route path="/reports" element={<Reports />} />
          <Route path="/budgets" element={<Budgets />} />
          <Route path="/tax-management" element={<TaxManagement />} />
          <Route path="/settings" element={<Navigate to="/settings/organization" replace />} />
          <Route path="/settings/organization" element={<SettingsOrganization />} />
          <Route path="/settings/backup" element={<SettingsBackup />} />
          <Route path="/settings/accounting" element={<SettingsAccounting />} />
          <Route path="/settings/security" element={<SettingsSecurity />} />
          <Route path="/settings/notifications" element={<SettingsNotifications />} />
          <Route path="/audit-logs" element={<AuditLogs />} />
          <Route path="/notification-test" element={<NotificationTest />} />
          <Route path="/help" element={<Help />} />
        </Route>

        {/* Admin Only Routes */}
        <Route element={<AdminRoute><Outlet /></AdminRoute>}>
          <Route path="/user-management" element={<UserManagement />} />
          <Route path="/monitoring" element={<MonitoringDashboard />} />
        </Route>

        {/* Legal Pages - Public Routes */}
        <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        <Route path="/terms-of-service" element={<TermsOfService />} />
        <Route path="/support" element={<Support />} />

        {/* 404 Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
      <Toaster />
    </Router>
    </ErrorBoundary>
  )
}

export default App
