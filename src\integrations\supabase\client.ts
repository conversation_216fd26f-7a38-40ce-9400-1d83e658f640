// Production-ready Supabase client with environment configuration
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { config } from '@/lib/config';

// Use environment-specific configuration
const SUPABASE_URL = config.supabase.url;
const SUPABASE_PUBLISHABLE_KEY = config.supabase.anonKey;

// Enhanced client configuration for production
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: window.localStorage,
    storageKey: 'kaya-finance-auth',
  },
  global: {
    headers: {
      'X-Client-Info': `kaya-finance@${config.app.version}`,
    },
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});
