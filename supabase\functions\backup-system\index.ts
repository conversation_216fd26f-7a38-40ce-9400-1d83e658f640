// =====================================================
// AUTOMATED BACKUP SYSTEM
// Comprehensive data backup and recovery functions
// =====================================================

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface BackupConfig {
  tables: string[]
  retentionDays: number
  compressionEnabled: boolean
  encryptionEnabled: boolean
}

interface BackupMetadata {
  id: string
  created_at: string
  backup_type: 'full' | 'incremental' | 'differential'
  size_bytes: number
  table_count: number
  record_count: number
  checksum: string
  status: 'pending' | 'completed' | 'failed'
  error_message?: string
}

const DEFAULT_CONFIG: BackupConfig = {
  tables: [
    'organizations',
    'profiles', 
    'customers',
    'vendors',
    'accounts',
    'invoices',
    'bills',
    'payments',
    'journal_entries',
    'transaction_lines',
    'audit_logs'
  ],
  retentionDays: 90,
  compressionEnabled: true,
  encryptionEnabled: true
}

serve(async (req) => {
  try {
    const { method } = req
    const url = new URL(req.url)
    const path = url.pathname

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    switch (path) {
      case '/backup/create':
        return await createBackup(supabase, req)
      case '/backup/restore':
        return await restoreBackup(supabase, req)
      case '/backup/list':
        return await listBackups(supabase, req)
      case '/backup/verify':
        return await verifyBackup(supabase, req)
      case '/backup/cleanup':
        return await cleanupOldBackups(supabase, req)
      default:
        return new Response('Not Found', { status: 404 })
    }
  } catch (error) {
    console.error('Backup function error:', error)

    // Log error details for debugging
    const errorDetails = {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    }

    console.error('Detailed error info:', errorDetails)

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Internal server error',
        error_id: crypto.randomUUID(),
        timestamp: new Date().toISOString()
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})

async function createBackup(supabase: SupabaseClient, req: Request) {
  const { org_id, backup_type = 'full', created_by } = await req.json()

  if (!org_id) {
    return new Response(
      JSON.stringify({ error: 'Organization ID is required' }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    )
  }

  const backupId = crypto.randomUUID()
  const timestamp = new Date().toISOString()

  // Use atomic backup operation
  const result = await performAtomicBackup(supabase, {
    backupId,
    orgId: org_id,
    backupType: backup_type,
    createdBy: created_by,
    timestamp
  })

  if (!result.success) {
    return new Response(
      JSON.stringify({ error: result.error }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({
      success: true,
      backup_id: backupId,
      message: 'Backup created successfully'
    }),
    { headers: { 'Content-Type': 'application/json' } }
  )
}

// Atomic backup operation with transaction-like behavior
async function performAtomicBackup(
  supabase: SupabaseClient,
  params: {
    backupId: string
    orgId: string
    backupType: string
    createdBy: string
    timestamp: string
  }
): Promise<{ success: boolean; error?: string }> {
  const { backupId, orgId, backupType, createdBy, timestamp } = params
  let metadataCreated = false
  let storageUploaded = false
  let storagePath = ''

  try {
    // Step 1: Validate prerequisites
    const validationResult = await validateBackupPrerequisites(supabase, orgId, backupType)
    if (!validationResult.valid) {
      throw new Error(validationResult.error)
    }

    // Step 2: Create backup metadata record (marks start of transaction)
    const { error: metadataError } = await supabase
      .from('backup_metadata')
      .insert({
        id: backupId,
        org_id: orgId,
        backup_type: backupType,
        status: 'pending',
        encryption_enabled: validationResult.encryptionEnabled,
        created_by: createdBy,
        created_at: timestamp,
        started_at: new Date().toISOString()
      })

    if (metadataError) {
      throw new Error(`Failed to create backup metadata: ${metadataError.message}`)
    }
    metadataCreated = true

    // Step 3: Update status to in_progress
    await supabase
      .from('backup_metadata')
      .update({ status: 'in_progress' })
      .eq('id', backupId)

    // Step 4: Perform the actual backup with progress tracking
    const backupResult = await performBackupWithProgress(supabase, orgId, backupType, backupId)
    if (!backupResult.success) {
      throw new Error(backupResult.error)
    }

    // Step 5: Process and encrypt data if needed
    let dataToStore = JSON.stringify(backupResult.data)
    let encryptionKeyId = null
    let encryptionIv = null
    let encryptionAlgorithm = null
    let encryptionVersion = null

    if (validationResult.encryptionEnabled) {
      const encryptionResult = await encryptBackupData(dataToStore, orgId, supabase)
      dataToStore = encryptionResult.encryptedData
      encryptionKeyId = encryptionResult.keyId
      encryptionIv = encryptionResult.iv
      encryptionAlgorithm = encryptionResult.algorithm
      encryptionVersion = encryptionResult.version
    }

    // Step 6: Calculate checksum
    const checksum = await calculateChecksum(dataToStore)

    // Step 7: Store backup data atomically
    storagePath = `${orgId}/${backupId}.json`
    const { error: storageError } = await supabase.storage
      .from('backups')
      .upload(storagePath, dataToStore, {
        contentType: 'application/json',
        metadata: {
          org_id: orgId,
          backup_type: backupType,
          checksum,
          encrypted: validationResult.encryptionEnabled,
          created_at: timestamp
        }
      })

    if (storageError) {
      throw new Error(`Failed to store backup data: ${storageError.message}`)
    }
    storageUploaded = true

    // Step 8: Update metadata with completion info (atomic commit)
    const updateData: Record<string, unknown> = {
      status: 'completed',
      size_bytes: dataToStore.length,
      table_count: Object.keys(backupResult.data).length,
      record_count: Object.values(backupResult.data).reduce((sum: number, table: unknown[]) => sum + table.length, 0),
      checksum,
      storage_path: storagePath,
      completed_at: new Date().toISOString()
    }

    // Add encryption fields if encryption was used
    if (validationResult.encryptionEnabled) {
      updateData.encryption_key_id = encryptionKeyId
      updateData.encryption_iv = encryptionIv
      updateData.encryption_algorithm = encryptionAlgorithm
      updateData.encryption_key_version = encryptionVersion
    }

    const { error: updateError } = await supabase
      .from('backup_metadata')
      .update(updateData)
      .eq('id', backupId)

    if (updateError) {
      throw new Error(`Failed to update backup metadata: ${updateError.message}`)
    }

    // Step 9: Send success notification
    await sendBackupNotification(supabase, orgId, 'success', {
      backup_id: backupId,
      backup_type: backupType,
      size: dataToStore.length,
      table_count: Object.keys(backupResult.data).length,
      record_count: Object.values(backupResult.data).reduce((sum: number, table: unknown[]) => sum + table.length, 0)
    })

    return { success: true }

  } catch (error) {
    console.error('Atomic backup failed:', error)

    // Rollback operations in reverse order
    try {
      // Remove uploaded file if it exists
      if (storageUploaded && storagePath) {
        await supabase.storage
          .from('backups')
          .remove([storagePath])
      }

      // Update metadata to failed status if it was created
      if (metadataCreated) {
        await supabase
          .from('backup_metadata')
          .update({
            status: 'failed',
            error_message: error instanceof Error ? error.message : 'Unknown error',
            completed_at: new Date().toISOString()
          })
          .eq('id', backupId)
      }

      // Send error notification
      await sendBackupNotification(supabase, orgId, 'error', {
        backup_id: backupId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

    } catch (rollbackError) {
      console.error('Rollback failed:', rollbackError)
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Validate backup prerequisites
async function validateBackupPrerequisites(
  supabase: SupabaseClient,
  orgId: string,
  backupType: string
): Promise<{ valid: boolean; error?: string; encryptionEnabled?: boolean }> {
  try {
    // Check if organization exists
    const { data: org, error: orgError } = await supabase
      .from('organizations')
      .select('id')
      .eq('id', orgId)
      .single()

    if (orgError || !org) {
      return { valid: false, error: 'Organization not found' }
    }

    // Check if there's already a backup in progress
    const { data: activeBackups, error: activeError } = await supabase
      .from('backup_metadata')
      .select('id')
      .eq('org_id', orgId)
      .in('status', ['pending', 'in_progress'])

    if (activeError) {
      return { valid: false, error: 'Failed to check active backups' }
    }

    if (activeBackups && activeBackups.length > 0) {
      return { valid: false, error: 'Another backup operation is already in progress' }
    }

    // Get backup settings
    const { data: settings } = await supabase
      .from('backup_settings')
      .select('encryption_enabled')
      .eq('org_id', orgId)
      .single()

    const encryptionEnabled = settings?.encryption_enabled || false

    return { valid: true, encryptionEnabled }
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Validation failed'
    }
  }
}

// Perform backup with progress tracking and performance optimization
async function performBackupWithProgress(
  supabase: SupabaseClient,
  orgId: string,
  backupType: string,
  backupId: string
): Promise<{ success: boolean; data?: Record<string, unknown[]>; error?: string }> {
  try {
    // Determine if we should use streaming for large datasets
    const shouldUseStreaming = await shouldUseStreamingBackup(supabase, orgId)

    if (shouldUseStreaming) {
      console.log('Using streaming backup for large dataset')
      return await performStreamingBackupWithProgress(supabase, orgId, backupId)
    } else {
      console.log('Using standard backup for normal dataset')
      return await performStandardBackupWithProgress(supabase, orgId, backupId)
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Backup failed'
    }
  }
}

// Check if streaming backup should be used based on data size
async function shouldUseStreamingBackup(supabase: SupabaseClient, orgId: string): Promise<boolean> {
  try {
    let totalRecords = 0

    // Check record counts for major tables
    const tablesToCheck = ['customers', 'invoices', 'bills', 'payments', 'journal_entries', 'transaction_lines']

    for (const table of tablesToCheck) {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true })
        .eq('org_id', orgId)

      if (!error && count) {
        totalRecords += count
      }
    }

    // Use streaming if more than 10,000 records
    return totalRecords > 10000

  } catch (error) {
    console.error('Error checking dataset size:', error)
    return false // Default to standard backup
  }
}

// Perform streaming backup with progress tracking
async function performStreamingBackupWithProgress(
  supabase: SupabaseClient,
  orgId: string,
  backupId: string
): Promise<{ success: boolean; data?: Record<string, unknown[]>; error?: string }> {
  const backupData: Record<string, unknown[]> = {}
  const totalTables = DEFAULT_CONFIG.tables.length
  let completedTables = 0

  try {
    for (const table of DEFAULT_CONFIG.tables) {
      try {
        // Update progress
        const progress = Math.round((completedTables / totalTables) * 100)
        await supabase
          .from('backup_metadata')
          .update({
            progress_percentage: progress,
            current_table: table
          })
          .eq('id', backupId)

        // Use streaming for large tables
        const tableData = await streamTableDataOptimized(supabase, table, orgId, backupId)
        backupData[table] = tableData
        completedTables++

        console.log(`Successfully backed up table ${table}: ${tableData.length} records`)

      } catch (error) {
        console.error(`Failed to backup table ${table}:`, error)
        throw error
      }
    }

    // Final progress update
    await supabase
      .from('backup_metadata')
      .update({
        progress_percentage: 100,
        current_table: null
      })
      .eq('id', backupId)

    return { success: true, data: backupData }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Streaming backup failed'
    }
  }
}

// Perform standard backup with progress tracking
async function performStandardBackupWithProgress(
  supabase: SupabaseClient,
  orgId: string,
  backupId: string
): Promise<{ success: boolean; data?: Record<string, unknown[]>; error?: string }> {
  const backupData: Record<string, unknown[]> = {}
  const totalTables = DEFAULT_CONFIG.tables.length
  let completedTables = 0

  try {
    for (const table of DEFAULT_CONFIG.tables) {
      try {
        // Update progress
        const progress = Math.round((completedTables / totalTables) * 100)
        await supabase
          .from('backup_metadata')
          .update({
            progress_percentage: progress,
            current_table: table
          })
          .eq('id', backupId)

        let query = supabase.from(table).select('*')

        // Add org_id filter for organization-specific tables
        if (['customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts', 'journal_entries', 'transaction_lines'].includes(table)) {
          query = query.eq('org_id', orgId)
        } else if (table === 'profiles') {
          query = query.eq('org_id', orgId)
        } else if (table === 'organizations') {
          query = query.eq('id', orgId)
        }

        const { data, error } = await query

        if (error) {
          console.error(`Error backing up table ${table}:`, error)
          throw new Error(`Failed to backup table ${table}: ${error.message}`)
        }

        backupData[table] = data || []
        completedTables++

        console.log(`Successfully backed up table ${table}: ${(data || []).length} records`)

      } catch (error) {
        console.error(`Failed to backup table ${table}:`, error)
        throw error
      }
    }

    // Final progress update
    await supabase
      .from('backup_metadata')
      .update({
        progress_percentage: 100,
        current_table: null
      })
      .eq('id', backupId)

    return { success: true, data: backupData }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Standard backup failed'
    }
  }
}

// Stream table data with optimization for large datasets
async function streamTableDataOptimized(
  supabase: SupabaseClient,
  table: string,
  orgId: string,
  backupId: string
): Promise<unknown[]> {
  const allData: unknown[] = []
  const chunkSize = 1000
  let offset = 0
  let hasMore = true
  let chunkCount = 0

  console.log(`Starting optimized streaming for table: ${table}`)

  while (hasMore) {
    try {
      // Build query with pagination
      let query = supabase
        .from(table)
        .select('*')
        .range(offset, offset + chunkSize - 1)
        .order('created_at', { ascending: true })

      // Add org_id filter for organization-specific tables
      if (['customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts', 'journal_entries', 'transaction_lines'].includes(table)) {
        query = query.eq('org_id', orgId)
      } else if (table === 'profiles') {
        query = query.eq('org_id', orgId)
      } else if (table === 'organizations') {
        query = query.eq('id', orgId)
      }

      const { data: chunk, error } = await query

      if (error) {
        throw new Error(`Failed to fetch chunk for table ${table}: ${error.message}`)
      }

      if (!chunk || chunk.length === 0) {
        hasMore = false
        break
      }

      allData.push(...chunk)
      chunkCount++

      // Update detailed progress for large tables
      if (chunkCount % 10 === 0) {
        await supabase
          .from('backup_metadata')
          .update({
            current_table: `${table} (${allData.length} records)`
          })
          .eq('id', backupId)
      }

      // Check if we got fewer records than requested (end of data)
      if (chunk.length < chunkSize) {
        hasMore = false
      } else {
        offset += chunkSize
      }

      // Small delay to prevent overwhelming the database
      if (chunkCount % 5 === 0) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      console.log(`Processed chunk ${chunkCount} for table ${table}: ${chunk.length} records (total: ${allData.length})`)

    } catch (error) {
      console.error(`Error in chunk ${chunkCount} for table ${table}:`, error)
      throw error
    }
  }

  console.log(`Completed optimized streaming for table ${table}: ${allData.length} total records in ${chunkCount} chunks`)
  return allData
}

async function performBackup(supabase: SupabaseClient, orgId: string, backupType: string) {
  const backupData: Record<string, unknown[]> = {}

  for (const table of DEFAULT_CONFIG.tables) {
    try {
      let query = supabase.from(table).select('*')

      // Add org_id filter for organization-specific tables
      if (['customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts', 'journal_entries', 'transaction_lines'].includes(table)) {
        query = query.eq('org_id', orgId)
      } else if (table === 'profiles') {
        // Get profiles for this organization
        query = query.eq('org_id', orgId)
      } else if (table === 'organizations') {
        // Get only this organization
        query = query.eq('id', orgId)
      }

      const { data, error } = await query

      if (error) {
        console.error(`Error backing up table ${table}:`, error)
        continue
      }

      backupData[table] = data || []
    } catch (error) {
      console.error(`Failed to backup table ${table}:`, error)
      backupData[table] = []
    }
  }

  return backupData
}

async function calculateChecksum(data: string): Promise<string> {
  const encoder = new TextEncoder()
  const dataBuffer = encoder.encode(data)
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

async function encryptBackupData(data: string, orgId: string, supabase: SupabaseClient): Promise<{
  encryptedData: string
  keyId: string
  iv: string
}> {
  // Get or create encryption key for organization
  const { data: keyRecord, error: keyError } = await supabase
    .from('backup_encryption_keys')
    .select('*')
    .eq('org_id', orgId)
    .eq('is_active', true)
    .single()

  let cryptoKey: CryptoKey
  let keyId: string

  if (keyError || !keyRecord) {
    // Generate new key
    cryptoKey = await crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      true,
      ['encrypt', 'decrypt']
    )

    // Export and store key
    const exportedKey = await crypto.subtle.exportKey('raw', cryptoKey)
    const keyData = btoa(String.fromCharCode(...new Uint8Array(exportedKey)))

    const { data: newKey, error: insertError } = await supabase
      .from('backup_encryption_keys')
      .insert({
        org_id: orgId,
        key_data: keyData,
        algorithm: 'AES-GCM-256',
        is_active: true
      })
      .select()
      .single()

    if (insertError) throw insertError
    keyId = newKey.id
  } else {
    // Import existing key
    const keyBuffer = Uint8Array.from(atob(keyRecord.key_data), c => c.charCodeAt(0))
    cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'AES-GCM' },
      true,
      ['encrypt', 'decrypt']
    )
    keyId = keyRecord.id
  }

  // Generate random IV
  const iv = crypto.getRandomValues(new Uint8Array(12))

  // Encrypt data
  const encodedData = new TextEncoder().encode(data)
  const encryptedBuffer = await crypto.subtle.encrypt(
    { name: 'AES-GCM', iv },
    cryptoKey,
    encodedData
  )

  return {
    encryptedData: btoa(String.fromCharCode(...new Uint8Array(encryptedBuffer))),
    keyId,
    iv: btoa(String.fromCharCode(...iv))
  }
}

async function decryptBackupData(
  encryptedData: string,
  keyId: string,
  iv: string,
  supabase: SupabaseClient
): Promise<string> {
  // Get encryption key
  const { data: keyRecord, error: keyError } = await supabase
    .from('backup_encryption_keys')
    .select('*')
    .eq('id', keyId)
    .single()

  if (keyError || !keyRecord) {
    throw new Error('Encryption key not found')
  }

  // Import key
  const keyBuffer = Uint8Array.from(atob(keyRecord.key_data), c => c.charCodeAt(0))
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBuffer,
    { name: 'AES-GCM' },
    true,
    ['encrypt', 'decrypt']
  )

  // Decode encrypted data and IV
  const encryptedBuffer = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0))
  const ivBuffer = Uint8Array.from(atob(iv), c => c.charCodeAt(0))

  // Decrypt data
  const decryptedBuffer = await crypto.subtle.decrypt(
    { name: 'AES-GCM', iv: ivBuffer },
    cryptoKey,
    encryptedBuffer
  )

  return new TextDecoder().decode(decryptedBuffer)
}

async function restoreBackup(supabase: SupabaseClient, req: Request) {
  const { backup_id, org_id, tables = [], restore_mode = 'replace' } = await req.json()
  
  if (!backup_id || !org_id) {
    return new Response(
      JSON.stringify({ error: 'Backup ID and Organization ID are required' }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    )
  }

  try {
    // Get backup metadata
    const { data: metadata, error: metadataError } = await supabase
      .from('backup_metadata')
      .select('*')
      .eq('id', backup_id)
      .eq('org_id', org_id)
      .single()

    if (metadataError || !metadata) {
      throw new Error('Backup not found')
    }

    // Download backup data
    const { data: backupFile, error: downloadError } = await supabase.storage
      .from('backups')
      .download(`${org_id}/${backup_id}.json`)

    if (downloadError) throw downloadError

    let backupDataText = await backupFile.text()

    // Decrypt data if it's encrypted
    if (metadata.encryption_enabled && metadata.encryption_key_id && metadata.encryption_iv) {
      backupDataText = await decryptBackupData(
        backupDataText,
        metadata.encryption_key_id,
        metadata.encryption_iv,
        supabase
      )
    }

    const backupData = JSON.parse(backupDataText)

    // Verify checksum (of the original encrypted data or plain data)
    const calculatedChecksum = await calculateChecksum(await backupFile.text())
    if (calculatedChecksum !== metadata.checksum) {
      throw new Error('Backup data integrity check failed')
    }

    // Restore specified tables or all tables
    const tablesToRestore = tables.length > 0 ? tables : Object.keys(backupData)
    const restoredTables: string[] = []

    // Perform secure restoration with proper safeguards
    const restorationResult = await performSecureRestoration(
      supabase,
      org_id,
      tablesToRestore,
      backupData,
      restore_mode,
      backup_id
    )

    if (!restorationResult.success) {
      throw new Error(`Restoration failed: ${restorationResult.error}`)
    }

    restoredTables.push(...restorationResult.restoredTables)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Backup restored successfully',
        restored_tables: restoredTables
      }),
      { headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}

async function listBackups(supabase: SupabaseClient, req: Request) {
  const url = new URL(req.url)
  const orgId = url.searchParams.get('org_id')
  
  if (!orgId) {
    return new Response(
      JSON.stringify({ error: 'Organization ID is required' }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    )
  }

  const { data, error } = await supabase
    .from('backup_metadata')
    .select('*')
    .eq('org_id', orgId)
    .order('created_at', { ascending: false })

  if (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ backups: data }),
    { headers: { 'Content-Type': 'application/json' } }
  )
}

async function verifyBackup(supabase: SupabaseClient, req: Request) {
  const { backup_id, org_id } = await req.json()
  
  try {
    // Get backup metadata
    const { data: metadata, error: metadataError } = await supabase
      .from('backup_metadata')
      .select('*')
      .eq('id', backup_id)
      .eq('org_id', org_id)
      .single()

    if (metadataError) throw metadataError

    // Download and verify backup
    const { data: backupFile, error: downloadError } = await supabase.storage
      .from('backups')
      .download(`${org_id}/${backup_id}.json`)

    if (downloadError) throw downloadError

    const rawData = await backupFile.text()
    let backupData: Record<string, unknown>

    // Handle encrypted backups
    if (metadata.encryption_enabled && metadata.encryption_key_id && metadata.encryption_iv) {
      try {
        const decryptedData = await decryptBackupData(
          rawData,
          metadata.encryption_key_id,
          metadata.encryption_iv,
          supabase
        )
        backupData = JSON.parse(decryptedData)
      } catch (error) {
        return new Response(
          JSON.stringify({
            valid: false,
            error: 'Failed to decrypt backup data',
            checksum_match: false
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )
      }
    } else {
      backupData = JSON.parse(rawData)
    }

    // Verify checksum (of the stored data, encrypted or plain)
    const calculatedChecksum = await calculateChecksum(rawData)
    const isValid = calculatedChecksum === metadata.checksum

    return new Response(
      JSON.stringify({
        valid: isValid,
        checksum_match: isValid,
        backup_size: JSON.stringify(backupData).length,
        table_count: Object.keys(backupData).length
      }),
      { headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message, valid: false }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}

async function cleanupOldBackups(supabase: SupabaseClient, req: Request) {
  const retentionDays = DEFAULT_CONFIG.retentionDays
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

  try {
    // Get old backups
    const { data: oldBackups, error: queryError } = await supabase
      .from('backup_metadata')
      .select('*')
      .lt('created_at', cutoffDate.toISOString())

    if (queryError) throw queryError

    let deletedCount = 0
    for (const backup of oldBackups || []) {
      try {
        // Delete from storage
        await supabase.storage
          .from('backups')
          .remove([`${backup.org_id}/${backup.id}.json`])

        // Delete metadata
        await supabase
          .from('backup_metadata')
          .delete()
          .eq('id', backup.id)

        deletedCount++
      } catch (error) {
        console.error(`Failed to delete backup ${backup.id}:`, error)
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        deleted_count: deletedCount,
        message: `Cleaned up ${deletedCount} old backups`
      }),
      { headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}

async function sendBackupNotification(supabase: SupabaseClient, orgId: string, type: 'success' | 'error', details: Record<string, unknown>) {
  try {
    const templateType = type === 'success' ? 'backup_completed' : 'backup_failed'

    await supabase.rpc('create_notification_from_template', {
      template_type: templateType,
      org_id: orgId,
      user_id: null, // System notification
      template_data: details
    })
  } catch (error) {
    console.error('Failed to send backup notification:', error)
  }
}

// Secure restoration function with proper safeguards
async function performSecureRestoration(
  supabase: SupabaseClient,
  orgId: string,
  tablesToRestore: string[],
  backupData: Record<string, unknown[]>,
  restoreMode: string,
  backupId: string
): Promise<{
  success: boolean
  restoredTables: string[]
  error?: string
}> {
  const restoredTables: string[] = []
  let preRestoreSnapshot: Record<string, unknown[]> = {}

  try {
    // Step 1: Create pre-restore snapshot for rollback capability
    if (restoreMode === 'replace') {
      console.log('Creating pre-restore snapshot...')
      preRestoreSnapshot = await createPreRestoreSnapshot(supabase, orgId, tablesToRestore)

      // Store snapshot metadata
      await supabase.from('backup_restorations').update({
        pre_restore_snapshot_id: `snapshot_${backupId}_${Date.now()}`
      }).eq('backup_id', backupId)
    }

    // Step 2: Validate backup data integrity before proceeding
    const validationResult = await validateBackupDataIntegrity(backupData, tablesToRestore)
    if (!validationResult.valid) {
      throw new Error(`Backup validation failed: ${validationResult.error}`)
    }

    // Step 3: Perform restoration with transaction-like behavior
    for (const table of tablesToRestore) {
      if (backupData[table] && Array.isArray(backupData[table]) && backupData[table].length > 0) {
        try {
          console.log(`Restoring table: ${table}`)

          if (restoreMode === 'replace') {
            // Safe deletion with confirmation and logging
            const deleteResult = await performSafeDeletion(supabase, table, orgId)
            if (!deleteResult.success) {
              throw new Error(`Failed to clear table ${table}: ${deleteResult.error}`)
            }
          }

          // Insert backup data with validation
          const insertResult = await performSafeInsertion(supabase, table, backupData[table])
          if (!insertResult.success) {
            throw new Error(`Failed to restore data to table ${table}: ${insertResult.error}`)
          }

          restoredTables.push(table)
          console.log(`Successfully restored table: ${table} (${backupData[table].length} records)`)

        } catch (error) {
          console.error(`Failed to restore table ${table}:`, error)

          // If we're in replace mode and something fails, attempt rollback
          if (restoreMode === 'replace' && preRestoreSnapshot[table]) {
            console.log(`Attempting rollback for table: ${table}`)
            await performRollback(supabase, table, preRestoreSnapshot[table], orgId)
          }

          throw error
        }
      }
    }

    return {
      success: true,
      restoredTables
    }

  } catch (error) {
    console.error('Restoration failed:', error)

    // Attempt to rollback all changes if in replace mode
    if (restoreMode === 'replace' && Object.keys(preRestoreSnapshot).length > 0) {
      console.log('Attempting full rollback due to restoration failure...')
      await performFullRollback(supabase, preRestoreSnapshot, orgId)
    }

    return {
      success: false,
      restoredTables,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Create a snapshot of current data before restoration
async function createPreRestoreSnapshot(
  supabase: SupabaseClient,
  orgId: string,
  tables: string[]
): Promise<Record<string, unknown[]>> {
  const snapshot: Record<string, unknown[]> = {}

  for (const table of tables) {
    try {
      let query = supabase.from(table).select('*')

      // Add org_id filter for organization-specific tables
      if (['customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts', 'journal_entries', 'transaction_lines'].includes(table)) {
        query = query.eq('org_id', orgId)
      } else if (table === 'profiles') {
        query = query.eq('org_id', orgId)
      } else if (table === 'organizations') {
        query = query.eq('id', orgId)
      }

      const { data, error } = await query

      if (error) {
        console.error(`Error creating snapshot for table ${table}:`, error)
        snapshot[table] = []
      } else {
        snapshot[table] = data || []
      }
    } catch (error) {
      console.error(`Failed to snapshot table ${table}:`, error)
      snapshot[table] = []
    }
  }

  return snapshot
}

// Validate backup data integrity
async function validateBackupDataIntegrity(
  backupData: Record<string, unknown[]>,
  tables: string[]
): Promise<{ valid: boolean; error?: string }> {
  try {
    // Check if all required tables are present
    for (const table of tables) {
      if (!backupData[table]) {
        return { valid: false, error: `Missing data for table: ${table}` }
      }

      if (!Array.isArray(backupData[table])) {
        return { valid: false, error: `Invalid data format for table: ${table}` }
      }
    }

    // Validate data structure for each table
    for (const [table, data] of Object.entries(backupData)) {
      if (tables.includes(table) && Array.isArray(data)) {
        for (const record of data) {
          if (!record || typeof record !== 'object') {
            return { valid: false, error: `Invalid record format in table: ${table}` }
          }

          // Check for required fields based on table
          if (['customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts'].includes(table)) {
            if (!Object.prototype.hasOwnProperty.call(record, 'org_id')) {
              return { valid: false, error: `Missing org_id in table: ${table}` }
            }
          }
        }
      }
    }

    return { valid: true }
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Validation error'
    }
  }
}

// Perform safe deletion with logging and confirmation
async function performSafeDeletion(
  supabase: SupabaseClient,
  table: string,
  orgId: string
): Promise<{ success: boolean; error?: string; deletedCount?: number }> {
  try {
    // First, count existing records for logging
    let countQuery = supabase.from(table).select('*', { count: 'exact', head: true })

    if (['customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts', 'journal_entries', 'transaction_lines'].includes(table)) {
      countQuery = countQuery.eq('org_id', orgId)
    } else if (table === 'profiles') {
      countQuery = countQuery.eq('org_id', orgId)
    } else if (table === 'organizations') {
      countQuery = countQuery.eq('id', orgId)
    }

    const { count: existingCount, error: countError } = await countQuery

    if (countError) {
      return { success: false, error: `Failed to count existing records: ${countError.message}` }
    }

    console.log(`Preparing to delete ${existingCount || 0} records from table: ${table}`)

    // Perform the deletion with proper org_id filtering
    let deleteQuery = supabase.from(table).delete()

    if (['customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts', 'journal_entries', 'transaction_lines'].includes(table)) {
      deleteQuery = deleteQuery.eq('org_id', orgId)
    } else if (table === 'profiles') {
      deleteQuery = deleteQuery.eq('org_id', orgId)
    } else if (table === 'organizations') {
      deleteQuery = deleteQuery.eq('id', orgId)
    }

    const { error: deleteError } = await deleteQuery

    if (deleteError) {
      return { success: false, error: `Deletion failed: ${deleteError.message}` }
    }

    console.log(`Successfully deleted ${existingCount || 0} records from table: ${table}`)

    return { success: true, deletedCount: existingCount || 0 }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown deletion error'
    }
  }
}

// Perform safe insertion with validation
async function performSafeInsertion(
  supabase: SupabaseClient,
  table: string,
  data: unknown[]
): Promise<{ success: boolean; error?: string; insertedCount?: number }> {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      return { success: true, insertedCount: 0 }
    }

    console.log(`Inserting ${data.length} records into table: ${table}`)

    // Insert data in batches to avoid timeout issues
    const batchSize = 1000
    let totalInserted = 0

    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize)

      const { error: insertError } = await supabase
        .from(table)
        .insert(batch)

      if (insertError) {
        return {
          success: false,
          error: `Batch insertion failed at record ${i}: ${insertError.message}`
        }
      }

      totalInserted += batch.length
      console.log(`Inserted batch ${Math.floor(i / batchSize) + 1}: ${batch.length} records`)
    }

    console.log(`Successfully inserted ${totalInserted} records into table: ${table}`)

    return { success: true, insertedCount: totalInserted }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown insertion error'
    }
  }
}

// Perform rollback for a single table
async function performRollback(
  supabase: SupabaseClient,
  table: string,
  snapshotData: unknown[],
  orgId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`Rolling back table: ${table}`)

    // Clear current data
    const deleteResult = await performSafeDeletion(supabase, table, orgId)
    if (!deleteResult.success) {
      return { success: false, error: `Rollback deletion failed: ${deleteResult.error}` }
    }

    // Restore snapshot data
    const insertResult = await performSafeInsertion(supabase, table, snapshotData)
    if (!insertResult.success) {
      return { success: false, error: `Rollback insertion failed: ${insertResult.error}` }
    }

    console.log(`Successfully rolled back table: ${table}`)
    return { success: true }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown rollback error'
    }
  }
}

// Perform full rollback for all tables
async function performFullRollback(
  supabase: SupabaseClient,
  snapshot: Record<string, unknown[]>,
  orgId: string
): Promise<void> {
  console.log('Performing full rollback...')

  for (const [table, data] of Object.entries(snapshot)) {
    try {
      await performRollback(supabase, table, data, orgId)
    } catch (error) {
      console.error(`Failed to rollback table ${table}:`, error)
      // Continue with other tables even if one fails
    }
  }

  console.log('Full rollback completed')
}
