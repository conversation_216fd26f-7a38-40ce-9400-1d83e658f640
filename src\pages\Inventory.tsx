import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Package, 
  Plus, 
  AlertTriangle, 
  TrendingDown, 
  BarChart3,
  MapPin,
  FolderTree
} from 'lucide-react'
import { ProductList } from '@/components/inventory/ProductList'
import { ProductForm } from '@/components/inventory/ProductForm'
import { ProductDetailView } from '@/components/inventory/ProductDetailView'
import { 
  useInventoryDashboardSummary,
  useInventoryAlerts,
  useLowStockProducts,
  useActiveProductCategories,
  useActiveInventoryLocations
} from '@/hooks/queries'
import type { ProductWithStock } from '@/types/inventory'

export default function Inventory() {
  const [activeTab, setActiveTab] = useState('overview')
  const [showProductForm, setShowProductForm] = useState(false)
  const [showProductDetail, setShowProductDetail] = useState(false)
  const [editingProduct, setEditingProduct] = useState<ProductWithStock | null>(null)
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null)

  // Data hooks
  const { data: dashboardData } = useInventoryDashboardSummary()
  const { data: alerts = [] } = useInventoryAlerts()
  const { data: lowStockProducts = [] } = useLowStockProducts()
  const { data: categories = [] } = useActiveProductCategories()
  const { data: locations = [] } = useActiveInventoryLocations()

  const handleCreateProduct = () => {
    setEditingProduct(null)
    setShowProductForm(true)
  }

  const handleEditProduct = (product: ProductWithStock) => {
    setEditingProduct(product)
    setShowProductForm(true)
  }

  const handleViewProduct = (product: ProductWithStock) => {
    setSelectedProductId(product.id)
    setShowProductDetail(true)
  }

  const handleProductFormSuccess = () => {
    setShowProductForm(false)
    setEditingProduct(null)
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Inventory Management</h1>
          <p className="text-gray-600">
            Manage your products, track stock levels, and monitor inventory movements
          </p>
        </div>
        <Button onClick={handleCreateProduct}>
          <Plus className="h-4 w-4 mr-2" />
          Add Product
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="locations">Locations</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.total_products || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.active_products || 0} active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
                <AlertTriangle className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {dashboardData?.low_stock_products || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Require attention
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {dashboardData?.out_of_stock_products || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Need restocking
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Inventory Value</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${dashboardData?.total_inventory_value?.toFixed(2) || '0.00'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Total value
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Alerts and Low Stock */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Low Stock Alert */}
            {lowStockProducts.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-600">
                    <AlertTriangle className="h-5 w-5" />
                    Low Stock Alert
                  </CardTitle>
                  <CardDescription>
                    Products that need to be restocked
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {lowStockProducts.slice(0, 5).map((product) => (
                      <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-gray-500">SKU: {product.sku}</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-orange-600">
                            {product.total_quantity_available || 0} {product.unit_of_measure}
                          </div>
                          <div className="text-xs text-gray-500">
                            Reorder at: {product.reorder_level || 0}
                          </div>
                        </div>
                      </div>
                    ))}
                    {lowStockProducts.length > 5 && (
                      <div className="text-center">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setActiveTab('products')}
                        >
                          View All ({lowStockProducts.length})
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
                <CardDescription>
                  Overview of your inventory setup
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FolderTree className="h-4 w-4 text-gray-500" />
                    <span>Product Categories</span>
                  </div>
                  <span className="font-medium">{categories.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span>Inventory Locations</span>
                  </div>
                  <span className="font-medium">{locations.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-gray-500" />
                    <span>Tracked Products</span>
                  </div>
                  <span className="font-medium">
                    {dashboardData?.active_products || 0}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Products Tab */}
        <TabsContent value="products">
          <ProductList
            onCreateProduct={handleCreateProduct}
            onEditProduct={handleEditProduct}
            onViewProduct={handleViewProduct}
          />
        </TabsContent>

        {/* Categories Tab */}
        <TabsContent value="categories">
          <Card>
            <CardHeader>
              <CardTitle>Product Categories</CardTitle>
              <CardDescription>
                Organize your products into categories for better management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                Category management coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Locations Tab */}
        <TabsContent value="locations">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Locations</CardTitle>
              <CardDescription>
                Manage warehouses, stores, and other inventory locations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                Location management coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Reports</CardTitle>
              <CardDescription>
                Analyze inventory performance and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                Inventory reports coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <ProductForm
        open={showProductForm}
        onOpenChange={setShowProductForm}
        product={editingProduct}
        onSuccess={handleProductFormSuccess}
      />

      <ProductDetailView
        open={showProductDetail}
        onOpenChange={setShowProductDetail}
        productId={selectedProductId}
        onEdit={handleEditProduct}
      />
    </div>
  )
}
