#!/bin/bash

# =====================================================
# KAYA FINANCE ENVIRONMENT DEPLOYMENT SCRIPT
# Automated deployment with environment validation
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Default values
ENVIRONMENT=""
SKIP_TESTS=false
SKIP_BUILD=false
DRY_RUN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -e|--environment)
      ENVIRONMENT="$2"
      shift 2
      ;;
    --skip-tests)
      SKIP_TESTS=true
      shift
      ;;
    --skip-build)
      SKIP_BUILD=true
      shift
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    -h|--help)
      echo "Usage: $0 -e <environment> [options]"
      echo "Options:"
      echo "  -e, --environment    Target environment (staging|production)"
      echo "  --skip-tests        Skip running tests"
      echo "  --skip-build        Skip build process"
      echo "  --dry-run           Show what would be done without executing"
      echo "  -h, --help          Show this help message"
      exit 0
      ;;
    *)
      print_error "Unknown option $1"
      exit 1
      ;;
  esac
done

# Validate environment
if [[ -z "$ENVIRONMENT" ]]; then
  print_error "Environment is required. Use -e staging or -e production"
  exit 1
fi

if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
  print_error "Environment must be 'staging' or 'production'"
  exit 1
fi

print_status "🚀 Starting deployment to $ENVIRONMENT environment"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  print_error "package.json not found. Please run this script from the project root."
  exit 1
fi

# Validate environment file exists
ENV_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
  print_error "Environment file $ENV_FILE not found"
  exit 1
fi

print_success "Environment file $ENV_FILE found"

# Function to validate environment variables
validate_env_vars() {
  print_status "Validating environment variables..."
  
  # Required variables
  REQUIRED_VARS=(
    "VITE_SUPABASE_URL"
    "VITE_SUPABASE_ANON_KEY"
    "VITE_APP_ENV"
  )
  
  # Source the environment file
  set -a
  source "$ENV_FILE"
  set +a
  
  # Check required variables
  for var in "${REQUIRED_VARS[@]}"; do
    if [[ -z "${!var}" ]]; then
      print_error "Required environment variable $var is not set"
      exit 1
    fi
  done
  
  # Validate Supabase URL format
  if [[ ! "$VITE_SUPABASE_URL" =~ ^https://.*\.supabase\.co$ ]]; then
    print_error "Invalid Supabase URL format"
    exit 1
  fi
  
  # Validate environment matches
  if [[ "$VITE_APP_ENV" != "$ENVIRONMENT" ]]; then
    print_error "VITE_APP_ENV ($VITE_APP_ENV) doesn't match target environment ($ENVIRONMENT)"
    exit 1
  fi
  
  print_success "Environment variables validated"
}

# Function to run tests
run_tests() {
  if [[ "$SKIP_TESTS" == true ]]; then
    print_warning "Skipping tests as requested"
    return
  fi
  
  print_status "Running test suite..."
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would run: npm run test:coverage"
    return
  fi
  
  if npm run test:coverage; then
    print_success "All tests passed"
  else
    print_error "Tests failed. Deployment aborted."
    exit 1
  fi
}

# Function to build application
build_application() {
  if [[ "$SKIP_BUILD" == true ]]; then
    print_warning "Skipping build as requested"
    return
  fi
  
  print_status "Building application for $ENVIRONMENT..."
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would run: npm run build:$ENVIRONMENT"
    return
  fi
  
  # Set environment variables for build
  export NODE_ENV=production
  export VITE_APP_ENV="$ENVIRONMENT"
  
  if npm run "build:$ENVIRONMENT"; then
    print_success "Build completed successfully"
  else
    print_error "Build failed. Deployment aborted."
    exit 1
  fi
}

# Function to validate build output
validate_build() {
  if [[ "$SKIP_BUILD" == true ]] || [[ "$DRY_RUN" == true ]]; then
    return
  fi
  
  print_status "Validating build output..."
  
  # Check if dist directory exists
  if [ ! -d "dist" ]; then
    print_error "Build directory 'dist' not found"
    exit 1
  fi
  
  # Check if index.html exists
  if [ ! -f "dist/index.html" ]; then
    print_error "index.html not found in build output"
    exit 1
  fi
  
  # Check bundle sizes
  MAIN_JS_SIZE=$(find dist/assets -name "index-*.js" -exec ls -lh {} \; | awk '{print $5}' | head -1)
  print_status "Main JS bundle size: $MAIN_JS_SIZE"
  
  # Warn if bundle is too large (>2MB)
  MAIN_JS_BYTES=$(find dist/assets -name "index-*.js" -exec ls -l {} \; | awk '{print $5}' | head -1)
  if [[ "$MAIN_JS_BYTES" -gt 2097152 ]]; then
    print_warning "Main JS bundle is larger than 2MB. Consider optimization."
  fi
  
  print_success "Build output validated"
}

# Function to deploy to environment
deploy_to_environment() {
  print_status "Deploying to $ENVIRONMENT environment..."
  
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would deploy to $ENVIRONMENT"
    print_status "[DRY RUN] Would run health checks"
    print_status "[DRY RUN] Would send notifications"
    return
  fi
  
  case "$ENVIRONMENT" in
    "staging")
      deploy_to_staging
      ;;
    "production")
      deploy_to_production
      ;;
  esac
}

# Function to deploy to staging
deploy_to_staging() {
  print_status "Deploying to staging environment..."
  
  # Add staging-specific deployment logic here
  # Example: Netlify, Vercel, or custom deployment
  
  print_success "Deployed to staging successfully"
  print_status "Staging URL: https://staging.kayafinance.net"
}

# Function to deploy to production
deploy_to_production() {
  print_status "Deploying to production environment..."
  
  # Production deployment requires additional confirmation
  if [[ "$DRY_RUN" != true ]]; then
    read -p "Are you sure you want to deploy to PRODUCTION? (yes/no): " -r
    if [[ ! $REPLY =~ ^yes$ ]]; then
      print_warning "Production deployment cancelled"
      exit 0
    fi
  fi
  
  # Add production-specific deployment logic here
  
  print_success "Deployed to production successfully"
  print_status "Production URL: https://kayafinance.net"
}

# Function to run post-deployment checks
post_deployment_checks() {
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would run post-deployment health checks"
    return
  fi
  
  print_status "Running post-deployment health checks..."
  
  # Add health check logic here
  # Example: curl health endpoints, run smoke tests
  
  print_success "Post-deployment checks completed"
}

# Function to send notifications
send_notifications() {
  if [[ "$DRY_RUN" == true ]]; then
    print_status "[DRY RUN] Would send deployment notifications"
    return
  fi
  
  print_status "Sending deployment notifications..."
  
  # Add notification logic here
  # Example: Slack, email, Discord webhooks
  
  print_success "Notifications sent"
}

# Main execution flow
main() {
  print_status "🚀 Kaya Finance Deployment Pipeline"
  print_status "Environment: $ENVIRONMENT"
  print_status "Dry Run: $DRY_RUN"
  
  validate_env_vars
  run_tests
  build_application
  validate_build
  deploy_to_environment
  post_deployment_checks
  send_notifications
  
  print_success "🎉 Deployment to $ENVIRONMENT completed successfully!"
}

# Run main function
main "$@"
