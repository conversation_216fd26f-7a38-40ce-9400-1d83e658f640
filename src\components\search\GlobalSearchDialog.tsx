import { useEffect, useRef } from 'react'
import { Search, Command } from 'lucide-react'
import {
  <PERSON>alog,
  DialogContent,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useGlobalSearch } from '@/hooks/useGlobalSearch'
import { SearchResults } from './SearchResults'
import { RecentSearches } from './RecentSearches'
import { cn } from '@/lib/utils'
import type { SearchResult } from '@/types/search'

interface GlobalSearchDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function GlobalSearchDialog({ open, onOpenChange }: GlobalSearchDialogProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const {
    searchState,
    setQuery,
    selectResult,
    navigateToResult,
    clearRecentSearches,
    removeRecentSearch
  } = useGlobalSearch()

  // Focus input when dialog opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [open])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          if (searchState.query && searchState.results.length > 0) {
            selectResult(searchState.selectedIndex + 1)
          }
          break
          
        case 'ArrowUp':
          e.preventDefault()
          if (searchState.query && searchState.results.length > 0) {
            selectResult(searchState.selectedIndex - 1)
          }
          break
          
        case 'Enter':
          e.preventDefault()
          if (searchState.selectedIndex >= 0 && searchState.results[searchState.selectedIndex]) {
            const selectedResult = searchState.results[searchState.selectedIndex]
            navigateToResult(selectedResult)
            onOpenChange(false)
          }
          break
          
        case 'Escape':
          onOpenChange(false)
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [open, searchState, selectResult, navigateToResult, onOpenChange])

  const handleResultClick = (result: SearchResult) => {
    navigateToResult(result)
    onOpenChange(false)
  }

  const handleRecentSearchSelect = (query: string) => {
    setQuery(query)
  }

  const showRecentSearches = !searchState.query && searchState.recentSearches.length > 0
  const showResults = searchState.query && (searchState.results.length > 0 || searchState.loading || searchState.error)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl p-0 gap-0 overflow-hidden">
        {/* Search Input */}
        <div className="flex items-center border-b px-4 py-3">
          <Search className="h-4 w-4 text-muted-foreground mr-3" />
          <Input
            ref={inputRef}
            placeholder={searchState.loading ? "Searching..." : "Search customers, vendors, invoices, bills, payments..."}
            value={searchState.query}
            onChange={(e) => setQuery(e.target.value)}
            className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-base"
          />
          <div className="ml-3 text-xs text-muted-foreground hidden sm:block">
            <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
              <span className="text-xs">⌘</span>K
            </kbd>
          </div>
        </div>

        {/* Content */}
        <div className="max-h-96 overflow-y-auto">
          {showRecentSearches && (
            <RecentSearches
              searches={searchState.recentSearches}
              onSearchSelect={handleRecentSearchSelect}
              onRemoveSearch={removeRecentSearch}
              onClearAll={clearRecentSearches}
            />
          )}

          {showResults && (
            <SearchResults
              results={searchState.results}
              loading={searchState.loading}
              error={searchState.error}
              selectedIndex={searchState.selectedIndex}
              onResultClick={handleResultClick}
              onResultHover={selectResult}
            />
          )}

          {!showRecentSearches && !showResults && (
            <div className="p-8 text-center text-muted-foreground">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm font-medium">Start typing to search</p>
              <p className="text-xs mt-1">
                Search across customers, vendors, invoices, bills, and payments
              </p>
              <div className="mt-4 text-xs">
                <p className="mb-1">💡 <strong>Pro tip:</strong> Use keyboard shortcuts</p>
                <div className="flex justify-center gap-4 text-xs">
                  <span>↑↓ Navigate</span>
                  <span>↵ Select</span>
                  <span>⌘K Open</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t px-4 py-2 text-xs text-muted-foreground bg-muted/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <kbd className="pointer-events-none inline-flex h-4 select-none items-center gap-1 rounded border bg-background px-1 font-mono text-[10px]">
                  ↑↓
                </kbd>
                navigate
              </span>
              <span className="flex items-center gap-1">
                <kbd className="pointer-events-none inline-flex h-4 select-none items-center gap-1 rounded border bg-background px-1 font-mono text-[10px]">
                  ↵
                </kbd>
                select
              </span>
              <span className="flex items-center gap-1">
                <kbd className="pointer-events-none inline-flex h-4 select-none items-center gap-1 rounded border bg-background px-1 font-mono text-[10px]">
                  esc
                </kbd>
                close
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
