import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { StatementOfAccount } from './StatementOfAccount'
import type { Vendor } from '@/types/database'

interface VendorStatementDialogProps {
  entity: Vendor
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function VendorStatementDialog({ 
  entity, 
  open, 
  onOpenChange 
}: VendorStatementDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Vendor Statement - {entity.name}</DialogTitle>
        </DialogHeader>
        <StatementOfAccount 
          entity={entity} 
          entity_type="vendor" 
        />
      </DialogContent>
    </Dialog>
  )
}
