import React, { useEffect } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { auditLogger } from '@/lib/auditLogger'
import type { AuditAction } from '@/types/database'

// Type for form data
type FormData = Record<string, unknown>

// Type for metadata
type MetadataRecord = Record<string, string | number | boolean | null | undefined>

export function useAuditLogger() {
  const { profile } = useAuth()

  useEffect(() => {
    if (profile?.id && profile?.org_id) {
      auditLogger.setContext({
        userId: profile.id,
        orgId: profile.org_id
      })
    }
  }, [profile])

  const logFormSubmission = async (
    entityType: string,
    entityId: string,
    action: AuditAction,
    formData: FormData,
    oldData?: FormData
  ) => {
    try {
      if (action === 'create') {
        await auditLogger.logCreate(entityType, entityId, formData)
      } else if (action === 'update' && oldData) {
        await auditLogger.logUpdate(entityType, entityId, oldData, formData)
      } else if (action === 'delete' && oldData) {
        await auditLogger.logDelete(entityType, entityId, oldData)
      }
    } catch (error) {
      console.error('Failed to log form submission:', error)
    }
  }

  const logBusinessAction = async (
    entityType: string,
    entityId: string,
    action: AuditAction,
    description: string,
    metadata?: MetadataRecord
  ) => {
    try {
      await auditLogger.log({
        entity_type: entityType,
        entity_id: entityId,
        action,
        description,
        metadata
      })
    } catch (error) {
      console.error('Failed to log business action:', error)
    }
  }

  const logDataAccess = async (
    entityType: string,
    entityId: string,
    accessType: 'view' | 'export' | 'print' = 'view',
    description?: string
  ) => {
    try {
      if (accessType === 'view') {
        await auditLogger.logView(entityType, entityId, description)
      } else if (accessType === 'export') {
        await auditLogger.logExport(entityType, [entityId], 'unknown', description)
      } else {
        // For 'print' action, we'll use 'view' as the closest valid action
        await auditLogger.log({
          entity_type: entityType,
          entity_id: entityId,
          action: 'view',
          description: description || `${accessType} ${entityType} ${entityId}`,
          severity: 'low'
        })
      }
    } catch (error) {
      console.error('Failed to log data access:', error)
    }
  }

  const logSecurityEvent = async (
    eventType: string,
    description: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    metadata?: MetadataRecord
  ) => {
    try {
      await auditLogger.log({
        entity_type: 'security',
        entity_id: 'system',
        action: 'view', // Using 'view' as the closest valid action for security events
        description,
        severity,
        category: 'security',
        metadata: {
          event_type: eventType,
          ...metadata
        }
      })
    } catch (error) {
      console.error('Failed to log security event:', error)
    }
  }

  const logSystemEvent = async (
    eventType: string,
    description: string,
    metadata?: MetadataRecord
  ) => {
    try {
      // Map common system events to valid audit actions
      const actionMap: Record<string, AuditAction> = {
        'backup': 'backup',
        'restore': 'restore',
        'archive': 'archive',
        'purge': 'purge',
        'configuration_change': 'update'
      }

      const action = actionMap[eventType] || 'update'

      await auditLogger.log({
        entity_type: 'system',
        entity_id: 'system',
        action,
        description,
        severity: 'low',
        category: 'system_configuration',
        metadata
      })
    } catch (error) {
      console.error('Failed to log system event:', error)
    }
  }

  return {
    logFormSubmission,
    logBusinessAction,
    logDataAccess,
    logSecurityEvent,
    logSystemEvent,
    // Direct access to the logger for advanced use cases
    auditLogger
  }
}

// Higher-order component to automatically log component access
export function withAuditLogging<T extends object>(
  Component: React.ComponentType<T>,
  entityType: string,
  getEntityId?: (props: T) => string
): React.ComponentType<T> {
  const AuditLoggedComponent = (props: T) => {
    const { logDataAccess } = useAuditLogger()

    useEffect(() => {
      const entityId = getEntityId ? getEntityId(props) : 'component'
      logDataAccess(entityType, entityId, 'view', `Accessed ${entityType} component`)
    }, [logDataAccess, props])

    return React.createElement(Component, props)
  }

  // Set display name for debugging
  AuditLoggedComponent.displayName = `withAuditLogging(${Component.displayName || Component.name || 'Component'})`

  return AuditLoggedComponent
}

// Hook for tracking user interactions
export function useInteractionTracking(entityType: string, entityId?: string) {
  const { logDataAccess } = useAuditLogger()

  const trackClick = (element: string, _metadata?: MetadataRecord) => {
    if (entityId) {
      logDataAccess(entityType, entityId, 'view', `Clicked ${element}`)
    }
  }

  const trackView = (description?: string) => {
    if (entityId) {
      logDataAccess(entityType, entityId, 'view', description)
    }
  }

  const trackExport = (format: string, description?: string) => {
    if (entityId) {
      logDataAccess(entityType, entityId, 'export', description || `Exported as ${format}`)
    }
  }

  return {
    trackClick,
    trackView,
    trackExport
  }
}

// Utility function to create audit-aware form handlers
export function createAuditedFormHandler<T extends FormData>(
  entityType: string,
  action: AuditAction,
  submitHandler: (data: T) => Promise<{ id: string; data: T }>,
  getOldData?: () => Promise<T>
) {
  return async (formData: T) => {
    const { logFormSubmission } = useAuditLogger()

    try {
      const result = await submitHandler(formData)

      // Log the form submission
      const oldData = getOldData ? await getOldData() : undefined
      await logFormSubmission(entityType, result.id, action, result.data, oldData)

      return result
    } catch (error) {
      // Log failed attempts
      await logFormSubmission(entityType, 'unknown', action, {
        ...formData,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      })
      throw error
    }
  }
}
