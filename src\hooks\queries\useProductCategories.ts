import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import type { 
  ProductCategory, 
  ProductCategoryWithChildren, 
  ProductCategoryFormData 
} from '@/types/inventory'

/**
 * Hook to fetch all product categories for an organization
 */
export function useProductCategories() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.productCategories.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('sort_order', { ascending: true })
        .order('name', { ascending: true })

      if (error) throw error
      return data as ProductCategory[]
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch active product categories only
 */
export function useActiveProductCategories() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.productCategories.active(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('sort_order', { ascending: true })
        .order('name', { ascending: true })

      if (error) throw error
      return data as ProductCategory[]
    },
    enabled: !!profile?.org_id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook to fetch product categories in a tree structure
 */
export function useProductCategoryTree() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.productCategories.tree(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      // First, get all categories
      const { data: categories, error } = await supabase
        .from('product_categories')
        .select(`
          *,
          products!inner(id)
        `)
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('sort_order', { ascending: true })
        .order('name', { ascending: true })

      if (error) throw error

      // Build tree structure
      const categoryMap = new Map<string, ProductCategoryWithChildren>()
      const rootCategories: ProductCategoryWithChildren[] = []

      // First pass: create all category objects with product count
      categories.forEach(category => {
        const categoryWithChildren: ProductCategoryWithChildren = {
          ...category,
          children: [],
          product_count: category.products?.length || 0
        }
        categoryMap.set(category.id, categoryWithChildren)
      })

      // Second pass: build parent-child relationships
      categories.forEach(category => {
        const categoryWithChildren = categoryMap.get(category.id)!
        
        if (category.parent_id) {
          const parent = categoryMap.get(category.parent_id)
          if (parent) {
            parent.children!.push(categoryWithChildren)
            categoryWithChildren.parent = parent
          }
        } else {
          rootCategories.push(categoryWithChildren)
        }
      })

      return rootCategories
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch a single product category by ID
 */
export function useProductCategory(categoryId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.productCategories.detail(profile?.org_id || '', categoryId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !categoryId) return null

      const { data, error } = await supabase
        .from('product_categories')
        .select(`
          *,
          parent:product_categories!parent_id(*),
          children:product_categories!parent_id(*),
          products(id, name, sku)
        `)
        .eq('id', categoryId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      
      const categoryWithChildren: ProductCategoryWithChildren = {
        ...data,
        children: data.children || [],
        parent: data.parent || null,
        product_count: data.products?.length || 0
      }

      return categoryWithChildren
    },
    enabled: !!profile?.org_id && !!categoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to create a new product category
 */
export function useCreateProductCategory() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (categoryData: ProductCategoryFormData) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('product_categories')
        .insert({
          ...categoryData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data as ProductCategory
    },
    onSuccess: () => {
      // Invalidate all category-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.tree(profile?.org_id || '') })
    },
  })
}

/**
 * Hook to update a product category
 */
export function useUpdateProductCategory() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ categoryId, categoryData }: { categoryId: string; categoryData: Partial<ProductCategoryFormData> }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('product_categories')
        .update({
          ...categoryData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', categoryId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data as ProductCategory
    },
    onSuccess: (data) => {
      // Invalidate all category-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.tree(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.detail(profile?.org_id || '', data.id) })
    },
  })
}

/**
 * Hook to delete a product category
 */
export function useDeleteProductCategory() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (categoryId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Check if category has products or children
      const { data: products } = await supabase
        .from('products')
        .select('id')
        .eq('category_id', categoryId)
        .eq('org_id', profile.org_id)
        .limit(1)

      const { data: children } = await supabase
        .from('product_categories')
        .select('id')
        .eq('parent_id', categoryId)
        .eq('org_id', profile.org_id)
        .limit(1)

      if (products && products.length > 0) {
        throw new Error('Cannot delete category that contains products')
      }

      if (children && children.length > 0) {
        throw new Error('Cannot delete category that has subcategories')
      }

      const { error } = await supabase
        .from('product_categories')
        .delete()
        .eq('id', categoryId)
        .eq('org_id', profile.org_id)

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate all category-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.tree(profile?.org_id || '') })
    },
  })
}

/**
 * Hook to toggle product category active status
 */
export function useToggleProductCategoryStatus() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ categoryId, isActive }: { categoryId: string; isActive: boolean }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('product_categories')
        .update({ 
          is_active: isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('id', categoryId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data as ProductCategory
    },
    onSuccess: (data) => {
      // Invalidate all category-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.tree(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.productCategories.detail(profile?.org_id || '', data.id) })
    },
  })
}
