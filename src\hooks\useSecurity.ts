/**
 * Security Hook
 * Provides security utilities for React components
 */

import { useState, useEffect, useCallback } from 'react'
import { security, InputSanitizer } from '@/lib/security'
import { useAuth } from '@/hooks/useAuthHook'
import { logger, logSecurity } from '@/lib/logger'
import { handleSecurityError } from '@/lib/errorHandler'

// Define types for security operations
type SanitizableInput = string | number | boolean | null | undefined
type SecurityMetadata = Record<string, string | number | boolean | null | undefined>
type FormData = Record<string, SanitizableInput>
type SanitizedFormData = Record<string, string>

export interface SecurityContext {
  userId?: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
}

export interface RateLimitStatus {
  isAllowed: boolean
  remaining: number
  resetTime: number
  blocked: boolean
}

export interface CSRFTokenInfo {
  token: string
  expires: number
}

export function useSecurity() {
  const { user, profile } = useAuth()
  const [csrfToken, setCsrfToken] = useState<string>('')
  const [securityContext, setSecurityContext] = useState<SecurityContext>({})

  // Initialize security context
  useEffect(() => {
    const context: SecurityContext = {
      userId: user?.id,
      sessionId: generateSessionId(),
      userAgent: navigator.userAgent
    }

    setSecurityContext(context)

    // Generate initial CSRF token
    if (context.sessionId) {
      const token = security.csrfProtection.generateToken(context.sessionId)
      setCsrfToken(token)
    }

    // Log security context initialization
    logSecurity('Security context initialized', 'low', {
      component: 'useSecurity',
      action: 'initialize',
      metadata: {
        userId: context.userId,
        hasSessionId: !!context.sessionId
      }
    })
  }, [user?.id, generateSessionId])

  // Generate session ID
  const generateSessionId = useCallback((): string => {
    const existing = sessionStorage.getItem('security_session_id')
    if (existing) return existing

    const sessionId = crypto.randomUUID()
    sessionStorage.setItem('security_session_id', sessionId)
    return sessionId
  }, [])

  // Check rate limit
  const checkRateLimit = useCallback((identifier?: string): RateLimitStatus => {
    const id = identifier || securityContext.userId || securityContext.sessionId || 'anonymous'
    
    const isAllowed = security.rateLimiter.isAllowed(id)
    const remaining = security.rateLimiter.getRemainingRequests(id)
    const resetTime = security.rateLimiter.getResetTime(id)

    if (!isAllowed) {
      logSecurity('Rate limit exceeded', 'medium', {
        component: 'useSecurity',
        action: 'checkRateLimit',
        metadata: {
          identifier: id,
          remaining,
          resetTime
        }
      })
    }

    return {
      isAllowed,
      remaining,
      resetTime,
      blocked: !isAllowed
    }
  }, [securityContext])

  // Validate CSRF token
  const validateCSRFToken = useCallback((token: string): boolean => {
    if (!securityContext.sessionId) {
      logSecurity('CSRF validation failed - no session ID', 'high', {
        component: 'useSecurity',
        action: 'validateCSRFToken'
      })
      return false
    }

    const isValid = security.csrfProtection.validateToken(securityContext.sessionId, token)
    
    if (!isValid) {
      logSecurity('CSRF token validation failed', 'high', {
        component: 'useSecurity',
        action: 'validateCSRFToken',
        metadata: {
          sessionId: securityContext.sessionId
        }
      })
    }

    return isValid
  }, [securityContext.sessionId])

  // Refresh CSRF token
  const refreshCSRFToken = useCallback((): string => {
    if (!securityContext.sessionId) {
      logSecurity('CSRF token refresh failed - no session ID', 'medium', {
        component: 'useSecurity',
        action: 'refreshCSRFToken'
      })
      return ''
    }

    const newToken = security.csrfProtection.refreshToken(securityContext.sessionId)
    setCsrfToken(newToken)

    logSecurity('CSRF token refreshed', 'low', {
      component: 'useSecurity',
      action: 'refreshCSRFToken',
      metadata: {
        sessionId: securityContext.sessionId
      }
    })

    return newToken
  }, [securityContext.sessionId])

  // Sanitize input with validation
  const sanitizeInput = useCallback((
    input: SanitizableInput,
    type: 'text' | 'email' | 'phone' | 'number' | 'html',
    required: boolean = false
  ) => {
    const result = InputSanitizer.validateAndSanitizeInput(input, type, required)
    
    if (!result.isValid) {
      logSecurity('Input validation failed', 'low', {
        component: 'useSecurity',
        action: 'sanitizeInput',
        metadata: {
          type,
          required,
          error: result.error,
          userId: securityContext.userId
        }
      })
    }

    return result
  }, [securityContext.userId])

  // Secure API call wrapper
  const secureApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    options?: {
      requireCSRF?: boolean
      rateLimitId?: string
      operation?: string
    }
  ): Promise<T> => {
    const { requireCSRF = false, rateLimitId, operation = 'api_call' } = options || {}

    // Check rate limit
    const rateLimitStatus = checkRateLimit(rateLimitId)
    if (!rateLimitStatus.isAllowed) {
      const error = new Error('Rate limit exceeded')
      handleSecurityError(error, {
        component: 'useSecurity',
        action: 'secureApiCall',
        userId: securityContext.userId,
        metadata: {
          operation,
          rateLimitStatus
        }
      })
      throw error
    }

    // Validate CSRF token if required
    if (requireCSRF && !validateCSRFToken(csrfToken)) {
      const error = new Error('CSRF token validation failed')
      handleSecurityError(error, {
        component: 'useSecurity',
        action: 'secureApiCall',
        userId: securityContext.userId,
        metadata: {
          operation,
          requireCSRF
        }
      })
      throw error
    }

    try {
      const startTime = Date.now()
      const result = await apiCall()
      const duration = Date.now() - startTime

      logger.info(`Secure API call completed: ${operation}`, {
        component: 'useSecurity',
        action: 'secureApiCall',
        duration,
        userId: securityContext.userId,
        metadata: {
          operation,
          success: true
        }
      })

      return result
    } catch (error) {
      logSecurity('Secure API call failed', 'medium', {
        component: 'useSecurity',
        action: 'secureApiCall',
        userId: securityContext.userId,
        metadata: {
          operation,
          error: (error as Error).message
        }
      })
      throw error
    }
  }, [checkRateLimit, validateCSRFToken, csrfToken, securityContext.userId])

  // Log security event
  const logSecurityEvent = useCallback((
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    metadata?: SecurityMetadata
  ) => {
    logSecurity(event, severity, {
      component: 'useSecurity',
      action: 'logSecurityEvent',
      userId: securityContext.userId,
      metadata: {
        ...metadata,
        securityContext
      }
    })
  }, [securityContext])

  // Validate user permissions
  const validatePermission = useCallback((
    requiredRole: string | string[],
    requiredPermissions?: string[]
  ): boolean => {
    if (!profile) {
      logSecurityEvent('Permission check failed - no profile', 'medium', {
        requiredRole,
        requiredPermissions
      })
      return false
    }

    const userRole = profile.role
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]

    if (!allowedRoles.includes(userRole)) {
      logSecurityEvent('Permission check failed - insufficient role', 'medium', {
        userRole,
        requiredRole,
        requiredPermissions
      })
      return false
    }

    // Additional permission checks could be implemented here
    // For now, we only check roles

    return true
  }, [profile, logSecurityEvent])

  // Secure form submission
  const secureFormSubmit = useCallback(async <T>(
    formData: FormData,
    submitFunction: (sanitizedData: SanitizedFormData) => Promise<T>,
    validationRules?: Record<string, { type: 'text' | 'email' | 'phone' | 'number' | 'html', required?: boolean }>
  ): Promise<T> => {
    // Sanitize all form data
    const sanitizedData: SanitizedFormData = {}
    const validationErrors: Record<string, string> = {}

    for (const [key, value] of Object.entries(formData)) {
      const rule = validationRules?.[key] || { type: 'text', required: false }
      const result = sanitizeInput(value, rule.type, rule.required)

      if (!result.isValid) {
        validationErrors[key] = result.error || 'Validation failed'
      } else {
        sanitizedData[key] = result.sanitized
      }
    }

    if (Object.keys(validationErrors).length > 0) {
      const error = new Error('Form validation failed')
      logSecurityEvent('Form validation failed', 'low', {
        validationErrors,
        formKeys: Object.keys(formData)
      })
      throw error
    }

    // Submit with security checks
    return secureApiCall(() => submitFunction(sanitizedData), {
      requireCSRF: true,
      operation: 'form_submit'
    })
  }, [sanitizeInput, secureApiCall, logSecurityEvent])

  return {
    // Security context
    securityContext,
    
    // CSRF protection
    csrfToken,
    validateCSRFToken,
    refreshCSRFToken,
    
    // Rate limiting
    checkRateLimit,
    
    // Input sanitization
    sanitizeInput,
    
    // Secure operations
    secureApiCall,
    secureFormSubmit,
    
    // Permission validation
    validatePermission,
    
    // Security logging
    logSecurityEvent,
    
    // Utility functions
    generateSessionId
  }
}
