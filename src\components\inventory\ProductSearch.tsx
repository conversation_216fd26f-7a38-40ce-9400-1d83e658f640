import React, { useState, useRef, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { 
  Search, 
  Package, 
  Barcode, 
  Tag, 
  X,
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { useProductSearch } from '@/hooks/queries'
import { useDebounce } from '@/hooks/use-debounce'
import type { ProductOption } from '@/types/inventory'

interface ProductSearchProps {
  onProductSelect: (product: ProductOption) => void
  placeholder?: string
  disabled?: boolean
  includeInactive?: boolean
  showStockInfo?: boolean
  selectedProductId?: string | null
  className?: string
}

interface ProductSearchResult extends ProductOption {
  stock_info?: {
    total_quantity_on_hand: number
    total_quantity_available: number
    is_low_stock: boolean
    is_out_of_stock: boolean
  }
}

export function ProductSearch({
  onProductSelect,
  placeholder = "Search products by name, SKU, or barcode...",
  disabled = false,
  includeInactive = false,
  showStockInfo = true,
  selectedProductId = null,
  className = ""
}: ProductSearchProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const debouncedSearch = useDebounce(searchTerm, 300)
  
  const { data: searchResults = [], isLoading } = useProductSearch(
    debouncedSearch, 
    { includeInactive }
  )

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || searchResults.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < searchResults.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : searchResults.length - 1
        )
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && selectedIndex < searchResults.length) {
          handleProductSelect(searchResults[selectedIndex])
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  const handleProductSelect = (product: ProductOption) => {
    onProductSelect(product)
    setSearchTerm('')
    setIsOpen(false)
    setSelectedIndex(-1)
    inputRef.current?.blur()
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    setIsOpen(value.length >= 2)
    setSelectedIndex(-1)
  }

  const handleInputFocus = () => {
    if (searchTerm.length >= 2) {
      setIsOpen(true)
    }
  }

  const clearSearch = () => {
    setSearchTerm('')
    setIsOpen(false)
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }

  const getStockStatusBadge = (product: ProductSearchResult) => {
    if (!showStockInfo || !product.stock_info) return null

    const { is_out_of_stock, is_low_stock, total_quantity_available } = product.stock_info

    if (is_out_of_stock) {
      return (
        <Badge variant="destructive" className="text-xs">
          Out of Stock
        </Badge>
      )
    }

    if (is_low_stock) {
      return (
        <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
          Low Stock
        </Badge>
      )
    }

    return (
      <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
        {total_quantity_available} in stock
      </Badge>
    )
  }

  const highlightMatch = (text: string, search: string) => {
    if (!search) return text
    
    const regex = new RegExp(`(${search})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-0">
          {part}
        </mark>
      ) : part
    )
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className="pl-10 pr-10"
        />
        {searchTerm && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={clearSearch}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-80 overflow-y-auto shadow-lg">
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span className="text-sm text-gray-500">Searching products...</span>
              </div>
            ) : searchResults.length === 0 ? (
              <div className="flex items-center justify-center p-4">
                <AlertCircle className="h-4 w-4 mr-2 text-gray-400" />
                <span className="text-sm text-gray-500">
                  {debouncedSearch.length < 2 
                    ? 'Type at least 2 characters to search'
                    : 'No products found'
                  }
                </span>
              </div>
            ) : (
              <div className="py-1">
                {searchResults.map((product, index) => (
                  <div
                    key={product.id}
                    className={`px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 hover:bg-gray-50 ${
                      index === selectedIndex ? 'bg-blue-50' : ''
                    } ${selectedProductId === product.id ? 'bg-green-50' : ''}`}
                    onClick={() => handleProductSelect(product)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Package className="h-4 w-4 text-gray-400 flex-shrink-0" />
                          <span className="font-medium text-sm truncate">
                            {highlightMatch(product.name, debouncedSearch)}
                          </span>
                          {selectedProductId === product.id && (
                            <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                          )}
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            <span className="font-mono">
                              {highlightMatch(product.sku, debouncedSearch)}
                            </span>
                          </div>
                          
                          {product.category && (
                            <span>{product.category.name}</span>
                          )}
                          
                          <span className="font-medium">
                            ${product.selling_price.toFixed(2)}
                          </span>
                        </div>

                        {product.description && (
                          <p className="text-xs text-gray-500 mt-1 truncate">
                            {highlightMatch(product.description, debouncedSearch)}
                          </p>
                        )}
                      </div>

                      <div className="flex flex-col items-end gap-1 ml-2">
                        {!product.is_active && (
                          <Badge variant="secondary" className="text-xs">
                            Inactive
                          </Badge>
                        )}
                        
                        {!product.is_sellable && (
                          <Badge variant="outline" className="text-xs">
                            Not Sellable
                          </Badge>
                        )}

                        {getStockStatusBadge(product as ProductSearchResult)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
