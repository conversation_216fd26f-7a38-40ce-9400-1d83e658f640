import { supabase } from '@/lib/supabase'
import type { 
  ApprovalInstance, 
  ApprovalAction,
  ApprovalStatus,
  DocumentType
} from '@/types/database'
import type {
  ApprovalInstanceWithDetails,
  ApprovalFilters,
  ApprovalSortOptions,
  ApprovalStats,
  DocumentDetails
} from '@/types/approval-workflow'

/**
 * Approval Instance Manager
 * Manages approval instances throughout their lifecycle
 */
export class ApprovalInstanceManager {

  /**
   * Get approval instances with filters and pagination
   */
  static async getApprovalInstances(
    orgId: string,
    filters?: ApprovalFilters,
    sortOptions?: ApprovalSortOptions,
    page: number = 1,
    pageSize: number = 20
  ): Promise<{
    instances: ApprovalInstanceWithDetails[]
    total: number
    page: number
    pageSize: number
  }> {
    try {
      let query = supabase
        .from('approval_instances')
        .select(`
          *,
          workflow_template:workflow_templates(*),
          approval_actions(
            *,
            approver:profiles!approval_actions_approver_id_fkey(id, full_name, email, role),
            delegated_to_profile:profiles!approval_actions_delegated_to_fkey(id, full_name, email, role)
          ),
          submitted_by_profile:profiles!approval_instances_submitted_by_fkey(id, full_name, email, role)
        `, { count: 'exact' })
        .eq('org_id', orgId)

      // Apply filters
      if (filters) {
        if (filters.status && filters.status.length > 0) {
          query = query.in('status', filters.status)
        }

        if (filters.document_type && filters.document_type.length > 0) {
          query = query.in('document_type', filters.document_type)
        }

        if (filters.submitted_by && filters.submitted_by.length > 0) {
          query = query.in('submitted_by', filters.submitted_by)
        }

        if (filters.date_range) {
          query = query
            .gte('submitted_at', filters.date_range.start)
            .lte('submitted_at', filters.date_range.end)
        }

        if (filters.amount_range) {
          query = query
            .gte('document_amount', filters.amount_range.min)
            .lte('document_amount', filters.amount_range.max)
        }

        if (filters.overdue_only) {
          // This would require a more complex query to check escalation timeouts
          // For now, we'll implement a basic version
          query = query.eq('status', 'pending')
        }
      }

      // Apply sorting
      if (sortOptions) {
        query = query.order(sortOptions.field, { ascending: sortOptions.direction === 'asc' })
      } else {
        query = query.order('submitted_at', { ascending: false })
      }

      // Apply pagination
      const offset = (page - 1) * pageSize
      query = query.range(offset, offset + pageSize - 1)

      const { data, error, count } = await query

      if (error) throw error

      // Enrich with document details
      const enrichedInstances = await Promise.all(
        (data || []).map(instance => this.enrichInstanceWithDocumentDetails(instance))
      )

      return {
        instances: enrichedInstances,
        total: count || 0,
        page,
        pageSize
      }
    } catch (error) {
      console.error('Error fetching approval instances:', error)
      throw error
    }
  }

  /**
   * Get a specific approval instance with full details
   */
  static async getApprovalInstance(instanceId: string): Promise<ApprovalInstanceWithDetails | null> {
    try {
      const { data, error } = await supabase
        .from('approval_instances')
        .select(`
          *,
          workflow_template:workflow_templates(
            *,
            approval_steps(*)
          ),
          approval_actions(
            *,
            approver:profiles!approval_actions_approver_id_fkey(id, full_name, email, role),
            delegated_to_profile:profiles!approval_actions_delegated_to_fkey(id, full_name, email, role)
          ),
          submitted_by_profile:profiles!approval_instances_submitted_by_fkey(id, full_name, email, role)
        `)
        .eq('id', instanceId)
        .single()

      if (error) throw error

      // Get current step details
      const currentStep = data.workflow_template.approval_steps?.find(
        step => step.step_order === data.current_step_order
      )

      // Enrich with document details
      const enrichedInstance = await this.enrichInstanceWithDocumentDetails({
        ...data,
        current_step: currentStep
      })

      return enrichedInstance
    } catch (error) {
      console.error('Error fetching approval instance:', error)
      return null
    }
  }

  /**
   * Get approval instances pending for a specific user
   * This method bridges the old approval system with the new centralized system
   */
  static async getPendingApprovalsForUser(
    userId: string,
    orgId: string
  ): Promise<ApprovalInstanceWithDetails[]> {
    try {
      // Get user's role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single()

      if (profileError) throw profileError

      const pendingApprovals: ApprovalInstanceWithDetails[] = []

      // 1. Get pending approvals from the new centralized system
      try {
        const { data: centralizedApprovals, error: centralizedError } = await supabase
          .from('approval_instances')
          .select(`
            *,
            workflow_template:workflow_templates(
              *,
              approval_steps(*)
            ),
            approval_actions(*),
            submitted_by_profile:profiles!approval_instances_submitted_by_fkey(id, full_name, email, role)
          `)
          .eq('org_id', orgId)
          .eq('status', 'pending')

        if (!centralizedError && centralizedApprovals) {
          // Filter instances where user can approve current step
          const userApprovals = centralizedApprovals.filter(instance => {
            const currentStep = instance.workflow_template.approval_steps?.find(
              step => step.step_order === instance.current_step_order
            )

            return currentStep && currentStep.required_role.includes(profile.role)
          })

          // Enrich with document details
          const enrichedInstances = await Promise.all(
            userApprovals.map(instance => this.enrichInstanceWithDocumentDetails(instance))
          )

          pendingApprovals.push(...enrichedInstances)
        }
      } catch (error) {
        console.warn('Centralized approval system not available, falling back to legacy system')
      }

      // 2. Get pending approvals from legacy individual tables
      const legacyApprovals = await this.getLegacyPendingApprovals(userId, orgId, profile.role)
      pendingApprovals.push(...legacyApprovals)

      return pendingApprovals
    } catch (error) {
      console.error('Error fetching pending approvals for user:', error)
      return []
    }
  }

  /**
   * Get approval statistics for dashboard
   */
  static async getApprovalStats(
    orgId: string,
    userId?: string
  ): Promise<ApprovalStats> {
    try {
      // Get total pending approvals
      const { count: totalPending } = await supabase
        .from('approval_instances')
        .select('*', { count: 'exact', head: true })
        .eq('org_id', orgId)
        .eq('status', 'pending')

      // Get pending by document type
      const { data: pendingByType } = await supabase
        .from('approval_instances')
        .select('document_type')
        .eq('org_id', orgId)
        .eq('status', 'pending')

      const pendingByTypeCount = (pendingByType || []).reduce((acc, item) => {
        acc[item.document_type] = (acc[item.document_type] || 0) + 1
        return acc
      }, {} as Record<DocumentType, number>)

      // Get today's approvals
      const today = new Date().toISOString().split('T')[0]
      
      const { count: approvedToday } = await supabase
        .from('approval_instances')
        .select('*', { count: 'exact', head: true })
        .eq('org_id', orgId)
        .eq('status', 'approved')
        .gte('completed_at', `${today}T00:00:00.000Z`)

      const { count: rejectedToday } = await supabase
        .from('approval_instances')
        .select('*', { count: 'exact', head: true })
        .eq('org_id', orgId)
        .eq('status', 'rejected')
        .gte('completed_at', `${today}T00:00:00.000Z`)

      // Get user-specific pending count if userId provided
      let myPendingCount = 0
      if (userId) {
        const userPendingApprovals = await this.getPendingApprovalsForUser(userId, orgId)
        myPendingCount = userPendingApprovals.length
      }

      // Calculate average approval time (simplified)
      const { data: completedInstances } = await supabase
        .from('approval_instances')
        .select('submitted_at, completed_at')
        .eq('org_id', orgId)
        .in('status', ['approved', 'rejected'])
        .not('completed_at', 'is', null)
        .limit(100)
        .order('completed_at', { ascending: false })

      let averageApprovalTimeHours = 0
      if (completedInstances && completedInstances.length > 0) {
        const totalHours = completedInstances.reduce((sum, instance) => {
          const submitted = new Date(instance.submitted_at)
          const completed = new Date(instance.completed_at!)
          const hours = (completed.getTime() - submitted.getTime()) / (1000 * 60 * 60)
          return sum + hours
        }, 0)
        
        averageApprovalTimeHours = totalHours / completedInstances.length
      }

      return {
        total_pending: totalPending || 0,
        pending_by_type: pendingByTypeCount,
        overdue_count: 0, // Would require more complex calculation
        approved_today: approvedToday || 0,
        rejected_today: rejectedToday || 0,
        average_approval_time_hours: Math.round(averageApprovalTimeHours * 100) / 100,
        my_pending_count: myPendingCount
      }
    } catch (error) {
      console.error('Error fetching approval stats:', error)
      return {
        total_pending: 0,
        pending_by_type: {},
        overdue_count: 0,
        approved_today: 0,
        rejected_today: 0,
        average_approval_time_hours: 0,
        my_pending_count: 0
      }
    }
  }

  /**
   * Update approval instance status
   */
  static async updateInstanceStatus(
    instanceId: string,
    status: ApprovalStatus,
    completedBy?: string
  ): Promise<ApprovalInstance> {
    try {
      const updateData: Partial<ApprovalInstance> = {
        status,
        updated_at: new Date().toISOString()
      }

      if (status === 'approved' || status === 'rejected' || status === 'cancelled') {
        updateData.completed_at = new Date().toISOString()
        if (completedBy) {
          updateData.completed_by = completedBy
        }
      }

      const { data, error } = await supabase
        .from('approval_instances')
        .update(updateData)
        .eq('id', instanceId)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating instance status:', error)
      throw error
    }
  }

  /**
   * Cancel an approval instance
   */
  static async cancelApprovalInstance(
    instanceId: string,
    cancelledBy: string,
    reason?: string
  ): Promise<void> {
    try {
      await this.updateInstanceStatus(instanceId, 'cancelled', cancelledBy)

      // Log cancellation action
      await supabase
        .from('approval_actions')
        .insert({
          approval_instance_id: instanceId,
          step_order: 0, // Special step for cancellation
          approver_id: cancelledBy,
          action: 'cancelled',
          comments: reason || 'Instance cancelled',
          action_taken_at: new Date().toISOString()
        })
    } catch (error) {
      console.error('Error cancelling approval instance:', error)
      throw error
    }
  }

  /**
   * Enrich instance with document details
   */
  private static async enrichInstanceWithDocumentDetails(
    instance: ApprovalInstance
  ): Promise<ApprovalInstanceWithDetails> {
    try {
      const documentDetails = await this.getDocumentDetails(
        instance.document_type,
        instance.document_id
      )

      return {
        ...instance,
        document_details: documentDetails
      }
    } catch (error) {
      console.error('Error enriching instance with document details:', error)
      return instance
    }
  }

  /**
   * Get document details based on type and ID
   */
  private static async getDocumentDetails(
    documentType: DocumentType,
    documentId: string
  ): Promise<DocumentDetails | undefined> {
    try {
      let tableName: string
      let numberField: string
      let dateField: string

      switch (documentType) {
        case 'invoice':
          tableName = 'invoices'
          numberField = 'invoice_number'
          dateField = 'date_issued'
          break
        case 'bill':
          tableName = 'bills'
          numberField = 'bill_number'
          dateField = 'date_issued'
          break
        case 'payment':
          tableName = 'payments'
          numberField = 'id' // Payments don't have a number field
          dateField = 'payment_date'
          break
        case 'budget':
          tableName = 'budgets'
          numberField = 'name'
          dateField = 'start_date'
          break
        default:
          return undefined
      }

      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .eq('id', documentId)
        .single()

      if (error) throw error

      return {
        id: data.id,
        type: documentType,
        number: data[numberField] || data.id,
        amount: data.total_amount || data.amount || 0,
        currency_code: data.currency_code || 'UGX',
        date: data[dateField],
        status: data.status,
        title: `${documentType.charAt(0).toUpperCase() + documentType.slice(1)} ${data[numberField] || data.id}`,
        description: data.notes || data.description
      }
    } catch (error) {
      console.error('Error fetching document details:', error)
      return undefined
    }
  }

  /**
   * Get pending approvals from legacy individual approval tables
   */
  private static async getLegacyPendingApprovals(
    userId: string,
    orgId: string,
    userRole: string
  ): Promise<ApprovalInstanceWithDetails[]> {
    const pendingApprovals: ApprovalInstanceWithDetails[] = []

    try {
      // Check if user can approve based on role (admin, owner can approve most things)
      const canApprove = ['admin', 'owner'].includes(userRole)

      if (!canApprove) {
        return pendingApprovals
      }

      // 1. Get pending budgets (status = 'pending_approval')
      const { data: pendingBudgets } = await supabase
        .from('budgets')
        .select(`
          id,
          name,
          status,
          total_amount,
          created_at,
          created_by,
          org_id,
          accounts(name, code)
        `)
        .eq('org_id', orgId)
        .eq('status', 'pending_approval')

      if (pendingBudgets) {
        for (const budget of pendingBudgets) {
          pendingApprovals.push({
            id: `budget-${budget.id}`,
            workflow_template_id: 'legacy-budget',
            document_type: 'budget',
            document_id: budget.id,
            document_amount: parseFloat(budget.total_amount),
            status: 'pending',
            current_step_order: 1,
            submitted_by: budget.created_by,
            submitted_at: budget.created_at,
            completed_at: null,
            org_id: budget.org_id,
            created_at: budget.created_at,
            updated_at: budget.created_at,
            workflow_template: {
              id: 'legacy-budget',
              name: 'Legacy Budget Approval',
              description: 'Legacy budget approval process',
              document_type: 'budget',
              is_active: true,
              org_id: budget.org_id,
              created_at: budget.created_at,
              updated_at: budget.created_at,
              approval_steps: [{
                id: 'legacy-budget-step-1',
                workflow_template_id: 'legacy-budget',
                step_order: 1,
                step_name: 'Budget Approval',
                required_role: ['admin', 'owner'],
                allow_self_approval: false,
                escalation_timeout_hours: null,
                is_parallel: false,
                created_at: budget.created_at
              }]
            },
            approval_actions: [],
            submitted_by_profile: null,
            document_details: {
              title: budget.name,
              amount: parseFloat(budget.total_amount),
              currency: 'UGX',
              description: `Budget: ${budget.name}`,
              account: budget.accounts ? `${budget.accounts.code} - ${budget.accounts.name}` : null,
              metadata: {
                budget_id: budget.id,
                account_info: budget.accounts
              }
            }
          })
        }
      }

      return pendingApprovals
    } catch (error) {
      console.error('Error fetching legacy pending approvals:', error)
      return []
    }
  }
}
