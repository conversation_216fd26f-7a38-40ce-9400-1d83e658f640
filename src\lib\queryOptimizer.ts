/**
 * Database Query Optimizer
 * Optimizes Supabase queries, implements intelligent caching, and provides query analytics
 */

import { supabase } from './supabase'
import { performanceMonitor } from './performance'
import { logger, logWarn, logError } from './logger'

export interface QueryConfig {
  table: string
  select?: string
  filters?: Record<string, unknown>
  orderBy?: { column: string; ascending?: boolean }[]
  limit?: number
  offset?: number
  cacheKey?: string
  cacheTTL?: number
  enableCache?: boolean
}

export interface QueryResult<T = unknown> {
  data: T[] | null
  error: unknown
  count?: number
  fromCache: boolean
  executionTime: number
  queryId: string
}

export interface QueryAnalytics {
  queryId: string
  table: string
  executionTime: number
  resultCount: number
  fromCache: boolean
  timestamp: Date
  filters: Record<string, unknown>
  cacheHit: boolean
}

class QueryOptimizer {
  private static instance: QueryOptimizer
  private queryAnalytics: QueryAnalytics[] = []
  private maxAnalytics = 1000
  private defaultCacheTTL = 5 * 60 * 1000 // 5 minutes

  // Common query patterns for optimization
  private optimizationPatterns = {
    // Frequently accessed tables that benefit from caching
    cacheFriendlyTables: ['accounts', 'tax_rates', 'users', 'organizations'],
    
    // Tables that change frequently and should have shorter cache TTL
    volatileTables: ['payments', 'journal_entries'],
    
    // Optimal select patterns for common operations
    selectPatterns: {
      list: 'id, name, created_at, updated_at',
      detail: '*',
      summary: 'id, name, status, total_amount, created_at'
    }
  }

  private constructor() {
    this.loadAnalytics()
  }

  public static getInstance(): QueryOptimizer {
    if (!QueryOptimizer.instance) {
      QueryOptimizer.instance = new QueryOptimizer()
    }
    return QueryOptimizer.instance
  }

  private loadAnalytics(): void {
    try {
      const saved = localStorage.getItem('query_analytics')
      if (saved) {
        this.queryAnalytics = JSON.parse(saved).map((item: QueryAnalytics & { timestamp: string }) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
      }
    } catch (error) {
      logError('Failed to load query analytics', {
        component: 'QueryOptimizer',
        action: 'loadAnalytics',
        metadata: { error: (error as Error).message }
      })
    }
  }

  private saveAnalytics(): void {
    try {
      localStorage.setItem('query_analytics', JSON.stringify(this.queryAnalytics))
    } catch (error) {
      logError('Failed to save query analytics', {
        component: 'QueryOptimizer',
        action: 'saveAnalytics',
        metadata: { error: (error as Error).message }
      })
    }
  }

  private generateCacheKey(config: QueryConfig): string {
    if (config.cacheKey) return config.cacheKey

    const keyParts = [
      config.table,
      config.select || 'default',
      JSON.stringify(config.filters || {}),
      JSON.stringify(config.orderBy || []),
      config.limit || 'no_limit',
      config.offset || 0
    ]

    return `query_${keyParts.join('_').replace(/[^a-zA-Z0-9_]/g, '_')}`
  }

  private shouldCache(config: QueryConfig): boolean {
    if (config.enableCache === false) return false
    if (config.enableCache === true) return true

    // Auto-determine caching based on table characteristics
    return this.optimizationPatterns.cacheFriendlyTables.includes(config.table)
  }

  private getCacheTTL(config: QueryConfig): number {
    if (config.cacheTTL) return config.cacheTTL

    // Shorter TTL for volatile tables
    if (this.optimizationPatterns.volatileTables.includes(config.table)) {
      return 1 * 60 * 1000 // 1 minute
    }

    return this.defaultCacheTTL
  }

  private optimizeSelectClause(config: QueryConfig): string {
    if (config.select) return config.select

    // Use optimized select patterns based on query characteristics
    if (config.limit && config.limit <= 20) {
      return this.optimizationPatterns.selectPatterns.list
    }

    if (Object.keys(config.filters || {}).length === 1 && config.filters?.id) {
      return this.optimizationPatterns.selectPatterns.detail
    }

    return this.optimizationPatterns.selectPatterns.summary
  }

  private addAnalytics(analytics: Omit<QueryAnalytics, 'timestamp'>): void {
    const entry: QueryAnalytics = {
      ...analytics,
      timestamp: new Date()
    }

    this.queryAnalytics.push(entry)

    // Maintain maximum analytics entries
    if (this.queryAnalytics.length > this.maxAnalytics) {
      this.queryAnalytics = this.queryAnalytics.slice(-this.maxAnalytics)
    }

    this.saveAnalytics()

    // Log slow queries
    if (analytics.executionTime > 3000) { // 3 seconds
      logWarn('Slow query detected', {
        component: 'QueryOptimizer',
        action: 'addAnalytics',
        metadata: {
          queryId: analytics.queryId,
          table: analytics.table,
          executionTime: analytics.executionTime,
          resultCount: analytics.resultCount
        }
      })
    }
  }

  public async executeQuery<T = unknown>(config: QueryConfig): Promise<QueryResult<T>> {
    const queryId = crypto.randomUUID()
    const startTime = performance.now()
    
    // Check cache first
    const shouldUseCache = this.shouldCache(config)
    const cacheKey = shouldUseCache ? this.generateCacheKey(config) : null
    
    if (cacheKey) {
      const cached = performanceMonitor.getCache<T[]>(cacheKey)
      if (cached) {
        const executionTime = performance.now() - startTime
        
        this.addAnalytics({
          queryId,
          table: config.table,
          executionTime,
          resultCount: cached.length,
          fromCache: true,
          filters: config.filters || {},
          cacheHit: true
        })

        return {
          data: cached,
          error: null,
          fromCache: true,
          executionTime,
          queryId
        }
      }
    }

    try {
      // Build optimized query
      let query = supabase.from(config.table)

      // Optimize select clause
      const selectClause = this.optimizeSelectClause(config)
      query = query.select(selectClause, { count: 'exact' })

      // Apply filters
      if (config.filters) {
        Object.entries(config.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              query = query.in(key, value)
            } else if (typeof value === 'string' && value.includes('%')) {
              query = query.ilike(key, value)
            } else {
              query = query.eq(key, value)
            }
          }
        })
      }

      // Apply ordering
      if (config.orderBy) {
        config.orderBy.forEach(order => {
          query = query.order(order.column, { ascending: order.ascending !== false })
        })
      }

      // Apply pagination
      if (config.limit) {
        query = query.limit(config.limit)
      }
      if (config.offset) {
        query = query.range(config.offset, config.offset + (config.limit || 10) - 1)
      }

      // Execute query
      const { data, error, count } = await query

      const executionTime = performance.now() - startTime

      // Cache successful results
      if (!error && data && cacheKey) {
        const cacheTTL = this.getCacheTTL(config)
        performanceMonitor.setCache(cacheKey, data, cacheTTL)
      }

      // Record analytics
      this.addAnalytics({
        queryId,
        table: config.table,
        executionTime,
        resultCount: data?.length || 0,
        fromCache: false,
        filters: config.filters || {},
        cacheHit: false
      })

      // Record performance metric
      performanceMonitor.addMetric({
        name: `Query: ${config.table}`,
        category: 'database',
        duration: executionTime,
        metadata: {
          table: config.table,
          resultCount: data?.length || 0,
          cached: false,
          queryId
        }
      })

      return {
        data: data as T[],
        error,
        count: count || undefined,
        fromCache: false,
        executionTime,
        queryId
      }
    } catch (error) {
      const executionTime = performance.now() - startTime

      this.addAnalytics({
        queryId,
        table: config.table,
        executionTime,
        resultCount: 0,
        fromCache: false,
        filters: config.filters || {},
        cacheHit: false
      })

      logError('Query execution failed', {
        component: 'QueryOptimizer',
        action: 'executeQuery',
        metadata: {
          queryId,
          table: config.table,
          error: (error as Error).message,
          config
        }
      })

      return {
        data: null,
        error,
        fromCache: false,
        executionTime,
        queryId
      }
    }
  }

  // Convenience methods for common query patterns
  public async findById<T = unknown>(table: string, id: string, select?: string): Promise<QueryResult<T>> {
    return this.executeQuery<T>({
      table,
      select: select || '*',
      filters: { id },
      limit: 1,
      enableCache: true,
      cacheTTL: 10 * 60 * 1000 // 10 minutes for single records
    })
  }

  public async findMany<T = unknown>(
    table: string,
    filters?: Record<string, unknown>,
    options?: {
      select?: string
      orderBy?: { column: string; ascending?: boolean }[]
      limit?: number
      offset?: number
      enableCache?: boolean
    }
  ): Promise<QueryResult<T>> {
    return this.executeQuery<T>({
      table,
      filters,
      ...options
    })
  }

  public async findWithPagination<T = unknown>(
    table: string,
    page: number,
    pageSize: number,
    filters?: Record<string, unknown>,
    orderBy?: { column: string; ascending?: boolean }[]
  ): Promise<QueryResult<T>> {
    return this.executeQuery<T>({
      table,
      filters,
      orderBy,
      limit: pageSize,
      offset: (page - 1) * pageSize,
      enableCache: false // Pagination results are less cache-friendly
    })
  }

  // Analytics and optimization insights
  public getQueryAnalytics(table?: string, limit?: number): QueryAnalytics[] {
    let analytics = table 
      ? this.queryAnalytics.filter(a => a.table === table)
      : this.queryAnalytics

    if (limit) {
      analytics = analytics.slice(-limit)
    }

    return analytics.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  public getSlowQueries(threshold: number = 3000): QueryAnalytics[] {
    return this.queryAnalytics
      .filter(a => a.executionTime > threshold)
      .sort((a, b) => b.executionTime - a.executionTime)
      .slice(0, 20) // Top 20 slowest queries
  }

  public getTableStats(): Record<string, {
    totalQueries: number
    averageExecutionTime: number
    cacheHitRate: number
    slowQueries: number
  }> {
    const stats: Record<string, {
      totalQueries: number
      totalExecutionTime: number
      cacheHits: number
      slowQueries: number
    }> = {}

    this.queryAnalytics.forEach(analytics => {
      if (!stats[analytics.table]) {
        stats[analytics.table] = {
          totalQueries: 0,
          totalExecutionTime: 0,
          cacheHits: 0,
          slowQueries: 0
        }
      }

      const tableStats = stats[analytics.table]
      tableStats.totalQueries++
      tableStats.totalExecutionTime += analytics.executionTime
      
      if (analytics.cacheHit) {
        tableStats.cacheHits++
      }
      
      if (analytics.executionTime > 3000) {
        tableStats.slowQueries++
      }
    })

    // Calculate derived metrics
    Object.keys(stats).forEach(table => {
      const tableStats = stats[table]
      tableStats.averageExecutionTime = tableStats.totalExecutionTime / tableStats.totalQueries
      tableStats.cacheHitRate = (tableStats.cacheHits / tableStats.totalQueries) * 100
      delete tableStats.totalExecutionTime
      delete tableStats.cacheHits
    })

    return stats
  }

  public invalidateTableCache(table: string): void {
    performanceMonitor.invalidateCache(`query_${table}_`)
    
    logger.info('Table cache invalidated', {
      component: 'QueryOptimizer',
      action: 'invalidateTableCache',
      metadata: { table }
    })
  }

  public clearAnalytics(): void {
    this.queryAnalytics = []
    this.saveAnalytics()
    
    logger.info('Query analytics cleared', {
      component: 'QueryOptimizer',
      action: 'clearAnalytics'
    })
  }

  // Optimization recommendations
  public getOptimizationRecommendations(): {
    slowTables: string[]
    lowCacheHitTables: string[]
    recommendations: string[]
  } {
    const tableStats = this.getTableStats()
    const slowTables: string[] = []
    const lowCacheHitTables: string[] = []
    const recommendations: string[] = []

    Object.entries(tableStats).forEach(([table, stats]) => {
      if (stats.averageExecutionTime > 2000) {
        slowTables.push(table)
        recommendations.push(`Consider adding indexes or optimizing queries for table: ${table}`)
      }

      if (stats.cacheHitRate < 30 && stats.totalQueries > 10) {
        lowCacheHitTables.push(table)
        recommendations.push(`Consider enabling caching for table: ${table}`)
      }

      if (stats.slowQueries > stats.totalQueries * 0.1) {
        recommendations.push(`High percentage of slow queries for table: ${table}. Review query patterns.`)
      }
    })

    return {
      slowTables,
      lowCacheHitTables,
      recommendations
    }
  }
}

// Export singleton instance
export const queryOptimizer = QueryOptimizer.getInstance()

// Utility function for React Query integration
export const createOptimizedQuery = <T = unknown>(
  table: string,
  filters?: Record<string, unknown>,
  options?: {
    select?: string
    orderBy?: { column: string; ascending?: boolean }[]
    limit?: number
    enableCache?: boolean
  }
) => ({
  queryKey: [table, filters, options],
  queryFn: () => queryOptimizer.findMany<T>(table, filters, options),
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
})
