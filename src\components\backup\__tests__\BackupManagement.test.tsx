import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BackupManagement } from '../BackupManagement'
import { useBackupManagement } from '@/hooks/useBackupManagement'
import { useRestoration } from '@/hooks/useRestoration'

// Mock hooks
jest.mock('@/hooks/useBackupManagement')
jest.mock('@/hooks/useRestoration')
jest.mock('@/hooks/useAuthHook', () => ({
  useAuth: () => ({
    profile: {
      id: 'test-user-id',
      org_id: 'test-org-id',
      role: 'admin'
    }
  })
}))

const mockUseBackupManagement = useBackupManagement as jest.MockedFunction<typeof useBackupManagement>
const mockUseRestoration = useRestoration as jest.MockedFunction<typeof useRestoration>

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('BackupManagement', () => {
  const mockBackupData = [
    {
      id: 'backup-1',
      backup_type: 'full',
      status: 'completed',
      size_bytes: 1024000,
      table_count: 5,
      record_count: 1000,
      created_at: '2024-01-01T00:00:00Z',
      completed_at: '2024-01-01T00:05:00Z'
    },
    {
      id: 'backup-2',
      backup_type: 'incremental',
      status: 'failed',
      size_bytes: 0,
      table_count: 0,
      record_count: 0,
      error_message: 'Network error',
      created_at: '2024-01-02T00:00:00Z'
    }
  ]

  const mockRestoreJobs = [
    {
      id: 'restore-1',
      backup_id: 'backup-1',
      restore_type: 'full',
      restore_mode: 'replace',
      status: 'completed',
      progress_percentage: 100,
      requested_at: '2024-01-03T00:00:00Z',
      completed_at: '2024-01-03T00:10:00Z'
    }
  ]

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock useBackupManagement hook
    mockUseBackupManagement.mockReturnValue({
      backups: mockBackupData,
      settings: null,
      statistics: null,
      isLoading: false,
      createBackup: jest.fn(),
      verifyBackup: jest.fn(),
      deleteBackup: jest.fn(),
      updateSettings: jest.fn(),
      isCreating: false,
      isUpdatingSettings: false,
      isDeleting: false,
      isVerifying: false,
      error: null,
      refetch: jest.fn()
    })

    // Mock useRestoration hook
    mockUseRestoration.mockReturnValue({
      restoreJobs: mockRestoreJobs,
      statistics: null,
      isLoading: false,
      error: null,
      createRestoreJob: jest.fn(),
      cancelRestoreJob: jest.fn(),
      approveRestoreJob: jest.fn(),
      deleteRestoreJob: jest.fn(),
      isCreating: false,
      isCancelling: false,
      isApproving: false,
      isDeleting: false,
      useRestoreLogs: jest.fn().mockReturnValue({ data: [] })
    })
  })

  it('should render backup management interface', () => {
    render(<BackupManagement />, { wrapper: createWrapper() })

    expect(screen.getByText('Data Backup & Recovery')).toBeInTheDocument()
    expect(screen.getByText('Create Full Backup')).toBeInTheDocument()
    expect(screen.getByText('Create Incremental Backup')).toBeInTheDocument()
    expect(screen.getByText('Backup History')).toBeInTheDocument()
  })

  it('should display backup history', () => {
    render(<BackupManagement />, { wrapper: createWrapper() })

    expect(screen.getByText('Full Backup')).toBeInTheDocument()
    expect(screen.getByText('Incremental Backup')).toBeInTheDocument()
    expect(screen.getByText('5 tables, 1,000 records')).toBeInTheDocument()
    expect(screen.getByText('Network error')).toBeInTheDocument()
  })

  it('should handle backup creation', async () => {
    const mockCreateBackup = jest.fn()
    mockUseBackupManagement.mockReturnValue({
      ...mockUseBackupManagement(),
      createBackup: mockCreateBackup
    })

    render(<BackupManagement />, { wrapper: createWrapper() })

    const createButton = screen.getByText('Create Full Backup')
    fireEvent.click(createButton)

    await waitFor(() => {
      expect(mockCreateBackup).toHaveBeenCalledWith('full')
    })
  })

  it('should handle backup verification', async () => {
    const mockVerifyBackup = jest.fn()
    mockUseBackupManagement.mockReturnValue({
      ...mockUseBackupManagement(),
      verifyBackup: mockVerifyBackup
    })

    render(<BackupManagement />, { wrapper: createWrapper() })

    const verifyButton = screen.getByText('Verify')
    fireEvent.click(verifyButton)

    await waitFor(() => {
      expect(mockVerifyBackup).toHaveBeenCalledWith('backup-1')
    })
  })

  it('should handle backup deletion', async () => {
    const mockDeleteBackup = jest.fn()
    mockUseBackupManagement.mockReturnValue({
      ...mockUseBackupManagement(),
      deleteBackup: mockDeleteBackup
    })

    render(<BackupManagement />, { wrapper: createWrapper() })

    const deleteButtons = screen.getAllByText('Delete')
    fireEvent.click(deleteButtons[0])

    await waitFor(() => {
      expect(mockDeleteBackup).toHaveBeenCalledWith('backup-1')
    })
  })

  it('should open restore dialog when restore button is clicked', async () => {
    render(<BackupManagement />, { wrapper: createWrapper() })

    const restoreButton = screen.getByText('Restore')
    fireEvent.click(restoreButton)

    await waitFor(() => {
      expect(screen.getByText('Restore Backup')).toBeInTheDocument()
    })
  })

  it('should display loading state', () => {
    mockUseBackupManagement.mockReturnValue({
      ...mockUseBackupManagement(),
      isLoading: true
    })

    render(<BackupManagement />, { wrapper: createWrapper() })

    expect(screen.getByText('Loading backup information...')).toBeInTheDocument()
  })

  it('should display error state', () => {
    mockUseBackupManagement.mockReturnValue({
      ...mockUseBackupManagement(),
      error: 'Failed to load backups'
    })

    render(<BackupManagement />, { wrapper: createWrapper() })

    expect(screen.getByText('Failed to load backups')).toBeInTheDocument()
  })

  it('should display empty state when no backups exist', () => {
    mockUseBackupManagement.mockReturnValue({
      ...mockUseBackupManagement(),
      backups: []
    })

    render(<BackupManagement />, { wrapper: createWrapper() })

    expect(screen.getByText('No backups found')).toBeInTheDocument()
    expect(screen.getByText('Create your first backup to ensure your data is protected')).toBeInTheDocument()
  })

  it('should display restoration history', () => {
    render(<BackupManagement />, { wrapper: createWrapper() })

    expect(screen.getByText('Restoration History')).toBeInTheDocument()
    expect(screen.getByText('Full Restoration')).toBeInTheDocument()
    expect(screen.getByText('Mode: replace')).toBeInTheDocument()
  })

  it('should handle backup creation progress', async () => {
    mockUseBackupManagement.mockReturnValue({
      ...mockUseBackupManagement(),
      isCreating: true
    })

    render(<BackupManagement />, { wrapper: createWrapper() })

    // The component should show progress when creating
    expect(screen.getByText('Create Full Backup')).toBeDisabled()
    expect(screen.getByText('Create Incremental Backup')).toBeDisabled()
  })

  it('should show backup information section', () => {
    render(<BackupManagement />, { wrapper: createWrapper() })

    expect(screen.getByText('Backup Information')).toBeInTheDocument()
    expect(screen.getByText("What's Included in Backups")).toBeInTheDocument()
    expect(screen.getByText('Backup Security')).toBeInTheDocument()
    expect(screen.getByText('Organization settings and configuration')).toBeInTheDocument()
    expect(screen.getByText('Data encrypted in transit and at rest')).toBeInTheDocument()
  })
})
