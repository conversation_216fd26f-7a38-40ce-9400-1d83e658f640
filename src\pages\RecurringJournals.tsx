
import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { Plus, Play, Pause, Eye, Calendar } from 'lucide-react'
import { RecurringJournalForm } from '@/components/recurring/RecurringJournalForm'
import { RecurringJournalView } from '@/components/recurring/RecurringJournalView'
import { GenerateEntriesDialog } from '@/components/recurring/GenerateEntriesDialog'
import { LoadingSpinner } from '@/components/ui/loading'

interface RecurringJournalWithLines {
  id: string
  name: string
  frequency: string
  start_date: string
  end_date: string | null
  next_date: string
  is_active: boolean
  created_at: string
  recurring_lines?: {
    id: string
    account_id: string
    debit: number
    credit: number
    description: string | null
    accounts?: {
      code: string
      name: string
    }
  }[]
}

export const RecurringJournals = () => {
  const { profile } = useAuth()
  const [recurringJournals, setRecurringJournals] = useState<RecurringJournalWithLines[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [isViewOpen, setIsViewOpen] = useState(false)
  const [isGenerateOpen, setIsGenerateOpen] = useState(false)
  const [selectedJournal, setSelectedJournal] = useState<RecurringJournalWithLines | null>(null)

  const fetchRecurringJournals = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('recurring_journals')
        .select(`
          *,
          recurring_lines (
            id,
            account_id,
            debit,
            credit,
            description,
            accounts (
              code,
              name
            )
          )
        `)
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setRecurringJournals(data || [])
    } catch (error) {
      console.error('Error fetching recurring journals:', error)
      toast.error('Failed to load recurring journals')
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id])

  useEffect(() => {
    if (profile?.org_id) {
      fetchRecurringJournals()
    }
  }, [profile?.org_id, fetchRecurringJournals])

  const toggleStatus = async (journalId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('recurring_journals')
        .update({ is_active: !currentStatus })
        .eq('id', journalId)
        .eq('org_id', profile?.org_id)

      if (error) throw error

      toast.success(`Recurring journal ${!currentStatus ? 'activated' : 'deactivated'}`)
      fetchRecurringJournals()
    } catch (error) {
      console.error('Error updating status:', error)
      toast.error('Failed to update status')
    }
  }

  const viewJournal = (journal: RecurringJournalWithLines) => {
    setSelectedJournal(journal)
    setIsViewOpen(true)
  }

  const openGenerateDialog = (journal: RecurringJournalWithLines) => {
    setSelectedJournal(journal)
    setIsGenerateOpen(true)
  }

  const formatFrequency = (frequency: string) => {
    switch (frequency.toLowerCase()) {
      case 'daily': return 'Daily'
      case 'weekly': return 'Weekly'
      case 'monthly': return 'Monthly'
      case 'quarterly': return 'Quarterly'
      case 'yearly': return 'Yearly'
      default: return frequency
    }
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Recurring Journals</h1>
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Recurring Journal
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Create Recurring Journal</DialogTitle>
            </DialogHeader>
            <RecurringJournalForm 
              onClose={() => setIsFormOpen(false)}
              onSuccess={fetchRecurringJournals}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recurring Journal Entries</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="py-4">
              <LoadingSpinner text="Loading recurring journals..." showText />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Frequency</TableHead>
                  <TableHead>Next Date</TableHead>
                  <TableHead>End Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recurringJournals.map((journal) => (
                  <TableRow key={journal.id}>
                    <TableCell className="font-medium">{journal.name}</TableCell>
                    <TableCell>{formatFrequency(journal.frequency)}</TableCell>
                    <TableCell>{new Date(journal.next_date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      {journal.end_date ? new Date(journal.end_date).toLocaleDateString() : 'No end date'}
                    </TableCell>
                    <TableCell>
                      <Badge variant={journal.is_active ? 'default' : 'secondary'}>
                        {journal.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => viewJournal(journal)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openGenerateDialog(journal)}
                          disabled={!journal.is_active}
                        >
                          <Calendar className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => toggleStatus(journal.id, journal.is_active)}
                        >
                          {journal.is_active ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Dialog */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Recurring Journal Details</DialogTitle>
          </DialogHeader>
          {selectedJournal && (
            <RecurringJournalView 
              journal={selectedJournal}
              onClose={() => setIsViewOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Generate Entries Dialog */}
      <Dialog open={isGenerateOpen} onOpenChange={setIsGenerateOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Generate Journal Entries</DialogTitle>
          </DialogHeader>
          {selectedJournal && (
            <GenerateEntriesDialog 
              journal={selectedJournal}
              onClose={() => setIsGenerateOpen(false)}
              onSuccess={fetchRecurringJournals}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
