-- =====================================================
-- JOURNAL ENTRY AUTOMATION - SEGMENT 3: PAYMENT AUTOMATION
-- =====================================================
-- This migration implements automated journal entries for payments
-- Date: 2025-06-29
-- Purpose: Create payment application and reconciliation journal entry automation
-- Dependencies: 20250629_01_journal_automation_foundation.sql, 20250629_02_invoice_automation.sql

-- =====================================================
-- STEP 1: PAYMENT APPLICATION JOURNAL ENTRY AUTOMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '💰 Creating payment application journal entry automation...';
END $$;

-- Function to create journal entry for payment application
CREATE OR REPLACE FUNCTION create_payment_application_journal_entry()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_journal_id UUID;
    cash_account_id UUID;
    ar_account_id UUID;
    ap_account_id UUID;
    payment_record RECORD;
    document_record RECORD;
    validation_result RECORD;
    required_mappings TEXT[];
    automation_enabled BOOLEAN := true;
BEGIN
    -- Check if automation is enabled
    SELECT setting_value->>'enabled' = 'true'
    INTO automation_enabled
    FROM automation_settings
    WHERE org_id = (SELECT org_id FROM payments WHERE id = NEW.payment_id)
    AND setting_key = 'payment_journal_automation';

    IF automation_enabled IS NULL THEN
        automation_enabled := true;
    END IF;

    IF NOT automation_enabled THEN
        RETURN NEW;
    END IF;

    -- Get payment details
    SELECT p.*, ba.name as bank_account_name
    INTO payment_record
    FROM payments p
    LEFT JOIN bank_accounts ba ON p.bank_account_id = ba.id
    WHERE p.id = NEW.payment_id;

    -- Check if journal entry already exists for this payment application
    IF EXISTS (
        SELECT 1 FROM journal_entries
        WHERE source_id = NEW.id AND source_type = 'payment_application'
    ) THEN
        RETURN NEW;
    END IF;

    BEGIN
        -- Determine required mappings based on payment type
        IF payment_record.payee_type = 'customer' THEN
            -- Customer payment: need cash and accounts receivable
            required_mappings := ARRAY['cash', 'accounts_receivable'];
        ELSE
            -- Vendor payment: need accounts payable and cash
            required_mappings := ARRAY['accounts_payable', 'cash'];
        END IF;

        -- Validate required account mappings
        FOR validation_result IN
            SELECT * FROM validate_account_mappings(payment_record.org_id, required_mappings)
        LOOP
            IF NOT validation_result.is_valid THEN
                PERFORM handle_journal_entry_error(
                    payment_record.org_id,
                    'payment_application',
                    NEW.id,
                    'missing_account_mapping',
                    validation_result.error_message,
                    jsonb_build_object(
                        'mapping_type', validation_result.mapping_type,
                        'payment_id', NEW.payment_id,
                        'payee_type', payment_record.payee_type
                    )
                );
                RETURN NEW;
            END IF;

            -- Store account IDs based on mapping type
            CASE validation_result.mapping_type
                WHEN 'cash' THEN cash_account_id := validation_result.account_id;
                WHEN 'accounts_receivable' THEN ar_account_id := validation_result.account_id;
                WHEN 'accounts_payable' THEN ap_account_id := validation_result.account_id;
            END CASE;
        END LOOP;

        -- Get document details for reference
        IF NEW.applied_to_type = 'invoice' THEN
            SELECT invoice_number as document_number, 'Invoice' as document_type
            INTO document_record
            FROM invoices WHERE id = NEW.applied_to_id;
        ELSIF NEW.applied_to_type = 'bill' THEN
            SELECT bill_number as document_number, 'Bill' as document_type
            INTO document_record
            FROM bills WHERE id = NEW.applied_to_id;
        END IF;

        -- Create journal entry
        INSERT INTO journal_entries (
            org_id, date, description, reference,
            source_id, source_type, created_by, is_posted
        )
        VALUES (
            payment_record.org_id,
            payment_record.payment_date,
            'Payment Application: ' || COALESCE(document_record.document_type, 'Document') || ' ' || COALESCE(document_record.document_number, ''),
            'PAY-' || payment_record.id::text,
            NEW.id,
            'payment_application',
            payment_record.created_by,
            false
        )
        RETURNING id INTO new_journal_id;

        -- Create transaction lines based on payment type
        IF payment_record.payee_type = 'customer' THEN
            -- Customer payment: DR Cash, CR Accounts Receivable

            -- Debit: Cash/Bank Account
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                payment_record.org_id, new_journal_id, cash_account_id,
                NEW.amount_applied, 0,
                'Cash received - ' || COALESCE(document_record.document_type, 'Payment') || ' ' || COALESCE(document_record.document_number, '')
            );

            -- Credit: Accounts Receivable
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                payment_record.org_id, new_journal_id, ar_account_id,
                0, NEW.amount_applied,
                'Payment received - ' || COALESCE(document_record.document_type, 'Payment') || ' ' || COALESCE(document_record.document_number, '')
            );

        ELSE
            -- Vendor payment: DR Accounts Payable, CR Cash

            -- Debit: Accounts Payable
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                payment_record.org_id, new_journal_id, ap_account_id,
                NEW.amount_applied, 0,
                'Payment made - ' || COALESCE(document_record.document_type, 'Payment') || ' ' || COALESCE(document_record.document_number, '')
            );

            -- Credit: Cash/Bank Account
            INSERT INTO transaction_lines (
                org_id, journal_entry_id, account_id,
                debit, credit, description
            )
            VALUES (
                payment_record.org_id, new_journal_id, cash_account_id,
                0, NEW.amount_applied,
                'Cash paid - ' || COALESCE(document_record.document_type, 'Payment') || ' ' || COALESCE(document_record.document_number, '')
            );
        END IF;

        -- Validate the journal entry is balanced
        DECLARE
            balance_check RECORD;
        BEGIN
            SELECT * INTO balance_check
            FROM validate_journal_entry_balance(new_journal_id);

            IF NOT balance_check.is_balanced THEN
                RAISE EXCEPTION 'Payment application journal entry is not balanced. Debits: %, Credits: %, Difference: %',
                    balance_check.total_debits, balance_check.total_credits, balance_check.difference;
            END IF;
        END;

    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        PERFORM handle_journal_entry_error(
            payment_record.org_id,
            'payment_application',
            NEW.id,
            'database_error',
            SQLERRM,
            jsonb_build_object(
                'payment_id', NEW.payment_id,
                'applied_to_type', NEW.applied_to_type,
                'applied_to_id', NEW.applied_to_id,
                'amount_applied', NEW.amount_applied,
                'payee_type', payment_record.payee_type
            )
        );
    END;

    RETURN NEW;
END;
$$;

-- Create the trigger for payment application journal entries
DROP TRIGGER IF EXISTS payment_application_journal_trigger ON payment_applications;
CREATE TRIGGER payment_application_journal_trigger
    AFTER INSERT ON payment_applications
    FOR EACH ROW
    EXECUTE FUNCTION create_payment_application_journal_entry();

-- =====================================================
-- STEP 2: DATA INTEGRITY AND VALIDATION ENHANCEMENTS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Creating data integrity and validation functions...';
END $$;

-- Function to validate payment application amounts
CREATE OR REPLACE FUNCTION validate_payment_application_amount()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    outstanding_amount DECIMAL(15,2);
    total_applied DECIMAL(15,2);
    document_total DECIMAL(15,2);
BEGIN
    -- Get the total amount of the document being paid
    IF NEW.applied_to_type = 'invoice' THEN
        SELECT total_amount INTO document_total
        FROM invoices WHERE id = NEW.applied_to_id;
    ELSIF NEW.applied_to_type = 'bill' THEN
        SELECT total_amount INTO document_total
        FROM bills WHERE id = NEW.applied_to_id;
    ELSE
        RAISE EXCEPTION 'Invalid applied_to_type: %', NEW.applied_to_type;
    END IF;

    -- Calculate total amount already applied to this document
    SELECT COALESCE(SUM(amount_applied), 0) INTO total_applied
    FROM payment_applications
    WHERE applied_to_type = NEW.applied_to_type
    AND applied_to_id = NEW.applied_to_id
    AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::UUID);

    -- Calculate outstanding amount
    outstanding_amount := document_total - total_applied;

    -- Validate that payment application doesn't exceed outstanding amount
    IF NEW.amount_applied > outstanding_amount THEN
        RAISE EXCEPTION 'Payment application amount (%) exceeds outstanding balance (%) for % %',
            NEW.amount_applied, outstanding_amount, NEW.applied_to_type, NEW.applied_to_id;
    END IF;

    RETURN NEW;
END;
$$;

-- Create trigger for payment application validation
DROP TRIGGER IF EXISTS validate_payment_application_trigger ON payment_applications;
CREATE TRIGGER validate_payment_application_trigger
    BEFORE INSERT OR UPDATE ON payment_applications
    FOR EACH ROW
    EXECUTE FUNCTION validate_payment_application_amount();

-- Function to update account mapping
CREATE OR REPLACE FUNCTION update_account_mapping(
    org_id_param UUID,
    mapping_type_param TEXT,
    new_account_id_param UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    account_exists BOOLEAN := false;
BEGIN
    -- Verify the account exists and belongs to the organization
    SELECT EXISTS (
        SELECT 1 FROM accounts
        WHERE id = new_account_id_param AND org_id = org_id_param
    ) INTO account_exists;

    IF NOT account_exists THEN
        RAISE EXCEPTION 'Account does not exist or does not belong to organization';
    END IF;

    -- Update or insert the mapping
    INSERT INTO account_mappings (org_id, mapping_type, account_id, is_default)
    VALUES (org_id_param, mapping_type_param, new_account_id_param, true)
    ON CONFLICT (org_id, mapping_type, is_default)
    DO UPDATE SET
        account_id = new_account_id_param,
        updated_at = NOW();

    RETURN true;
END;
$$;

-- =====================================================
-- STEP 3: SETUP AND INITIALIZATION FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🚀 Creating setup and initialization functions...';
END $$;

-- Function to initialize automation settings for an organization
CREATE OR REPLACE FUNCTION initialize_automation_settings(org_id_param UUID)
RETURNS TABLE (
    setting_key TEXT,
    setting_value JSONB,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    default_settings RECORD;
    settings_to_create TEXT[] := ARRAY[
        'invoice_journal_automation',
        'payment_journal_automation',
        'reconciliation_automation'
    ];
    setting_item TEXT;
BEGIN
    FOREACH setting_item IN ARRAY settings_to_create
    LOOP
        -- Check if setting already exists
        IF EXISTS (
            SELECT 1 FROM automation_settings
            WHERE org_id = org_id_param AND setting_key = setting_item
        ) THEN
            -- Return existing setting
            SELECT as_table.setting_key, as_table.setting_value, 'existing'
            INTO default_settings
            FROM automation_settings as_table
            WHERE as_table.org_id = org_id_param AND as_table.setting_key = setting_item;

            RETURN QUERY SELECT
                default_settings.setting_key,
                default_settings.setting_value,
                default_settings.status;
        ELSE
            -- Create new setting
            INSERT INTO automation_settings (org_id, setting_key, setting_value, enabled)
            VALUES (
                org_id_param,
                setting_item,
                jsonb_build_object('enabled', true, 'auto_post', false),
                true
            );

            RETURN QUERY SELECT
                setting_item,
                jsonb_build_object('enabled', true, 'auto_post', false),
                'created'::TEXT;
        END IF;
    END LOOP;
END;
$$;

-- Add RLS policies for new tables
ALTER TABLE account_mappings ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_entry_errors ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_settings ENABLE ROW LEVEL SECURITY;

-- Account mappings policies
CREATE POLICY "Users can view account mappings in their organization" ON account_mappings
    FOR SELECT USING (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
    );

CREATE POLICY "Users can manage account mappings in their organization" ON account_mappings
    FOR ALL USING (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
    );

-- Journal entry errors policies
CREATE POLICY "Users can view journal entry errors in their organization" ON journal_entry_errors
    FOR SELECT USING (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
    );

CREATE POLICY "Users can manage journal entry errors in their organization" ON journal_entry_errors
    FOR ALL USING (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
    );

-- Automation settings policies
CREATE POLICY "Users can view automation settings in their organization" ON automation_settings
    FOR SELECT USING (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
    );

CREATE POLICY "Users can manage automation settings in their organization" ON automation_settings
    FOR ALL USING (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid())
    );

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ PAYMENT AUTOMATION & DATA INTEGRITY COMPLETE!';
    RAISE NOTICE '===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📋 CREATED:';
    RAISE NOTICE '  • create_payment_application_journal_entry() function';
    RAISE NOTICE '  • payment_application_journal_trigger trigger';
    RAISE NOTICE '  • validate_payment_application_amount() function';
    RAISE NOTICE '  • validate_payment_application_trigger trigger';
    RAISE NOTICE '  • update_account_mapping() function';
    RAISE NOTICE '  • initialize_automation_settings() function';
    RAISE NOTICE '  • RLS policies for all automation tables';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 JOURNAL ENTRY AUTOMATION SYSTEM COMPLETE!';
    RAISE NOTICE '';
    RAISE NOTICE '📝 TO GET STARTED:';
    RAISE NOTICE '  1. Run: SELECT * FROM setup_default_account_mappings(''your-org-id'');';
    RAISE NOTICE '  2. Run: SELECT * FROM initialize_automation_settings(''your-org-id'');';
    RAISE NOTICE '  3. Create invoices with status ''sent'' to test automation';
    RAISE NOTICE '  4. Apply payments to invoices to test payment automation';
    RAISE NOTICE '';
END $$;