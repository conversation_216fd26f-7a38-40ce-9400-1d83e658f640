# Component Documentation

Complete guide to all notification system components, their props, and usage examples.

## Table of Contents

1. [Core Components](#core-components)
2. [Layout Components](#layout-components)
3. [Settings Components](#settings-components)
4. [Analytics Components](#analytics-components)
5. [Integration Components](#integration-components)
6. [Utility Components](#utility-components)

## Core Components

### NotificationDropdown

A dropdown component that displays recent notifications in the header/navigation area.

**Props:**
```typescript
interface NotificationDropdownProps {
  maxItems?: number          // Maximum notifications to show (default: 5)
  showMarkAllRead?: boolean  // Show "Mark all as read" button (default: true)
  showViewAll?: boolean      // Show "View all" link (default: true)
  className?: string         // Additional CSS classes
  onNotificationClick?: (notification: NotificationWithMeta) => void
}
```

**Usage:**
```tsx
import { NotificationDropdown } from '@/components/notifications'

function Header() {
  return (
    <div className="header">
      <NotificationDropdown 
        maxItems={10}
        onNotificationClick={(notification) => {
          console.log('Clicked:', notification.title)
        }}
      />
    </div>
  )
}
```

**Features:**
- Real-time notification count badge
- Unread notification highlighting
- Click to mark as read
- Keyboard navigation support
- Loading and error states

### NotificationList

A comprehensive list component for displaying notifications with filtering and pagination.

**Props:**
```typescript
interface NotificationListProps {
  filters?: NotificationFilters
  showFilters?: boolean      // Show filter controls (default: true)
  showSearch?: boolean       // Show search input (default: true)
  showBulkActions?: boolean  // Show bulk action controls (default: true)
  pageSize?: number          // Items per page (default: 20)
  virtualized?: boolean      // Use virtual scrolling (default: false)
  compact?: boolean          // Compact display mode (default: false)
  className?: string
  onNotificationClick?: (notification: NotificationWithMeta) => void
  onSelectionChange?: (selectedIds: string[]) => void
}
```

**Usage:**
```tsx
import { NotificationList } from '@/components/notifications'

function NotificationsPage() {
  const [selectedIds, setSelectedIds] = useState<string[]>([])

  return (
    <NotificationList
      showFilters={true}
      showBulkActions={true}
      pageSize={50}
      virtualized={true}
      onSelectionChange={setSelectedIds}
      onNotificationClick={(notification) => {
        // Navigate to related entity
        router.push(`/${notification.entity_type}s/${notification.entity_id}`)
      }}
    />
  )
}
```

### NotificationItem

Individual notification display component with actions and metadata.

**Props:**
```typescript
interface NotificationItemProps {
  notification: NotificationWithMeta
  compact?: boolean          // Compact display mode
  showActions?: boolean      // Show action buttons (default: true)
  showMetadata?: boolean     // Show metadata (time, category) (default: true)
  selectable?: boolean       // Show selection checkbox (default: false)
  selected?: boolean         // Selection state
  className?: string
  onClick?: (notification: NotificationWithMeta) => void
  onSelectionChange?: (selected: boolean) => void
}
```

**Usage:**
```tsx
import { NotificationItem } from '@/components/notifications'

function CustomNotificationList({ notifications }) {
  return (
    <div>
      {notifications.map(notification => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          compact={false}
          selectable={true}
          onClick={(notification) => handleClick(notification)}
          onSelectionChange={(selected) => handleSelection(notification.id, selected)}
        />
      ))}
    </div>
  )
}
```

### NotificationCenter

Full-featured notification management interface with tabs, filters, and bulk operations.

**Props:**
```typescript
interface NotificationCenterProps {
  defaultTab?: 'all' | 'unread' | 'archived'
  showTabs?: boolean         // Show tab navigation (default: true)
  showFilters?: boolean      // Show filter sidebar (default: true)
  showSearch?: boolean       // Show search functionality (default: true)
  showAnalytics?: boolean    // Show analytics summary (default: false)
  className?: string
}
```

**Usage:**
```tsx
import { NotificationCenter } from '@/components/notifications'

function NotificationCenterPage() {
  return (
    <div className="page-container">
      <h1>Notification Center</h1>
      <NotificationCenter
        defaultTab="unread"
        showAnalytics={true}
        showFilters={true}
      />
    </div>
  )
}
```

## Layout Components

### VirtualizedNotificationList

High-performance virtualized list for handling large numbers of notifications.

**Props:**
```typescript
interface VirtualizedNotificationListProps {
  notifications: NotificationWithMeta[]
  height: number             // Container height in pixels
  itemHeight?: number        // Individual item height (default: 80)
  overscan?: number          // Number of items to render outside viewport (default: 5)
  onLoadMore?: () => void    // Callback for infinite scrolling
  hasNextPage?: boolean      // Whether more items are available
  isLoading?: boolean        // Loading state for pagination
  className?: string
}
```

**Usage:**
```tsx
import { VirtualizedNotificationList } from '@/components/notifications'

function LargeNotificationList() {
  const { data, hasNextPage, fetchNextPage, isLoading } = useInfiniteNotifications()

  return (
    <VirtualizedNotificationList
      notifications={data?.pages.flat() || []}
      height={600}
      itemHeight={100}
      onLoadMore={fetchNextPage}
      hasNextPage={hasNextPage}
      isLoading={isLoading}
    />
  )
}
```

### CompactNotificationList

Space-efficient notification list for sidebars or small containers.

**Props:**
```typescript
interface CompactNotificationListProps {
  maxItems?: number          // Maximum items to display
  showTimestamp?: boolean    // Show relative timestamps (default: true)
  showPriority?: boolean     // Show priority indicators (default: true)
  className?: string
}
```

### EmptyNotifications

Empty state component displayed when no notifications are available.

**Props:**
```typescript
interface EmptyNotificationsProps {
  title?: string             // Custom title
  description?: string       // Custom description
  showAction?: boolean       // Show action button (default: false)
  actionText?: string        // Action button text
  onAction?: () => void      // Action button callback
  className?: string
}
```

## Settings Components

### NotificationSettings

Comprehensive notification preferences management interface.

**Props:**
```typescript
interface NotificationSettingsProps {
  showGlobalToggle?: boolean    // Show master enable/disable (default: true)
  showEmailSettings?: boolean   // Show email preferences (default: true)
  showPushSettings?: boolean    // Show push notification settings (default: true)
  showCategoryGroups?: boolean  // Group by category (default: true)
  className?: string
}
```

**Usage:**
```tsx
import { NotificationSettings } from '@/components/notifications'

function UserSettingsPage() {
  return (
    <div className="settings-page">
      <h2>Notification Preferences</h2>
      <NotificationSettings
        showPushSettings={true}
        showCategoryGroups={true}
      />
    </div>
  )
}
```

### NotificationFilters

Filter controls for notification lists.

**Props:**
```typescript
interface NotificationFiltersProps {
  filters: NotificationFilters
  onFiltersChange: (filters: NotificationFilters) => void
  showDateRange?: boolean    // Show date range picker (default: true)
  showCategory?: boolean     // Show category filter (default: true)
  showPriority?: boolean     // Show priority filter (default: true)
  showStatus?: boolean       // Show read/unread filter (default: true)
  className?: string
}
```

## Analytics Components

### NotificationAnalytics

Comprehensive analytics dashboard with charts and metrics.

**Props:**
```typescript
interface NotificationAnalyticsProps {
  dateRange?: number         // Days to analyze (default: 30)
  showOverview?: boolean     // Show overview cards (default: true)
  showCharts?: boolean       // Show analytics charts (default: true)
  showExport?: boolean       // Show export functionality (default: true)
  className?: string
}
```

**Usage:**
```tsx
import { NotificationAnalytics } from '@/components/notifications'

function AnalyticsPage() {
  return (
    <div className="analytics-page">
      <h1>Notification Analytics</h1>
      <NotificationAnalytics
        dateRange={90}
        showExport={true}
      />
    </div>
  )
}
```

## Integration Components

### IntegrationSettings

External integration management interface.

**Props:**
```typescript
interface IntegrationSettingsProps {
  showTestButtons?: boolean  // Show test integration buttons (default: true)
  allowedTypes?: IntegrationType[]  // Restrict integration types
  className?: string
}
```

**Usage:**
```tsx
import { IntegrationSettings } from '@/components/notifications'

function IntegrationsPage() {
  return (
    <div className="integrations-page">
      <h1>External Integrations</h1>
      <IntegrationSettings
        allowedTypes={['slack', 'teams', 'webhook']}
        showTestButtons={true}
      />
    </div>
  )
}
```

## Utility Components

### NotificationActions

Action menu component for individual notifications.

**Props:**
```typescript
interface NotificationActionsProps {
  notification: NotificationWithMeta
  compact?: boolean          // Compact button layout
  showSnooze?: boolean       // Show snooze option (default: true)
  showForward?: boolean      // Show forward option (default: true)
  showArchive?: boolean      // Show archive option (default: true)
  showDelete?: boolean       // Show delete option (default: false)
  onActionComplete?: () => void
  className?: string
}
```

## Styling and Theming

All components support CSS customization through:

1. **CSS Classes**: Pass custom classes via `className` prop
2. **CSS Variables**: Override theme variables
3. **Tailwind Classes**: Use Tailwind utility classes
4. **Component Variants**: Built-in size and style variants

**CSS Variables:**
```css
:root {
  --notification-primary: #3b82f6;
  --notification-success: #10b981;
  --notification-warning: #f59e0b;
  --notification-error: #ef4444;
  --notification-border: #e5e7eb;
  --notification-background: #ffffff;
  --notification-text: #374151;
  --notification-text-muted: #6b7280;
}
```

**Example Custom Styling:**
```tsx
<NotificationDropdown 
  className="custom-dropdown"
  style={{
    '--notification-primary': '#8b5cf6',
    '--notification-border': '#d1d5db'
  }}
/>
```

## Accessibility

All components include:

- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Proper ARIA labels and roles
- **Focus Management**: Logical focus order
- **High Contrast**: Support for high contrast themes
- **Reduced Motion**: Respects `prefers-reduced-motion`

**ARIA Attributes:**
- `role="button"` for interactive elements
- `aria-label` for icon buttons
- `aria-expanded` for dropdown states
- `aria-live` for dynamic content updates
- `aria-describedby` for additional context

## Performance Considerations

- **Virtual Scrolling**: Use for lists with 100+ items
- **Lazy Loading**: Components load data on demand
- **Memoization**: Expensive calculations are memoized
- **Debounced Search**: Search inputs are debounced
- **Optimistic Updates**: UI updates before server confirmation

**Performance Tips:**
```tsx
// Use virtualization for large lists
<VirtualizedNotificationList notifications={largeList} height={400} />

// Debounce search input
const [searchTerm, setSearchTerm] = useState('')
const debouncedSearch = useDebounce(searchTerm, 300)

// Memoize expensive calculations
const filteredNotifications = useMemo(() => 
  notifications.filter(n => n.title.includes(searchTerm)),
  [notifications, searchTerm]
)
```
