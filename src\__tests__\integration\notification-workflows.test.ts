/**
 * Integration Tests for Notification Workflows
 * Tests end-to-end notification creation, delivery, and user interactions
 */

import { createNotification } from '@/lib/notificationHelpers'
import { NotificationAPI } from '@/lib/notificationApi'
import { ExternalIntegrationService } from '@/lib/externalIntegrations'
import { EnhancedEmailService } from '@/lib/enhancedEmailService'
import { PushNotificationService } from '@/lib/pushNotificationService'
import { supabase } from '@/integrations/supabase/client'
import type { NotificationPayload } from '@/types/notifications'

// Mock external services
jest.mock('@/integrations/supabase/client')
jest.mock('@/lib/externalIntegrations')
jest.mock('@/lib/enhancedEmailService')
jest.mock('@/lib/pushNotificationService')

const mockSupabase = supabase as jest.Mocked<typeof supabase>
const mockExternalIntegrationService = ExternalIntegrationService as jest.Mocked<typeof ExternalIntegrationService>

describe('Notification Workflow Integration Tests', () => {
  const mockOrgId = 'org-123'
  const mockUserId = 'user-456'
  
  const mockNotificationPayload: NotificationPayload = {
    type: 'payment_pending_approval',
    title: 'Payment Approval Required',
    message: 'Payment of $1,000 to Vendor ABC requires your approval',
    category: 'financial',
    priority: 'high',
    data: {
      amount: '$1,000',
      payee: 'Vendor ABC',
      payment_id: 'payment-789'
    },
    entity_type: 'payment',
    entity_id: 'payment-789'
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock successful database operations
    mockSupabase.from = jest.fn().mockReturnValue({
      insert: jest.fn().mockResolvedValue({
        data: {
          id: 'notification-123',
          ...mockNotificationPayload,
          user_id: mockUserId,
          org_id: mockOrgId,
          is_read: false,
          is_archived: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          read_at: null
        },
        error: null
      }),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis()
    })
  })

  describe('Notification Creation Workflow', () => {
    it('creates notification and triggers all delivery channels', async () => {
      // Mock external service responses
      mockExternalIntegrationService.sendToSlack = jest.fn().mockResolvedValue(true)
      mockExternalIntegrationService.sendToTeams = jest.fn().mockResolvedValue(true)
      mockExternalIntegrationService.sendToWebhook = jest.fn().mockResolvedValue(true)

      // Create notification
      const notification = await createNotification(
        mockNotificationPayload,
        mockUserId,
        mockOrgId
      )

      expect(notification).toBeDefined()
      expect(notification?.id).toBe('notification-123')
      expect(mockSupabase.from).toHaveBeenCalledWith('notifications')
    })

    it('handles notification creation with user targeting', async () => {
      const userTargetedPayload = {
        ...mockNotificationPayload,
        user_id: mockUserId
      }

      const notification = await createNotification(
        userTargetedPayload,
        mockUserId,
        mockOrgId
      )

      expect(notification).toBeDefined()
      expect(notification?.user_id).toBe(mockUserId)
    })

    it('handles organization-wide notifications', async () => {
      const orgWidePayload = {
        ...mockNotificationPayload,
        type: 'system_maintenance' as const,
        user_id: null // Organization-wide
      }

      const notification = await createNotification(
        orgWidePayload,
        null,
        mockOrgId
      )

      expect(notification).toBeDefined()
      expect(notification?.user_id).toBeNull()
      expect(notification?.org_id).toBe(mockOrgId)
    })
  })

  describe('Notification Delivery Workflow', () => {
    it('delivers notification via email when enabled', async () => {
      const mockEmailService = new EnhancedEmailService({}, 'https://test.com')
      mockEmailService.sendNotificationEmail = jest.fn().mockResolvedValue({
        success: true,
        deliveryId: 'delivery-123'
      })

      // Mock user preferences
      mockSupabase.from = jest.fn().mockReturnValue({
        select: jest.fn().mockResolvedValue({
          data: [{
            notification_type: 'payment_pending_approval',
            email_enabled: true,
            in_app_enabled: true,
            push_enabled: true
          }],
          error: null
        }),
        eq: jest.fn().mockReturnThis()
      })

      const result = await mockEmailService.sendNotificationEmail(
        'payment_pending_approval',
        '<EMAIL>',
        mockNotificationPayload.data,
        mockOrgId,
        'notification-123'
      )

      expect(result.success).toBe(true)
      expect(result.deliveryId).toBe('delivery-123')
    })

    it('delivers notification via push when subscribed', async () => {
      const mockPushService = new PushNotificationService('test-vapid-key')
      mockPushService.sendTestNotification = jest.fn().mockResolvedValue(true)

      const result = await mockPushService.sendTestNotification()
      expect(result).toBe(true)
    })

    it('delivers notification to external integrations', async () => {
      const mockIntegrations = [
        {
          id: 'integration-1',
          type: 'slack' as const,
          config: { webhook_url: 'https://hooks.slack.com/test' },
          notification_types: ['payment_pending_approval'],
          is_active: true
        }
      ]

      // Mock integration API
      jest.doMock('@/lib/externalIntegrations', () => ({
        IntegrationAPI: {
          getIntegrations: jest.fn().mockResolvedValue(mockIntegrations)
        },
        ExternalIntegrationService: {
          sendToSlack: jest.fn().mockResolvedValue(true)
        }
      }))

      const { sendToIntegrations } = await import('@/lib/externalIntegrations')
      
      await sendToIntegrations({
        id: 'notification-123',
        type: 'payment_pending_approval',
        title: 'Test Notification',
        message: 'Test message',
        category: 'financial',
        priority: 'high',
        data: {},
        is_read: false,
        is_archived: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: mockUserId,
        org_id: mockOrgId,
        entity_type: 'payment',
        entity_id: 'payment-789',
        read_at: null
      }, mockOrgId)

      // Verify integration was called
      expect(sendToIntegrations).toBeDefined()
    })
  })

  describe('Notification User Interaction Workflow', () => {
    it('marks notification as read and updates analytics', async () => {
      const mockUpdate = jest.fn().mockResolvedValue({
        data: {
          id: 'notification-123',
          is_read: true,
          read_at: new Date().toISOString()
        },
        error: null
      })

      mockSupabase.from = jest.fn().mockReturnValue({
        update: mockUpdate,
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis()
      })

      const result = await NotificationAPI.markAsRead('notification-123')
      
      expect(result.success).toBe(true)
      expect(mockUpdate).toHaveBeenCalledWith({
        is_read: true,
        read_at: expect.any(String)
      })
    })

    it('archives notification and maintains history', async () => {
      const mockUpdate = jest.fn().mockResolvedValue({
        data: {
          id: 'notification-123',
          is_archived: true
        },
        error: null
      })

      mockSupabase.from = jest.fn().mockReturnValue({
        update: mockUpdate,
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis()
      })

      const result = await NotificationAPI.archiveNotification('notification-123')
      
      expect(result.success).toBe(true)
      expect(mockUpdate).toHaveBeenCalledWith({
        is_archived: true
      })
    })

    it('handles bulk operations efficiently', async () => {
      const notificationIds = ['notification-1', 'notification-2', 'notification-3']
      
      const mockUpdate = jest.fn().mockResolvedValue({
        data: notificationIds.map(id => ({ id, is_read: true })),
        error: null
      })

      mockSupabase.from = jest.fn().mockReturnValue({
        update: mockUpdate,
        in: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis()
      })

      const result = await NotificationAPI.bulkMarkAsRead(notificationIds)
      
      expect(result.success).toBe(true)
      expect(mockUpdate).toHaveBeenCalledWith({
        is_read: true,
        read_at: expect.any(String)
      })
    })
  })

  describe('Notification Preferences Workflow', () => {
    it('respects user notification preferences', async () => {
      const mockPreferences = [
        {
          notification_type: 'payment_pending_approval',
          enabled: true,
          email_enabled: false, // Email disabled
          in_app_enabled: true,
          push_enabled: true
        }
      ]

      mockSupabase.from = jest.fn().mockReturnValue({
        select: jest.fn().mockResolvedValue({
          data: mockPreferences,
          error: null
        }),
        eq: jest.fn().mockReturnThis()
      })

      // Should not send email but should send in-app and push
      const notification = await createNotification(
        mockNotificationPayload,
        mockUserId,
        mockOrgId
      )

      expect(notification).toBeDefined()
      // Verify preferences were checked
      expect(mockSupabase.from).toHaveBeenCalledWith('notification_preferences')
    })

    it('updates preferences and applies immediately', async () => {
      const newPreferences = {
        notification_type: 'payment_pending_approval',
        enabled: true,
        email_enabled: true,
        in_app_enabled: true,
        push_enabled: false // Disable push
      }

      const mockUpsert = jest.fn().mockResolvedValue({
        data: newPreferences,
        error: null
      })

      mockSupabase.from = jest.fn().mockReturnValue({
        upsert: mockUpsert,
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis()
      })

      const result = await NotificationAPI.updatePreferences(mockUserId, newPreferences)
      
      expect(result.success).toBe(true)
      expect(mockUpsert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: mockUserId,
          ...newPreferences
        }),
        expect.any(Object)
      )
    })
  })

  describe('Error Handling and Recovery', () => {
    it('handles database errors gracefully', async () => {
      const mockError = new Error('Database connection failed')
      
      mockSupabase.from = jest.fn().mockReturnValue({
        insert: jest.fn().mockResolvedValue({
          data: null,
          error: mockError
        }),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis()
      })

      const notification = await createNotification(
        mockNotificationPayload,
        mockUserId,
        mockOrgId
      )

      expect(notification).toBeNull()
    })

    it('handles external service failures without blocking', async () => {
      // Mock external service failure
      mockExternalIntegrationService.sendToSlack = jest.fn().mockRejectedValue(
        new Error('Slack webhook failed')
      )

      // Notification creation should still succeed
      const notification = await createNotification(
        mockNotificationPayload,
        mockUserId,
        mockOrgId
      )

      expect(notification).toBeDefined()
      // External service failure should not prevent notification creation
    })

    it('implements retry logic for failed deliveries', async () => {
      let attemptCount = 0
      const mockEmailService = new EnhancedEmailService({}, 'https://test.com')
      
      mockEmailService.sendNotificationEmail = jest.fn().mockImplementation(async () => {
        attemptCount++
        if (attemptCount < 3) {
          throw new Error('Temporary failure')
        }
        return { success: true, deliveryId: 'delivery-123' }
      })

      // Should retry and eventually succeed
      const result = await mockEmailService.sendNotificationEmail(
        'payment_pending_approval',
        '<EMAIL>',
        mockNotificationPayload.data,
        mockOrgId,
        'notification-123'
      )

      expect(result.success).toBe(true)
      expect(attemptCount).toBe(3)
    })
  })

  describe('Performance and Scalability', () => {
    it('handles high volume notification creation', async () => {
      const notifications = Array.from({ length: 100 }, (_, i) => ({
        ...mockNotificationPayload,
        title: `Notification ${i + 1}`
      }))

      const startTime = Date.now()
      
      const results = await Promise.all(
        notifications.map(payload => 
          createNotification(payload, mockUserId, mockOrgId)
        )
      )

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(results).toHaveLength(100)
      expect(results.every(result => result !== null)).toBe(true)
      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
    })

    it('efficiently queries notifications with pagination', async () => {
      const mockNotifications = Array.from({ length: 50 }, (_, i) => ({
        id: `notification-${i + 1}`,
        ...mockNotificationPayload
      }))

      mockSupabase.from = jest.fn().mockReturnValue({
        select: jest.fn().mockResolvedValue({
          data: mockNotifications.slice(0, 20), // First page
          error: null,
          count: 50
        }),
        eq: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockReturnThis()
      })

      const result = await NotificationAPI.getNotifications(
        mockUserId,
        mockOrgId,
        {},
        1,
        20
      )

      expect(result.success).toBe(true)
      expect(result.data?.data).toHaveLength(20)
      expect(result.data?.total).toBe(50)
    })
  })
})
