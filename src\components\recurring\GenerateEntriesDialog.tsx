
import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'

interface GenerateEntriesDialogProps {
  journal: {
    id: string
    name: string
    frequency: string
    next_date: string
    recurring_lines?: {
      id: string
      account_id: string
      debit: number
      credit: number
      description: string | null
    }[]
  }
  onClose: () => void
  onSuccess: () => void
}

export const GenerateEntriesDialog = ({ journal, onClose, onSuccess }: GenerateEntriesDialogProps) => {
  const { profile } = useAuth()
  const [date, setDate] = useState(journal.next_date)
  const [description, setDescription] = useState(`${journal.name} - ${new Date().toLocaleDateString()}`)
  const [reference, setReference] = useState('')
  const [loading, setLoading] = useState(false)

  const calculateNextDate = (currentDate: string, frequency: string) => {
    const date = new Date(currentDate)
    switch (frequency.toLowerCase()) {
      case 'daily':
        date.setDate(date.getDate() + 1)
        break
      case 'weekly':
        date.setDate(date.getDate() + 7)
        break
      case 'monthly':
        date.setMonth(date.getMonth() + 1)
        break
      case 'quarterly':
        date.setMonth(date.getMonth() + 3)
        break
      case 'yearly':
        date.setFullYear(date.getFullYear() + 1)
        break
    }
    return date.toISOString().split('T')[0]
  }

  const handleGenerate = async () => {
    if (!profile?.org_id || !journal.recurring_lines?.length) {
      toast.error('Invalid data')
      return
    }

    setLoading(true)
    try {
      // Create journal entry
      const { data: journalEntry, error: journalError } = await supabase
        .from('journal_entries')
        .insert({
          org_id: profile.org_id,
          date: date,
          description: description,
          reference: reference || null,
          created_by: profile.id,
          is_posted: false
        })
        .select()
        .single()

      if (journalError) throw journalError

      // Create transaction lines
      const lines = journal.recurring_lines.map(line => ({
        org_id: profile.org_id,
        journal_entry_id: journalEntry.id,
        account_id: line.account_id,
        debit: line.debit,
        credit: line.credit,
        description: line.description
      }))

      const { error: linesError } = await supabase
        .from('transaction_lines')
        .insert(lines)

      if (linesError) throw linesError

      // Update next date for recurring journal
      const nextDate = calculateNextDate(date, journal.frequency)
      const { error: updateError } = await supabase
        .from('recurring_journals')
        .update({ next_date: nextDate })
        .eq('id', journal.id)
        .eq('org_id', profile.org_id)

      if (updateError) throw updateError

      toast.success('Journal entry generated successfully')
      onSuccess()
      onClose()
    } catch (error) {
      console.error('Error generating journal entry:', error)
      toast.error('Failed to generate journal entry')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="date">Entry Date</Label>
        <Input
          id="date"
          type="date"
          value={date}
          onChange={(e) => setDate(e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="reference">Reference (Optional)</Label>
        <Input
          id="reference"
          value={reference}
          onChange={(e) => setReference(e.target.value)}
          placeholder="Enter reference"
        />
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <p className="text-sm text-gray-600 mb-2">
          This will create a new journal entry with the following lines:
        </p>
        <ul className="text-sm space-y-1">
          {journal.recurring_lines?.map((line, index) => (
            <li key={index} className="flex justify-between">
              <span>Account: {line.account_id}</span>
              <span>
                {line.debit > 0 ? `Debit: ${line.debit}` : `Credit: ${line.credit}`}
              </span>
            </li>
          ))}
        </ul>
      </div>

      <div className="flex gap-2 pt-4">
        <Button 
          onClick={handleGenerate} 
          disabled={loading || !date || !description}
          className="flex-1"
        >
          {loading ? 'Generating...' : 'Generate Entry'}
        </Button>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
      </div>
    </div>
  )
}
