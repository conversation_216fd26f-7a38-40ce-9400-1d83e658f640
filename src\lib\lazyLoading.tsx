/**
 * Lazy Loading System
 * Implements intelligent lazy loading for components, data, and resources
 */

import React, { Suspense, lazy, ComponentType } from 'react'
import { performanceMonitor } from './performance'
import { logger } from './logger'

export interface LazyLoadConfig {
  threshold?: number // Intersection threshold (0-1)
  rootMargin?: string // Root margin for intersection observer
  triggerOnce?: boolean // Only trigger once
  placeholder?: React.ComponentType
  errorBoundary?: React.ComponentType<{ error: Error; retry: () => void }>
  preload?: boolean // Preload on hover/focus
  priority?: 'low' | 'normal' | 'high'
}

export interface LazyComponentConfig extends LazyLoadConfig {
  loader: () => Promise<{ default: ComponentType<unknown> }>
  fallback?: React.ComponentType
  chunkName?: string
}

export interface LazyDataConfig<T> extends LazyLoadConfig {
  loader: () => Promise<T>
  cacheKey?: string
  cacheTTL?: number
}

// Component lazy loading with performance tracking
export function createLazyComponent<P = Record<string, unknown>>(
  config: LazyComponentConfig
): React.ComponentType<P> {
  const LazyComponent = lazy(async () => {
    const startTime = performance.now()
    
    try {
      const module = await config.loader()
      const loadTime = performance.now() - startTime
      
      performanceMonitor.addMetric({
        name: `Lazy Component Load: ${config.chunkName || 'Unknown'}`,
        category: 'bundle',
        duration: loadTime,
        metadata: {
          chunkName: config.chunkName,
          priority: config.priority || 'normal'
        }
      })

      logger.debug('Lazy component loaded', {
        component: 'LazyLoading',
        action: 'createLazyComponent',
        metadata: {
          chunkName: config.chunkName,
          loadTime,
          priority: config.priority
        }
      })

      return module
    } catch (error) {
      logger.error('Failed to load lazy component', {
        component: 'LazyLoading',
        action: 'createLazyComponent',
        metadata: {
          chunkName: config.chunkName,
          error: (error as Error).message
        }
      })
      throw error
    }
  })

  const WrappedComponent: React.ComponentType<P> = (props) => {
    const fallback = config.fallback ? React.createElement(config.fallback) :
      React.createElement('div', { className: "flex items-center justify-center p-8" },
        React.createElement('div', { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary" })
      )

    return React.createElement(
      Suspense,
      { fallback },
      React.createElement(LazyComponent, props)
    )
  }

  WrappedComponent.displayName = `LazyComponent(${config.chunkName || 'Unknown'})`
  
  return WrappedComponent
}

// Intersection Observer based lazy loading hook
export function useLazyLoad(config: LazyLoadConfig = {}) {
  const [isVisible, setIsVisible] = React.useState(false)
  const [hasTriggered, setHasTriggered] = React.useState(false)
  const elementRef = React.useRef<HTMLElement>(null)

  React.useEffect(() => {
    const element = elementRef.current
    if (!element || (config.triggerOnce && hasTriggered)) return

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting) {
          setIsVisible(true)
          setHasTriggered(true)
          
          if (config.triggerOnce) {
            observer.unobserve(element)
          }

          performanceMonitor.addMetric({
            name: 'Lazy Load Triggered',
            category: 'user_interaction',
            duration: 0,
            metadata: {
              triggerOnce: config.triggerOnce,
              threshold: config.threshold
            }
          })
        } else if (!config.triggerOnce) {
          setIsVisible(false)
        }
      },
      {
        threshold: config.threshold || 0.1,
        rootMargin: config.rootMargin || '50px'
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [config.threshold, config.rootMargin, config.triggerOnce, hasTriggered])

  return {
    ref: elementRef,
    isVisible,
    hasTriggered
  }
}

// Lazy data loading hook with caching
export function useLazyData<T>(
  config: LazyDataConfig<T>,
  dependencies: React.DependencyList = []
) {
  const [data, setData] = React.useState<T | null>(null)
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<Error | null>(null)
  const { ref, isVisible } = useLazyLoad(config)

  const loadData = React.useCallback(async () => {
    if (loading) return

    setLoading(true)
    setError(null)

    try {
      // Check cache first
      if (config.cacheKey) {
        const cached = performanceMonitor.getCache<T>(config.cacheKey)
        if (cached) {
          setData(cached)
          setLoading(false)
          return
        }
      }

      const startTime = performance.now()
      const result = await config.loader()
      const loadTime = performance.now() - startTime

      setData(result)

      // Cache the result
      if (config.cacheKey) {
        performanceMonitor.setCache(
          config.cacheKey,
          result,
          config.cacheTTL || 5 * 60 * 1000 // 5 minutes default
        )
      }

      performanceMonitor.addMetric({
        name: 'Lazy Data Load',
        category: 'api',
        duration: loadTime,
        metadata: {
          cacheKey: config.cacheKey,
          cached: false,
          priority: config.priority
        }
      })

      logger.debug('Lazy data loaded', {
        component: 'LazyLoading',
        action: 'useLazyData',
        metadata: {
          cacheKey: config.cacheKey,
          loadTime,
          priority: config.priority
        }
      })
    } catch (err) {
      const error = err as Error
      setError(error)
      
      logger.error('Failed to load lazy data', {
        component: 'LazyLoading',
        action: 'useLazyData',
        metadata: {
          cacheKey: config.cacheKey,
          error: error.message
        }
      })
    } finally {
      setLoading(false)
    }
  }, [config, loading])

  React.useEffect(() => {
    if (isVisible && !data && !loading) {
      loadData()
    }
  }, [isVisible, data, loading, loadData])

  const retry = React.useCallback(() => {
    setError(null)
    loadData()
  }, [loadData])

  return {
    ref,
    data,
    loading,
    error,
    retry,
    isVisible
  }
}

// Preloading utilities
export class PreloadManager {
  private static instance: PreloadManager
  private preloadedComponents = new Set<string>()
  private preloadedData = new Map<string, unknown>()

  private constructor() {}

  public static getInstance(): PreloadManager {
    if (!PreloadManager.instance) {
      PreloadManager.instance = new PreloadManager()
    }
    return PreloadManager.instance
  }

  public preloadComponent(loader: () => Promise<unknown>, chunkName?: string): void {
    const key = chunkName || loader.toString()
    
    if (this.preloadedComponents.has(key)) return

    this.preloadedComponents.add(key)
    
    loader().then(() => {
      logger.debug('Component preloaded', {
        component: 'PreloadManager',
        action: 'preloadComponent',
        metadata: { chunkName }
      })
    }).catch((error) => {
      this.preloadedComponents.delete(key)
      logger.warn('Failed to preload component', {
        component: 'PreloadManager',
        action: 'preloadComponent',
        metadata: { chunkName, error: error.message }
      })
    })
  }

  public async preloadData<T>(
    loader: () => Promise<T>,
    cacheKey: string,
    cacheTTL?: number
  ): Promise<void> {
    if (this.preloadedData.has(cacheKey)) return

    try {
      const data = await loader()
      this.preloadedData.set(cacheKey, data)
      
      // Also cache in performance monitor
      performanceMonitor.setCache(cacheKey, data, cacheTTL || 5 * 60 * 1000)
      
      logger.debug('Data preloaded', {
        component: 'PreloadManager',
        action: 'preloadData',
        metadata: { cacheKey }
      })
    } catch (error) {
      logger.warn('Failed to preload data', {
        component: 'PreloadManager',
        action: 'preloadData',
        metadata: { cacheKey, error: (error as Error).message }
      })
    }
  }

  public getPreloadedData<T>(cacheKey: string): T | null {
    return this.preloadedData.get(cacheKey) || null
  }

  public clearPreloaded(): void {
    this.preloadedComponents.clear()
    this.preloadedData.clear()
  }
}

export const preloadManager = PreloadManager.getInstance()

// Higher-order component for lazy loading with preloading on hover
export function withPreloadOnHover<P extends object>(
  Component: React.ComponentType<P>,
  preloadConfig: {
    componentLoader?: () => Promise<unknown>
    dataLoader?: () => Promise<unknown>
    cacheKey?: string
    chunkName?: string
  }
): React.ComponentType<P> {
  return React.forwardRef<unknown, P>((props, ref) => {
    const handleMouseEnter = React.useCallback(() => {
      if (preloadConfig.componentLoader) {
        preloadManager.preloadComponent(preloadConfig.componentLoader, preloadConfig.chunkName)
      }
      
      if (preloadConfig.dataLoader && preloadConfig.cacheKey) {
        preloadManager.preloadData(preloadConfig.dataLoader, preloadConfig.cacheKey)
      }
    }, [])

    return React.createElement(
      'div',
      {
        onMouseEnter: handleMouseEnter,
        onFocus: handleMouseEnter
      },
      React.createElement(Component, { ...props, ref })
    )
  })
}

// Lazy image component with intersection observer
export const LazyImage: React.FC<{
  src: string
  alt: string
  className?: string
  placeholder?: string
  onLoad?: () => void
  onError?: () => void
}> = ({ src, alt, className, placeholder, onLoad, onError }) => {
  const [loaded, setLoaded] = React.useState(false)
  const [error, setError] = React.useState(false)
  const { ref, isVisible } = useLazyLoad({ triggerOnce: true })

  const handleLoad = React.useCallback(() => {
    setLoaded(true)
    onLoad?.()
  }, [onLoad])

  const handleError = React.useCallback(() => {
    setError(true)
    onError?.()
  }, [onError])

  return (
    <div ref={ref} className={className}>
      {isVisible && (
        <>
          {!loaded && !error && placeholder && (
            <img
              src={placeholder}
              alt={alt}
              className={className}
              style={{ filter: 'blur(5px)' }}
            />
          )}
          <img
            src={src}
            alt={alt}
            className={`${className} ${loaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
            onLoad={handleLoad}
            onError={handleError}
            style={{ display: loaded ? 'block' : 'none' }}
          />
          {error && (
            <div className="flex items-center justify-center bg-gray-200 text-gray-500">
              Failed to load image
            </div>
          )}
        </>
      )}
    </div>
  )
}

// Virtual scrolling for large lists
export const VirtualList = <T,>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5
}: {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  overscan?: number
}) => {
  const [scrollTop, setScrollTop] = React.useState(0)
  const containerRef = React.useRef<HTMLDivElement>(null)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = items.slice(startIndex, endIndex + 1)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  const handleScroll = React.useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return (
    <div
      ref={containerRef}
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
