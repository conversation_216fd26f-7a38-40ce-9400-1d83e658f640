#!/usr/bin/env node

/**
 * Health Check Script for Kaya Finance
 * Performs comprehensive health checks on deployed application
 */

const https = require('https')
const http = require('http')
const { URL } = require('url')

// Configuration
const DEFAULT_TIMEOUT = 30000 // 30 seconds
const RETRY_COUNT = 3
const RETRY_DELAY = 5000 // 5 seconds

// Parse command line arguments
const args = process.argv.slice(2)
const urlArg = args.find(arg => arg.startsWith('--url='))
const timeoutArg = args.find(arg => arg.startsWith('--timeout='))
const verboseArg = args.includes('--verbose')

const TARGET_URL = urlArg ? urlArg.split('=')[1] : 'http://localhost:3000'
const TIMEOUT = timeoutArg ? parseInt(timeoutArg.split('=')[1]) : DEFAULT_TIMEOUT

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

// Logging functions
const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`)
}

const logInfo = (message) => log(`[INFO] ${message}`, colors.blue)
const logSuccess = (message) => log(`[SUCCESS] ${message}`, colors.green)
const logWarning = (message) => log(`[WARNING] ${message}`, colors.yellow)
const logError = (message) => log(`[ERROR] ${message}`, colors.red)
const logVerbose = (message) => {
  if (verboseArg) {
    log(`[VERBOSE] ${message}`, colors.cyan)
  }
}

// Health check functions
async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const isHttps = urlObj.protocol === 'https:'
    const client = isHttps ? https : http
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      timeout: TIMEOUT,
      headers: {
        'User-Agent': 'Kaya-Finance-Health-Check/1.0',
        'Accept': 'text/html,application/json,*/*',
        ...options.headers
      }
    }

    logVerbose(`Making ${requestOptions.method} request to ${url}`)

    const req = client.request(requestOptions, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          responseTime: Date.now() - startTime
        })
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.on('timeout', () => {
      req.destroy()
      reject(new Error(`Request timeout after ${TIMEOUT}ms`))
    })

    const startTime = Date.now()
    req.end()
  })
}

async function checkBasicConnectivity() {
  logInfo('Checking basic connectivity...')
  
  try {
    const response = await makeRequest(TARGET_URL)
    
    if (response.statusCode >= 200 && response.statusCode < 400) {
      logSuccess(`Basic connectivity OK (${response.statusCode}) - ${response.responseTime}ms`)
      return { success: true, responseTime: response.responseTime }
    } else {
      logError(`Unexpected status code: ${response.statusCode}`)
      return { success: false, error: `Status code: ${response.statusCode}` }
    }
  } catch (error) {
    logError(`Connectivity failed: ${error.message}`)
    return { success: false, error: error.message }
  }
}

async function checkApiEndpoints() {
  logInfo('Checking API endpoints...')
  
  const endpoints = [
    { path: '/api/health', name: 'Health endpoint' },
    { path: '/api/auth/session', name: 'Auth session' },
    { path: '/api/organizations', name: 'Organizations API' }
  ]

  const results = []
  
  for (const endpoint of endpoints) {
    try {
      const url = new URL(endpoint.path, TARGET_URL).toString()
      const response = await makeRequest(url)
      
      if (response.statusCode >= 200 && response.statusCode < 500) {
        logSuccess(`${endpoint.name} OK (${response.statusCode}) - ${response.responseTime}ms`)
        results.push({ ...endpoint, success: true, responseTime: response.responseTime })
      } else {
        logWarning(`${endpoint.name} returned ${response.statusCode}`)
        results.push({ ...endpoint, success: false, statusCode: response.statusCode })
      }
    } catch (error) {
      logError(`${endpoint.name} failed: ${error.message}`)
      results.push({ ...endpoint, success: false, error: error.message })
    }
  }
  
  const successCount = results.filter(r => r.success).length
  logInfo(`API endpoints: ${successCount}/${endpoints.length} healthy`)
  
  return { success: successCount > 0, results }
}

async function checkStaticAssets() {
  logInfo('Checking static assets...')
  
  const assets = [
    '/assets/index.css',
    '/assets/index.js',
    '/favicon.ico'
  ]

  const results = []
  
  for (const asset of assets) {
    try {
      const url = new URL(asset, TARGET_URL).toString()
      const response = await makeRequest(url)
      
      if (response.statusCode === 200) {
        logSuccess(`Asset ${asset} OK - ${response.responseTime}ms`)
        results.push({ asset, success: true, responseTime: response.responseTime })
      } else {
        logWarning(`Asset ${asset} returned ${response.statusCode}`)
        results.push({ asset, success: false, statusCode: response.statusCode })
      }
    } catch (error) {
      logError(`Asset ${asset} failed: ${error.message}`)
      results.push({ asset, success: false, error: error.message })
    }
  }
  
  const successCount = results.filter(r => r.success).length
  logInfo(`Static assets: ${successCount}/${assets.length} available`)
  
  return { success: successCount >= assets.length * 0.8, results } // 80% success rate
}

async function checkPerformance() {
  logInfo('Checking performance metrics...')
  
  const performanceChecks = []
  
  // Check response time
  try {
    const start = Date.now()
    const response = await makeRequest(TARGET_URL)
    const responseTime = Date.now() - start
    
    if (responseTime < 2000) {
      logSuccess(`Response time OK: ${responseTime}ms`)
      performanceChecks.push({ check: 'response_time', success: true, value: responseTime })
    } else if (responseTime < 5000) {
      logWarning(`Response time slow: ${responseTime}ms`)
      performanceChecks.push({ check: 'response_time', success: true, value: responseTime, warning: true })
    } else {
      logError(`Response time too slow: ${responseTime}ms`)
      performanceChecks.push({ check: 'response_time', success: false, value: responseTime })
    }
  } catch (error) {
    logError(`Performance check failed: ${error.message}`)
    performanceChecks.push({ check: 'response_time', success: false, error: error.message })
  }
  
  return { success: performanceChecks.every(c => c.success), results: performanceChecks }
}

async function checkSecurity() {
  logInfo('Checking security headers...')
  
  try {
    const response = await makeRequest(TARGET_URL)
    const headers = response.headers
    
    const securityChecks = [
      {
        name: 'X-Frame-Options',
        present: !!headers['x-frame-options'],
        expected: true
      },
      {
        name: 'X-Content-Type-Options',
        present: !!headers['x-content-type-options'],
        expected: true
      },
      {
        name: 'Strict-Transport-Security',
        present: !!headers['strict-transport-security'],
        expected: TARGET_URL.startsWith('https')
      },
      {
        name: 'Content-Security-Policy',
        present: !!headers['content-security-policy'],
        expected: true
      }
    ]
    
    let passedChecks = 0
    
    for (const check of securityChecks) {
      if (check.expected) {
        if (check.present) {
          logSuccess(`Security header ${check.name} present`)
          passedChecks++
        } else {
          logWarning(`Security header ${check.name} missing`)
        }
      } else {
        passedChecks++
      }
    }
    
    const expectedChecks = securityChecks.filter(c => c.expected).length
    logInfo(`Security headers: ${passedChecks}/${expectedChecks} present`)
    
    return { success: passedChecks >= expectedChecks * 0.7, results: securityChecks }
  } catch (error) {
    logError(`Security check failed: ${error.message}`)
    return { success: false, error: error.message }
  }
}

async function runHealthCheck() {
  logInfo(`Starting health check for: ${TARGET_URL}`)
  logInfo(`Timeout: ${TIMEOUT}ms`)
  logInfo('=' .repeat(50))
  
  const startTime = Date.now()
  const results = {}
  
  try {
    // Run all health checks
    results.connectivity = await checkBasicConnectivity()
    results.api = await checkApiEndpoints()
    results.assets = await checkStaticAssets()
    results.performance = await checkPerformance()
    results.security = await checkSecurity()
    
    // Calculate overall health
    const checks = Object.values(results)
    const successfulChecks = checks.filter(check => check.success).length
    const totalChecks = checks.length
    const healthPercentage = (successfulChecks / totalChecks) * 100
    
    const totalTime = Date.now() - startTime
    
    logInfo('=' .repeat(50))
    logInfo(`Health check completed in ${totalTime}ms`)
    logInfo(`Overall health: ${successfulChecks}/${totalChecks} checks passed (${healthPercentage.toFixed(1)}%)`)
    
    if (healthPercentage >= 80) {
      logSuccess('Application is healthy! ✅')
      process.exit(0)
    } else if (healthPercentage >= 60) {
      logWarning('Application has some issues but is functional ⚠️')
      process.exit(1)
    } else {
      logError('Application is unhealthy! ❌')
      process.exit(2)
    }
    
  } catch (error) {
    logError(`Health check failed: ${error.message}`)
    process.exit(3)
  }
}

// Handle script interruption
process.on('SIGINT', () => {
  logWarning('Health check interrupted')
  process.exit(130)
})

process.on('SIGTERM', () => {
  logWarning('Health check terminated')
  process.exit(143)
})

// Show usage if help is requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Kaya Finance Health Check Script

Usage: node scripts/health-check.js [options]

Options:
  --url=<url>        Target URL to check (default: http://localhost:3000)
  --timeout=<ms>     Request timeout in milliseconds (default: 30000)
  --verbose          Enable verbose logging
  --help, -h         Show this help message

Examples:
  node scripts/health-check.js --url=https://app.kaya-finance.com
  node scripts/health-check.js --url=https://staging.kaya-finance.com --verbose
  node scripts/health-check.js --url=http://localhost:3000 --timeout=10000

Exit codes:
  0 - Healthy (80%+ checks passed)
  1 - Warning (60-79% checks passed)
  2 - Unhealthy (<60% checks passed)
  3 - Error (health check failed to run)
`)
  process.exit(0)
}

// Run the health check
runHealthCheck().catch((error) => {
  logError(`Unexpected error: ${error.message}`)
  process.exit(3)
})
