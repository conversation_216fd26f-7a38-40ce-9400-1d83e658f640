# Invoice PDF Export & Print Feature

## Overview

The invoice PDF export and print feature allows users to generate professional PDF documents and print invoices directly from the invoice details modal. The feature includes organization branding and comprehensive invoice formatting.

## Features

### PDF Export
- **High-Quality PDF Generation**: Uses jsPDF and html2canvas for professional output
- **Organization Branding**: Automatically includes organization name and details
- **Professional Layout**: Clean, business-appropriate design with proper spacing
- **Comprehensive Content**: Includes all invoice details, line items, totals, and notes
- **Automatic Download**: PDF files are automatically downloaded to the user's device

### Print Functionality
- **Direct Printing**: Opens browser print dialog with formatted invoice
- **Print-Optimized Layout**: Responsive design optimized for standard paper sizes
- **No External Dependencies**: Uses browser's native print functionality
- **Popup Window**: Opens in new window for better print control

## Usage

### From Invoice Details Modal

1. **Open Invoice Details**: Click "View" on any invoice from the invoices list
2. **Access Export Options**: In the modal header, you'll see two buttons:
   - **Print Button**: Opens print dialog immediately
   - **Export PDF Button**: Downloads PDF file

### Export Process

1. Click "Export PDF" button
2. System fetches organization details automatically
3. PDF is generated with professional formatting
4. File is automatically downloaded as `invoice-{number}-{date}.pdf`

### Print Process

1. Click "Print" button
2. New window opens with formatted invoice
3. Browser print dialog appears automatically
4. Select printer and print options
5. Print window closes automatically after printing

## PDF Content Structure

### Header Section
- **Organization Name**: Prominently displayed (fetched from organization settings)
- **Organization Details**: Address, phone, email if available
- **Invoice Title**: "INVOICE" with invoice number
- **Professional Branding**: Clean, business-appropriate styling

### Invoice Information
- **Bill To Section**: Customer name, email, phone, address
- **Invoice Details**: Issue date, due date, status with color coding
- **Organized Layout**: Two-column responsive design

### Line Items Table
- **Detailed Breakdown**: Description, quantity, unit price, tax rate, total
- **Account Information**: Account codes and names for each line item
- **Professional Styling**: Alternating row colors, clear headers
- **Responsive Design**: Adapts to content length

### Totals Section
- **Subtotal**: Pre-tax amount
- **Tax Amount**: Total tax across all line items
- **Grand Total**: Final amount due
- **Highlighted Design**: Prominent display of total amount

### Additional Information
- **Notes Section**: Customer notes if provided
- **Footer**: Thank you message and generation timestamp
- **Professional Formatting**: Consistent spacing and typography

## File Naming Convention

PDF files are automatically named using this pattern:
```
invoice-{invoice_number}-{YYYY-MM-DD}.pdf
```

Examples:
- `invoice-INV-2025-001-2025-06-29.pdf`
- `invoice-BILL-001-2025-06-29.pdf`

## Technical Implementation

### Core Files
- `src/lib/invoicePdfExport.ts` - PDF generation logic
- `src/hooks/useInvoicePdfExport.ts` - React hook for export functionality
- `src/components/invoices/InvoiceDetailsModal.tsx` - UI integration

### Key Technologies
- **jsPDF**: PDF document generation
- **html2canvas**: HTML to canvas conversion for high-quality rendering
- **React Hooks**: State management and API integration
- **Supabase**: Organization data fetching

### Key Functions

#### `exportInvoiceToPdf(invoice, organization)`
- Generates and downloads PDF file
- Creates temporary HTML template
- Converts to high-resolution canvas
- Generates multi-page PDF if needed

#### `createInvoiceHtmlTemplate(invoice, organization)`
- Creates formatted HTML template
- Includes all invoice data and styling
- Responsive design for different content lengths
- Professional business formatting

#### `useInvoicePdfExport()` Hook
- Manages export state and loading
- Handles organization data fetching
- Provides error handling and user feedback
- Supports both PDF export and print functionality

## Styling & Design

### Color Scheme
- **Primary**: Professional blue (#3b82f6)
- **Success**: Green for paid status (#10b981)
- **Warning**: Red for overdue status (#ef4444)
- **Neutral**: Gray tones for secondary information

### Typography
- **Headers**: Bold, larger fonts for emphasis
- **Body Text**: Clean, readable Arial font
- **Amounts**: Highlighted and properly formatted
- **Status Badges**: Color-coded for quick recognition

### Layout
- **Responsive Grid**: Adapts to different content lengths
- **Professional Spacing**: Consistent margins and padding
- **Print Optimization**: Proper page breaks and margins
- **Brand Consistency**: Matches application design language

## Error Handling

### Common Scenarios
- **Missing Organization**: Continues without organization details
- **Network Issues**: Graceful fallback with error messages
- **Browser Compatibility**: Handles popup blockers and print restrictions
- **Large Invoices**: Automatic page breaks for long content

### User Feedback
- **Loading States**: Visual indicators during PDF generation
- **Success Messages**: Confirmation when export completes
- **Error Messages**: Clear descriptions of any issues
- **Toast Notifications**: Non-intrusive status updates

## Browser Compatibility

### Supported Browsers
- **Chrome**: Full support for all features
- **Firefox**: Full support for all features
- **Safari**: Full support for all features
- **Edge**: Full support for all features

### Requirements
- **JavaScript Enabled**: Required for PDF generation
- **Popup Permissions**: Needed for print functionality
- **Download Permissions**: Required for PDF downloads
- **Canvas Support**: Needed for high-quality rendering

## Performance Considerations

### Optimization Features
- **Lazy Loading**: PDF libraries loaded only when needed
- **Memory Management**: Temporary elements cleaned up after use
- **Efficient Rendering**: Optimized canvas generation
- **Minimal Dependencies**: Uses existing project libraries

### Large Invoice Handling
- **Multi-page Support**: Automatic page breaks for long invoices
- **Memory Efficient**: Streams large content without memory issues
- **Progress Indicators**: Shows progress for large exports
- **Timeout Handling**: Prevents hanging on large documents

## Security Considerations

### Data Protection
- **Client-side Generation**: No data sent to external servers
- **Temporary Elements**: HTML templates removed after use
- **Organization Data**: Fetched securely through authenticated API
- **Access Control**: Respects existing user permissions

### Privacy
- **Local Processing**: All PDF generation happens in browser
- **No External Services**: No third-party PDF services used
- **Secure Downloads**: Files saved directly to user's device
- **Data Cleanup**: Temporary data cleared after export

## Future Enhancements

### Planned Features
- **Custom Templates**: User-defined invoice layouts
- **Batch Export**: Multiple invoices in single PDF
- **Email Integration**: Direct email sending of PDFs
- **Watermarks**: Draft/paid status watermarks
- **Digital Signatures**: Electronic signature support

### Template Customization
- **Logo Upload**: Organization logo in header
- **Color Themes**: Customizable brand colors
- **Layout Options**: Different template styles
- **Field Selection**: Choose which fields to include
- **Custom Footer**: Personalized footer content
