import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'

export type CostingMethod = 'fifo' | 'lifo' | 'average' | 'specific'

export interface OrganizationCostingSettings {
  id: string
  org_id: string
  default_costing_method: CostingMethod
  auto_update_costs: boolean
  revalue_on_receipt: boolean
  cost_decimal_places: number
  created_at: string
  updated_at: string
}

export interface ProductCostingSettings {
  id: string
  org_id: string
  product_id: string
  costing_method: CostingMethod
  created_at: string
  updated_at: string
}

export interface InventoryCostLayer {
  id: string
  org_id: string
  product_id: string
  location_id: string
  layer_date: string
  unit_cost: number
  quantity_received: number
  quantity_remaining: number
  source_transaction_id: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CostCalculationResult {
  total_cost: number
  average_unit_cost: number
  layers_consumed?: any[]
}

export interface CostingMethodSummary {
  product_id: string
  product_name: string
  costing_method: CostingMethod
  current_average_cost: number
  total_value: number
  total_quantity: number
  cost_layers_count?: number
}

/**
 * Hook for managing inventory costing methods
 */
export function useCostingMethods() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  // Get organization costing settings
  const organizationSettingsQuery = useQuery({
    queryKey: ['organization-costing-settings', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('organization_costing_settings')
        .select('*')
        .eq('org_id', profile.org_id)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      
      // Return default settings if none exist
      if (!data) {
        return {
          id: '',
          org_id: profile.org_id,
          default_costing_method: 'average' as CostingMethod,
          auto_update_costs: true,
          revalue_on_receipt: true,
          cost_decimal_places: 4,
          created_at: '',
          updated_at: ''
        }
      }

      return data as OrganizationCostingSettings
    },
    enabled: !!profile?.org_id,
  })

  // Get product costing settings
  const productCostingSettingsQuery = useQuery({
    queryKey: ['product-costing-settings', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('product_costing_settings')
        .select(`
          *,
          product:products(id, name, sku)
        `)
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as (ProductCostingSettings & { product: { id: string; name: string; sku: string } })[]
    },
    enabled: !!profile?.org_id,
  })

  // Get cost layers for a specific product
  const getCostLayers = (productId: string, locationId?: string) => {
    return useQuery({
      queryKey: ['cost-layers', profile?.org_id, productId, locationId],
      queryFn: async () => {
        if (!profile?.org_id) throw new Error('No organization ID')

        let query = supabase
          .from('inventory_cost_layers')
          .select(`
            *,
            product:products(name, sku),
            location:inventory_locations(name)
          `)
          .eq('org_id', profile.org_id)
          .eq('product_id', productId)
          .eq('is_active', true)
          .order('layer_date', { ascending: true })

        if (locationId) {
          query = query.eq('location_id', locationId)
        }

        const { data, error } = await query

        if (error) throw error
        return data as (InventoryCostLayer & {
          product: { name: string; sku: string }
          location: { name: string }
        })[]
      },
      enabled: !!profile?.org_id && !!productId,
    })
  }

  // Get costing method summary for all products
  const costingMethodSummaryQuery = useQuery({
    queryKey: ['costing-method-summary', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      // Get products with their costing methods and stock levels
      const { data, error } = await supabase
        .from('products')
        .select(`
          id,
          name,
          sku,
          track_inventory,
          stock_levels(
            quantity_on_hand,
            average_cost,
            location:inventory_locations(name)
          ),
          product_costing_settings(costing_method)
        `)
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .eq('track_inventory', true)

      if (error) throw error

      // Get organization default costing method
      const orgSettings = organizationSettingsQuery.data

      const summary: CostingMethodSummary[] = data.map(product => {
        const stockLevels = product.stock_levels || []
        const totalQuantity = stockLevels.reduce((sum, sl) => sum + (sl.quantity_on_hand || 0), 0)
        const weightedCost = stockLevels.reduce((sum, sl) => 
          sum + ((sl.quantity_on_hand || 0) * (sl.average_cost || 0)), 0)
        const averageCost = totalQuantity > 0 ? weightedCost / totalQuantity : 0

        return {
          product_id: product.id,
          product_name: product.name,
          costing_method: product.product_costing_settings?.[0]?.costing_method || 
                        orgSettings?.default_costing_method || 'average',
          current_average_cost: averageCost,
          total_value: weightedCost,
          total_quantity: totalQuantity
        }
      })

      return summary
    },
    enabled: !!profile?.org_id && !!organizationSettingsQuery.data,
  })

  // Update organization costing settings
  const updateOrganizationSettingsMutation = useMutation({
    mutationFn: async (settings: Partial<OrganizationCostingSettings>) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { error } = await supabase
        .from('organization_costing_settings')
        .upsert({
          org_id: profile.org_id,
          ...settings,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'org_id'
        })

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization-costing-settings', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: ['costing-method-summary', profile?.org_id] })
      toast.success('Costing settings updated successfully')
    },
    onError: (error) => {
      console.error('Failed to update costing settings:', error)
      toast.error('Failed to update costing settings')
    }
  })

  // Set product costing method
  const setProductCostingMethodMutation = useMutation({
    mutationFn: async ({
      productId,
      costingMethod
    }: {
      productId: string
      costingMethod: CostingMethod
    }) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { error } = await supabase
        .from('product_costing_settings')
        .upsert({
          org_id: profile.org_id,
          product_id: productId,
          costing_method: costingMethod,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'org_id,product_id'
        })

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-costing-settings', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: ['costing-method-summary', profile?.org_id] })
      toast.success('Product costing method updated successfully')
    },
    onError: (error) => {
      console.error('Failed to update product costing method:', error)
      toast.error('Failed to update product costing method')
    }
  })

  // Remove product costing method override (use organization default)
  const removeProductCostingMethodMutation = useMutation({
    mutationFn: async (productId: string) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { error } = await supabase
        .from('product_costing_settings')
        .delete()
        .eq('org_id', profile.org_id)
        .eq('product_id', productId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-costing-settings', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: ['costing-method-summary', profile?.org_id] })
      toast.success('Product costing method reset to organization default')
    },
    onError: (error) => {
      console.error('Failed to remove product costing method:', error)
      toast.error('Failed to remove product costing method')
    }
  })

  // Calculate cost for a specific quantity using the product's costing method
  const calculateCostMutation = useMutation({
    mutationFn: async ({
      productId,
      locationId,
      quantity,
      method
    }: {
      productId: string
      locationId: string
      quantity: number
      method?: CostingMethod
    }) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const costingMethod = method || 
        productCostingSettingsQuery.data?.find(p => p.product_id === productId)?.costing_method ||
        organizationSettingsQuery.data?.default_costing_method ||
        'average'

      let result: CostCalculationResult

      if (costingMethod === 'fifo') {
        const { data, error } = await supabase.rpc('calculate_fifo_cost', {
          p_org_id: profile.org_id,
          p_product_id: productId,
          p_location_id: locationId,
          p_quantity: quantity
        })
        if (error) throw error
        result = data[0]
      } else if (costingMethod === 'lifo') {
        const { data, error } = await supabase.rpc('calculate_lifo_cost', {
          p_org_id: profile.org_id,
          p_product_id: productId,
          p_location_id: locationId,
          p_quantity: quantity
        })
        if (error) throw error
        result = data[0]
      } else {
        const { data, error } = await supabase.rpc('calculate_average_cost', {
          p_org_id: profile.org_id,
          p_product_id: productId,
          p_location_id: locationId,
          p_quantity: quantity
        })
        if (error) throw error
        result = data[0]
      }

      return result
    }
  })

  return {
    // Queries
    organizationSettings: organizationSettingsQuery.data,
    productCostingSettings: productCostingSettingsQuery.data || [],
    costingMethodSummary: costingMethodSummaryQuery.data || [],
    
    // Query functions
    getCostLayers,
    
    // Loading states
    isLoadingOrgSettings: organizationSettingsQuery.isLoading,
    isLoadingProductSettings: productCostingSettingsQuery.isLoading,
    isLoadingSummary: costingMethodSummaryQuery.isLoading,
    
    // Mutations
    updateOrganizationSettings: updateOrganizationSettingsMutation.mutateAsync,
    setProductCostingMethod: setProductCostingMethodMutation.mutateAsync,
    removeProductCostingMethod: removeProductCostingMethodMutation.mutateAsync,
    calculateCost: calculateCostMutation.mutateAsync,
    
    // Mutation states
    isUpdatingOrgSettings: updateOrganizationSettingsMutation.isPending,
    isUpdatingProductMethod: setProductCostingMethodMutation.isPending,
    isRemovingProductMethod: removeProductCostingMethodMutation.isPending,
    isCalculatingCost: calculateCostMutation.isPending,
    
    // Refetch functions
    refetchOrgSettings: organizationSettingsQuery.refetch,
    refetchProductSettings: productCostingSettingsQuery.refetch,
    refetchSummary: costingMethodSummaryQuery.refetch
  }
}
