
import { useEffect, useState, useCallback } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/integrations/supabase/client'
import type { Profile } from '@/types/database'
import { AuthContext } from '@/contexts/AuthContext'
import { checkOnboardingStatus, debugOnboardingStatus } from '@/lib/onboardingUtils'

// REMOVED: Manual cleanup is unreliable and creates security vulnerabilities
// Supabase handles session cleanup automatically with signOut({ scope: 'global' })

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [needsOnboarding, setNeedsOnboarding] = useState(false)

  const fetchUserProfile = useCallback(async (userId: string) => {
    try {
      console.log('Fetching profile for user:', userId)

      // Use the comprehensive onboarding status check
      const status = await checkOnboardingStatus(userId)

      // Debug in development
      if (process.env.NODE_ENV === 'development') {
        await debugOnboardingStatus(userId)
      }

      setNeedsOnboarding(status.needsOnboarding)
      setProfile(status.profile)

    } catch (error) {
      console.error('Error fetching user profile:', error)
      // If profile doesn't exist, user needs to complete onboarding
      setNeedsOnboarding(true)
      setProfile(null)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event, session?.user?.id)

        if (event === 'SIGNED_OUT') {
          // Immediate cleanup on sign out
          setUser(null)
          setProfile(null)
          setNeedsOnboarding(false)
          setLoading(false)
          return
        }

        setUser(session?.user ?? null)

        if (session?.user) {
          // Fetch profile immediately with proper async handling
          fetchUserProfile(session.user.id)
        } else {
          setProfile(null)
          setNeedsOnboarding(false)
          setLoading(false)
        }
      }
    )

    // THEN check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('Initial session:', session?.user?.id)
      setUser(session?.user ?? null)
      if (session?.user) {
        fetchUserProfile(session.user.id)
      } else {
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [fetchUserProfile])
  const signIn = async (email: string, password: string) => {
    // Ensure clean state before sign in
    try {
      await supabase.auth.signOut({ scope: 'global' })
    } catch (err) {
      // Continue even if this fails - user might not be signed in
      console.debug('Pre-signin cleanup failed:', err)
    }

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) throw error
  }

  const signUp = async (email: string, password: string, role: 'admin' | 'accountant' = 'accountant') => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          role: role
        }
      }
    })
    if (error) throw error

    // Profile will be created by the database trigger when user confirms email
    // or we'll handle it in the onboarding flow
    console.log('User signed up:', data.user?.id)
  }

  const signOut = async () => {
    try {
      // Set loading to prevent flash
      setLoading(true)

      // Use Supabase's secure global sign out - this handles all session cleanup
      await supabase.auth.signOut({ scope: 'global' })

      // State will be cleared automatically by auth state listener
      // No manual state clearing needed - prevents race conditions

    } catch (error) {
      console.error('Error signing out:', error)
      // Force clear state even if signout fails
      setUser(null)
      setProfile(null)
      setNeedsOnboarding(false)
      setLoading(false)
    }
  }

  const value = {
    user,
    profile,
    loading,
    needsOnboarding,
    signIn,
    signUp,
    signOut,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
