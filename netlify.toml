# =====================================================
# NETLIFY DEPLOYMENT CONFIGURATION
# For Kaya Finance Production Deployment
# =====================================================

[build]
  # Build command
  command = "npm run build:prod"
  
  # Output directory
  publish = "dist"
  
  # Environment variables for build
  environment = { NODE_VERSION = "18", NPM_VERSION = "9" }

[build.processing]
  # Skip processing for better performance
  skip_processing = false

[build.processing.css]
  # CSS optimization
  bundle = true
  minify = true

[build.processing.js]
  # JavaScript optimization
  bundle = true
  minify = true

[build.processing.html]
  # HTML optimization
  pretty_urls = true

# Production environment variables
[context.production.environment]
  VITE_APP_ENV = "production"
  NODE_ENV = "production"

# Staging environment variables
[context.branch-deploy.environment]
  VITE_APP_ENV = "staging"
  NODE_ENV = "staging"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security Headers
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"
    Strict-Transport-Security = "max-age=********; includeSubDomains; preload"
    
    # Content Security Policy
    Content-Security-Policy = '''
      default-src 'self';
      script-src 'self';
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: https:;
      font-src 'self' data:;
      connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.ipify.org https://ipapi.co;
      frame-ancestors 'none';
      base-uri 'self';
      form-action 'self'
    '''

# Cache optimization for static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Cache control for HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Cache control for manifest and service worker
[[headers]]
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Redirects for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin", "user"]}

# API redirects (if needed)
[[redirects]]
  from = "/api/*"
  to = "https://kmejequnwwngmzwkszqs.supabase.co/rest/v1/:splat"
  status = 200
  headers = {Authorization = "Bearer [TOKEN]"}

# Error pages
[[redirects]]
  from = "/404"
  to = "/index.html"
  status = 404

# Functions (if using Netlify Functions)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Edge functions (if needed)
[edge_functions]
  directory = "netlify/edge-functions"

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-lighthouse"
  
  [plugins.inputs.thresholds]
    performance = 0.9
    accessibility = 0.9
    best-practices = 0.9
    seo = 0.9

[[plugins]]
  package = "netlify-plugin-submit-sitemap"
  
  [plugins.inputs]
    baseUrl = "https://kayafinance.com"
    sitemapPath = "/sitemap.xml"
    providers = [
      "google",
      "bing"
    ]
