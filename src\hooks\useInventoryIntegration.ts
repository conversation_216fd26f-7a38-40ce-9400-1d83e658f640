import { useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import type { InvoiceLineData } from '@/types/invoices'
import type { BillLineData } from '@/types/bills'
import type { InventoryTransactionType } from '@/types/inventory'

interface InventoryTransactionData {
  product_id: string
  location_id: string
  transaction_type: InventoryTransactionType
  quantity: number
  unit_cost?: number
  reference_type: string
  reference_id: string
  reference_number: string
  notes?: string
}

/**
 * Hook to handle inventory transactions for invoice/bill line items
 */
export function useInventoryIntegration() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  /**
   * Process inventory transactions for invoice lines (sales)
   */
  const processInvoiceInventory = useMutation({
    mutationFn: async ({
      invoiceId,
      invoiceNumber,
      lines,
      locationId
    }: {
      invoiceId: string
      invoiceNumber: string
      lines: InvoiceLineData[]
      locationId?: string
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Get default location if not provided
      let defaultLocationId = locationId
      if (!defaultLocationId) {
        const { data: defaultLocation } = await supabase
          .from('inventory_locations')
          .select('id')
          .eq('org_id', profile.org_id)
          .eq('is_default', true)
          .eq('is_active', true)
          .single()

        if (!defaultLocation) {
          throw new Error('No default inventory location found')
        }
        defaultLocationId = defaultLocation.id
      }

      // Process each line item that has a product
      const transactions: InventoryTransactionData[] = []
      
      for (const line of lines) {
        if (line.product_id && line.quantity > 0) {
          // Check if product tracks inventory
          const { data: product } = await supabase
            .from('products')
            .select('track_inventory, name')
            .eq('id', line.product_id)
            .eq('org_id', profile.org_id)
            .single()

          if (product?.track_inventory) {
            transactions.push({
              product_id: line.product_id,
              location_id: defaultLocationId,
              transaction_type: 'sale',
              quantity: -line.quantity, // Negative for sales (outgoing)
              unit_cost: line.unit_price,
              reference_type: 'invoice',
              reference_id: invoiceId,
              reference_number: invoiceNumber,
              notes: `Sale: ${line.item}`
            })
          }
        }
      }

      // Create inventory transactions
      if (transactions.length > 0) {
        const { error } = await supabase
          .from('inventory_transactions')
          .insert(
            transactions.map(transaction => ({
              ...transaction,
              org_id: profile.org_id,
              created_by: profile.id,
              total_cost: transaction.unit_cost 
                ? Math.abs(transaction.quantity) * transaction.unit_cost 
                : null
            }))
          )

        if (error) throw error

        // Update stock levels using the database function
        for (const transaction of transactions) {
          const { error: stockError } = await supabase.rpc('update_stock_level_from_transaction', {
            p_org_id: profile.org_id,
            p_product_id: transaction.product_id,
            p_location_id: transaction.location_id,
            p_quantity_change: transaction.quantity,
            p_unit_cost: transaction.unit_cost || 0
          })

          if (stockError) {
            console.error('Failed to update stock level:', stockError)
          }
        }
      }

      return transactions
    },
    onSuccess: () => {
      // Invalidate inventory-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.withStock(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lowStock(profile?.org_id || '') })
    }
  })

  /**
   * Process inventory transactions for bill lines (purchases)
   */
  const processBillInventory = useMutation({
    mutationFn: async ({
      billId,
      billNumber,
      lines,
      locationId
    }: {
      billId: string
      billNumber: string
      lines: BillLineData[]
      locationId?: string
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Get default location if not provided
      let defaultLocationId = locationId
      if (!defaultLocationId) {
        const { data: defaultLocation } = await supabase
          .from('inventory_locations')
          .select('id')
          .eq('org_id', profile.org_id)
          .eq('is_default', true)
          .eq('is_active', true)
          .single()

        if (!defaultLocation) {
          throw new Error('No default inventory location found')
        }
        defaultLocationId = defaultLocation.id
      }

      // Process each line item that has a product
      const transactions: InventoryTransactionData[] = []
      
      for (const line of lines) {
        if (line.product_id && line.quantity > 0) {
          // Check if product tracks inventory
          const { data: product } = await supabase
            .from('products')
            .select('track_inventory, name')
            .eq('id', line.product_id)
            .eq('org_id', profile.org_id)
            .single()

          if (product?.track_inventory) {
            transactions.push({
              product_id: line.product_id,
              location_id: defaultLocationId,
              transaction_type: 'purchase',
              quantity: line.quantity, // Positive for purchases (incoming)
              unit_cost: line.unit_price,
              reference_type: 'bill',
              reference_id: billId,
              reference_number: billNumber,
              notes: `Purchase: ${line.item}`
            })
          }
        }
      }

      // Create inventory transactions
      if (transactions.length > 0) {
        const { error } = await supabase
          .from('inventory_transactions')
          .insert(
            transactions.map(transaction => ({
              ...transaction,
              org_id: profile.org_id,
              created_by: profile.id,
              total_cost: transaction.unit_cost 
                ? transaction.quantity * transaction.unit_cost 
                : null
            }))
          )

        if (error) throw error

        // Update stock levels using the database function
        for (const transaction of transactions) {
          const { error: stockError } = await supabase.rpc('update_stock_level_from_transaction', {
            p_org_id: profile.org_id,
            p_product_id: transaction.product_id,
            p_location_id: transaction.location_id,
            p_quantity_change: transaction.quantity,
            p_unit_cost: transaction.unit_cost || 0
          })

          if (stockError) {
            console.error('Failed to update stock level:', stockError)
          }
        }
      }

      return transactions
    },
    onSuccess: () => {
      // Invalidate inventory-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.withStock(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lowStock(profile?.org_id || '') })
    }
  })

  /**
   * Reverse inventory transactions (for invoice/bill cancellation)
   */
  const reverseInventoryTransactions = useMutation({
    mutationFn: async ({
      referenceType,
      referenceId
    }: {
      referenceType: 'invoice' | 'bill'
      referenceId: string
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Get existing transactions for this reference
      const { data: existingTransactions, error: fetchError } = await supabase
        .from('inventory_transactions')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('reference_type', referenceType)
        .eq('reference_id', referenceId)

      if (fetchError) throw fetchError

      if (existingTransactions && existingTransactions.length > 0) {
        // Create reverse transactions
        const reverseTransactions = existingTransactions.map(transaction => ({
          org_id: profile.org_id,
          product_id: transaction.product_id,
          location_id: transaction.location_id,
          transaction_type: 'adjustment' as InventoryTransactionType,
          quantity: -transaction.quantity, // Reverse the quantity
          unit_cost: transaction.unit_cost,
          reference_type: `${referenceType}_reversal`,
          reference_id: referenceId,
          reference_number: `REV-${transaction.reference_number}`,
          reason_code: 'reversal',
          notes: `Reversal of ${referenceType} ${transaction.reference_number}`,
          created_by: profile.id,
          total_cost: transaction.total_cost ? -transaction.total_cost : null
        }))

        const { error: insertError } = await supabase
          .from('inventory_transactions')
          .insert(reverseTransactions)

        if (insertError) throw insertError

        // Update stock levels
        for (const transaction of reverseTransactions) {
          const { error: stockError } = await supabase.rpc('update_stock_level_from_transaction', {
            p_org_id: profile.org_id,
            p_product_id: transaction.product_id,
            p_location_id: transaction.location_id,
            p_quantity_change: transaction.quantity,
            p_unit_cost: transaction.unit_cost || 0
          })

          if (stockError) {
            console.error('Failed to update stock level:', stockError)
          }
        }
      }

      return existingTransactions?.length || 0
    },
    onSuccess: () => {
      // Invalidate inventory-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.withStock(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lowStock(profile?.org_id || '') })
    }
  })

  return {
    processInvoiceInventory,
    processBillInventory,
    reverseInventoryTransactions,
    isProcessingInvoice: processInvoiceInventory.isPending,
    isProcessingBill: processBillInventory.isPending,
    isReversing: reverseInventoryTransactions.isPending
  }
}
