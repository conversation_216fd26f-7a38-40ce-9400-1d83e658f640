
import React, { useState } from 'react'
import { FileUpload } from './FileUpload'
import { AttachmentList } from './AttachmentList'

interface DocumentManagerProps {
  attachedToType: string
  attachedToId: string
}

export function DocumentManager({ attachedToType, attachedToId }: DocumentManagerProps) {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleUploadComplete = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <div className="space-y-6">
      <FileUpload
        attachedToType={attachedToType}
        attachedToId={attachedToId}
        onUploadComplete={handleUploadComplete}
      />
      <AttachmentList
        attachedToType={attachedToType}
        attachedToId={attachedToId}
        refreshTrigger={refreshTrigger}
      />
    </div>
  )
}
