import React from 'react'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useBackupManagement } from '../useBackupManagement'
import { BackupService } from '@/lib/backup-service'
import { supabase } from '@/lib/supabase'

// Mock dependencies
jest.mock('@/lib/backup-service')
jest.mock('@/lib/supabase')
jest.mock('@/hooks/useAuthHook', () => ({
  useAuth: () => ({
    profile: {
      id: 'test-user-id',
      org_id: 'test-org-id',
      role: 'admin'
    }
  })
}))

const mockBackupService = BackupService as jest.Mocked<typeof BackupService>
const mockSupabase = supabase as jest.Mocked<typeof supabase>

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  return ({ children }: { children: React.ReactNode }): JSX.Element => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useBackupManagement', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock Supabase queries
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            data: [],
            error: null
          }),
          single: jest.fn().mockReturnValue({
            data: null,
            error: null
          })
        })
      }),
      insert: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockReturnValue({
            data: { id: 'test-backup-id' },
            error: null
          })
        })
      }),
      update: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          data: null,
          error: null
        })
      }),
      delete: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          data: null,
          error: null
        })
      })
    } as unknown as typeof supabase)
  })

  describe('createBackup', () => {
    it('should create a backup successfully', async () => {
      const mockBackupResult = {
        success: true,
        backup_id: 'test-backup-id',
        message: 'Backup created successfully'
      }

      mockBackupService.createBackup.mockResolvedValue(mockBackupResult)

      const { result } = renderHook(() => useBackupManagement(), {
        wrapper: createWrapper()
      })

      await waitFor(() => {
        expect(result.current.createBackup).toBeDefined()
      })

      // Trigger backup creation
      result.current.createBackup('full')

      await waitFor(() => {
        expect(mockBackupService.createBackup).toHaveBeenCalledWith(
          'test-org-id',
          'full',
          'test-user-id'
        )
      })
    })

    it('should handle backup creation errors', async () => {
      const mockError = new Error('Backup creation failed')
      mockBackupService.createBackup.mockRejectedValue(mockError)

      const { result } = renderHook(() => useBackupManagement(), {
        wrapper: createWrapper()
      })

      await waitFor(() => {
        expect(result.current.createBackup).toBeDefined()
      })

      // Trigger backup creation
      result.current.createBackup('full')

      await waitFor(() => {
        expect(mockBackupService.createBackup).toHaveBeenCalled()
      })
    })
  })

  describe('verifyBackup', () => {
    it('should verify a backup successfully', async () => {
      const mockVerifyResult = {
        valid: true,
        checksum_match: true,
        backup_size: 1024,
        table_count: 5
      }

      mockBackupService.verifyBackup.mockResolvedValue(mockVerifyResult)

      const { result } = renderHook(() => useBackupManagement(), {
        wrapper: createWrapper()
      })

      await waitFor(() => {
        expect(result.current.verifyBackup).toBeDefined()
      })

      // Trigger backup verification
      result.current.verifyBackup('test-backup-id')

      await waitFor(() => {
        expect(mockBackupService.verifyBackup).toHaveBeenCalledWith(
          'test-backup-id',
          'test-org-id'
        )
      })
    })

    it('should handle verification failures', async () => {
      const mockVerifyResult = {
        valid: false,
        checksum_match: false,
        error: 'Checksum mismatch'
      }

      mockBackupService.verifyBackup.mockResolvedValue(mockVerifyResult)

      const { result } = renderHook(() => useBackupManagement(), {
        wrapper: createWrapper()
      })

      await waitFor(() => {
        expect(result.current.verifyBackup).toBeDefined()
      })

      // Trigger backup verification
      result.current.verifyBackup('test-backup-id')

      await waitFor(() => {
        expect(mockBackupService.verifyBackup).toHaveBeenCalled()
      })
    })
  })

  describe('deleteBackup', () => {
    it('should delete a backup successfully', async () => {
      const { result } = renderHook(() => useBackupManagement(), {
        wrapper: createWrapper()
      })

      await waitFor(() => {
        expect(result.current.deleteBackup).toBeDefined()
      })

      // Trigger backup deletion
      result.current.deleteBackup('test-backup-id')

      await waitFor(() => {
        expect(mockSupabase.from).toHaveBeenCalledWith('backup_metadata')
      })
    })
  })

  describe('loading states', () => {
    it('should handle loading states correctly', async () => {
      const { result } = renderHook(() => useBackupManagement(), {
        wrapper: createWrapper()
      })

      // Initially should not be loading (mocked to return immediately)
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })
    })
  })

  describe('error handling', () => {
    it('should handle query errors', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              data: null,
              error: { message: 'Database error' }
            })
          })
        })
      } as unknown as typeof supabase)

      const { result } = renderHook(() => useBackupManagement(), {
        wrapper: createWrapper()
      })

      await waitFor(() => {
        expect(result.current.error).toBeDefined()
      })
    })
  })
})
