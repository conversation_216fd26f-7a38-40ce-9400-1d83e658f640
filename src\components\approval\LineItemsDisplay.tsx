import { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { supabase } from '@/lib/supabase'
import { formatCurrency } from '@/lib/utils'
import type { DocumentType } from '@/types/database'

interface LineItem {
  id: string
  description: string
  quantity?: number
  unit_price?: number
  amount?: number
  tax_rate_pct?: number
  line_total?: number
  tax_amount?: number
  notes?: string
  accounts?: {
    code: string
    name: string
  }
}

interface LineItemsDisplayProps {
  documentType: DocumentType
  documentId: string
  currencyCode: string
}

export function LineItemsDisplay({ 
  documentType, 
  documentId, 
  currencyCode 
}: LineItemsDisplayProps) {
  const [lineItems, setLineItems] = useState<LineItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchLineItems = async () => {
      console.log('🔍 LineItemsDisplay: Fetching line items for:', { documentType, documentId, currencyCode })

      if (!documentId) {
        console.log('❌ LineItemsDisplay: No document ID provided')
        return
      }

      setLoading(true)
      setError(null)

      try {
        type LineItemWithAccount = {
          id: string
          description: string
          quantity?: number
          unit_price?: number
          tax_rate_pct?: number | null
          amount?: number
          notes?: string | null
          accounts?: {
            code: string
            name: string
          } | null
        }

        let data: LineItemWithAccount[] = []

        switch (documentType) {
          case 'invoice': {
            const { data: invoiceLines, error: invoiceError } = await supabase
              .from('invoice_lines')
              .select(`
                id,
                description,
                quantity,
                unit_price,
                tax_rate_pct,
                accounts(code, name)
              `)
              .eq('invoice_id', documentId)
              .order('created_at')

            if (invoiceError) {
              console.error('❌ LineItemsDisplay: Error fetching invoice lines:', invoiceError)
              throw invoiceError
            }
            console.log('✅ LineItemsDisplay: Found invoice lines:', invoiceLines?.length || 0)
            data = invoiceLines || []
            break
          }

          case 'bill': {
            const { data: billLines, error: billError } = await supabase
              .from('bill_lines')
              .select(`
                id,
                description,
                quantity,
                unit_price,
                tax_rate_pct,
                accounts(code, name)
              `)
              .eq('bill_id', documentId)
              .order('created_at')

            if (billError) {
              console.error('❌ LineItemsDisplay: Error fetching bill lines:', billError)
              throw billError
            }
            console.log('✅ LineItemsDisplay: Found bill lines:', billLines?.length || 0)
            data = billLines || []
            break
          }

          case 'budget': {
            const { data: budgetLines, error: budgetError } = await supabase
              .from('budget_lines')
              .select(`
                id,
                amount,
                notes,
                accounts(code, name)
              `)
              .eq('budget_id', documentId)
              .order('created_at')

            if (budgetError) {
              console.error('❌ LineItemsDisplay: Error fetching budget lines:', budgetError)
              throw budgetError
            }
            console.log('✅ LineItemsDisplay: Found budget lines:', budgetLines?.length || 0)
            data = budgetLines || []
            break
          }

          default:
            // For payment and other types, no line items
            data = []
        }

        // Transform data to common format
        const transformedItems: LineItem[] = data.map(item => {
          if (documentType === 'budget') {
            return {
              id: item.id,
              description: item.notes || 'Budget Item',
              amount: item.amount,
              accounts: item.accounts
            }
          } else {
            // For invoices and bills
            const lineTotal = (item.quantity || 0) * (item.unit_price || 0)
            const taxAmount = lineTotal * ((item.tax_rate_pct || 0) / 100)
            
            return {
              id: item.id,
              description: item.description,
              quantity: item.quantity,
              unit_price: item.unit_price,
              tax_rate_pct: item.tax_rate_pct,
              line_total: lineTotal,
              tax_amount: taxAmount,
              accounts: item.accounts
            }
          }
        })

        console.log('✅ LineItemsDisplay: Transformed items:', transformedItems)
        setLineItems(transformedItems)
      } catch (err) {
        console.error('Error fetching line items:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch line items')
      } finally {
        setLoading(false)
      }
    }

    fetchLineItems()
  }, [documentType, documentId, currencyCode])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Line Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Line Items</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">Error loading line items: {error}</p>
        </CardContent>
      </Card>
    )
  }

  if (lineItems.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Line Items</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No line items found for this {documentType}.</p>
        </CardContent>
      </Card>
    )
  }

  const renderBudgetLineItem = (item: LineItem) => (
    <div key={item.id} className="flex justify-between items-center p-3 border rounded-lg">
      <div className="flex-1">
        <div className="font-medium">{item.description}</div>
        {item.accounts && (
          <div className="text-sm text-muted-foreground">
            Account: {item.accounts.code} - {item.accounts.name}
          </div>
        )}
      </div>
      <div className="text-right">
        <div className="font-semibold">{formatCurrency(item.amount || 0, currencyCode)}</div>
      </div>
    </div>
  )

  const renderInvoiceBillLineItem = (item: LineItem) => (
    <div key={item.id} className="p-3 border rounded-lg space-y-2">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="font-medium">{item.description}</div>
          {item.accounts && (
            <div className="text-sm text-muted-foreground">
              Account: {item.accounts.code} - {item.accounts.name}
            </div>
          )}
        </div>
        <div className="text-right">
          <div className="font-semibold">{formatCurrency(item.line_total || 0, currencyCode)}</div>
        </div>
      </div>
      
      <div className="grid grid-cols-3 gap-4 text-sm">
        <div>
          <span className="text-muted-foreground">Qty:</span> {item.quantity || 0}
        </div>
        <div>
          <span className="text-muted-foreground">Unit Price:</span> {formatCurrency(item.unit_price || 0, currencyCode)}
        </div>
        <div>
          <span className="text-muted-foreground">Tax:</span> {item.tax_rate_pct || 0}%
          {item.tax_amount && item.tax_amount > 0 && (
            <span className="ml-1">({formatCurrency(item.tax_amount, currencyCode)})</span>
          )}
        </div>
      </div>
    </div>
  )

  const totalAmount = lineItems.reduce((sum, item) => {
    if (documentType === 'budget') {
      return sum + (item.amount || 0)
    } else {
      return sum + (item.line_total || 0)
    }
  }, 0)

  const totalTax = lineItems.reduce((sum, item) => sum + (item.tax_amount || 0), 0)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Line Items
          <Badge variant="outline">{lineItems.length} items</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {lineItems.map(item => 
            documentType === 'budget' 
              ? renderBudgetLineItem(item)
              : renderInvoiceBillLineItem(item)
          )}
        </div>
        
        {/* Summary */}
        <div className="mt-4 pt-4 border-t space-y-2">
          <div className="flex justify-between text-sm">
            <span>Subtotal:</span>
            <span>{formatCurrency(totalAmount, currencyCode)}</span>
          </div>
          {totalTax > 0 && (
            <div className="flex justify-between text-sm">
              <span>Tax:</span>
              <span>{formatCurrency(totalTax, currencyCode)}</span>
            </div>
          )}
          <div className="flex justify-between font-semibold">
            <span>Total:</span>
            <span>{formatCurrency(totalAmount + totalTax, currencyCode)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
