-- =====================================================
-- RLS CLEANUP SCRIPT - RUN BEFORE MAIN FIX
-- =====================================================
-- Migration: 20241231_rls_cleanup_before_fix.sql
-- Description: Comprehensive cleanup of existing RLS policies
-- Purpose: Ensures clean slate before applying the complete RLS security fix
-- Author: Kaya Finance Team
-- Date: 2024-12-31

-- =====================================================
-- COMPREHENSIVE POLICY CLEANUP
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🧹 KAYA FINANCE - RLS POLICY CLEANUP';
    RAISE NOTICE '';
    RAISE NOTICE '📋 Removing all existing RLS policies to ensure clean slate...';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- DROP ALL EXISTING POLICIES BY TABLE
-- =====================================================

-- Organizations policies
DROP POLICY IF EXISTS "Users can view their organization" ON organizations;
DROP POLICY IF EXISTS "Owners and admins can update their organization" ON organizations;
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON organizations;
DROP POLICY IF EXISTS "Users can manage their organization" ON organizations;
DROP POLICY IF EXISTS "Organization owners can update" ON organizations;

-- Profiles policies
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Users can view org profiles" ON profiles;
DROP POLICY IF EXISTS "Users can view their own profile only" ON profiles;
DROP POLICY IF EXISTS "Users can manage their own profile" ON profiles;

-- Customers policies
DROP POLICY IF EXISTS "Users can view customers in their organization" ON customers;
DROP POLICY IF EXISTS "Users can manage customers in their organization" ON customers;
DROP POLICY IF EXISTS "Users can access customers in their organization" ON customers;

-- Vendors policies
DROP POLICY IF EXISTS "Users can view vendors in their organization" ON vendors;
DROP POLICY IF EXISTS "Users can manage vendors in their organization" ON vendors;
DROP POLICY IF EXISTS "Users can access vendors in their organization" ON vendors;

-- Invoices policies
DROP POLICY IF EXISTS "Users can view invoices in their organization" ON invoices;
DROP POLICY IF EXISTS "Users can manage invoices in their organization" ON invoices;
DROP POLICY IF EXISTS "Users can access invoices in their organization" ON invoices;

-- Invoice lines policies
DROP POLICY IF EXISTS "Users can view invoice lines in their organization" ON invoice_lines;
DROP POLICY IF EXISTS "Users can manage invoice lines in their organization" ON invoice_lines;
DROP POLICY IF EXISTS "Users can access invoice lines in their organization" ON invoice_lines;

-- Bills policies
DROP POLICY IF EXISTS "Users can view bills in their organization" ON bills;
DROP POLICY IF EXISTS "Users can manage bills in their organization" ON bills;
DROP POLICY IF EXISTS "Users can access bills in their organization" ON bills;

-- Bill lines policies
DROP POLICY IF EXISTS "Users can view bill lines in their organization" ON bill_lines;
DROP POLICY IF EXISTS "Users can manage bill lines in their organization" ON bill_lines;
DROP POLICY IF EXISTS "Users can access bill lines in their organization" ON bill_lines;

-- Payments policies
DROP POLICY IF EXISTS "Users can view payments in their organization" ON payments;
DROP POLICY IF EXISTS "Users can manage payments in their organization" ON payments;
DROP POLICY IF EXISTS "Users can access payments in their organization" ON payments;

-- Payment applications policies
DROP POLICY IF EXISTS "Users can view payment applications in their organization" ON payment_applications;
DROP POLICY IF EXISTS "Accountants and above can manage payment applications" ON payment_applications;
DROP POLICY IF EXISTS "Users can access payment applications in their organization" ON payment_applications;

-- Bank accounts policies
DROP POLICY IF EXISTS "Users can view bank accounts in their organization" ON bank_accounts;
DROP POLICY IF EXISTS "Admins and above can manage bank accounts" ON bank_accounts;
DROP POLICY IF EXISTS "Users can access bank accounts in their organization" ON bank_accounts;

-- Mobile money accounts policies
DROP POLICY IF EXISTS "Users can view mobile money accounts in their organization" ON mobile_money_accounts;
DROP POLICY IF EXISTS "Admins and above can manage mobile money accounts" ON mobile_money_accounts;
DROP POLICY IF EXISTS "Users can access mobile money accounts in their organization" ON mobile_money_accounts;

-- Bank transactions policies
DROP POLICY IF EXISTS "Users can view bank transactions in their organization" ON bank_transactions;
DROP POLICY IF EXISTS "Accountants and above can manage bank transactions" ON bank_transactions;
DROP POLICY IF EXISTS "Users can access bank transactions in their organization" ON bank_transactions;

-- Tax rates policies
DROP POLICY IF EXISTS "Users can view tax rates in their organization" ON tax_rates;
DROP POLICY IF EXISTS "Admins and above can manage tax rates" ON tax_rates;
DROP POLICY IF EXISTS "Users can access tax rates in their organization" ON tax_rates;

-- Budgets policies
DROP POLICY IF EXISTS "Users can view budgets in their organization" ON budgets;
DROP POLICY IF EXISTS "Accountants and above can manage budgets" ON budgets;
DROP POLICY IF EXISTS "Users can access budgets in their organization" ON budgets;

-- Budget lines policies
DROP POLICY IF EXISTS "Users can view budget lines in their organization" ON budget_lines;
DROP POLICY IF EXISTS "Accountants and above can manage budget lines" ON budget_lines;
DROP POLICY IF EXISTS "Users can access budget lines in their organization" ON budget_lines;

-- Budget approvals policies
DROP POLICY IF EXISTS "Users can view budget approvals in their organization" ON budget_approvals;
DROP POLICY IF EXISTS "Admins and above can manage budget approvals" ON budget_approvals;
DROP POLICY IF EXISTS "Users can access budget approvals in their organization" ON budget_approvals;

-- Recurring journals policies
DROP POLICY IF EXISTS "Users can view recurring journals in their organization" ON recurring_journals;
DROP POLICY IF EXISTS "Accountants and above can manage recurring journals" ON recurring_journals;
DROP POLICY IF EXISTS "Users can access recurring journals in their organization" ON recurring_journals;

-- Recurring lines policies
DROP POLICY IF EXISTS "Users can view recurring lines in their organization" ON recurring_lines;
DROP POLICY IF EXISTS "Accountants and above can manage recurring lines" ON recurring_lines;
DROP POLICY IF EXISTS "Users can access recurring lines in their organization" ON recurring_lines;

-- URA tax filings policies
DROP POLICY IF EXISTS "Users can view URA tax filings in their organization" ON ura_tax_filings;
DROP POLICY IF EXISTS "Accountants and above can manage URA tax filings" ON ura_tax_filings;
DROP POLICY IF EXISTS "Users can access URA tax filings in their organization" ON ura_tax_filings;

-- Attachments policies
DROP POLICY IF EXISTS "Users can view attachments in their organization" ON attachments;
DROP POLICY IF EXISTS "Users can manage attachments in their organization" ON attachments;
DROP POLICY IF EXISTS "Users can access attachments in their organization" ON attachments;

-- Accounts policies (if they exist)
DROP POLICY IF EXISTS "Users can view accounts in their organization" ON accounts;
DROP POLICY IF EXISTS "Users can manage accounts in their organization" ON accounts;
DROP POLICY IF EXISTS "Users can access accounts in their organization" ON accounts;

-- Journal entries policies (if they exist)
DROP POLICY IF EXISTS "Users can view journal entries in their organization" ON journal_entries;
DROP POLICY IF EXISTS "Users can manage journal entries in their organization" ON journal_entries;
DROP POLICY IF EXISTS "Users can access journal entries in their organization" ON journal_entries;

-- Transaction lines policies (if they exist)
DROP POLICY IF EXISTS "Users can view transaction lines in their organization" ON transaction_lines;
DROP POLICY IF EXISTS "Users can manage transaction lines in their organization" ON transaction_lines;
DROP POLICY IF EXISTS "Users can access transaction lines in their organization" ON transaction_lines;

-- Audit logs policies (if they exist)
DROP POLICY IF EXISTS "Users can view audit logs in their organization" ON audit_logs;
DROP POLICY IF EXISTS "Users can manage audit logs in their organization" ON audit_logs;
DROP POLICY IF EXISTS "Users can access audit logs in their organization" ON audit_logs;

-- Notifications policies (if they exist)
DROP POLICY IF EXISTS "Users can view notifications in their organization" ON notifications;
DROP POLICY IF EXISTS "Users can manage notifications in their organization" ON notifications;
DROP POLICY IF EXISTS "Users can access notifications in their organization" ON notifications;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    total_policies INTEGER := 0;
    table_record RECORD;
BEGIN
    RAISE NOTICE '📋 Cleanup verification...';
    RAISE NOTICE '';
    
    -- Count remaining policies on our target tables
    SELECT COUNT(*) INTO total_policies
    FROM pg_policies 
    WHERE schemaname = 'public'
    AND tablename IN (
        'organizations', 'profiles', 'customers', 'vendors', 'invoices', 'invoice_lines',
        'bills', 'bill_lines', 'payments', 'payment_applications', 'bank_accounts', 
        'mobile_money_accounts', 'bank_transactions', 'tax_rates', 'budgets', 
        'budget_lines', 'budget_approvals', 'recurring_journals', 'recurring_lines', 
        'ura_tax_filings', 'attachments', 'accounts', 'journal_entries', 
        'transaction_lines', 'audit_logs', 'notifications'
    );
    
    RAISE NOTICE '✅ RLS POLICY CLEANUP COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '📊 RESULTS:';
    RAISE NOTICE '  ✅ Remaining policies on target tables: %', total_policies;
    RAISE NOTICE '';
    
    IF total_policies = 0 THEN
        RAISE NOTICE '🎉 PERFECT! All policies cleaned up successfully.';
        RAISE NOTICE '    Ready to run the main RLS security fix migration.';
    ELSE
        RAISE NOTICE '⚠️  Some policies still remain. This is normal if:';
        RAISE NOTICE '    - They are on tables not in our target list';
        RAISE NOTICE '    - They have different names than expected';
        RAISE NOTICE '    - They were created by other migrations';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '1. Run the main RLS security fix migration';
    RAISE NOTICE '2. Test the onboarding flow';
    RAISE NOTICE '3. Verify all application functionality';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- CLEANUP COMPLETE
-- =====================================================
