import { useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import type { Payment, Customer, Vendor } from '@/types/database'
import type { PaymentWithDetails } from '@/hooks/queries/usePayments'

interface UsePaymentEnhancementOptions {
  orgId: string
}

/**
 * Custom hook for enhancing payment data with related information
 * This separates the enhancement logic for better testability and reusability
 */
export function usePaymentEnhancement({ orgId }: UsePaymentEnhancementOptions) {
  const enhancePayment = useCallback(async (
    payment: Payment,
    customers: Customer[],
    vendors: Vendor[]
  ): Promise<PaymentWithDetails> => {
    try {
      // Enhance payee name
      let payee_name = 'Unknown'
      
      if (payment.payee_type === 'customer') {
        const customer = customers.find(c => c.id === payment.payee_id)
        payee_name = customer?.name || 'Unknown Customer'
      } else if (payment.payee_type === 'vendor') {
        const vendor = vendors.find(v => v.id === payment.payee_id)
        payee_name = vendor?.name || 'Unknown Vendor'
      }

      // Calculate applications total
      const applications_total = payment.payment_applications?.reduce(
        (sum, app) => sum + (app.amount_applied || 0), 0
      ) || 0

      // Fetch related documents with proper error handling
      const related_documents = []
      if (payment.payment_applications) {
        for (const app of payment.payment_applications) {
          try {
            if (app.applied_to_type === 'invoice') {
              const { data: invoice, error } = await supabase
                .from('invoices')
                .select('invoice_number, total_amount')
                .eq('id', app.applied_to_id)
                .eq('org_id', orgId)
                .single()

              if (error) {
                console.warn(`Failed to fetch invoice ${app.applied_to_id}:`, error)
                // Fallback to basic info
                related_documents.push({
                  type: 'invoice' as const,
                  id: app.applied_to_id || '',
                  number: `INV-${app.applied_to_id}`,
                  amount: app.amount_applied || 0
                })
                continue
              }

              if (invoice) {
                related_documents.push({
                  type: 'invoice' as const,
                  id: app.applied_to_id || '',
                  number: invoice.invoice_number,
                  amount: app.amount_applied || 0
                })
              }
            } else if (app.applied_to_type === 'bill') {
              const { data: bill, error } = await supabase
                .from('bills')
                .select('bill_number, total_amount')
                .eq('id', app.applied_to_id)
                .eq('org_id', orgId)
                .single()

              if (error) {
                console.warn(`Failed to fetch bill ${app.applied_to_id}:`, error)
                // Fallback to basic info
                related_documents.push({
                  type: 'bill' as const,
                  id: app.applied_to_id || '',
                  number: `BILL-${app.applied_to_id}`,
                  amount: app.amount_applied || 0
                })
                continue
              }

              if (bill) {
                related_documents.push({
                  type: 'bill' as const,
                  id: app.applied_to_id || '',
                  number: bill.bill_number,
                  amount: app.amount_applied || 0
                })
              }
            }
          } catch (docError) {
            console.warn(`Error fetching related document for payment ${payment.id}:`, docError)
            // Add fallback document info
            related_documents.push({
              type: (app.applied_to_type as 'invoice' | 'bill') || 'unknown',
              id: app.applied_to_id || '',
              number: `${app.applied_to_type?.toUpperCase()}-${app.applied_to_id}`,
              amount: app.amount_applied || 0
            })
          }
        }
      }

      return {
        ...payment,
        payee_name,
        applications_total,
        related_documents
      }
    } catch (enhanceError) {
      console.warn(`Error enhancing payment ${payment.id}:`, enhanceError)
      // Return basic payment data if enhancement fails
      return {
        ...payment,
        payee_name: 'Unknown',
        applications_total: 0,
        related_documents: []
      }
    }
  }, [orgId])

  const enhancePayments = useCallback(async (
    payments: Payment[],
    customers: Customer[],
    vendors: Vendor[]
  ): Promise<PaymentWithDetails[]> => {
    return Promise.all(
      payments.map(payment => enhancePayment(payment, customers, vendors))
    )
  }, [enhancePayment])

  return {
    enhancePayment,
    enhancePayments
  }
}
