// Pre-Restore Safety Checks and Validation System
// Provides comprehensive safety checks before backup restoration

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'
import { BackupValidator } from './backup-validator'
import { BackupErrorHandler } from './backup-error-handler'

export interface RestoreSafetyCheck {
  id: string
  name: string
  description: string
  category: 'compatibility' | 'safety' | 'validation' | 'permission'
  severity: 'info' | 'warning' | 'error' | 'critical'
  passed: boolean
  message: string
  details?: Record<string, unknown>
  blocking: boolean // If true, prevents restoration
}

export interface RestoreSafetyResult {
  safe: boolean
  score: number // 0-100 safety score
  checks: RestoreSafetyCheck[]
  summary: {
    totalChecks: number
    passedChecks: number
    failedChecks: number
    blockingIssues: number
    warnings: number
  }
  recommendations: string[]
  requiresConfirmation: boolean
  confirmationMessage?: string
}

export interface RestoreContext {
  backupId: string
  orgId: string
  userId: string
  restoreMode: 'replace' | 'merge' | 'preview'
  selectedTables?: string[]
  restorePoint?: string
  notes?: string
}

/**
 * Pre-Restore Safety Checker
 */
export class RestoreSafetyChecker {
  /**
   * Perform comprehensive pre-restore safety checks
   */
  static async performSafetyChecks(context: RestoreContext): Promise<RestoreSafetyResult> {
    const checks: RestoreSafetyCheck[] = []
    const recommendations: string[] = []

    try {
      // 1. Backup validation checks
      const validationChecks = await this.performBackupValidationChecks(context)
      checks.push(...validationChecks.checks)
      recommendations.push(...validationChecks.recommendations)

      // 2. Compatibility checks
      const compatibilityChecks = await this.performCompatibilityChecks(context)
      checks.push(...compatibilityChecks.checks)
      recommendations.push(...compatibilityChecks.recommendations)

      // 3. Safety checks
      const safetyChecks = await this.performSafetyChecks(context)
      checks.push(...safetyChecks.checks)
      recommendations.push(...safetyChecks.recommendations)

      // 4. Permission checks
      const permissionChecks = await this.performPermissionChecks(context)
      checks.push(...permissionChecks.checks)
      recommendations.push(...permissionChecks.recommendations)

      // 5. Data impact assessment
      const impactChecks = await this.performDataImpactAssessment(context)
      checks.push(...impactChecks.checks)
      recommendations.push(...impactChecks.recommendations)

      // Calculate safety score and summary
      const summary = this.calculateSummary(checks)
      const score = this.calculateSafetyScore(checks)
      const safe = summary.blockingIssues === 0
      const requiresConfirmation = this.requiresConfirmation(context, checks)

      // Log safety check results
      await this.logSafetyCheckResults(context, { safe, score, summary })

      return {
        safe,
        score,
        checks,
        summary,
        recommendations: [...new Set(recommendations)], // Remove duplicates
        requiresConfirmation,
        confirmationMessage: requiresConfirmation ? this.generateConfirmationMessage(context, checks) : undefined
      }

    } catch (error) {
      await BackupErrorHandler.handleError(error, {
        operation: 'pre_restore_safety_check',
        orgId: context.orgId,
        userId: context.userId,
        backupId: context.backupId
      })

      // Return failed safety check
      return {
        safe: false,
        score: 0,
        checks: [{
          id: 'safety_check_error',
          name: 'Safety Check Error',
          description: 'Failed to perform safety checks',
          category: 'safety',
          severity: 'critical',
          passed: false,
          message: error instanceof Error ? error.message : 'Unknown error',
          blocking: true
        }],
        summary: {
          totalChecks: 1,
          passedChecks: 0,
          failedChecks: 1,
          blockingIssues: 1,
          warnings: 0
        },
        recommendations: ['Contact support to resolve safety check issues'],
        requiresConfirmation: false
      }
    }
  }

  /**
   * Perform backup validation checks
   */
  private static async performBackupValidationChecks(context: RestoreContext): Promise<{
    checks: RestoreSafetyCheck[]
    recommendations: string[]
  }> {
    const checks: RestoreSafetyCheck[] = []
    const recommendations: string[] = []

    try {
      // Validate backup integrity
      const validationResult = await BackupValidator.validateBackup(context.backupId, context.orgId, {
        checkChecksum: true,
        checkDataIntegrity: true,
        checkCompleteness: true,
        checkEncryption: true
      })

      checks.push({
        id: 'backup_validation',
        name: 'Backup Validation',
        description: 'Validates backup integrity and completeness',
        category: 'validation',
        severity: validationResult.valid ? 'info' : 'error',
        passed: validationResult.valid,
        message: validationResult.valid 
          ? `Backup validation passed (score: ${validationResult.score}%)`
          : `Backup validation failed: ${validationResult.issues.length} issues found`,
        details: { validation_result: validationResult },
        blocking: !validationResult.valid
      })

      if (!validationResult.valid) {
        recommendations.push('Resolve backup validation issues before attempting restoration')
        
        // Add specific recommendations based on validation issues
        if (validationResult.issues.some(i => i.category === 'checksum')) {
          recommendations.push('Backup file may be corrupted - consider using a different backup')
        }
        if (validationResult.issues.some(i => i.category === 'completeness')) {
          recommendations.push('Backup appears incomplete - verify all required data is present')
        }
      }

    } catch (error) {
      checks.push({
        id: 'backup_validation_error',
        name: 'Backup Validation Error',
        description: 'Failed to validate backup',
        category: 'validation',
        severity: 'critical',
        passed: false,
        message: 'Could not validate backup integrity',
        blocking: true
      })
      recommendations.push('Backup validation failed - contact support')
    }

    return { checks, recommendations }
  }

  /**
   * Perform compatibility checks
   */
  private static async performCompatibilityChecks(context: RestoreContext): Promise<{
    checks: RestoreSafetyCheck[]
    recommendations: string[]
  }> {
    const checks: RestoreSafetyCheck[] = []
    const recommendations: string[] = []

    try {
      // Get backup metadata
      const { data: backup, error } = await supabase
        .from('backup_metadata')
        .select('*')
        .eq('id', context.backupId)
        .single()

      if (error || !backup) {
        checks.push({
          id: 'backup_metadata_missing',
          name: 'Backup Metadata',
          description: 'Check backup metadata availability',
          category: 'compatibility',
          severity: 'critical',
          passed: false,
          message: 'Backup metadata not found',
          blocking: true
        })
        return { checks, recommendations }
      }

      // Check backup age
      const backupAge = Date.now() - new Date(backup.created_at).getTime()
      const daysSinceBackup = Math.floor(backupAge / (1000 * 60 * 60 * 24))
      
      checks.push({
        id: 'backup_age',
        name: 'Backup Age',
        description: 'Check if backup is recent enough for safe restoration',
        category: 'compatibility',
        severity: daysSinceBackup > 30 ? 'warning' : 'info',
        passed: daysSinceBackup <= 90,
        message: `Backup is ${daysSinceBackup} days old`,
        details: { days_old: daysSinceBackup, created_at: backup.created_at },
        blocking: daysSinceBackup > 90
      })

      if (daysSinceBackup > 30) {
        recommendations.push('Consider using a more recent backup if available')
      }

      // Check backup type compatibility
      const isFullBackup = backup.backup_type === 'full'
      checks.push({
        id: 'backup_type_compatibility',
        name: 'Backup Type Compatibility',
        description: 'Check if backup type is suitable for restoration',
        category: 'compatibility',
        severity: isFullBackup ? 'info' : 'warning',
        passed: true,
        message: `Backup type: ${backup.backup_type}`,
        details: { backup_type: backup.backup_type },
        blocking: false
      })

      if (!isFullBackup && context.restoreMode === 'replace') {
        recommendations.push('Consider using a full backup for replace mode restoration')
      }

      // Check organization compatibility
      checks.push({
        id: 'organization_compatibility',
        name: 'Organization Compatibility',
        description: 'Verify backup belongs to the correct organization',
        category: 'compatibility',
        severity: 'critical',
        passed: backup.org_id === context.orgId,
        message: backup.org_id === context.orgId 
          ? 'Backup belongs to current organization'
          : 'Backup belongs to different organization',
        blocking: backup.org_id !== context.orgId
      })

    } catch (error) {
      checks.push({
        id: 'compatibility_check_error',
        name: 'Compatibility Check Error',
        description: 'Failed to perform compatibility checks',
        category: 'compatibility',
        severity: 'error',
        passed: false,
        message: 'Could not verify backup compatibility',
        blocking: true
      })
    }

    return { checks, recommendations }
  }

  /**
   * Perform safety checks
   */
  private static async performSafetyChecks(context: RestoreContext): Promise<{
    checks: RestoreSafetyCheck[]
    recommendations: string[]
  }> {
    const checks: RestoreSafetyCheck[] = []
    const recommendations: string[] = []

    // Check for active users/sessions
    const { data: activeSessions } = await supabase
      .from('profiles')
      .select('id')
      .eq('org_id', context.orgId)
      .gte('last_seen_at', new Date(Date.now() - 15 * 60 * 1000).toISOString()) // Last 15 minutes

    const activeUserCount = activeSessions?.length || 0
    checks.push({
      id: 'active_users',
      name: 'Active Users Check',
      description: 'Check for currently active users',
      category: 'safety',
      severity: activeUserCount > 1 ? 'warning' : 'info',
      passed: true,
      message: `${activeUserCount} active users detected`,
      details: { active_user_count: activeUserCount },
      blocking: false
    })

    if (activeUserCount > 1 && context.restoreMode === 'replace') {
      recommendations.push('Consider notifying other users before performing replace mode restoration')
    }

    // Check for recent data changes
    const { data: recentChanges } = await supabase
      .from('audit_logs')
      .select('id')
      .eq('org_id', context.orgId)
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour
      .limit(10)

    const hasRecentChanges = (recentChanges?.length || 0) > 0
    checks.push({
      id: 'recent_data_changes',
      name: 'Recent Data Changes',
      description: 'Check for recent data modifications',
      category: 'safety',
      severity: hasRecentChanges && context.restoreMode === 'replace' ? 'warning' : 'info',
      passed: true,
      message: hasRecentChanges 
        ? `${recentChanges?.length} recent changes detected`
        : 'No recent changes detected',
      details: { recent_changes_count: recentChanges?.length || 0 },
      blocking: false
    })

    if (hasRecentChanges && context.restoreMode === 'replace') {
      recommendations.push('Recent data changes will be lost in replace mode - consider creating a current backup first')
    }

    // Check restore mode safety
    const isReplaceMode = context.restoreMode === 'replace'
    checks.push({
      id: 'restore_mode_safety',
      name: 'Restore Mode Safety',
      description: 'Assess safety of selected restore mode',
      category: 'safety',
      severity: isReplaceMode ? 'warning' : 'info',
      passed: true,
      message: `Restore mode: ${context.restoreMode}`,
      details: { restore_mode: context.restoreMode, destructive: isReplaceMode },
      blocking: false
    })

    if (isReplaceMode) {
      recommendations.push('Replace mode will permanently delete current data - ensure you have a recent backup')
    }

    return { checks, recommendations }
  }

  /**
   * Perform permission checks
   */
  private static async performPermissionChecks(context: RestoreContext): Promise<{
    checks: RestoreSafetyCheck[]
    recommendations: string[]
  }> {
    const checks: RestoreSafetyCheck[] = []
    const recommendations: string[] = []

    try {
      // Get user profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', context.userId)
        .eq('org_id', context.orgId)
        .single()

      if (!profile) {
        checks.push({
          id: 'user_profile_missing',
          name: 'User Profile',
          description: 'Verify user profile exists',
          category: 'permission',
          severity: 'critical',
          passed: false,
          message: 'User profile not found',
          blocking: true
        })
        return { checks, recommendations }
      }

      // Check role permissions
      const hasRestorePermission = ['owner', 'admin'].includes(profile.role)
      const hasReplacePermission = profile.role === 'owner' || 
        (profile.role === 'admin' && context.restoreMode !== 'replace')

      checks.push({
        id: 'restore_permission',
        name: 'Restore Permission',
        description: 'Check user permission to perform restoration',
        category: 'permission',
        severity: hasRestorePermission ? 'info' : 'critical',
        passed: hasRestorePermission,
        message: hasRestorePermission 
          ? `User role '${profile.role}' has restore permission`
          : `User role '${profile.role}' lacks restore permission`,
        details: { user_role: profile.role },
        blocking: !hasRestorePermission
      })

      if (context.restoreMode === 'replace') {
        checks.push({
          id: 'replace_mode_permission',
          name: 'Replace Mode Permission',
          description: 'Check permission for destructive replace mode',
          category: 'permission',
          severity: hasReplacePermission ? 'info' : 'critical',
          passed: hasReplacePermission,
          message: hasReplacePermission 
            ? 'User authorized for replace mode restoration'
            : 'User not authorized for replace mode restoration',
          blocking: !hasReplacePermission
        })

        if (!hasReplacePermission) {
          recommendations.push('Replace mode requires owner privileges or admin approval')
        }
      }

    } catch (error) {
      checks.push({
        id: 'permission_check_error',
        name: 'Permission Check Error',
        description: 'Failed to verify user permissions',
        category: 'permission',
        severity: 'critical',
        passed: false,
        message: 'Could not verify user permissions',
        blocking: true
      })
    }

    return { checks, recommendations }
  }

  /**
   * Perform data impact assessment
   */
  private static async performDataImpactAssessment(context: RestoreContext): Promise<{
    checks: RestoreSafetyCheck[]
    recommendations: string[]
  }> {
    const checks: RestoreSafetyCheck[] = []
    const recommendations: string[] = []

    try {
      // Estimate data that will be affected
      const tablesToRestore = context.selectedTables || [
        'customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts'
      ]

      let totalRecordsAffected = 0
      for (const table of tablesToRestore) {
        const { count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true })
          .eq('org_id', context.orgId)

        totalRecordsAffected += count || 0
      }

      checks.push({
        id: 'data_impact_assessment',
        name: 'Data Impact Assessment',
        description: 'Estimate amount of data that will be affected',
        category: 'safety',
        severity: totalRecordsAffected > 10000 ? 'warning' : 'info',
        passed: true,
        message: `Approximately ${totalRecordsAffected} records will be affected`,
        details: { 
          affected_records: totalRecordsAffected,
          affected_tables: tablesToRestore.length,
          restore_mode: context.restoreMode
        },
        blocking: false
      })

      if (totalRecordsAffected > 10000) {
        recommendations.push('Large amount of data will be affected - consider performing restoration during off-peak hours')
      }

      if (context.restoreMode === 'replace' && totalRecordsAffected > 1000) {
        recommendations.push('Create a backup of current data before proceeding with replace mode')
      }

    } catch (error) {
      checks.push({
        id: 'impact_assessment_error',
        name: 'Impact Assessment Error',
        description: 'Failed to assess data impact',
        category: 'safety',
        severity: 'warning',
        passed: false,
        message: 'Could not estimate data impact',
        blocking: false
      })
    }

    return { checks, recommendations }
  }

  /**
   * Calculate summary statistics
   */
  private static calculateSummary(checks: RestoreSafetyCheck[]): {
    totalChecks: number
    passedChecks: number
    failedChecks: number
    blockingIssues: number
    warnings: number
  } {
    return {
      totalChecks: checks.length,
      passedChecks: checks.filter(c => c.passed).length,
      failedChecks: checks.filter(c => !c.passed).length,
      blockingIssues: checks.filter(c => c.blocking && !c.passed).length,
      warnings: checks.filter(c => c.severity === 'warning').length
    }
  }

  /**
   * Calculate safety score
   */
  private static calculateSafetyScore(checks: RestoreSafetyCheck[]): number {
    if (checks.length === 0) return 0

    const weights = {
      critical: 10,
      error: 5,
      warning: 2,
      info: 1
    }

    let totalWeight = 0
    let passedWeight = 0

    checks.forEach(check => {
      const weight = weights[check.severity]
      totalWeight += weight
      if (check.passed) {
        passedWeight += weight
      }
    })

    return totalWeight > 0 ? Math.round((passedWeight / totalWeight) * 100) : 0
  }

  /**
   * Determine if confirmation is required
   */
  private static requiresConfirmation(context: RestoreContext, checks: RestoreSafetyCheck[]): boolean {
    // Always require confirmation for replace mode
    if (context.restoreMode === 'replace') return true

    // Require confirmation if there are warnings or failed checks
    return checks.some(c => c.severity === 'warning' || !c.passed)
  }

  /**
   * Generate confirmation message
   */
  private static generateConfirmationMessage(context: RestoreContext, checks: RestoreSafetyCheck[]): string {
    const warnings = checks.filter(c => c.severity === 'warning' || (c.severity === 'error' && !c.blocking))
    const blockingIssues = checks.filter(c => c.blocking && !c.passed)

    if (blockingIssues.length > 0) {
      return `Cannot proceed: ${blockingIssues.length} blocking issues must be resolved first.`
    }

    let message = `You are about to restore data using ${context.restoreMode} mode.`

    if (context.restoreMode === 'replace') {
      message += ' This will permanently delete all current data and replace it with the backup data.'
    }

    if (warnings.length > 0) {
      message += ` There are ${warnings.length} warnings that require your attention.`
    }

    message += ' Do you want to proceed?'

    return message
  }

  /**
   * Log safety check results
   */
  private static async logSafetyCheckResults(
    context: RestoreContext,
    results: { safe: boolean; score: number; summary: Record<string, unknown> }
  ): Promise<void> {
    try {
      await auditLogger.logActivity({
        entity_type: 'backup_restore_safety',
        entity_id: context.backupId,
        action: 'safety_check_completed',
        description: `Pre-restore safety check completed: ${results.safe ? 'SAFE' : 'UNSAFE'} (score: ${results.score}%)`,
        severity: results.safe ? 'info' : 'warning',
        category: 'security',
        metadata: {
          ...context,
          safety_score: results.score,
          summary: results.summary
        }
      })
    } catch (error) {
      console.error('Failed to log safety check results:', error)
    }
  }

  /**
   * Quick safety check for simple validations
   */
  static async quickSafetyCheck(backupId: string, orgId: string, userId: string): Promise<{
    safe: boolean
    message: string
    issues: string[]
  }> {
    try {
      const context: RestoreContext = {
        backupId,
        orgId,
        userId,
        restoreMode: 'preview' // Use preview mode for quick check
      }

      const result = await this.performSafetyChecks(context)

      return {
        safe: result.safe,
        message: result.safe
          ? 'Backup appears safe for restoration'
          : `${result.summary.blockingIssues} blocking issues found`,
        issues: result.checks
          .filter(c => !c.passed && c.blocking)
          .map(c => c.message)
      }
    } catch (error) {
      return {
        safe: false,
        message: 'Safety check failed',
        issues: ['Could not perform safety validation']
      }
    }
  }
}
