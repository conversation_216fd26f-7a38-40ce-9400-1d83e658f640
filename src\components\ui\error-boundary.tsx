import React, { Component, ErrorInfo, ReactNode } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Al<PERSON><PERSON>riangle, Refresh<PERSON>w, Home, Copy, Bug } from 'lucide-react'
import { errorHand<PERSON> } from '@/lib/errorHandler'
import { logger } from '@/lib/logger'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  component?: string
  showDetails?: boolean
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
  errorId?: string
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Generate error ID and log to our error handling system
    const errorId = errorHandler.handleError(
      error,
      {
        component: this.props.component || 'ErrorBoundary',
        action: 'componentDidCatch',
        metadata: {
          componentStack: errorInfo.componentStack,
          errorBoundary: true
        }
      },
      'high',
      'system',
      true
    )

    // Log to our logging system
    logger.error('React Error Boundary caught an error', {
      component: this.props.component || 'ErrorBoundary',
      action: 'componentDidCatch',
      metadata: {
        errorId,
        errorMessage: error.message,
        errorStack: error.stack,
        componentStack: errorInfo.componentStack
      }
    })

    this.setState({
      errorInfo,
      errorId
    })

    this.props.onError?.(error, errorInfo)
  }

  private handleRetry = () => {
    logger.info('Error boundary retry attempted', {
      component: this.props.component || 'ErrorBoundary',
      action: 'retry',
      metadata: { errorId: this.state.errorId }
    })
    this.setState({ hasError: false, error: undefined, errorInfo: undefined, errorId: undefined })
  }

  private handleCopyError = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString()
    }

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2)).then(() => {
      console.log('Error details copied to clipboard')
    }).catch(() => {
      console.warn('Failed to copy error details')
    })
  }

  private handleGoHome = () => {
    logger.info('Error boundary navigation to home', {
      component: this.props.component || 'ErrorBoundary',
      action: 'goHome',
      metadata: { errorId: this.state.errorId }
    })
    window.location.href = '/'
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card className="max-w-2xl mx-auto mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="w-5 h-5" />
              Something went wrong
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              An unexpected error occurred while loading this page. Our team has been notified and is working to fix this issue.
            </p>

            {this.state.errorId && (
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm font-medium">Error ID: {this.state.errorId}</p>
                <p className="text-xs text-muted-foreground">
                  Please include this ID when contacting support.
                </p>
              </div>
            )}

            {this.state.error && this.props.showDetails && (
              <details className="text-sm">
                <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                  <Bug className="w-4 h-4 inline mr-1" />
                  Technical details
                </summary>
                <div className="mt-2 space-y-2">
                  <div>
                    <p className="font-medium text-xs text-muted-foreground">Error Message:</p>
                    <pre className="p-2 bg-muted rounded text-xs overflow-auto">
                      {this.state.error.message}
                    </pre>
                  </div>
                  {this.state.error.stack && (
                    <div>
                      <p className="font-medium text-xs text-muted-foreground">Stack Trace:</p>
                      <pre className="p-2 bg-muted rounded text-xs overflow-auto max-h-32">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            <div className="flex flex-wrap gap-2">
              <Button onClick={this.handleRetry} className="flex items-center gap-2">
                <RefreshCw className="w-4 h-4" />
                Try Again
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </Button>
              <Button
                variant="outline"
                onClick={this.handleGoHome}
                className="flex items-center gap-2"
              >
                <Home className="w-4 h-4" />
                Go Home
              </Button>
              {this.state.error && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={this.handleCopyError}
                  className="flex items-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  Copy Error
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

// Hook version for functional components
export function useErrorBoundary() {
  const [error, setError] = React.useState<Error | null>(null)

  const resetError = React.useCallback(() => {
    setError(null)
  }, [])

  const captureError = React.useCallback((error: Error) => {
    setError(error)
  }, [])

  React.useEffect(() => {
    if (error) {
      throw error
    }
  }, [error])

  return { captureError, resetError }
}
