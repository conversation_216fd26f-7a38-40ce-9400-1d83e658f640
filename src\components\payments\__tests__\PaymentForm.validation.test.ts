/**
 * Test file to verify payment form validation logic
 * This tests the core validation rules for payment amounts against invoices/bills
 */

import { describe, it, expect } from 'vitest'
import { formatCurrency } from '@/lib/utils'

// Mock invoice/bill data for testing
const mockInvoice = {
  id: 'inv-1',
  invoice_number: 'INV-001',
  total_amount: 1000,
  amount_due: 800,
  status: 'sent' as const,
  due_date: '2024-01-15'
}

const mockPaidInvoice = {
  id: 'inv-2',
  invoice_number: 'INV-002',
  total_amount: 1000,
  amount_due: 0,
  status: 'paid' as const,
  due_date: '2024-01-15'
}

// Validation function extracted from PaymentForm for testing
function validatePaymentAmount(
  paymentAmount: number,
  selectedDocument: typeof mockInvoice | null
): string {
  if (!selectedDocument) {
    return ''
  }

  const documentAmountDue = selectedDocument.amount_due || selectedDocument.total_amount || 0
  
  if (paymentAmount <= 0) {
    return 'Payment amount must be greater than 0'
  } else if (paymentAmount > documentAmountDue) {
    return `Payment amount cannot exceed ${formatCurrency(documentAmountDue)} (amount due)`
  } else if (selectedDocument.status === 'paid') {
    return 'This invoice is already marked as paid'
  }
  
  return ''
}

describe('Payment Form Validation', () => {
  describe('validatePaymentAmount', () => {
    it('should return empty string for valid payment amount', () => {
      const result = validatePaymentAmount(800, mockInvoice)
      expect(result).toBe('')
    })

    it('should return empty string for partial payment', () => {
      const result = validatePaymentAmount(500, mockInvoice)
      expect(result).toBe('')
    })

    it('should return error for zero payment amount', () => {
      const result = validatePaymentAmount(0, mockInvoice)
      expect(result).toBe('Payment amount must be greater than 0')
    })

    it('should return error for negative payment amount', () => {
      const result = validatePaymentAmount(-100, mockInvoice)
      expect(result).toBe('Payment amount must be greater than 0')
    })

    it('should return error for payment exceeding amount due', () => {
      const result = validatePaymentAmount(1000, mockInvoice)
      expect(result).toContain('Payment amount cannot exceed')
      expect(result).toContain('800') // amount due
    })

    it('should return error for already paid invoice', () => {
      const result = validatePaymentAmount(100, mockPaidInvoice)
      expect(result).toBe('This invoice is already marked as paid')
    })

    it('should return empty string when no document is selected', () => {
      const result = validatePaymentAmount(500, null)
      expect(result).toBe('')
    })

    it('should handle edge case where amount_due is 0 but total_amount exists', () => {
      const zeroAmountDueInvoice = {
        ...mockInvoice,
        amount_due: 0,
        total_amount: 1000
      }
      const result = validatePaymentAmount(500, zeroAmountDueInvoice)
      expect(result).toContain('Payment amount cannot exceed')
    })
  })

  describe('Business Logic Validation', () => {
    it('should allow exact payment of amount due', () => {
      const result = validatePaymentAmount(800, mockInvoice)
      expect(result).toBe('')
    })

    it('should allow partial payments (less than amount due)', () => {
      const result = validatePaymentAmount(400, mockInvoice)
      expect(result).toBe('')
    })

    it('should prevent overpayment', () => {
      const result = validatePaymentAmount(900, mockInvoice)
      expect(result).toContain('cannot exceed')
    })

    it('should prevent payments to already paid documents', () => {
      const result = validatePaymentAmount(100, mockPaidInvoice)
      expect(result).toContain('already marked as paid')
    })
  })
})

// Integration test scenarios
describe('Payment Form Integration Scenarios', () => {
  it('should handle typical invoice payment flow', () => {
    // User selects invoice with amount due of 800
    // Form auto-fills amount to 800
    let result = validatePaymentAmount(800, mockInvoice)
    expect(result).toBe('') // Should be valid

    // User reduces amount for partial payment
    result = validatePaymentAmount(400, mockInvoice)
    expect(result).toBe('') // Should be valid

    // User accidentally increases amount above due
    result = validatePaymentAmount(1000, mockInvoice)
    expect(result).toContain('cannot exceed') // Should be invalid
  })

  it('should prevent payment to paid invoice', () => {
    // User tries to pay already paid invoice
    const result = validatePaymentAmount(100, mockPaidInvoice)
    expect(result).toContain('already marked as paid')
  })
})
