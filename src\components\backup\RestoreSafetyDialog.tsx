// Restore Safety Check Dialog Component
// Displays comprehensive safety check results before restoration

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Info, 
  Shield, 
  Database,
  Users,
  Clock,
  AlertCircle
} from 'lucide-react'
import { RestoreSafetyChecker, type RestoreSafetyResult, type RestoreContext } from '@/lib/backup-restore-safety'

interface RestoreSafetyDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  context: RestoreContext
  onProceed: () => void
  onCancel: () => void
}

export function RestoreSafetyDialog({
  open,
  onOpenChange,
  context,
  onProceed,
  onCancel
}: RestoreSafetyDialogProps) {
  const [safetyResult, setSafetyResult] = useState<RestoreSafetyResult | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [userConfirmed, setUserConfirmed] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    if (open) {
      performSafetyCheck()
    }
  }, [open, performSafetyCheck])

  const performSafetyCheck = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await RestoreSafetyChecker.performSafetyChecks(context)
      setSafetyResult(result)
    } catch (error) {
      console.error('Safety check failed:', error)
      setSafetyResult({
        safe: false,
        score: 0,
        checks: [],
        summary: {
          totalChecks: 0,
          passedChecks: 0,
          failedChecks: 1,
          blockingIssues: 1,
          warnings: 0
        },
        recommendations: ['Safety check failed - contact support'],
        requiresConfirmation: false
      })
    } finally {
      setIsLoading(false)
    }
  }, [context])

  const getCheckIcon = (check: { passed: boolean; blocking: boolean }) => {
    if (check.passed) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else if (check.blocking) {
      return <XCircle className="h-4 w-4 text-red-500" />
    } else {
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'validation':
        return <Database className="h-4 w-4" />
      case 'compatibility':
        return <Clock className="h-4 w-4" />
      case 'safety':
        return <Shield className="h-4 w-4" />
      case 'permission':
        return <Users className="h-4 w-4" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'destructive'
      case 'error':
        return 'destructive'
      case 'warning':
        return 'warning'
      default:
        return 'secondary'
    }
  }

  const canProceed = safetyResult && safetyResult.summary.blockingIssues === 0 && 
    (!safetyResult.requiresConfirmation || userConfirmed)

  if (!open) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Pre-Restore Safety Check
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 overflow-y-auto max-h-[70vh]">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Performing safety checks...</span>
            </div>
          ) : safetyResult ? (
            <>
              {/* Safety Score Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Safety Score</span>
                      <Badge variant={safetyResult.safe ? 'default' : 'destructive'}>
                        {safetyResult.safe ? 'SAFE' : 'UNSAFE'}
                      </Badge>
                    </div>
                    <Progress value={safetyResult.score} className="mb-2" />
                    <span className="text-2xl font-bold">{safetyResult.score}%</span>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Total Checks:</span>
                        <span className="font-medium">{safetyResult.summary.totalChecks}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-green-600">Passed:</span>
                        <span className="font-medium">{safetyResult.summary.passedChecks}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-red-600">Failed:</span>
                        <span className="font-medium">{safetyResult.summary.failedChecks}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-yellow-600">Warnings:</span>
                        <span className="font-medium">{safetyResult.summary.warnings}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Blocking Issues Alert */}
              {safetyResult.summary.blockingIssues > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>{safetyResult.summary.blockingIssues} blocking issues</strong> must be resolved before restoration can proceed.
                  </AlertDescription>
                </Alert>
              )}

              {/* Recommendations */}
              {safetyResult.recommendations.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1">
                      {safetyResult.recommendations.map((rec, index) => (
                        <li key={index} className="text-sm flex items-start gap-2">
                          <Info className="h-3 w-3 mt-0.5 text-blue-500 flex-shrink-0" />
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {/* Safety Checks Details */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">Safety Check Details</CardTitle>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowDetails(!showDetails)}
                    >
                      {showDetails ? 'Hide Details' : 'Show Details'}
                    </Button>
                  </div>
                </CardHeader>
                {showDetails && (
                  <CardContent>
                    <div className="space-y-3">
                      {safetyResult.checks.map((check, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 border rounded">
                          <div className="flex items-center gap-2">
                            {getCheckIcon(check)}
                            {getCategoryIcon(check.category)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium">{check.name}</span>
                              <Badge variant={getSeverityColor(check.severity) as "default" | "secondary" | "destructive" | "outline"} size="sm">
                                {check.severity}
                              </Badge>
                              {check.blocking && (
                                <Badge variant="destructive" size="sm">
                                  Blocking
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-1">
                              {check.description}
                            </p>
                            <p className="text-sm">{check.message}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* Confirmation */}
              {safetyResult.requiresConfirmation && safetyResult.summary.blockingIssues === 0 && (
                <Card>
                  <CardContent className="pt-4">
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        {safetyResult.confirmationMessage}
                      </AlertDescription>
                    </Alert>
                    <div className="flex items-center space-x-2 mt-4">
                      <Checkbox
                        id="confirm-restore"
                        checked={userConfirmed}
                        onCheckedChange={setUserConfirmed}
                      />
                      <label
                        htmlFor="confirm-restore"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        I understand the risks and want to proceed with the restoration
                      </label>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 justify-end pt-4 border-t">
                <Button variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button
                  onClick={onProceed}
                  disabled={!canProceed}
                  variant={safetyResult.safe ? "default" : "destructive"}
                >
                  {safetyResult.summary.blockingIssues > 0
                    ? 'Cannot Proceed'
                    : safetyResult.requiresConfirmation && !userConfirmed
                    ? 'Confirm to Proceed'
                    : 'Proceed with Restoration'
                  }
                </Button>
              </div>
            </>
          ) : (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to perform safety checks. Please try again or contact support.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
