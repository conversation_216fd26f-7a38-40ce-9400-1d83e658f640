
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface RecurringJournalViewProps {
  journal: {
    id: string
    name: string
    frequency: string
    start_date: string
    end_date: string | null
    next_date: string
    is_active: boolean
    created_at: string
    recurring_lines?: {
      id: string
      account_id: string
      debit: number
      credit: number
      description: string | null
      accounts?: {
        code: string
        name: string
      }
    }[]
  }
  onClose: () => void
}

export const RecurringJournalView = ({ journal, onClose }: RecurringJournalViewProps) => {
  const formatFrequency = (frequency: string) => {
    switch (frequency.toLowerCase()) {
      case 'daily': return 'Daily'
      case 'weekly': return 'Weekly'
      case 'monthly': return 'Monthly'
      case 'quarterly': return 'Quarterly'
      case 'yearly': return 'Yearly'
      default: return frequency
    }
  }

  const totalDebits = journal.recurring_lines?.reduce((sum, line) => sum + line.debit, 0) || 0
  const totalCredits = journal.recurring_lines?.reduce((sum, line) => sum + line.credit, 0) || 0

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Name</Label>
          <p className="text-sm font-medium">{journal.name}</p>
        </div>
        <div>
          <Label>Frequency</Label>
          <p className="text-sm">{formatFrequency(journal.frequency)}</p>
        </div>
        <div>
          <Label>Start Date</Label>
          <p className="text-sm">{new Date(journal.start_date).toLocaleDateString()}</p>
        </div>
        <div>
          <Label>End Date</Label>
          <p className="text-sm">
            {journal.end_date ? new Date(journal.end_date).toLocaleDateString() : 'No end date'}
          </p>
        </div>
        <div>
          <Label>Next Date</Label>
          <p className="text-sm">{new Date(journal.next_date).toLocaleDateString()}</p>
        </div>
        <div>
          <Label>Status</Label>
          <div>
            <Badge variant={journal.is_active ? 'default' : 'secondary'}>
              {journal.is_active ? 'Active' : 'Inactive'}
            </Badge>
          </div>
        </div>
      </div>

      <div>
        <Label>Transaction Lines</Label>
        <Table className="mt-2">
          <TableHeader>
            <TableRow>
              <TableHead>Account</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Debit</TableHead>
              <TableHead>Credit</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {journal.recurring_lines?.map((line) => (
              <TableRow key={line.id}>
                <TableCell>
                  {line.accounts?.code} - {line.accounts?.name}
                </TableCell>
                <TableCell>{line.description || '-'}</TableCell>
                <TableCell>{line.debit > 0 ? line.debit.toFixed(2) : '-'}</TableCell>
                <TableCell>{line.credit > 0 ? line.credit.toFixed(2) : '-'}</TableCell>
              </TableRow>
            ))}
            <TableRow className="font-bold">
              <TableCell colSpan={2}>Totals</TableCell>
              <TableCell>{totalDebits.toFixed(2)}</TableCell>
              <TableCell>{totalCredits.toFixed(2)}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>

      <div className="flex justify-end">
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>
    </div>
  )
}
