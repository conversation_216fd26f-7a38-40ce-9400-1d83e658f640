import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/components/ui/toast-utils'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { 
  createPaymentApprovalNotification,
  createInvoiceOverdueNotification,
  createBillDueSoonNotification,
  createUserInvitationNotification,
  createBackupNotification
} from '@/lib/notificationHelpers'
import type { CreateNotificationPayload } from '@/types/notifications'

export function NotificationTest() {
  const { profile } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  
  // Form state for custom notification
  const [customNotification, setCustomNotification] = useState({
    title: '',
    message: '',
    type: 'system',
    category: 'system',
    priority: 'normal'
  })

  // Test creating a custom notification
  const handleCreateCustomNotification = async () => {
    if (!profile?.org_id || !profile?.id) {
      toast.error('User not authenticated')
      return
    }

    if (!customNotification.title || !customNotification.message) {
      toast.error('Title and message are required')
      return
    }

    setIsLoading(true)
    try {
      const payload: CreateNotificationPayload = {
        org_id: profile.org_id,
        user_id: profile.id,
        type: customNotification.type,
        category: customNotification.category as 'system' | 'financial' | 'approval' | 'reminder' | 'alert',
        title: customNotification.title,
        message: customNotification.message,
        priority: customNotification.priority as 'low' | 'normal' | 'high' | 'urgent',
      }

      const { data, error } = await supabase
        .from('notifications')
        .insert(payload)
        .select()
        .single()

      if (error) throw error

      toast.success('Custom notification created successfully!')
      
      // Reset form
      setCustomNotification({
        title: '',
        message: '',
        type: 'system',
        category: 'system',
        priority: 'normal'
      })
    } catch (error) {
      console.error('Error creating notification:', error)
      toast.error('Failed to create notification')
    } finally {
      setIsLoading(false)
    }
  }

  // Test template-based notifications
  const handleCreateTemplateNotification = async (type: string) => {
    if (!profile?.org_id || !profile?.id) {
      toast.error('User not authenticated')
      return
    }

    setIsLoading(true)
    try {
      switch (type) {
        case 'payment_approval':
          await createPaymentApprovalNotification(
            profile.org_id,
            profile.id,
            {
              amount: 'UGX 500,000',
              payeeName: 'Test Vendor Ltd',
              paymentId: 'test-payment-123'
            }
          )
          break

        case 'invoice_overdue':
          await createInvoiceOverdueNotification(
            profile.org_id,
            profile.id,
            {
              invoiceNumber: 'INV-2024-001',
              customerName: 'Test Customer Ltd',
              daysOverdue: 15,
              invoiceId: 'test-invoice-123'
            }
          )
          break

        case 'bill_due_soon':
          await createBillDueSoonNotification(
            profile.org_id,
            profile.id,
            {
              billNumber: 'BILL-2024-001',
              vendorName: 'Test Supplier Ltd',
              daysUntilDue: 3,
              billId: 'test-bill-123'
            }
          )
          break

        case 'user_invitation':
          await createUserInvitationNotification(
            profile.org_id,
            profile.id,
            '<EMAIL>'
          )
          break

        case 'backup_success':
          await createBackupNotification(
            profile.org_id,
            true,
            'Daily backup completed successfully'
          )
          break

        case 'backup_failed':
          await createBackupNotification(
            profile.org_id,
            false,
            'Backup failed due to network timeout'
          )
          break

        default:
          throw new Error('Unknown notification type')
      }

      toast.success(`${type} notification created successfully!`)
    } catch (error) {
      console.error('Error creating template notification:', error)
      toast.error(`Failed to create ${type} notification`)
    } finally {
      setIsLoading(false)
    }
  }

  if (!profile) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <p>Please log in to test notifications.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Notification System Test</h1>
        <p className="text-muted-foreground">
          Test the notification system functionality. Check the bell icon in the navbar to see created notifications.
        </p>
      </div>

      {/* Custom Notification */}
      <Card>
        <CardHeader>
          <CardTitle>Create Custom Notification</CardTitle>
          <CardDescription>
            Create a custom notification with your own content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={customNotification.title}
                onChange={(e) => setCustomNotification(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Notification title"
              />
            </div>
            <div>
              <Label htmlFor="type">Type</Label>
              <Input
                id="type"
                value={customNotification.type}
                onChange={(e) => setCustomNotification(prev => ({ ...prev, type: e.target.value }))}
                placeholder="notification_type"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Category</Label>
              <Select 
                value={customNotification.category} 
                onValueChange={(value) => setCustomNotification(prev => ({ ...prev, category: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">System</SelectItem>
                  <SelectItem value="financial">Financial</SelectItem>
                  <SelectItem value="approval">Approval</SelectItem>
                  <SelectItem value="reminder">Reminder</SelectItem>
                  <SelectItem value="alert">Alert</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select 
                value={customNotification.priority} 
                onValueChange={(value) => setCustomNotification(prev => ({ ...prev, priority: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              value={customNotification.message}
              onChange={(e) => setCustomNotification(prev => ({ ...prev, message: e.target.value }))}
              placeholder="Notification message content"
              rows={3}
            />
          </div>

          <Button 
            onClick={handleCreateCustomNotification} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? 'Creating...' : 'Create Custom Notification'}
          </Button>
        </CardContent>
      </Card>

      {/* Template Notifications */}
      <Card>
        <CardHeader>
          <CardTitle>Template-Based Notifications</CardTitle>
          <CardDescription>
            Test pre-defined notification templates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Button 
              variant="outline" 
              onClick={() => handleCreateTemplateNotification('payment_approval')}
              disabled={isLoading}
            >
              Payment Approval
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleCreateTemplateNotification('invoice_overdue')}
              disabled={isLoading}
            >
              Invoice Overdue
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleCreateTemplateNotification('bill_due_soon')}
              disabled={isLoading}
            >
              Bill Due Soon
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleCreateTemplateNotification('user_invitation')}
              disabled={isLoading}
            >
              User Invitation
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleCreateTemplateNotification('backup_success')}
              disabled={isLoading}
            >
              Backup Success
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleCreateTemplateNotification('backup_failed')}
              disabled={isLoading}
            >
              Backup Failed
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
