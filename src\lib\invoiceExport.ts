/**
 * Invoice Export Utilities
 * Functions for exporting invoice data in various formats
 */

import { supabase } from '@/lib/supabase'
import { formatCurrency } from '@/lib/utils'
import type { InvoiceWithCustomer } from '@/types/invoices'

export interface InvoiceExportOptions {
  format: 'csv' | 'json' | 'pdf'
  includeLineItems: boolean
  dateRange?: {
    start: string
    end: string
  }
  status?: string[]
  customerId?: string
}

export interface InvoiceLineItem {
  id: string
  account_id: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number
  line_total: number
  tax_amount: number
  accounts?: {
    code: string
    name: string
  }
}

export interface InvoiceExportData extends InvoiceWithCustomer {
  invoice_lines?: InvoiceLineItem[]
}

/**
 * Fetch invoices with optional filters for export
 */
export async function fetchInvoicesForExport(
  orgId: string,
  options: InvoiceExportOptions
): Promise<InvoiceExportData[]> {
  let query = supabase
    .from('invoices')
    .select(`
      *,
      customers(id, name, email, phone, address),
      ${options.includeLineItems ? 'invoice_lines(*, accounts(code, name))' : ''}
    `)
    .eq('org_id', orgId)
    .order('date_issued', { ascending: false })

  // Apply date range filter
  if (options.dateRange) {
    query = query
      .gte('date_issued', options.dateRange.start)
      .lte('date_issued', options.dateRange.end)
  }

  // Apply status filter
  if (options.status && options.status.length > 0) {
    query = query.in('status', options.status)
  }

  // Apply customer filter
  if (options.customerId) {
    query = query.eq('customer_id', options.customerId)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch invoices: ${error.message}`)
  }

  return data || []
}

/**
 * Export invoices to CSV format
 */
export function exportInvoicesToCSV(
  invoices: InvoiceExportData[],
  options: InvoiceExportOptions
): string {
  if (options.includeLineItems) {
    return exportInvoicesWithLineItemsToCSV(invoices)
  } else {
    return exportInvoicesSummaryToCSV(invoices)
  }
}

/**
 * Export invoice summary to CSV
 */
function exportInvoicesSummaryToCSV(invoices: InvoiceExportData[]): string {
  const headers = [
    'Invoice Number',
    'Customer Name',
    'Customer Email',
    'Date Issued',
    'Due Date',
    'Status',
    'Subtotal',
    'Tax Amount',
    'Total Amount',
    'Notes',
    'Created At'
  ]

  const rows = invoices.map(invoice => [
    invoice.invoice_number,
    invoice.customers?.name || 'Unknown',
    invoice.customers?.email || '',
    invoice.date_issued,
    invoice.due_date,
    invoice.status,
    (invoice.total_amount - invoice.tax_amount).toFixed(2),
    invoice.tax_amount.toFixed(2),
    invoice.total_amount.toFixed(2),
    invoice.notes || '',
    invoice.created_at
  ])

  return [
    headers.join(','),
    ...rows.map(row => 
      row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
    )
  ].join('\n')
}

/**
 * Export invoices with line items to CSV
 */
function exportInvoicesWithLineItemsToCSV(invoices: InvoiceExportData[]): string {
  const headers = [
    'Invoice Number',
    'Customer Name',
    'Customer Email',
    'Date Issued',
    'Due Date',
    'Status',
    'Line Item Description',
    'Account Code',
    'Account Name',
    'Quantity',
    'Unit Price',
    'Line Total',
    'Tax Rate %',
    'Tax Amount',
    'Invoice Total',
    'Notes'
  ]

  const rows: string[][] = []

  invoices.forEach(invoice => {
    if (invoice.invoice_lines && invoice.invoice_lines.length > 0) {
      invoice.invoice_lines.forEach(line => {
        rows.push([
          invoice.invoice_number,
          invoice.customers?.name || 'Unknown',
          invoice.customers?.email || '',
          invoice.date_issued,
          invoice.due_date,
          invoice.status,
          line.description,
          line.accounts?.code || '',
          line.accounts?.name || '',
          line.quantity.toString(),
          line.unit_price.toFixed(2),
          line.line_total.toFixed(2),
          line.tax_rate_pct.toString(),
          line.tax_amount.toFixed(2),
          invoice.total_amount.toFixed(2),
          invoice.notes || ''
        ])
      })
    } else {
      // Invoice without line items
      rows.push([
        invoice.invoice_number,
        invoice.customers?.name || 'Unknown',
        invoice.customers?.email || '',
        invoice.date_issued,
        invoice.due_date,
        invoice.status,
        'No line items',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        invoice.total_amount.toFixed(2),
        invoice.notes || ''
      ])
    }
  })

  return [
    headers.join(','),
    ...rows.map(row => 
      row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
    )
  ].join('\n')
}

/**
 * Export invoices to JSON format
 */
export function exportInvoicesToJSON(invoices: InvoiceExportData[]): string {
  const exportData = {
    exportDate: new Date().toISOString(),
    totalInvoices: invoices.length,
    totalAmount: invoices.reduce((sum, inv) => sum + inv.total_amount, 0),
    invoices: invoices.map(invoice => ({
      ...invoice,
      formattedTotal: formatCurrency(invoice.total_amount),
      formattedTax: formatCurrency(invoice.tax_amount),
      formattedSubtotal: formatCurrency(invoice.total_amount - invoice.tax_amount)
    }))
  }

  return JSON.stringify(exportData, null, 2)
}

/**
 * Download exported data as file
 */
export function downloadExportFile(
  content: string,
  filename: string,
  mimeType: string
): void {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

/**
 * Generate filename for export
 */
export function generateExportFilename(
  format: string,
  options: InvoiceExportOptions
): string {
  const timestamp = new Date().toISOString().split('T')[0]
  const baseFilename = `invoices-export-${timestamp}`
  
  let suffix = ''
  if (options.dateRange) {
    suffix += `-${options.dateRange.start}-to-${options.dateRange.end}`
  }
  if (options.status && options.status.length > 0) {
    suffix += `-${options.status.join('-')}`
  }
  
  return `${baseFilename}${suffix}.${format}`
}

/**
 * Main export function
 */
export async function exportInvoices(
  orgId: string,
  options: InvoiceExportOptions
): Promise<void> {
  try {
    // Fetch invoice data
    const invoices = await fetchInvoicesForExport(orgId, options)
    
    if (invoices.length === 0) {
      throw new Error('No invoices found matching the specified criteria')
    }

    let content: string
    let mimeType: string

    // Generate content based on format
    switch (options.format) {
      case 'csv':
        content = exportInvoicesToCSV(invoices, options)
        mimeType = 'text/csv'
        break
      case 'json':
        content = exportInvoicesToJSON(invoices)
        mimeType = 'application/json'
        break
      default:
        throw new Error(`Unsupported export format: ${options.format}`)
    }

    // Generate filename and download
    const filename = generateExportFilename(options.format, options)
    downloadExportFile(content, filename, mimeType)

  } catch (error) {
    console.error('Export failed:', error)
    throw error
  }
}
