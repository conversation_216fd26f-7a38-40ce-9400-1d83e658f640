import React, { useState } from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { 
  AlertTriangle, 
  TrendingUp, 
  ChevronDown, 
  ChevronUp, 
  X,
  DollarSign
} from 'lucide-react'
import { useBudgetAlerts, BudgetAlert } from '@/hooks/queries/useBudgetAlerts'

interface BudgetAlertBannerProps {
  className?: string
  showDismiss?: boolean
  onDismiss?: () => void
}

export function BudgetAlertBanner({ 
  className = "", 
  showDismiss = false, 
  onDismiss 
}: BudgetAlertBannerProps) {
  const { data: alertSummary, isLoading } = useBudgetAlerts()
  const [isExpanded, setIsExpanded] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const getAlertIcon = (level: BudgetAlert['alertLevel']) => {
    switch (level) {
      case 'exceeded':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'critical':
        return <TrendingUp className="h-4 w-4 text-orange-600" />
      case 'warning':
        return <TrendingUp className="h-4 w-4 text-yellow-600" />
      default:
        return <DollarSign className="h-4 w-4" />
    }
  }

  const getAlertColor = (level: BudgetAlert['alertLevel']) => {
    switch (level) {
      case 'exceeded':
        return 'border-red-200 bg-red-50'
      case 'critical':
        return 'border-orange-200 bg-orange-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getBadgeVariant = (level: BudgetAlert['alertLevel']) => {
    switch (level) {
      case 'exceeded':
        return 'destructive'
      case 'critical':
        return 'secondary'
      case 'warning':
        return 'outline'
      default:
        return 'default'
    }
  }

  if (isLoading || !alertSummary || alertSummary.totalAlerts === 0 || isDismissed) {
    return null
  }

  const handleDismiss = () => {
    setIsDismissed(true)
    onDismiss?.()
  }

  const criticalAndExceeded = alertSummary.criticalAlerts + alertSummary.exceededAlerts

  return (
    <Card className={`${className} ${getAlertColor(alertSummary.alerts[0]?.alertLevel)}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm flex items-center gap-2">
            {getAlertIcon(alertSummary.alerts[0]?.alertLevel)}
            Budget Alerts
            <Badge variant={getBadgeVariant(alertSummary.alerts[0]?.alertLevel)}>
              {alertSummary.totalAlerts} Alert{alertSummary.totalAlerts !== 1 ? 's' : ''}
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
            {showDismiss && (
              <Button variant="ghost" size="sm" onClick={handleDismiss}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Summary */}
        <div className="space-y-2">
          <Alert className={criticalAndExceeded > 0 ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'}>
            <AlertDescription>
              {criticalAndExceeded > 0 ? (
                <span className="font-medium text-red-700">
                  {criticalAndExceeded} budget{criticalAndExceeded !== 1 ? 's' : ''} require immediate attention
                </span>
              ) : (
                <span className="font-medium text-yellow-700">
                  {alertSummary.warningAlerts} budget{alertSummary.warningAlerts !== 1 ? 's' : ''} approaching limit
                </span>
              )}
            </AlertDescription>
          </Alert>

          {/* Quick Stats */}
          <div className="flex gap-4 text-sm">
            {alertSummary.exceededAlerts > 0 && (
              <div className="flex items-center gap-1 text-red-600">
                <AlertTriangle className="h-3 w-3" />
                <span>{alertSummary.exceededAlerts} Exceeded</span>
              </div>
            )}
            {alertSummary.criticalAlerts > 0 && (
              <div className="flex items-center gap-1 text-orange-600">
                <TrendingUp className="h-3 w-3" />
                <span>{alertSummary.criticalAlerts} Critical</span>
              </div>
            )}
            {alertSummary.warningAlerts > 0 && (
              <div className="flex items-center gap-1 text-yellow-600">
                <TrendingUp className="h-3 w-3" />
                <span>{alertSummary.warningAlerts} Warning</span>
              </div>
            )}
          </div>
        </div>

        {/* Detailed Alerts */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="mt-4">
            <div className="space-y-3">
              <div className="text-sm font-medium">Alert Details:</div>
              {alertSummary.alerts.slice(0, 5).map((alert) => (
                <div 
                  key={alert.id}
                  className={`p-3 rounded-lg border ${getAlertColor(alert.alertLevel)}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        {getAlertIcon(alert.alertLevel)}
                        <span className="font-medium text-sm">{alert.accountName}</span>
                        <Badge variant={getBadgeVariant(alert.alertLevel)} className="text-xs">
                          {alert.alertLevel.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {alert.accountCode} • {alert.budgetName}
                      </div>
                      <div className="text-sm">{alert.message}</div>
                    </div>
                    <div className="text-right text-sm">
                      <div className="font-medium">
                        {alert.utilizationPercent.toFixed(1)}%
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatCurrency(alert.actualAmount)} / {formatCurrency(alert.budgetAmount)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {alertSummary.alerts.length > 5 && (
                <div className="text-sm text-muted-foreground text-center">
                  ... and {alertSummary.alerts.length - 5} more alert{alertSummary.alerts.length - 5 !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  )
}
