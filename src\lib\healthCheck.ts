import React from 'react'
import { supabase } from './supabase'
import { config } from './config'

// Health check status types
export type HealthStatus = 'healthy' | 'degraded' | 'unhealthy'

export interface HealthCheckResult {
  service: string
  status: HealthStatus
  responseTime: number
  message?: string
  details?: Record<string, unknown>
  timestamp: number
}

export interface SystemHealth {
  overall: HealthStatus
  services: HealthCheckResult[]
  timestamp: number
  version: string
  environment: string
}

class HealthCheckService {
  private static instance: HealthCheckService
  private checkInterval?: NodeJS.Timeout
  private lastHealthCheck?: SystemHealth

  static getInstance(): HealthCheckService {
    if (!HealthCheckService.instance) {
      HealthCheckService.instance = new HealthCheckService()
    }
    return HealthCheckService.instance
  }

  // Start periodic health checks
  startPeriodicChecks(intervalMs = 60000) { // Default: 1 minute
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }

    this.checkInterval = setInterval(async () => {
      try {
        await this.performHealthCheck()
      } catch (error) {
        console.error('Health check failed:', error)
      }
    }, intervalMs)

    console.log('🏥 Health check monitoring started')
  }

  // Stop periodic health checks
  stopPeriodicChecks() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = undefined
    }
  }

  // Perform comprehensive health check
  async performHealthCheck(): Promise<SystemHealth> {
    const startTime = Date.now()
    
    const services = await Promise.allSettled([
      this.checkDatabase(),
      this.checkAuthentication(),
      this.checkStorage(),
      this.checkExternalAPIs(),
      this.checkBrowserAPIs()
    ])

    const healthResults: HealthCheckResult[] = services.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        const serviceNames = ['database', 'authentication', 'storage', 'external_apis', 'browser_apis']
        return {
          service: serviceNames[index],
          status: 'unhealthy' as HealthStatus,
          responseTime: Date.now() - startTime,
          message: result.reason?.message || 'Health check failed',
          timestamp: Date.now()
        }
      }
    })

    // Determine overall health status
    const overall = this.calculateOverallHealth(healthResults)

    const systemHealth: SystemHealth = {
      overall,
      services: healthResults,
      timestamp: Date.now(),
      version: config.app.version,
      environment: config.app.environment
    }

    this.lastHealthCheck = systemHealth

    // Log health status
    if (overall !== 'healthy') {
      console.warn('System health check:', systemHealth)
    }

    return systemHealth
  }

  // Check database connectivity
  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      // Simple query to test database connectivity
      const { data, error } = await supabase
        .from('organizations')
        .select('id')
        .limit(1)

      const responseTime = Date.now() - startTime

      if (error) {
        return {
          service: 'database',
          status: 'unhealthy',
          responseTime,
          message: error.message,
          details: { error: error.code },
          timestamp: Date.now()
        }
      }

      return {
        service: 'database',
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
        message: 'Database connection successful',
        timestamp: Date.now()
      }
    } catch (error) {
      return {
        service: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: error instanceof Error ? error.message : 'Database check failed',
        timestamp: Date.now()
      }
    }
  }

  // Check authentication service
  private async checkAuthentication(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      // Check if we can get session info
      const { data, error } = await supabase.auth.getSession()
      const responseTime = Date.now() - startTime

      if (error) {
        return {
          service: 'authentication',
          status: 'degraded',
          responseTime,
          message: error.message,
          timestamp: Date.now()
        }
      }

      return {
        service: 'authentication',
        status: 'healthy',
        responseTime,
        message: 'Authentication service operational',
        details: { hasSession: !!data.session },
        timestamp: Date.now()
      }
    } catch (error) {
      return {
        service: 'authentication',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: error instanceof Error ? error.message : 'Auth check failed',
        timestamp: Date.now()
      }
    }
  }

  // Check storage service
  private async checkStorage(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      // Check if we can list buckets (basic storage connectivity)
      const { data, error } = await supabase.storage.listBuckets()
      const responseTime = Date.now() - startTime

      if (error) {
        return {
          service: 'storage',
          status: 'degraded',
          responseTime,
          message: error.message,
          timestamp: Date.now()
        }
      }

      return {
        service: 'storage',
        status: 'healthy',
        responseTime,
        message: 'Storage service operational',
        details: { bucketCount: data?.length || 0 },
        timestamp: Date.now()
      }
    } catch (error) {
      return {
        service: 'storage',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: error instanceof Error ? error.message : 'Storage check failed',
        timestamp: Date.now()
      }
    }
  }

  // Check external APIs
  private async checkExternalAPIs(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      // Check IP detection service (if enabled)
      if (config.features.ipDetection) {
        const response = await fetch('https://api.ipify.org?format=json', {
          method: 'GET',
          timeout: 5000
        } as RequestInit & { timeout: number })

        if (!response.ok) {
          throw new Error(`IP API returned ${response.status}`)
        }
      }

      const responseTime = Date.now() - startTime

      return {
        service: 'external_apis',
        status: responseTime < 2000 ? 'healthy' : 'degraded',
        responseTime,
        message: 'External APIs operational',
        timestamp: Date.now()
      }
    } catch (error) {
      return {
        service: 'external_apis',
        status: 'degraded', // External APIs are not critical
        responseTime: Date.now() - startTime,
        message: error instanceof Error ? error.message : 'External API check failed',
        timestamp: Date.now()
      }
    }
  }

  // Check browser APIs
  private async checkBrowserAPIs(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      const checks = {
        localStorage: typeof localStorage !== 'undefined',
        sessionStorage: typeof sessionStorage !== 'undefined',
        indexedDB: typeof indexedDB !== 'undefined',
        crypto: typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined',
        fetch: typeof fetch !== 'undefined'
      }

      const failedChecks = Object.entries(checks)
        .filter(([_, available]) => !available)
        .map(([api]) => api)

      const responseTime = Date.now() - startTime

      if (failedChecks.length > 0) {
        return {
          service: 'browser_apis',
          status: 'degraded',
          responseTime,
          message: `Missing browser APIs: ${failedChecks.join(', ')}`,
          details: checks,
          timestamp: Date.now()
        }
      }

      return {
        service: 'browser_apis',
        status: 'healthy',
        responseTime,
        message: 'All browser APIs available',
        details: checks,
        timestamp: Date.now()
      }
    } catch (error) {
      return {
        service: 'browser_apis',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: error instanceof Error ? error.message : 'Browser API check failed',
        timestamp: Date.now()
      }
    }
  }

  // Calculate overall health status
  private calculateOverallHealth(services: HealthCheckResult[]): HealthStatus {
    const criticalServices = ['database', 'authentication']
    const healthyCount = services.filter(s => s.status === 'healthy').length
    const unhealthyCount = services.filter(s => s.status === 'unhealthy').length
    
    // Check if any critical services are unhealthy
    const criticalUnhealthy = services.some(s => 
      criticalServices.includes(s.service) && s.status === 'unhealthy'
    )

    if (criticalUnhealthy || unhealthyCount > services.length / 2) {
      return 'unhealthy'
    }

    if (healthyCount === services.length) {
      return 'healthy'
    }

    return 'degraded'
  }

  // Get last health check result
  getLastHealthCheck(): SystemHealth | undefined {
    return this.lastHealthCheck
  }

  // Get health status for specific service
  getServiceHealth(serviceName: string): HealthCheckResult | undefined {
    return this.lastHealthCheck?.services.find(s => s.service === serviceName)
  }

  // Check if system is healthy
  isHealthy(): boolean {
    return this.lastHealthCheck?.overall === 'healthy'
  }

  // Export health data for monitoring
  exportHealthData(): string {
    if (!this.lastHealthCheck) {
      return JSON.stringify({ error: 'No health data available' })
    }

    return JSON.stringify(this.lastHealthCheck, null, 2)
  }
}

// Export singleton instance
export const healthCheck = HealthCheckService.getInstance()

// React hook for health monitoring
export function useHealthMonitoring() {
  const [health, setHealth] = React.useState<SystemHealth | undefined>()
  const [loading, setLoading] = React.useState(false)

  const checkHealth = async () => {
    setLoading(true)
    try {
      const result = await healthCheck.performHealthCheck()
      setHealth(result)
    } catch (error) {
      console.error('Health check failed:', error)
    } finally {
      setLoading(false)
    }
  }

  React.useEffect(() => {
    // Initial health check
    checkHealth()

    // Set up periodic checks
    const interval = setInterval(checkHealth, 60000) // Every minute

    return () => clearInterval(interval)
  }, [])

  return {
    health,
    loading,
    checkHealth,
    isHealthy: health?.overall === 'healthy'
  }
}
