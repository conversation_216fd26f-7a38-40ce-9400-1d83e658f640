/**
 * Enhanced Notification Actions
 * Snooze, remind later, custom actions, and forwarding capabilities
 */

import { useState } from 'react'
import { 
  Clock, 
  Bell, 
  Forward, 
  ExternalLink, 
  Archive, 
  Trash2, 
  MoreHorizontal,
  Calendar,
  User,
  Mail
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger
} from '@/components/ui/dropdown-menu'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from '@/components/ui/toast-utils'
import { 
  useMarkNotificationAsRead,
  useArchiveNotification,
  useDeleteNotification,
  useSnoozeNotification,
  useForwardNotification
} from '@/hooks/queries/useNotifications'
import type { NotificationWithMeta } from '@/types/notifications'

interface NotificationActionsProps {
  notification: NotificationWithMeta
  onActionComplete?: () => void
  compact?: boolean
}

interface SnoozeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSnooze: (duration: number, unit: 'minutes' | 'hours' | 'days') => void
}

interface ForwardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  notification: NotificationWithMeta
  onForward: (email: string, message?: string) => void
}

// Snooze duration options
const SNOOZE_OPTIONS = [
  { label: '15 minutes', value: 15, unit: 'minutes' as const },
  { label: '30 minutes', value: 30, unit: 'minutes' as const },
  { label: '1 hour', value: 1, unit: 'hours' as const },
  { label: '2 hours', value: 2, unit: 'hours' as const },
  { label: '4 hours', value: 4, unit: 'hours' as const },
  { label: '1 day', value: 1, unit: 'days' as const },
  { label: '3 days', value: 3, unit: 'days' as const },
  { label: '1 week', value: 7, unit: 'days' as const }
]

function SnoozeDialog({ open, onOpenChange, onSnooze }: SnoozeDialogProps) {
  const [selectedOption, setSelectedOption] = useState(SNOOZE_OPTIONS[0])
  const [customDuration, setCustomDuration] = useState('')
  const [customUnit, setCustomUnit] = useState<'minutes' | 'hours' | 'days'>('hours')

  const handleSnooze = () => {
    if (selectedOption.label === 'Custom') {
      const duration = parseInt(customDuration)
      if (isNaN(duration) || duration <= 0) {
        toast.error('Please enter a valid duration')
        return
      }
      onSnooze(duration, customUnit)
    } else {
      onSnooze(selectedOption.value, selectedOption.unit)
    }
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Snooze Notification</DialogTitle>
          <DialogDescription>
            Choose when you'd like to be reminded about this notification
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            {SNOOZE_OPTIONS.map((option) => (
              <Button
                key={option.label}
                variant={selectedOption === option ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedOption(option)}
                className="justify-start"
              >
                <Clock className="h-4 w-4 mr-2" />
                {option.label}
              </Button>
            ))}
            <Button
              variant={selectedOption.label === 'Custom' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedOption({ label: 'Custom', value: 0, unit: 'hours' })}
              className="justify-start"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Custom
            </Button>
          </div>
          
          {selectedOption.label === 'Custom' && (
            <div className="flex gap-2">
              <div className="flex-1">
                <Label htmlFor="duration">Duration</Label>
                <Input
                  id="duration"
                  type="number"
                  placeholder="Enter duration"
                  value={customDuration}
                  onChange={(e) => setCustomDuration(e.target.value)}
                />
              </div>
              <div className="w-32">
                <Label htmlFor="unit">Unit</Label>
                <Select value={customUnit} onValueChange={(value: string) => setCustomUnit(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="minutes">Minutes</SelectItem>
                    <SelectItem value="hours">Hours</SelectItem>
                    <SelectItem value="days">Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSnooze}>
            <Clock className="h-4 w-4 mr-2" />
            Snooze
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

function ForwardDialog({ open, onOpenChange, notification, onForward }: ForwardDialogProps) {
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState('')

  const handleForward = () => {
    if (!email.trim()) {
      toast.error('Please enter an email address')
      return
    }
    
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      toast.error('Please enter a valid email address')
      return
    }
    
    onForward(email, message)
    onOpenChange(false)
    setEmail('')
    setMessage('')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Forward Notification</DialogTitle>
          <DialogDescription>
            Send this notification to someone else
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          
          <div>
            <Label htmlFor="message">Additional Message (Optional)</Label>
            <Textarea
              id="message"
              placeholder="Add a personal message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
            />
          </div>
          
          <div className="p-3 bg-muted rounded-lg">
            <h4 className="font-medium text-sm mb-2">Notification Preview:</h4>
            <div className="text-sm">
              <div className="font-medium">{notification.title}</div>
              <div className="text-muted-foreground">{notification.message}</div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleForward}>
            <Forward className="h-4 w-4 mr-2" />
            Forward
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function NotificationActions({ 
  notification, 
  onActionComplete, 
  compact = false 
}: NotificationActionsProps) {
  const [snoozeDialogOpen, setSnoozeDialogOpen] = useState(false)
  const [forwardDialogOpen, setForwardDialogOpen] = useState(false)

  // Mutations
  const markAsRead = useMarkNotificationAsRead()
  const archiveNotification = useArchiveNotification()
  const deleteNotification = useDeleteNotification()
  const snoozeNotification = useSnoozeNotification()
  const forwardNotification = useForwardNotification()

  // Action handlers
  const handleMarkAsRead = async () => {
    try {
      await markAsRead.mutateAsync(notification.id)
      onActionComplete?.()
    } catch (error) {
      toast.error('Failed to mark as read')
    }
  }

  const handleArchive = async () => {
    try {
      await archiveNotification.mutateAsync(notification.id)
      toast.success('Notification archived')
      onActionComplete?.()
    } catch (error) {
      toast.error('Failed to archive notification')
    }
  }

  const handleDelete = async () => {
    try {
      await deleteNotification.mutateAsync(notification.id)
      toast.success('Notification deleted')
      onActionComplete?.()
    } catch (error) {
      toast.error('Failed to delete notification')
    }
  }

  const handleSnooze = async (duration: number, unit: 'minutes' | 'hours' | 'days') => {
    try {
      await snoozeNotification.mutateAsync({ 
        notificationId: notification.id, 
        duration, 
        unit 
      })
      toast.success(`Notification snoozed for ${duration} ${unit}`)
      onActionComplete?.()
    } catch (error) {
      toast.error('Failed to snooze notification')
    }
  }

  const handleForward = async (email: string, message?: string) => {
    try {
      await forwardNotification.mutateAsync({
        notificationId: notification.id,
        email,
        message
      })
      toast.success('Notification forwarded successfully')
    } catch (error) {
      toast.error('Failed to forward notification')
    }
  }

  const handleNavigateToEntity = () => {
    if (notification.entity_type && notification.entity_id) {
      // Navigate to the related entity
      const entityRoutes: Record<string, string> = {
        invoice: `/invoices/${notification.entity_id}`,
        bill: `/bills/${notification.entity_id}`,
        payment: `/payments/${notification.entity_id}`,
        budget: `/budgets/${notification.entity_id}`,
        customer: `/customers/${notification.entity_id}`,
        vendor: `/vendors/${notification.entity_id}`
      }
      
      const route = entityRoutes[notification.entity_type]
      if (route) {
        window.location.href = route
      }
    }
  }

  // Get custom actions based on notification type
  const getCustomActions = () => {
    const actions = []
    
    switch (notification.type) {
      case 'payment_pending_approval':
        actions.push(
          <DropdownMenuItem key="approve" onClick={() => {/* Handle approval */}}>
            <User className="h-4 w-4 mr-2" />
            Approve Payment
          </DropdownMenuItem>,
          <DropdownMenuItem key="reject" onClick={() => {/* Handle rejection */}}>
            <User className="h-4 w-4 mr-2" />
            Reject Payment
          </DropdownMenuItem>
        )
        break
      
      case 'invoice_overdue':
      case 'bill_overdue':
        actions.push(
          <DropdownMenuItem key="send-reminder" onClick={() => {/* Send reminder */}}>
            <Mail className="h-4 w-4 mr-2" />
            Send Reminder
          </DropdownMenuItem>
        )
        break
    }
    
    return actions
  }

  if (compact) {
    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {!notification.is_read && (
              <DropdownMenuItem onClick={handleMarkAsRead}>
                <Bell className="h-4 w-4 mr-2" />
                Mark as Read
              </DropdownMenuItem>
            )}
            
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Clock className="h-4 w-4 mr-2" />
                Snooze
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                {SNOOZE_OPTIONS.slice(0, 4).map((option) => (
                  <DropdownMenuItem 
                    key={option.label}
                    onClick={() => handleSnooze(option.value, option.unit)}
                  >
                    {option.label}
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setSnoozeDialogOpen(true)}>
                  Custom...
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuSub>
            
            {notification.entity_type && notification.entity_id && (
              <DropdownMenuItem onClick={handleNavigateToEntity}>
                <ExternalLink className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
            )}
            
            <DropdownMenuItem onClick={() => setForwardDialogOpen(true)}>
              <Forward className="h-4 w-4 mr-2" />
              Forward
            </DropdownMenuItem>
            
            {getCustomActions()}
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem onClick={handleArchive}>
              <Archive className="h-4 w-4 mr-2" />
              Archive
            </DropdownMenuItem>
            
            <DropdownMenuItem onClick={handleDelete} className="text-destructive">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <SnoozeDialog
          open={snoozeDialogOpen}
          onOpenChange={setSnoozeDialogOpen}
          onSnooze={handleSnooze}
        />

        <ForwardDialog
          open={forwardDialogOpen}
          onOpenChange={setForwardDialogOpen}
          notification={notification}
          onForward={handleForward}
        />
      </>
    )
  }

  // Full action buttons for expanded view
  return (
    <div className="flex items-center gap-2">
      {!notification.is_read && (
        <Button variant="outline" size="sm" onClick={handleMarkAsRead}>
          <Bell className="h-4 w-4 mr-2" />
          Mark as Read
        </Button>
      )}
      
      <Button variant="outline" size="sm" onClick={() => setSnoozeDialogOpen(true)}>
        <Clock className="h-4 w-4 mr-2" />
        Snooze
      </Button>
      
      {notification.entity_type && notification.entity_id && (
        <Button variant="outline" size="sm" onClick={handleNavigateToEntity}>
          <ExternalLink className="h-4 w-4 mr-2" />
          View Details
        </Button>
      )}
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <MoreHorizontal className="h-4 w-4 mr-2" />
            More
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => setForwardDialogOpen(true)}>
            <Forward className="h-4 w-4 mr-2" />
            Forward
          </DropdownMenuItem>
          
          {getCustomActions()}
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={handleArchive}>
            <Archive className="h-4 w-4 mr-2" />
            Archive
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={handleDelete} className="text-destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <SnoozeDialog
        open={snoozeDialogOpen}
        onOpenChange={setSnoozeDialogOpen}
        onSnooze={handleSnooze}
      />

      <ForwardDialog
        open={forwardDialogOpen}
        onOpenChange={setForwardDialogOpen}
        notification={notification}
        onForward={handleForward}
      />
    </div>
  )
}
