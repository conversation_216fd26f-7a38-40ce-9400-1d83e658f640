import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Filter,
  Search,
  MoreHorizontal,
  Eye,
  MessageSquare,
  Edit,
  Trash
} from 'lucide-react'
import { LoadingPage } from '@/components/ui/loading'
import { formatCurrency } from '@/lib/utils'
import { useAuth } from '@/hooks/useAuthHook'
import { ApprovalWorkflowTest } from '@/debug/approval-workflow-test'
import { DatabaseVerification } from '@/debug/database-verification'
import { WorkflowSetup } from '@/debug/setup-default-workflows'
import {
  useApprovalInstances,
  usePendingApprovals,
  useApprovalStats,
  useBulkApprovalAction,
  useApprovalAction
} from '@/hooks/queries/useApprovalWorkflow'
import { ApprovalActionDialog } from '@/components/approval/ApprovalActionDialog'
import { ApprovalFiltersDialog } from '@/components/approval/ApprovalFiltersDialog'
import { BulkApprovalDialog } from '@/components/approval/BulkApprovalDialog'
import type { 
  ApprovalFilters, 
  ApprovalSortOptions,
  ApprovalInstanceWithDetails 
} from '@/types/approval-workflow'
import type { ApprovalStatus } from '@/types/database'

export default function ApprovalDashboard() {
  const [selectedTab, setSelectedTab] = useState('pending')
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<ApprovalFilters>({})
  const [sortOptions, setSortOptions] = useState<ApprovalSortOptions>({
    field: 'submitted_at',
    direction: 'desc'
  })
  const [selectedInstances, setSelectedInstances] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [selectedInstance, setSelectedInstance] = useState<ApprovalInstanceWithDetails | null>(null)

  // Auth
  const { user, profile } = useAuth()

  // Queries
  const { data: stats, isLoading: statsLoading } = useApprovalStats()
  const { data: pendingApprovals, isLoading: pendingLoading } = usePendingApprovals()
  const { data: allInstances, isLoading: instancesLoading } = useApprovalInstances(
    filters,
    sortOptions,
    1,
    50
  )

  // Mutations
  const bulkApprovalMutation = useBulkApprovalAction()
  const approvalActionMutation = useApprovalAction()

  if (statsLoading) {
    return <LoadingPage text="Loading approval dashboard..." />
  }

  const getStatusBadge = (status: ApprovalStatus) => {
    const variants = {
      pending: 'default',
      approved: 'success',
      rejected: 'destructive',
      escalated: 'warning',
      cancelled: 'outline'
    } as const

    const icons = {
      pending: Clock,
      approved: CheckCircle,
      rejected: XCircle,
      escalated: AlertTriangle,
      cancelled: XCircle
    }

    const Icon = icons[status]

    return (
      <Badge variant={variants[status]} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const handleBulkAction = (action: 'approve' | 'reject') => {
    if (selectedInstances.length === 0) return

    bulkApprovalMutation.mutate({
      approval_instance_ids: selectedInstances,
      action,
      comments: `Bulk ${action} action`
    })

    setSelectedInstances([])
    setShowBulkActions(false)
  }

  const handleQuickAction = async (instance: ApprovalInstanceWithDetails, action: 'approve' | 'reject') => {
    try {
      await approvalActionMutation.mutateAsync({
        approvalInstanceId: instance.id,
        actionRequest: {
          action,
          comments: `Quick ${action} action from dashboard`
        }
      })
    } catch (error) {
      console.error(`Error ${action}ing instance:`, error)
    }
  }

  const renderInstanceRow = (instance: ApprovalInstanceWithDetails) => (
    <tr key={instance.id} className="border-b hover:bg-muted/50">
      <td className="p-4">
        <input
          type="checkbox"
          checked={selectedInstances.includes(instance.id)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedInstances([...selectedInstances, instance.id])
            } else {
              setSelectedInstances(selectedInstances.filter(id => id !== instance.id))
            }
          }}
          className="rounded"
        />
      </td>
      <td className="p-4">
        <div className="flex flex-col">
          <span className="font-medium">{instance.document_details?.title}</span>
          <span className="text-sm text-muted-foreground">
            {instance.document_details?.number}
          </span>
        </div>
      </td>
      <td className="p-4">
        <Badge variant="outline">
          {instance.document_type.charAt(0).toUpperCase() + instance.document_type.slice(1)}
        </Badge>
      </td>
      <td className="p-4">
        {formatCurrency(instance.document_amount || 0, instance.currency_code)}
      </td>
      <td className="p-4">
        {getStatusBadge(instance.status)}
      </td>
      <td className="p-4">
        <div className="flex flex-col">
          <span className="text-sm">{instance.submitted_by_profile?.full_name}</span>
          <span className="text-xs text-muted-foreground">
            {new Date(instance.submitted_at).toLocaleDateString()}
          </span>
        </div>
      </td>
      <td className="p-4">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSelectedInstance(instance)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setSelectedInstance(instance)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              {instance.status === 'pending' && (
                <>
                  <DropdownMenuItem onClick={() => handleQuickAction(instance, 'approve')}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Quick Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleQuickAction(instance, 'reject')}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Quick Reject
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </td>
    </tr>
  )

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Approval Dashboard</h1>
          <p className="text-muted-foreground">
            Manage and track approval workflows across your organization
          </p>
        </div>
        <div className="flex items-center gap-2">
          {selectedInstances.length > 0 && (
            <Button
              variant="outline"
              onClick={() => setShowBulkActions(true)}
            >
              Bulk Actions ({selectedInstances.length})
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => setShowFilters(true)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button
            variant="destructive"
            onClick={async () => {
              console.log('🚀 Running Database Verification...')
              await DatabaseVerification.runAllChecks()
            }}
          >
            🔧 DB Check
          </Button>
          <Button
            variant="secondary"
            onClick={async () => {
              console.log('🚀 Running Approval Workflow Tests...')
              await ApprovalWorkflowTest.runAllTests()
            }}
          >
            🔧 Workflow Test
          </Button>
          <Button
            variant="default"
            onClick={async () => {
              if (!profile?.org_id || !user?.id) {
                console.error('❌ User or organization not found')
                return
              }
              console.log('🚀 Setting up default workflows...')
              await WorkflowSetup.createDefaultWorkflows(profile.org_id, user.id)
            }}
          >
            🔧 Setup Workflows
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_pending || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.my_pending_count || 0} require your action
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.approved_today || 0}</div>
            <p className="text-xs text-muted-foreground">
              +{stats?.rejected_today || 0} rejected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Approval Time</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.average_approval_time_hours?.toFixed(1) || 0}h
            </div>
            <p className="text-xs text-muted-foreground">
              {stats?.overdue_count || 0} overdue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">By Document Type</CardTitle>
            <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              {Object.entries(stats?.pending_by_type || {}).map(([type, count]) => (
                <div key={type} className="flex justify-between text-sm">
                  <span className="capitalize">{type}</span>
                  <span className="font-medium">{count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Approval Instances</CardTitle>
              <CardDescription>
                View and manage approval workflows
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search approvals..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 w-64"
                />
              </div>
              <Select value={sortOptions.field} onValueChange={(value: ApprovalSortOptions['field']) =>
                setSortOptions({ ...sortOptions, field: value })
              }>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="submitted_at">Date Submitted</SelectItem>
                  <SelectItem value="document_amount">Amount</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList>
              <TabsTrigger value="pending">Pending ({stats?.my_pending_count || 0})</TabsTrigger>
              <TabsTrigger value="all">All Instances</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList>

            <TabsContent value="pending" className="mt-4">
              {pendingLoading ? (
                <div className="flex justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-4">
                          <input
                            type="checkbox"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedInstances(pendingApprovals?.map(i => i.id) || [])
                              } else {
                                setSelectedInstances([])
                              }
                            }}
                            className="rounded"
                          />
                        </th>
                        <th className="text-left p-4">Document</th>
                        <th className="text-left p-4">Type</th>
                        <th className="text-left p-4">Amount</th>
                        <th className="text-left p-4">Status</th>
                        <th className="text-left p-4">Submitted By</th>
                        <th className="text-left p-4">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {pendingApprovals?.map(renderInstanceRow)}
                    </tbody>
                  </table>
                </div>
              )}
            </TabsContent>

            <TabsContent value="all" className="mt-4">
              {instancesLoading ? (
                <div className="flex justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-4">
                          <input type="checkbox" className="rounded" />
                        </th>
                        <th className="text-left p-4">Document</th>
                        <th className="text-left p-4">Type</th>
                        <th className="text-left p-4">Amount</th>
                        <th className="text-left p-4">Status</th>
                        <th className="text-left p-4">Submitted By</th>
                        <th className="text-left p-4">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {allInstances?.instances.map(renderInstanceRow)}
                    </tbody>
                  </table>
                </div>
              )}
            </TabsContent>

            <TabsContent value="completed" className="mt-4">
              <div className="text-center p-8 text-muted-foreground">
                Completed approvals view - implementation pending
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Dialogs */}
      {selectedInstance && (
        <ApprovalActionDialog
          instance={selectedInstance}
          open={!!selectedInstance}
          onClose={() => setSelectedInstance(null)}
        />
      )}

      <ApprovalFiltersDialog
        open={showFilters}
        onClose={() => setShowFilters(false)}
        filters={filters}
        onFiltersChange={setFilters}
      />

      <BulkApprovalDialog
        open={showBulkActions}
        onClose={() => setShowBulkActions(false)}
        selectedCount={selectedInstances.length}
        onApprove={() => handleBulkAction('approve')}
        onReject={() => handleBulkAction('reject')}
      />
    </div>
  )
}
