# Kaya Finance Notification System

A comprehensive, real-time notification system built for the Kaya Finance platform. This system provides multi-channel notification delivery, user preferences management, analytics, and external integrations.

## 🚀 Features

### Core Functionality
- **Real-time Notifications**: WebSocket-based real-time updates with fallback to polling
- **Multi-channel Delivery**: In-app, email, push notifications, and external integrations
- **User Preferences**: Granular control over notification types and delivery methods
- **Rich Templates**: HTML email templates with organization branding
- **Analytics & Tracking**: Comprehensive delivery and engagement analytics
- **External Integrations**: Slack, Microsoft Teams, webhooks, and SMS support

### User Experience
- **Notification Center**: Dedicated interface for managing all notifications
- **Smart Filtering**: Filter by read status, category, priority, and date
- **Bulk Operations**: Mark multiple notifications as read, archive, or delete
- **Search Functionality**: Full-text search across notification content
- **Accessibility**: Full keyboard navigation and screen reader support

### Performance & Scalability
- **Virtual Scrolling**: Efficient rendering of large notification lists
- **Cursor-based Pagination**: Optimized database queries for large datasets
- **Caching Strategy**: Intelligent caching with automatic invalidation
- **Background Processing**: Asynchronous notification delivery

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Getting Started](#getting-started)
3. [API Reference](#api-reference)
4. [Components](#components)
5. [Hooks](#hooks)
6. [Database Schema](#database-schema)
7. [Configuration](#configuration)
8. [Testing](#testing)
9. [Deployment](#deployment)
10. [Troubleshooting](#troubleshooting)

## 🏗️ Architecture Overview

The notification system follows a modular architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   API Layer     │    │   Database      │
│                 │    │                 │    │                 │
│ • Components    │◄──►│ • Hooks         │◄──►│ • Tables        │
│ • Pages         │    │ • Services      │    │ • Functions     │
│ • Contexts      │    │ • Utilities     │    │ • Triggers      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  External APIs  │    │  Real-time      │    │  Background     │
│                 │    │  Updates        │    │  Jobs           │
│ • Email Service │    │                 │    │                 │
│ • Push Service  │    │ • WebSockets    │    │ • Email Queue   │
│ • Integrations  │    │ • Polling       │    │ • Cleanup       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components

1. **Notification Engine**: Core logic for creating and managing notifications
2. **Delivery System**: Multi-channel delivery with retry logic and tracking
3. **User Interface**: React components for displaying and managing notifications
4. **Real-time Layer**: WebSocket connections with fallback mechanisms
5. **Analytics Engine**: Tracking and reporting on notification performance
6. **Integration Layer**: External service connections and webhooks

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ and npm/yarn
- Supabase project with database access
- Email service provider (optional)
- Push notification service (optional)

### Installation

1. **Install Dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   ```

   Configure the following environment variables:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # Push Notifications (optional)
   NEXT_PUBLIC_VAPID_PUBLIC_KEY=your_vapid_public_key
   VAPID_PRIVATE_KEY=your_vapid_private_key

   # Email Service (optional)
   EMAIL_SERVICE_API_KEY=your_email_api_key
   EMAIL_FROM_ADDRESS=<EMAIL>
   ```

3. **Database Setup**
   ```bash
   # Run database migrations
   npx supabase db push

   # Or apply migrations manually
   psql -h your_db_host -U your_user -d your_db -f supabase/migrations/20250628_notifications_system.sql
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

### Basic Usage

1. **Add Notification Components**
   ```tsx
   import { NotificationDropdown, NotificationCenter } from '@/components/notifications'

   function App() {
     return (
       <div>
         {/* Notification bell in header */}
         <NotificationDropdown />
         
         {/* Full notification center page */}
         <NotificationCenter />
       </div>
     )
   }
   ```

2. **Create Notifications**
   ```tsx
   import { createNotification } from '@/lib/notificationHelpers'

   // Create a notification
   await createNotification({
     type: 'payment_pending_approval',
     title: 'Payment Approval Required',
     message: 'Payment of $1,000 requires your approval',
     category: 'financial',
     priority: 'high',
     data: { amount: 1000, payee: 'Vendor ABC' },
     entity_type: 'payment',
     entity_id: 'payment-123'
   }, userId, orgId)
   ```

3. **Configure User Preferences**
   ```tsx
   import { NotificationSettings } from '@/components/notifications'

   function SettingsPage() {
     return <NotificationSettings />
   }
   ```

## 📚 Quick Start Examples

### Creating Different Notification Types

```tsx
// Payment approval notification
await createNotification({
  type: 'payment_pending_approval',
  title: 'Payment Approval Required',
  message: 'Payment of $1,000 to Vendor ABC requires approval',
  priority: 'high',
  data: { amount: '$1,000', payee: 'Vendor ABC' }
}, userId, orgId)

// Invoice overdue notification
await createNotification({
  type: 'invoice_overdue',
  title: 'Invoice Overdue',
  message: 'Invoice #INV-001 is 5 days overdue',
  priority: 'urgent',
  data: { invoice_number: 'INV-001', days_overdue: 5 }
}, userId, orgId)

// System maintenance notification (organization-wide)
await createNotification({
  type: 'system_maintenance',
  title: 'Scheduled Maintenance',
  message: 'System will be down for maintenance on Sunday at 2 AM',
  priority: 'normal',
  data: { maintenance_date: '2025-07-01T02:00:00Z' }
}, null, orgId) // null userId for org-wide notifications
```

### Setting Up Real-time Updates

```tsx
import { useNotificationRealtime } from '@/hooks/useNotificationRealtime'

function MyComponent() {
  const { isConnected, connectionError, reconnect } = useNotificationRealtime()

  if (connectionError) {
    return (
      <div>
        Connection lost. 
        <button onClick={reconnect}>Reconnect</button>
      </div>
    )
  }

  return (
    <div>
      Status: {isConnected ? 'Connected' : 'Connecting...'}
    </div>
  )
}
```

### Configuring External Integrations

```tsx
import { IntegrationSettings } from '@/components/notifications'

// Add to your settings page
function IntegrationsPage() {
  return (
    <div>
      <h1>External Integrations</h1>
      <IntegrationSettings />
    </div>
  )
}
```

## 🔧 Configuration Options

### Notification Types

The system supports the following notification types:

- `payment_pending_approval` - Payment requires approval
- `payment_approved` - Payment has been approved
- `payment_rejected` - Payment has been rejected
- `invoice_overdue` - Invoice payment is overdue
- `invoice_due_soon` - Invoice payment due soon
- `invoice_paid` - Invoice has been paid
- `bill_due_soon` - Bill payment due soon
- `bill_overdue` - Bill payment is overdue
- `budget_exceeded` - Budget limit exceeded
- `budget_warning` - Approaching budget limit
- `user_invited` - User invitation sent
- `backup_completed` - System backup completed
- `backup_failed` - System backup failed
- `system_maintenance` - System maintenance notification
- `audit_alert` - Security audit alert

### Priority Levels

- `urgent` - Requires immediate attention
- `high` - Important but not critical
- `normal` - Standard priority
- `low` - Informational only

### Categories

- `financial` - Financial transactions and alerts
- `system` - System-related notifications
- `security` - Security and audit alerts
- `user` - User management notifications

## 📖 Next Steps

- [API Reference](./api-reference.md) - Detailed API documentation
- [Components Guide](./components.md) - Component usage and customization
- [Database Schema](./database-schema.md) - Complete database structure
- [Testing Guide](./testing.md) - How to test the notification system
- [Deployment Guide](./deployment.md) - Production deployment instructions

## 🤝 Contributing

Please read our [Contributing Guide](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 🆘 Support

- [Troubleshooting Guide](./troubleshooting.md)
- [FAQ](./faq.md)
- [GitHub Issues](https://github.com/your-org/kaya-finance/issues)
- [Discord Community](https://discord.gg/your-invite)
