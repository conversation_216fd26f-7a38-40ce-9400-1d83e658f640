/**
 * Global Setup for E2E Tests
 * Prepares test environment and data
 */

import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test environment setup...')

  const { baseURL } = config.projects[0].use
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...')
    await page.goto(baseURL || 'http://localhost:3000')
    await page.waitForSelector('body', { timeout: 30000 })
    
    // Check if the app is running
    const title = await page.title()
    console.log(`📱 Application loaded: ${title}`)

    // Setup test data
    await setupTestData(page)

    // Setup test user authentication
    await setupTestAuthentication(page)

    console.log('✅ E2E test environment setup complete')
  } catch (error) {
    console.error('❌ E2E test environment setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

async function setupTestData(page: import('playwright').Page) {
  console.log('📊 Setting up test data...')

  // Create test organization
  const testOrg = {
    id: 'test-org-123',
    name: 'Test Organization',
    slug: 'test-org',
    settings: {
      notifications: {
        email_enabled: true,
        push_enabled: true,
        slack_enabled: false,
        teams_enabled: false
      }
    }
  }

  // Create test user
  const testUser = {
    id: 'test-user-456',
    email: '<EMAIL>',
    full_name: 'Test User',
    org_id: testOrg.id,
    role: 'admin'
  }

  // Create test notifications
  const testNotifications = [
    {
      id: 'notification-1',
      type: 'payment_pending_approval',
      category: 'financial',
      priority: 'high',
      title: 'Payment Approval Required',
      message: 'Payment of $1,000 to Vendor ABC requires approval',
      user_id: testUser.id,
      org_id: testOrg.id,
      entity_type: 'payment',
      entity_id: 'payment-1',
      is_read: false,
      is_archived: false,
      data: {
        amount: '$1,000',
        payee: 'Vendor ABC',
        payment_id: 'payment-1'
      }
    },
    {
      id: 'notification-2',
      type: 'invoice_overdue',
      category: 'financial',
      priority: 'urgent',
      title: 'Invoice Overdue',
      message: 'Invoice #INV-001 is 5 days overdue',
      user_id: testUser.id,
      org_id: testOrg.id,
      entity_type: 'invoice',
      entity_id: 'invoice-1',
      is_read: true,
      is_archived: false,
      data: {
        invoice_number: 'INV-001',
        days_overdue: 5,
        amount: '$2,500'
      }
    },
    {
      id: 'notification-3',
      type: 'budget_warning',
      category: 'financial',
      priority: 'normal',
      title: 'Budget Warning',
      message: 'Marketing budget is at 85% capacity',
      user_id: testUser.id,
      org_id: testOrg.id,
      entity_type: 'budget',
      entity_id: 'budget-1',
      is_read: false,
      is_archived: false,
      data: {
        budget_name: 'Marketing',
        percentage: 85,
        amount_used: '$8,500',
        total_budget: '$10,000'
      }
    }
  ]

  // Store test data in localStorage for tests to access
  await page.evaluate((data) => {
    localStorage.setItem('e2e-test-org', JSON.stringify(data.testOrg))
    localStorage.setItem('e2e-test-user', JSON.stringify(data.testUser))
    localStorage.setItem('e2e-test-notifications', JSON.stringify(data.testNotifications))
  }, { testOrg, testUser, testNotifications })

  console.log('✅ Test data setup complete')
}

async function setupTestAuthentication(page: import('playwright').Page) {
  console.log('🔐 Setting up test authentication...')

  // Mock authentication state
  const authState = {
    user: {
      id: 'test-user-456',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Test User'
      }
    },
    session: {
      access_token: 'test-access-token',
      refresh_token: 'test-refresh-token',
      expires_at: Date.now() + 3600000, // 1 hour from now
      user: {
        id: 'test-user-456',
        email: '<EMAIL>'
      }
    }
  }

  // Store auth state
  await page.evaluate((authState) => {
    localStorage.setItem('supabase.auth.token', JSON.stringify(authState))
    sessionStorage.setItem('supabase.auth.token', JSON.stringify(authState))
  }, authState)

  console.log('✅ Test authentication setup complete')
}

export default globalSetup
