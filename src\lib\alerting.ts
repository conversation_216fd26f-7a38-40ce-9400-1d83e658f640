/**
 * Alerting System
 * Configurable alerting for notification system monitoring
 */

import { notificationMetrics, type AlertRule } from '@/lib/monitoring'
import { createNotification } from '@/lib/notificationHelpers'

// Define types for alert channel configurations
type EmailConfig = {
  recipients: string[]
  subject?: string
}

type SlackConfig = {
  webhookUrl: string
  channel?: string
  username?: string
}

type WebhookConfig = {
  url: string
  headers?: Record<string, string>
  method?: 'POST' | 'PUT'
}

type NotificationConfig = {
  userIds: string[]
  orgIds?: string[]
}

type AlertChannelConfig = EmailConfig | SlackConfig | WebhookConfig | NotificationConfig

export interface AlertChannel {
  id: string
  type: 'email' | 'slack' | 'webhook' | 'notification'
  name: string
  config: AlertChannelConfig
  enabled: boolean
}

export interface AlertHistory {
  id: string
  ruleId: string
  ruleName: string
  triggeredAt: Date
  resolvedAt?: Date
  value: number
  threshold: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  channels: string[]
}

/**
 * Alert Manager for notification system
 */
export class NotificationAlertManager {
  private rules: Map<string, AlertRule> = new Map()
  private channels: Map<string, AlertChannel> = new Map()
  private activeAlerts: Map<string, AlertHistory> = new Map()
  private alertHistory: AlertHistory[] = []
  private evaluationInterval: NodeJS.Timeout | null = null

  constructor() {
    this.initializeDefaultRules()
    this.initializeDefaultChannels()
  }

  /**
   * Initialize default alert rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'high-notification-failure-rate',
        name: 'High Notification Failure Rate',
        metric: 'notifications_delivered_total',
        condition: 'lt',
        threshold: 0.95, // 95% success rate
        duration: 300, // 5 minutes
        severity: 'high',
        enabled: true,
        channels: ['email', 'slack']
      },
      {
        id: 'slow-api-response',
        name: 'Slow API Response Time',
        metric: 'api_response_time_seconds',
        condition: 'gt',
        threshold: 2.0, // 2 seconds
        duration: 180, // 3 minutes
        severity: 'medium',
        enabled: true,
        channels: ['slack']
      },
      {
        id: 'database-connection-issues',
        name: 'Database Connection Issues',
        metric: 'db_query_time_seconds',
        condition: 'gt',
        threshold: 5.0, // 5 seconds
        duration: 120, // 2 minutes
        severity: 'critical',
        enabled: true,
        channels: ['email', 'slack', 'notification']
      },
      {
        id: 'realtime-connection-drop',
        name: 'Real-time Connection Drop',
        metric: 'realtime_connections_active',
        condition: 'lt',
        threshold: 1, // At least 1 connection expected
        duration: 60, // 1 minute
        severity: 'medium',
        enabled: true,
        channels: ['slack']
      },
      {
        id: 'high-notification-volume',
        name: 'High Notification Volume',
        metric: 'notifications_created_total',
        condition: 'gt',
        threshold: 1000, // 1000 notifications per evaluation period
        duration: 300, // 5 minutes
        severity: 'low',
        enabled: true,
        channels: ['notification']
      },
      {
        id: 'email-delivery-failure',
        name: 'Email Delivery Failure',
        metric: 'email_delivery_total',
        condition: 'lt',
        threshold: 0.90, // 90% success rate
        duration: 600, // 10 minutes
        severity: 'high',
        enabled: true,
        channels: ['email', 'slack']
      }
    ]

    defaultRules.forEach(rule => this.rules.set(rule.id, rule))
  }

  /**
   * Initialize default alert channels
   */
  private initializeDefaultChannels(): void {
    const defaultChannels: AlertChannel[] = [
      {
        id: 'email',
        type: 'email',
        name: 'Admin Email',
        config: {
          recipients: ['<EMAIL>', '<EMAIL>']
        },
        enabled: true
      },
      {
        id: 'slack',
        type: 'slack',
        name: 'Alerts Channel',
        config: {
          webhookUrl: process.env.SLACK_ALERT_WEBHOOK_URL,
          channel: '#alerts'
        },
        enabled: !!process.env.SLACK_ALERT_WEBHOOK_URL
      },
      {
        id: 'webhook',
        type: 'webhook',
        name: 'External Monitoring',
        config: {
          url: process.env.ALERT_WEBHOOK_URL,
          headers: {
            'Authorization': `Bearer ${process.env.ALERT_WEBHOOK_TOKEN}`
          }
        },
        enabled: !!process.env.ALERT_WEBHOOK_URL
      },
      {
        id: 'notification',
        type: 'notification',
        name: 'In-App Notifications',
        config: {
          orgId: 'system',
          userIds: ['admin-user-id'] // Configure admin user IDs
        },
        enabled: true
      }
    ]

    defaultChannels.forEach(channel => this.channels.set(channel.id, channel))
  }

  /**
   * Start alert evaluation
   */
  startEvaluation(intervalMs: number = 60000): void {
    if (this.evaluationInterval) {
      clearInterval(this.evaluationInterval)
    }

    this.evaluationInterval = setInterval(() => {
      this.evaluateAllRules()
    }, intervalMs)

    console.log(`Alert evaluation started with ${intervalMs}ms interval`)
  }

  /**
   * Stop alert evaluation
   */
  stopEvaluation(): void {
    if (this.evaluationInterval) {
      clearInterval(this.evaluationInterval)
      this.evaluationInterval = null
    }
  }

  /**
   * Evaluate all alert rules
   */
  private async evaluateAllRules(): Promise<void> {
    for (const rule of this.rules.values()) {
      if (!rule.enabled) continue

      try {
        await this.evaluateRule(rule)
      } catch (error) {
        console.error(`Error evaluating rule ${rule.id}:`, error)
      }
    }
  }

  /**
   * Evaluate a single alert rule
   */
  private async evaluateRule(rule: AlertRule): Promise<void> {
    const since = new Date(Date.now() - rule.duration * 1000)
    const stats = notificationMetrics.getStats(rule.metric, since)
    
    let value: number
    
    // Calculate value based on metric type
    if (rule.metric.includes('_total')) {
      // For rate calculations
      if (rule.metric === 'notifications_delivered_total') {
        const delivered = stats.sum
        const created = notificationMetrics.getStats('notifications_created_total', since).sum
        value = created > 0 ? delivered / created : 1.0
      } else {
        value = stats.sum
      }
    } else {
      // For time-based metrics, use average
      value = stats.avg
    }

    const isTriggered = this.checkCondition(value, rule.condition, rule.threshold)
    const alertKey = rule.id

    if (isTriggered && !this.activeAlerts.has(alertKey)) {
      // New alert
      await this.triggerAlert(rule, value)
    } else if (!isTriggered && this.activeAlerts.has(alertKey)) {
      // Alert resolved
      await this.resolveAlert(rule, value)
    }
  }

  /**
   * Check if condition is met
   */
  private checkCondition(value: number, condition: string, threshold: number): boolean {
    switch (condition) {
      case 'gt': return value > threshold
      case 'gte': return value >= threshold
      case 'lt': return value < threshold
      case 'lte': return value <= threshold
      case 'eq': return value === threshold
      default: return false
    }
  }

  /**
   * Trigger an alert
   */
  private async triggerAlert(rule: AlertRule, value: number): Promise<void> {
    const alert: AlertHistory = {
      id: `${rule.id}-${Date.now()}`,
      ruleId: rule.id,
      ruleName: rule.name,
      triggeredAt: new Date(),
      value,
      threshold: rule.threshold,
      severity: rule.severity,
      message: this.generateAlertMessage(rule, value, false),
      channels: rule.channels
    }

    this.activeAlerts.set(rule.id, alert)
    this.alertHistory.push(alert)

    // Send to configured channels
    for (const channelId of rule.channels) {
      const channel = this.channels.get(channelId)
      if (channel?.enabled) {
        await this.sendToChannel(channel, alert, false)
      }
    }

    console.error('ALERT TRIGGERED:', alert)
  }

  /**
   * Resolve an alert
   */
  private async resolveAlert(rule: AlertRule, value: number): Promise<void> {
    const activeAlert = this.activeAlerts.get(rule.id)
    if (!activeAlert) return

    activeAlert.resolvedAt = new Date()
    this.activeAlerts.delete(rule.id)

    const resolutionMessage = this.generateAlertMessage(rule, value, true)

    // Send resolution to configured channels
    for (const channelId of rule.channels) {
      const channel = this.channels.get(channelId)
      if (channel?.enabled) {
        await this.sendToChannel(channel, {
          ...activeAlert,
          message: resolutionMessage
        }, true)
      }
    }

    console.info('ALERT RESOLVED:', { rule: rule.name, value })
  }

  /**
   * Generate alert message
   */
  private generateAlertMessage(rule: AlertRule, value: number, isResolution: boolean): string {
    const action = isResolution ? 'RESOLVED' : 'TRIGGERED'
    const comparison = this.getComparisonText(rule.condition)
    
    return `🚨 ALERT ${action}: ${rule.name}
    
Metric: ${rule.metric}
Current Value: ${this.formatValue(value, rule.metric)}
Threshold: ${comparison} ${this.formatValue(rule.threshold, rule.metric)}
Severity: ${rule.severity.toUpperCase()}
Time: ${new Date().toISOString()}`
  }

  /**
   * Get comparison text for condition
   */
  private getComparisonText(condition: string): string {
    switch (condition) {
      case 'gt': return '>'
      case 'gte': return '>='
      case 'lt': return '<'
      case 'lte': return '<='
      case 'eq': return '='
      default: return condition
    }
  }

  /**
   * Format value based on metric type
   */
  private formatValue(value: number, metric: string): string {
    if (metric.includes('_rate') || metric.includes('delivered_total')) {
      return `${(value * 100).toFixed(1)}%`
    } else if (metric.includes('_time_') || metric.includes('_duration_')) {
      return `${value.toFixed(3)}s`
    } else {
      return value.toFixed(2)
    }
  }

  /**
   * Send alert to specific channel
   */
  private async sendToChannel(
    channel: AlertChannel, 
    alert: AlertHistory, 
    isResolution: boolean
  ): Promise<void> {
    try {
      switch (channel.type) {
        case 'email':
          await this.sendEmailAlert(channel, alert, isResolution)
          break
        case 'slack':
          await this.sendSlackAlert(channel, alert, isResolution)
          break
        case 'webhook':
          await this.sendWebhookAlert(channel, alert, isResolution)
          break
        case 'notification':
          await this.sendNotificationAlert(channel, alert, isResolution)
          break
        default:
          console.warn('Unknown alert channel type:', channel.type)
      }
    } catch (error) {
      console.error(`Failed to send alert to ${channel.name}:`, error)
    }
  }

  /**
   * Send email alert
   */
  private async sendEmailAlert(
    channel: AlertChannel, 
    alert: AlertHistory, 
    isResolution: boolean
  ): Promise<void> {
    // Implementation would integrate with your email service
    console.log('Email alert:', { channel: channel.name, alert: alert.message })
  }

  /**
   * Send Slack alert
   */
  private async sendSlackAlert(
    channel: AlertChannel, 
    alert: AlertHistory, 
    isResolution: boolean
  ): Promise<void> {
    if (!channel.config.webhookUrl) return

    const color = isResolution ? 'good' : 
                 alert.severity === 'critical' ? 'danger' : 
                 alert.severity === 'high' ? 'warning' : '#439FE0'

    const payload = {
      channel: channel.config.channel,
      attachments: [{
        color,
        title: isResolution ? '✅ Alert Resolved' : '🚨 Alert Triggered',
        text: alert.message,
        fields: [
          { title: 'Rule', value: alert.ruleName, short: true },
          { title: 'Metric', value: alert.ruleId, short: true },
          { title: 'Value', value: this.formatValue(alert.value, alert.ruleId), short: true },
          { title: 'Severity', value: alert.severity.toUpperCase(), short: true }
        ],
        ts: Math.floor(alert.triggeredAt.getTime() / 1000)
      }]
    }

    await fetch(channel.config.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    })
  }

  /**
   * Send webhook alert
   */
  private async sendWebhookAlert(
    channel: AlertChannel, 
    alert: AlertHistory, 
    isResolution: boolean
  ): Promise<void> {
    const payload = {
      ...alert,
      resolved: isResolution,
      timestamp: new Date().toISOString()
    }

    await fetch(channel.config.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...channel.config.headers
      },
      body: JSON.stringify(payload)
    })
  }

  /**
   * Send in-app notification alert
   */
  private async sendNotificationAlert(
    channel: AlertChannel, 
    alert: AlertHistory, 
    isResolution: boolean
  ): Promise<void> {
    const title = isResolution ? 'Alert Resolved' : 'System Alert'
    const type = isResolution ? 'system_maintenance' : 'audit_alert'
    
    // Send to admin users
    for (const userId of channel.config.userIds || []) {
      await createNotification({
        type: type as 'system' | 'user' | 'business',
        title,
        message: alert.message,
        category: 'system',
        priority: alert.severity === 'critical' ? 'urgent' : 
                 alert.severity === 'high' ? 'high' : 'normal',
        data: {
          alertId: alert.id,
          ruleId: alert.ruleId,
          metric: alert.ruleId,
          value: alert.value,
          threshold: alert.threshold
        }
      }, userId, channel.config.orgId)
    }
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): AlertHistory[] {
    return Array.from(this.activeAlerts.values())
  }

  /**
   * Get alert history
   */
  getAlertHistory(limit: number = 100): AlertHistory[] {
    return this.alertHistory
      .sort((a, b) => b.triggeredAt.getTime() - a.triggeredAt.getTime())
      .slice(0, limit)
  }

  /**
   * Add or update alert rule
   */
  setRule(rule: AlertRule): void {
    this.rules.set(rule.id, rule)
  }

  /**
   * Remove alert rule
   */
  removeRule(ruleId: string): void {
    this.rules.delete(ruleId)
    this.activeAlerts.delete(ruleId)
  }

  /**
   * Add or update alert channel
   */
  setChannel(channel: AlertChannel): void {
    this.channels.set(channel.id, channel)
  }

  /**
   * Remove alert channel
   */
  removeChannel(channelId: string): void {
    this.channels.delete(channelId)
  }
}

// Global alert manager instance
export const notificationAlertManager = new NotificationAlertManager()

// Auto-start evaluation in production
if (process.env.NODE_ENV === 'production') {
  notificationAlertManager.startEvaluation(60000) // Every minute
}
