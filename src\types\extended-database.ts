// Temporary type extensions until generated types are updated
// This file extends the database types to include newly added fields and inventory tables

import type { Customer as BaseCustomer, Vendor as BaseVendor } from './database'

// Extended Customer type with notes field
export interface CustomerWithNotes extends BaseCustomer {
  notes?: string | null
}

// Extended Vendor type with notes field
export interface VendorWithNotes extends BaseVendor {
  notes?: string | null
}

// =====================================================
// INVENTORY MANAGEMENT TYPES
// =====================================================
// These types will be moved to generated types once the database is updated

export interface ProductCategory {
  id: string
  org_id: string
  name: string
  description: string | null
  parent_id: string | null
  code: string | null
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface ProductCategoryInsert {
  id?: string
  org_id: string
  name: string
  description?: string | null
  parent_id?: string | null
  code?: string | null
  is_active?: boolean
  sort_order?: number
  created_at?: string
  updated_at?: string
}

export interface ProductCategoryUpdate {
  id?: string
  org_id?: string
  name?: string
  description?: string | null
  parent_id?: string | null
  code?: string | null
  is_active?: boolean
  sort_order?: number
  created_at?: string
  updated_at?: string
}

export interface InventoryLocation {
  id: string
  org_id: string
  name: string
  code: string
  description: string | null
  address: string | null
  is_default: boolean
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface InventoryLocationInsert {
  id?: string
  org_id: string
  name: string
  code: string
  description?: string | null
  address?: string | null
  is_default?: boolean
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

export interface InventoryLocationUpdate {
  id?: string
  org_id?: string
  name?: string
  code?: string
  description?: string | null
  address?: string | null
  is_default?: boolean
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

export interface Product {
  id: string
  org_id: string
  sku: string
  name: string
  description: string | null
  category_id: string | null
  unit_of_measure: string
  cost_price: number
  selling_price: number
  track_inventory: boolean
  reorder_level: number
  reorder_quantity: number
  barcode: string | null
  weight: number | null
  dimensions: string | null
  is_active: boolean
  is_sellable: boolean
  is_purchasable: boolean
  created_at: string
  updated_at: string
  created_by: string | null
}

export interface ProductInsert {
  id?: string
  org_id: string
  sku: string
  name: string
  description?: string | null
  category_id?: string | null
  unit_of_measure?: string
  cost_price?: number
  selling_price?: number
  track_inventory?: boolean
  reorder_level?: number
  reorder_quantity?: number
  barcode?: string | null
  weight?: number | null
  dimensions?: string | null
  is_active?: boolean
  is_sellable?: boolean
  is_purchasable?: boolean
  created_at?: string
  updated_at?: string
  created_by?: string | null
}

export interface ProductUpdate {
  id?: string
  org_id?: string
  sku?: string
  name?: string
  description?: string | null
  category_id?: string | null
  unit_of_measure?: string
  cost_price?: number
  selling_price?: number
  track_inventory?: boolean
  reorder_level?: number
  reorder_quantity?: number
  barcode?: string | null
  weight?: number | null
  dimensions?: string | null
  is_active?: boolean
  is_sellable?: boolean
  is_purchasable?: boolean
  created_at?: string
  updated_at?: string
  created_by?: string | null
}

export interface StockLevel {
  id: string
  org_id: string
  product_id: string
  location_id: string
  quantity_on_hand: number
  quantity_reserved: number
  quantity_available: number
  average_cost: number
  last_cost: number
  last_updated: string
}

export interface StockLevelInsert {
  id?: string
  org_id: string
  product_id: string
  location_id: string
  quantity_on_hand?: number
  quantity_reserved?: number
  average_cost?: number
  last_cost?: number
  last_updated?: string
}

export interface StockLevelUpdate {
  id?: string
  org_id?: string
  product_id?: string
  location_id?: string
  quantity_on_hand?: number
  quantity_reserved?: number
  average_cost?: number
  last_cost?: number
  last_updated?: string
}

export interface InventoryTransaction {
  id: string
  org_id: string
  product_id: string
  location_id: string
  transaction_type: string
  quantity: number
  unit_cost: number | null
  total_cost: number | null
  reference_type: string | null
  reference_id: string | null
  reference_number: string | null
  reason_code: string | null
  notes: string | null
  batch_number: string | null
  expiry_date: string | null
  transaction_date: string
  created_by: string | null
}

export interface InventoryTransactionInsert {
  id?: string
  org_id: string
  product_id: string
  location_id: string
  transaction_type: string
  quantity: number
  unit_cost?: number | null
  total_cost?: number | null
  reference_type?: string | null
  reference_id?: string | null
  reference_number?: string | null
  reason_code?: string | null
  notes?: string | null
  batch_number?: string | null
  expiry_date?: string | null
  transaction_date?: string
  created_by?: string | null
}

export interface InventoryTransactionUpdate {
  id?: string
  org_id?: string
  product_id?: string
  location_id?: string
  transaction_type?: string
  quantity?: number
  unit_cost?: number | null
  total_cost?: number | null
  reference_type?: string | null
  reference_id?: string | null
  reference_number?: string | null
  reason_code?: string | null
  notes?: string | null
  batch_number?: string | null
  expiry_date?: string | null
  transaction_date?: string
  created_by?: string | null
}

// =====================================================
// EXTENDED LINE ITEM TYPES WITH PRODUCT REFERENCES
// =====================================================

export interface InvoiceLineWithProduct {
  id: string
  invoice_id: string
  account_id: string | null
  product_id: string | null
  item: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number | null
  line_total: number | null
  created_at: string
}

export interface BillLineWithProduct {
  id: string
  bill_id: string
  account_id: string | null
  product_id: string | null
  item: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number | null
  line_total: number | null
  created_at: string
}

// Re-export other types for convenience
export * from './database'
export type { CustomerWithNotes as Customer, VendorWithNotes as Vendor }
