<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP Address Detection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .loading { border-left-color: #ffc107; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 IP Address Detection Test</h1>
        <p>This test checks if the audit logging system can detect your IP address correctly.</p>
        
        <div class="info">
            <strong>User Agent:</strong> <span id="userAgent"></span><br>
            <strong>Session ID:</strong> <span id="sessionId"></span>
        </div>

        <button onclick="testIPDetection()">🚀 Test IP Detection</button>
        <button onclick="clearCache()">🗑️ Clear Cache</button>
        
        <div id="results"></div>
        
        <h2>📋 Test Results</h2>
        <div id="summary"></div>
    </div>

    <script>
        // Display basic info
        document.getElementById('userAgent').textContent = navigator.userAgent;
        
        // Generate session ID like the audit logger does
        let sessionId = sessionStorage.getItem('audit_session_id');
        if (!sessionId) {
            sessionId = `session_${Date.now()}_${crypto.randomUUID()}`;
            sessionStorage.setItem('audit_session_id', sessionId);
        }
        document.getElementById('sessionId').textContent = sessionId;

        async function testIPDetection() {
            const resultsDiv = document.getElementById('results');
            const summaryDiv = document.getElementById('summary');
            
            resultsDiv.innerHTML = '<div class="result loading">🔄 Testing IP detection methods...</div>';
            summaryDiv.innerHTML = '';
            
            const results = [];
            
            // Method 1: ipify API
            try {
                const start = Date.now();
                const response = await fetch('https://api.ipify.org?format=json', {
                    signal: AbortSignal.timeout(5000)
                });
                const duration = Date.now() - start;
                
                if (response.ok) {
                    const data = await response.json();
                    results.push({
                        method: 'ipify.org',
                        success: true,
                        ip: data.ip,
                        duration: duration
                    });
                    addResult(`✅ ipify.org: ${data.ip} (${duration}ms)`, 'success');
                } else {
                    results.push({
                        method: 'ipify.org',
                        success: false,
                        error: `HTTP ${response.status}`,
                        duration: duration
                    });
                    addResult(`❌ ipify.org: HTTP ${response.status} (${duration}ms)`, 'error');
                }
            } catch (error) {
                results.push({
                    method: 'ipify.org',
                    success: false,
                    error: error.message
                });
                addResult(`❌ ipify.org: ${error.message}`, 'error');
            }
            
            // Method 2: ipapi.co
            try {
                const start = Date.now();
                const response = await fetch('https://ipapi.co/ip/', {
                    signal: AbortSignal.timeout(5000)
                });
                const duration = Date.now() - start;
                
                if (response.ok) {
                    const ip = await response.text();
                    results.push({
                        method: 'ipapi.co',
                        success: true,
                        ip: ip.trim(),
                        duration: duration
                    });
                    addResult(`✅ ipapi.co: ${ip.trim()} (${duration}ms)`, 'success');
                } else {
                    results.push({
                        method: 'ipapi.co',
                        success: false,
                        error: `HTTP ${response.status}`,
                        duration: duration
                    });
                    addResult(`❌ ipapi.co: HTTP ${response.status} (${duration}ms)`, 'error');
                }
            } catch (error) {
                results.push({
                    method: 'ipapi.co',
                    success: false,
                    error: error.message
                });
                addResult(`❌ ipapi.co: ${error.message}`, 'error');
            }
            
            // Generate summary
            const successfulResults = results.filter(r => r.success);
            const uniqueIPs = [...new Set(successfulResults.map(r => r.ip))];
            
            let summary = '<h3>📊 Summary</h3>';
            summary += `<p><strong>Successful methods:</strong> ${successfulResults.length}/${results.length}</p>`;
            summary += `<p><strong>Detected IPs:</strong> ${uniqueIPs.join(', ')}</p>`;
            
            if (uniqueIPs.length === 1) {
                summary += `<p class="result success">✅ All methods agree on IP: <strong>${uniqueIPs[0]}</strong></p>`;
            } else if (uniqueIPs.length > 1) {
                summary += `<p class="result error">⚠️ Different IPs detected - this might indicate proxy/VPN usage</p>`;
            } else {
                summary += `<p class="result error">❌ No IP addresses detected - audit logs will show "unknown"</p>`;
            }
            
            // Show fastest method
            const fastestSuccess = successfulResults.sort((a, b) => a.duration - b.duration)[0];
            if (fastestSuccess) {
                summary += `<p><strong>Fastest method:</strong> ${fastestSuccess.method} (${fastestSuccess.duration}ms)</p>`;
            }
            
            summaryDiv.innerHTML = summary;
        }
        
        function addResult(text, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = text;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearCache() {
            sessionStorage.removeItem('audit_session_id');
            location.reload();
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(testIPDetection, 1000);
        });
    </script>
</body>
</html>
