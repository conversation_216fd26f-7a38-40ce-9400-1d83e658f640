import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'

export interface InventoryAuditLog {
  id: string
  entity_type: string
  entity_id: string
  action: string
  description: string
  severity: string
  category: string
  changed_data: any
  metadata: any
  created_at: string
  profile_id: string | null
  profiles?: {
    email: string
  }
}

export interface InventoryAuditFilters {
  entity_types?: string[]
  actions?: string[]
  severity?: string[]
  product_id?: string
  location_id?: string
  date_from?: string
  date_to?: string
  user_id?: string
}

export interface InventoryAuditSummary {
  total_events: number
  by_entity_type: Array<{
    entity_type: string
    count: number
  }>
  by_action: Array<{
    action: string
    count: number
  }>
  by_severity: Array<{
    severity: string
    count: number
  }>
  recent_critical_events: InventoryAuditLog[]
}

/**
 * Hook for querying inventory-related audit logs
 */
export function useInventoryAuditLogs(filters?: InventoryAuditFilters) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['inventory-audit-logs', profile?.org_id, filters],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      let query = supabase
        .from('audit_logs')
        .select(`
          *,
          profiles:profile_id(email)
        `)
        .eq('org_id', profile.org_id)
        .or('metadata->>inventory_operation.eq.true,entity_type.in.(inventory_transaction,stock_movement,stock_reservation,purchase_order,cost_adjustment,inventory_valuation)')
        .order('created_at', { ascending: false })
        .limit(500)

      // Apply filters
      if (filters?.entity_types && filters.entity_types.length > 0) {
        query = query.in('entity_type', filters.entity_types)
      }

      if (filters?.actions && filters.actions.length > 0) {
        query = query.in('action', filters.actions)
      }

      if (filters?.severity && filters.severity.length > 0) {
        query = query.in('severity', filters.severity)
      }

      if (filters?.product_id) {
        query = query.eq('metadata->>product_id', filters.product_id)
      }

      if (filters?.location_id) {
        query = query.eq('metadata->>location_id', filters.location_id)
      }

      if (filters?.date_from) {
        query = query.gte('created_at', `${filters.date_from}T00:00:00`)
      }

      if (filters?.date_to) {
        query = query.lte('created_at', `${filters.date_to}T23:59:59`)
      }

      if (filters?.user_id) {
        query = query.eq('profile_id', filters.user_id)
      }

      const { data, error } = await query

      if (error) throw error
      return data as InventoryAuditLog[]
    },
    enabled: !!profile?.org_id,
    staleTime: 30 * 1000, // 30 seconds
  })
}

/**
 * Hook for getting inventory audit summary statistics
 */
export function useInventoryAuditSummary(dateRange?: { from: string; to: string }) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['inventory-audit-summary', profile?.org_id, dateRange],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      let query = supabase
        .from('audit_logs')
        .select('entity_type, action, severity, created_at, description, metadata')
        .eq('org_id', profile.org_id)
        .or('metadata->>inventory_operation.eq.true,entity_type.in.(inventory_transaction,stock_movement,stock_reservation,purchase_order,cost_adjustment,inventory_valuation)')

      // Apply date range if provided
      if (dateRange?.from) {
        query = query.gte('created_at', `${dateRange.from}T00:00:00`)
      }
      if (dateRange?.to) {
        query = query.lte('created_at', `${dateRange.to}T23:59:59`)
      }

      const { data, error } = await query

      if (error) throw error

      // Process the data to create summary statistics
      const summary: InventoryAuditSummary = {
        total_events: data.length,
        by_entity_type: [],
        by_action: [],
        by_severity: [],
        recent_critical_events: []
      }

      // Group by entity type
      const entityTypeCounts = data.reduce((acc, log) => {
        acc[log.entity_type] = (acc[log.entity_type] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      summary.by_entity_type = Object.entries(entityTypeCounts).map(([entity_type, count]) => ({
        entity_type,
        count
      }))

      // Group by action
      const actionCounts = data.reduce((acc, log) => {
        acc[log.action] = (acc[log.action] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      summary.by_action = Object.entries(actionCounts).map(([action, count]) => ({
        action,
        count
      }))

      // Group by severity
      const severityCounts = data.reduce((acc, log) => {
        acc[log.severity] = (acc[log.severity] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      summary.by_severity = Object.entries(severityCounts).map(([severity, count]) => ({
        severity,
        count
      }))

      // Get recent critical events (last 10)
      summary.recent_critical_events = data
        .filter(log => log.severity === 'critical' || log.severity === 'high')
        .slice(0, 10)
        .map(log => ({
          id: crypto.randomUUID(), // Generate a temporary ID for display
          entity_type: log.entity_type,
          entity_id: log.entity_type, // This would be the actual entity_id from the log
          action: log.action,
          description: log.description,
          severity: log.severity,
          category: 'inventory', // Default category for inventory operations
          changed_data: {},
          metadata: log.metadata,
          created_at: log.created_at,
          profile_id: null
        }))

      return summary
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for getting audit logs for a specific product
 */
export function useProductAuditLogs(productId: string | null) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['product-audit-logs', profile?.org_id, productId],
    queryFn: async () => {
      if (!profile?.org_id || !productId) throw new Error('Missing required parameters')

      const { data, error } = await supabase
        .from('audit_logs')
        .select(`
          *,
          profiles:profile_id(email)
        `)
        .eq('org_id', profile.org_id)
        .eq('metadata->>product_id', productId)
        .order('created_at', { ascending: false })
        .limit(100)

      if (error) throw error
      return data as InventoryAuditLog[]
    },
    enabled: !!profile?.org_id && !!productId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

/**
 * Hook for getting audit logs for a specific location
 */
export function useLocationAuditLogs(locationId: string | null) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['location-audit-logs', profile?.org_id, locationId],
    queryFn: async () => {
      if (!profile?.org_id || !locationId) throw new Error('Missing required parameters')

      const { data, error } = await supabase
        .from('audit_logs')
        .select(`
          *,
          profiles:profile_id(email)
        `)
        .eq('org_id', profile.org_id)
        .eq('metadata->>location_id', locationId)
        .order('created_at', { ascending: false })
        .limit(100)

      if (error) throw error
      return data as InventoryAuditLog[]
    },
    enabled: !!profile?.org_id && !!locationId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

/**
 * Hook for getting audit logs for a specific transaction
 */
export function useTransactionAuditLogs(transactionId: string | null) {
  const { profile } = useAuth()

  return useQuery({
    queryKey: ['transaction-audit-logs', profile?.org_id, transactionId],
    queryFn: async () => {
      if (!profile?.org_id || !transactionId) throw new Error('Missing required parameters')

      const { data, error } = await supabase
        .from('audit_logs')
        .select(`
          *,
          profiles:profile_id(email)
        `)
        .eq('org_id', profile.org_id)
        .or(`entity_id.eq.${transactionId},metadata->>transaction_id.eq.${transactionId}`)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as InventoryAuditLog[]
    },
    enabled: !!profile?.org_id && !!transactionId,
    staleTime: 30 * 1000, // 30 seconds
  })
}
