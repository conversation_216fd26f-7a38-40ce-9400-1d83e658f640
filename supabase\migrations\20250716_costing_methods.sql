-- =====================================================
-- COSTING METHODS MIGRATION
-- =====================================================
-- This migration implements configurable costing methods (FIFO, LIFO, Average Cost)
-- with automatic cost calculation triggers and valuation updates

-- Organization costing settings table
CREATE TABLE IF NOT EXISTS organization_costing_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Costing method configuration
    default_costing_method VARCHAR(20) DEFAULT 'average' CHECK (default_costing_method IN ('fifo', 'lifo', 'average', 'specific')),
    
    -- Valuation settings
    auto_update_costs BOOLEAN DEFAULT true,
    revalue_on_receipt BOOLEAN DEFAULT true,
    
    -- Rounding settings
    cost_decimal_places INTEGER DEFAULT 4 CHECK (cost_decimal_places >= 0 AND cost_decimal_places <= 8),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(org_id)
);

-- Product-specific costing overrides
CREATE TABLE IF NOT EXISTS product_costing_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    
    -- Override costing method for this product
    costing_method VARCHAR(20) NOT NULL CHECK (costing_method IN ('fifo', 'lifo', 'average', 'specific')),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(org_id, product_id)
);

-- Cost layers table for FIFO/LIFO tracking
CREATE TABLE IF NOT EXISTS inventory_cost_layers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES inventory_locations(id) ON DELETE CASCADE,
    
    -- Layer details
    layer_date DATE NOT NULL,
    unit_cost DECIMAL(15,4) NOT NULL CHECK (unit_cost >= 0),
    quantity_received DECIMAL(15,3) NOT NULL CHECK (quantity_received > 0),
    quantity_remaining DECIMAL(15,3) NOT NULL CHECK (quantity_remaining >= 0),
    
    -- Reference to the transaction that created this layer
    source_transaction_id UUID REFERENCES inventory_transactions(id) ON DELETE SET NULL,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_org_costing_settings_org_id ON organization_costing_settings(org_id);
CREATE INDEX IF NOT EXISTS idx_product_costing_settings_org_product ON product_costing_settings(org_id, product_id);
CREATE INDEX IF NOT EXISTS idx_cost_layers_product_location ON inventory_cost_layers(org_id, product_id, location_id);
CREATE INDEX IF NOT EXISTS idx_cost_layers_date ON inventory_cost_layers(layer_date);
CREATE INDEX IF NOT EXISTS idx_cost_layers_active ON inventory_cost_layers(is_active) WHERE is_active = true;

-- RLS Policies
ALTER TABLE organization_costing_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_costing_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_cost_layers ENABLE ROW LEVEL SECURITY;

-- Organization costing settings policies
CREATE POLICY "Users can view costing settings in their organization" ON organization_costing_settings
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage costing settings in their organization" ON organization_costing_settings
    FOR ALL USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

-- Product costing settings policies
CREATE POLICY "Users can view product costing settings in their organization" ON product_costing_settings
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage product costing settings in their organization" ON product_costing_settings
    FOR ALL USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

-- Cost layers policies
CREATE POLICY "Users can view cost layers in their organization" ON inventory_cost_layers
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage cost layers in their organization" ON inventory_cost_layers
    FOR ALL USING (
        org_id IN (
            SELECT org_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

-- Function to get costing method for a product
CREATE OR REPLACE FUNCTION get_product_costing_method(
    p_org_id UUID,
    p_product_id UUID
)
RETURNS VARCHAR(20)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    product_method VARCHAR(20);
    org_method VARCHAR(20);
BEGIN
    -- Check for product-specific override
    SELECT costing_method INTO product_method
    FROM product_costing_settings
    WHERE org_id = p_org_id AND product_id = p_product_id;
    
    IF FOUND THEN
        RETURN product_method;
    END IF;
    
    -- Use organization default
    SELECT default_costing_method INTO org_method
    FROM organization_costing_settings
    WHERE org_id = p_org_id;
    
    -- Return organization default or 'average' if not set
    RETURN COALESCE(org_method, 'average');
END;
$$;

-- Function to calculate cost using FIFO method
CREATE OR REPLACE FUNCTION calculate_fifo_cost(
    p_org_id UUID,
    p_product_id UUID,
    p_location_id UUID,
    p_quantity DECIMAL(15,3)
)
RETURNS TABLE(
    total_cost DECIMAL(15,2),
    average_unit_cost DECIMAL(15,4),
    layers_consumed JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    layer_record RECORD;
    remaining_quantity DECIMAL(15,3) := p_quantity;
    total_cost_calc DECIMAL(15,2) := 0;
    layers_used JSONB := '[]'::jsonb;
    layer_info JSONB;
    quantity_from_layer DECIMAL(15,3);
BEGIN
    -- Process cost layers in FIFO order (oldest first)
    FOR layer_record IN
        SELECT *
        FROM inventory_cost_layers
        WHERE org_id = p_org_id
        AND product_id = p_product_id
        AND location_id = p_location_id
        AND quantity_remaining > 0
        AND is_active = true
        ORDER BY layer_date ASC, created_at ASC
    LOOP
        EXIT WHEN remaining_quantity <= 0;
        
        -- Calculate quantity to take from this layer
        quantity_from_layer := LEAST(remaining_quantity, layer_record.quantity_remaining);
        
        -- Add to total cost
        total_cost_calc := total_cost_calc + (quantity_from_layer * layer_record.unit_cost);
        
        -- Track layer usage
        layer_info := jsonb_build_object(
            'layer_id', layer_record.id,
            'unit_cost', layer_record.unit_cost,
            'quantity_used', quantity_from_layer,
            'layer_date', layer_record.layer_date
        );
        layers_used := layers_used || layer_info;
        
        -- Reduce remaining quantity
        remaining_quantity := remaining_quantity - quantity_from_layer;
    END LOOP;
    
    -- Return results
    total_cost := total_cost_calc;
    average_unit_cost := CASE 
        WHEN p_quantity > 0 THEN total_cost_calc / p_quantity 
        ELSE 0 
    END;
    layers_consumed := layers_used;
    
    RETURN NEXT;
END;
$$;

-- Function to calculate cost using LIFO method
CREATE OR REPLACE FUNCTION calculate_lifo_cost(
    p_org_id UUID,
    p_product_id UUID,
    p_location_id UUID,
    p_quantity DECIMAL(15,3)
)
RETURNS TABLE(
    total_cost DECIMAL(15,2),
    average_unit_cost DECIMAL(15,4),
    layers_consumed JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    layer_record RECORD;
    remaining_quantity DECIMAL(15,3) := p_quantity;
    total_cost_calc DECIMAL(15,2) := 0;
    layers_used JSONB := '[]'::jsonb;
    layer_info JSONB;
    quantity_from_layer DECIMAL(15,3);
BEGIN
    -- Process cost layers in LIFO order (newest first)
    FOR layer_record IN
        SELECT *
        FROM inventory_cost_layers
        WHERE org_id = p_org_id
        AND product_id = p_product_id
        AND location_id = p_location_id
        AND quantity_remaining > 0
        AND is_active = true
        ORDER BY layer_date DESC, created_at DESC
    LOOP
        EXIT WHEN remaining_quantity <= 0;
        
        -- Calculate quantity to take from this layer
        quantity_from_layer := LEAST(remaining_quantity, layer_record.quantity_remaining);
        
        -- Add to total cost
        total_cost_calc := total_cost_calc + (quantity_from_layer * layer_record.unit_cost);
        
        -- Track layer usage
        layer_info := jsonb_build_object(
            'layer_id', layer_record.id,
            'unit_cost', layer_record.unit_cost,
            'quantity_used', quantity_from_layer,
            'layer_date', layer_record.layer_date
        );
        layers_used := layers_used || layer_info;
        
        -- Reduce remaining quantity
        remaining_quantity := remaining_quantity - quantity_from_layer;
    END LOOP;
    
    -- Return results
    total_cost := total_cost_calc;
    average_unit_cost := CASE 
        WHEN p_quantity > 0 THEN total_cost_calc / p_quantity 
        ELSE 0 
    END;
    layers_consumed := layers_used;
    
    RETURN NEXT;
END;
$$;

-- Function to calculate average cost
CREATE OR REPLACE FUNCTION calculate_average_cost(
    p_org_id UUID,
    p_product_id UUID,
    p_location_id UUID,
    p_quantity DECIMAL(15,3)
)
RETURNS TABLE(
    total_cost DECIMAL(15,2),
    average_unit_cost DECIMAL(15,4)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_avg_cost DECIMAL(15,4);
BEGIN
    -- Get current average cost from stock levels
    SELECT average_cost INTO current_avg_cost
    FROM stock_levels
    WHERE org_id = p_org_id
    AND product_id = p_product_id
    AND location_id = p_location_id;
    
    current_avg_cost := COALESCE(current_avg_cost, 0);
    
    -- Calculate total cost
    total_cost := current_avg_cost * p_quantity;
    average_unit_cost := current_avg_cost;
    
    RETURN NEXT;
END;
$$;

-- Function to update cost layers when inventory is received
CREATE OR REPLACE FUNCTION update_cost_layers_on_receipt()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    costing_method VARCHAR(20);
BEGIN
    -- Only process purchase transactions
    IF NEW.transaction_type != 'purchase' THEN
        RETURN NEW;
    END IF;
    
    -- Get costing method for this product
    costing_method := get_product_costing_method(NEW.org_id, NEW.product_id);
    
    -- For FIFO/LIFO, create cost layers
    IF costing_method IN ('fifo', 'lifo') THEN
        INSERT INTO inventory_cost_layers (
            org_id,
            product_id,
            location_id,
            layer_date,
            unit_cost,
            quantity_received,
            quantity_remaining,
            source_transaction_id
        )
        VALUES (
            NEW.org_id,
            NEW.product_id,
            NEW.location_id,
            NEW.transaction_date::date,
            COALESCE(NEW.unit_cost, 0),
            NEW.quantity,
            NEW.quantity,
            NEW.id
        );
    END IF;
    
    RETURN NEW;
END;
$$;

-- Function to consume cost layers when inventory is sold
CREATE OR REPLACE FUNCTION consume_cost_layers_on_sale()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    costing_method VARCHAR(20);
    cost_result RECORD;
    layer_info JSONB;
    layer_record RECORD;
    quantity_to_consume DECIMAL(15,3);
BEGIN
    -- Only process sale transactions
    IF NEW.transaction_type != 'sale' OR NEW.quantity >= 0 THEN
        RETURN NEW;
    END IF;
    
    -- Get costing method for this product
    costing_method := get_product_costing_method(NEW.org_id, NEW.product_id);
    
    -- For FIFO/LIFO, consume cost layers
    IF costing_method IN ('fifo', 'lifo') THEN
        -- Calculate cost and get layers to consume
        IF costing_method = 'fifo' THEN
            SELECT * INTO cost_result
            FROM calculate_fifo_cost(NEW.org_id, NEW.product_id, NEW.location_id, ABS(NEW.quantity));
        ELSE
            SELECT * INTO cost_result
            FROM calculate_lifo_cost(NEW.org_id, NEW.product_id, NEW.location_id, ABS(NEW.quantity));
        END IF;
        
        -- Update the transaction with calculated cost
        UPDATE inventory_transactions
        SET 
            unit_cost = cost_result.average_unit_cost,
            total_cost = cost_result.total_cost
        WHERE id = NEW.id;
        
        -- Consume the layers
        FOR layer_record IN
            SELECT 
                (value->>'layer_id')::UUID as layer_id,
                (value->>'quantity_used')::DECIMAL(15,3) as quantity_used
            FROM jsonb_array_elements(cost_result.layers_consumed)
        LOOP
            UPDATE inventory_cost_layers
            SET 
                quantity_remaining = quantity_remaining - layer_record.quantity_used,
                updated_at = NOW()
            WHERE id = layer_record.layer_id;
            
            -- Mark layer as inactive if fully consumed
            UPDATE inventory_cost_layers
            SET is_active = false
            WHERE id = layer_record.layer_id
            AND quantity_remaining <= 0;
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Create triggers for cost layer management
DROP TRIGGER IF EXISTS cost_layers_receipt_trigger ON inventory_transactions;
CREATE TRIGGER cost_layers_receipt_trigger
    AFTER INSERT ON inventory_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_cost_layers_on_receipt();

DROP TRIGGER IF EXISTS cost_layers_consumption_trigger ON inventory_transactions;
CREATE TRIGGER cost_layers_consumption_trigger
    AFTER INSERT ON inventory_transactions
    FOR EACH ROW
    EXECUTE FUNCTION consume_cost_layers_on_sale();

-- Initialize default costing settings for existing organizations
INSERT INTO organization_costing_settings (org_id, default_costing_method, auto_update_costs, revalue_on_receipt, cost_decimal_places)
SELECT 
    id,
    'average',
    true,
    true,
    4
FROM organizations
WHERE NOT EXISTS (
    SELECT 1 FROM organization_costing_settings 
    WHERE org_id = organizations.id
);

-- Comments for documentation
COMMENT ON TABLE organization_costing_settings IS 'Organization-level costing method configuration and settings';
COMMENT ON TABLE product_costing_settings IS 'Product-specific costing method overrides';
COMMENT ON TABLE inventory_cost_layers IS 'Cost layers for FIFO/LIFO inventory costing methods';
COMMENT ON FUNCTION get_product_costing_method(UUID, UUID) IS 'Returns the costing method for a specific product, considering overrides';
COMMENT ON FUNCTION calculate_fifo_cost(UUID, UUID, UUID, DECIMAL) IS 'Calculates cost using First-In-First-Out method';
COMMENT ON FUNCTION calculate_lifo_cost(UUID, UUID, UUID, DECIMAL) IS 'Calculates cost using Last-In-First-Out method';
COMMENT ON FUNCTION calculate_average_cost(UUID, UUID, UUID, DECIMAL) IS 'Calculates cost using weighted average method';
