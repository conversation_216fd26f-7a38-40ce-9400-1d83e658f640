# Kaya Finance API Documentation

Welcome to the Kaya Finance API documentation. This API provides comprehensive access to all financial management features through a RESTful interface built on Supabase.

## Overview

The Kaya Finance API is built on Supabase, providing:
- **RESTful endpoints** for all data operations
- **Real-time subscriptions** for live updates
- **Row Level Security (RLS)** for data protection
- **JWT-based authentication** for secure access
- **Automatic API generation** from database schema

## Base URL

```
Production: https://your-project.supabase.co/rest/v1/
Staging: https://your-staging-project.supabase.co/rest/v1/
```

## Authentication

All API requests require authentication using JWT tokens provided by Supabase Auth.

### Headers

```http
Authorization: Bearer <your-jwt-token>
apikey: <your-supabase-anon-key>
Content-Type: application/json
```

### Getting a Token

```javascript
// Using Supabase client
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})

const token = data.session?.access_token
```

## Core Entities

### Organizations
- **Endpoint**: `/organizations`
- **Description**: Company/organization management
- **Access**: Admin only

### Users
- **Endpoint**: `/users`
- **Description**: User management and profiles
- **Access**: Admin and self

### Customers
- **Endpoint**: `/customers`
- **Description**: Customer contact management
- **Access**: Organization members

### Vendors
- **Endpoint**: `/vendors`
- **Description**: Vendor/supplier management
- **Access**: Organization members

### Invoices
- **Endpoint**: `/invoices`
- **Description**: Invoice creation and management
- **Access**: Organization members

### Bills
- **Endpoint**: `/bills`
- **Description**: Bill and expense management
- **Access**: Organization members

### Payments
- **Endpoint**: `/payments`
- **Description**: Payment tracking and reconciliation
- **Access**: Organization members

## Common Patterns

### Filtering

Use query parameters to filter results:

```http
GET /customers?name=eq.John&status=eq.active
GET /invoices?created_at=gte.2024-01-01&status=in.(draft,sent)
```

### Ordering

```http
GET /invoices?order=created_at.desc
GET /customers?order=name.asc,created_at.desc
```

### Pagination

```http
GET /invoices?limit=20&offset=40
```

### Selecting Fields

```http
GET /customers?select=id,name,email
GET /invoices?select=id,invoice_number,total_amount,customer:customers(name)
```

### Relationships

```http
# Include related data
GET /invoices?select=*,customer:customers(*),line_items:invoice_line_items(*)

# Filter by related data
GET /invoices?customer.name=eq.John
```

## Response Format

### Success Response

```json
{
  "data": [
    {
      "id": "uuid",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      // ... entity fields
    }
  ],
  "count": 100,
  "status": 200,
  "statusText": "OK"
}
```

### Error Response

```json
{
  "error": {
    "message": "Error description",
    "details": "Detailed error information",
    "hint": "Suggestion for fixing the error",
    "code": "ERROR_CODE"
  },
  "status": 400,
  "statusText": "Bad Request"
}
```

## Common Operations

### Create a Customer

```http
POST /customers
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "*********",
  "tin_number": "1234567890",
  "payment_terms": 30
}
```

### Update a Customer

```http
PATCH /customers?id=eq.customer-uuid
Content-Type: application/json

{
  "name": "John Smith",
  "email": "<EMAIL>"
}
```

### Create an Invoice

```http
POST /invoices
Content-Type: application/json

{
  "customer_id": "customer-uuid",
  "invoice_number": "INV-001",
  "date_issued": "2024-01-01",
  "due_date": "2024-01-31",
  "subtotal": 1000,
  "tax_amount": 180,
  "total_amount": 1180,
  "status": "draft"
}
```

### Add Invoice Line Items

```http
POST /invoice_line_items
Content-Type: application/json

[
  {
    "invoice_id": "invoice-uuid",
    "item": "Product A",
    "description": "Product description",
    "quantity": 2,
    "unit_price": 500,
    "tax_rate_pct": 18
  }
]
```

### Record a Payment

```http
POST /payments
Content-Type: application/json

{
  "payee_type": "customer",
  "payee_id": "customer-uuid",
  "invoice_id": "invoice-uuid",
  "amount": 1180,
  "payment_method": "bank_transfer",
  "payment_date": "2024-01-15",
  "reference_number": "TXN-001"
}
```

## Real-time Subscriptions

Subscribe to real-time updates using Supabase's real-time features:

```javascript
// Subscribe to invoice changes
const subscription = supabase
  .channel('invoices')
  .on('postgres_changes', 
    { 
      event: '*', 
      schema: 'public', 
      table: 'invoices' 
    }, 
    (payload) => {
      console.log('Invoice updated:', payload)
    }
  )
  .subscribe()

// Unsubscribe
subscription.unsubscribe()
```

## Error Handling

### Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 401 | Unauthorized | Check authentication token |
| 403 | Forbidden | Check user permissions |
| 404 | Not Found | Verify resource exists |
| 409 | Conflict | Check for duplicate data |
| 422 | Validation Error | Check request data format |
| 500 | Server Error | Contact support |

### Error Handling Example

```javascript
try {
  const { data, error } = await supabase
    .from('customers')
    .insert(customerData)
  
  if (error) {
    switch (error.code) {
      case '23505': // Unique violation
        throw new Error('Customer already exists')
      case '23503': // Foreign key violation
        throw new Error('Invalid reference data')
      default:
        throw new Error(error.message)
    }
  }
  
  return data
} catch (error) {
  console.error('API Error:', error)
  throw error
}
```

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Authenticated requests**: 1000 requests per hour
- **Unauthenticated requests**: 100 requests per hour
- **Bulk operations**: 100 requests per hour

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Best Practices

### 1. Use Appropriate HTTP Methods

- **GET**: Retrieve data
- **POST**: Create new resources
- **PATCH**: Update existing resources
- **DELETE**: Remove resources

### 2. Handle Errors Gracefully

Always check for errors and handle them appropriately:

```javascript
const { data, error } = await supabase
  .from('customers')
  .select('*')

if (error) {
  console.error('Error:', error)
  // Handle error appropriately
  return
}

// Process data
console.log('Customers:', data)
```

### 3. Use Transactions for Related Operations

```javascript
const { data, error } = await supabase.rpc('create_invoice_with_items', {
  invoice_data: invoiceData,
  line_items: lineItems
})
```

### 4. Optimize Queries

- Select only needed fields
- Use appropriate filters
- Implement pagination for large datasets
- Use indexes for frequently queried fields

### 5. Security Considerations

- Never expose sensitive data in URLs
- Validate all input data
- Use HTTPS for all requests
- Implement proper authentication
- Follow principle of least privilege

## SDK and Libraries

### JavaScript/TypeScript

```bash
npm install @supabase/supabase-js
```

```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'your-project-url',
  'your-anon-key'
)
```

### React Integration

```javascript
import { useQuery, useMutation } from '@tanstack/react-query'

// Fetch customers
const { data: customers, isLoading } = useQuery({
  queryKey: ['customers'],
  queryFn: () => supabase.from('customers').select('*')
})

// Create customer
const createCustomer = useMutation({
  mutationFn: (customerData) => 
    supabase.from('customers').insert(customerData),
  onSuccess: () => {
    queryClient.invalidateQueries(['customers'])
  }
})
```

## Testing

### Test Environment

Use a separate Supabase project for testing:

```javascript
const testSupabase = createClient(
  process.env.VITE_SUPABASE_URL_TEST,
  process.env.VITE_SUPABASE_ANON_KEY_TEST
)
```

### Example Test

```javascript
describe('Customer API', () => {
  it('should create a customer', async () => {
    const customerData = {
      name: 'Test Customer',
      email: '<EMAIL>'
    }

    const { data, error } = await supabase
      .from('customers')
      .insert(customerData)
      .select()

    expect(error).toBeNull()
    expect(data[0]).toMatchObject(customerData)
  })
})
```

## Support

For API support:

- 📖 [Full API Reference](https://your-project.supabase.co/rest/v1/)
- 💬 [GitHub Discussions](https://github.com/your-username/kaya-finance/discussions)
- 🐛 [Report Issues](https://github.com/your-username/kaya-finance/issues)
- 📧 [Email Support](mailto:<EMAIL>)

---

*For more detailed endpoint documentation, see the individual API reference pages.*
