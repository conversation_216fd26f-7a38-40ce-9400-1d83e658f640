/**
 * React Query Hooks Index
 * 
 * This file exports all the custom React Query hooks for easy importing
 * across the application. It provides a centralized location for all
 * data fetching and mutation hooks.
 */

// Customer hooks
export {
  useCustomers,
  useActiveCustomers,
  useCustomer,
  useCreateCustomer,
  useUpdateCustomer,
  useDeleteCustomer,
  useCanDeleteCustomer,
  useToggleCustomerStatus,
} from './useCustomers'

// Vendor hooks
export {
  useVendors,
  useActiveVendors,
  useVendor,
  useCreateVendor,
  useUpdateVendor,
  useDeleteVendor,
  useToggleVendorStatus,
} from './useVendors'

// Account hooks
export {
  useAccounts,
  useActiveAccounts,
  useAccountsByType,
  useAccount,
  useAccountBalance,
  useCreateAccount,
  useUpdateAccount,
  useDeleteAccount,
} from './useAccounts'

// Invoice hooks
export {
  useInvoices,
  useInvoicesByCustomer,
  useInvoicesByStatus,
  useInvoice,
  useCreateInvoice,
  useUpdateInvoice,
  useDeleteInvoice,
  useUpdateInvoiceStatus,
} from './useInvoices'

// Bill hooks
export {
  useBills,
  useBillsByVendor,
  useBillsByStatus,
  useBill,
  useCreateBill,
  useUpdateBill,
  useDeleteBill,
  useUpdateBillStatus,
} from './useBills'

// Payment hooks
export {
  usePayments,
  useRecentPayments,
  usePaymentsByEntity,
  usePayment,
  useCreatePayment,
  useUpdatePayment,
  useDeletePayment,
} from './usePayments'

// Journal Entry hooks
export {
  useJournalEntries,
  useJournalEntriesByAccount,
  useJournalEntry,
  useCreateJournalEntry,
  useUpdateJournalEntry,
  useDeleteJournalEntry,
} from './useJournalEntries'

// Tax hooks
export {
  useTaxRates,
  useWithholdingTaxRates,
  useActiveWithholdingTaxRates,
  useUraTaxFilings,
  useCreateTaxRate,
  useCreateWithholdingTaxRate,
  useCreateUraTaxFiling,
  useUpdateTaxRate,
  useUpdateWithholdingTaxRate,
  useDeleteTaxRate,
} from './useTax'

// Notification hooks
export {
  useNotifications,
  useNotificationCount,
  useNotificationStats,
  useMarkNotificationAsRead,
  useMarkAllNotificationsAsRead,
  useArchiveNotification,
  useDeleteNotification,
  useCreateNotification,
  useNotificationPreferences,
  useUpdateNotificationPreference,
  useBulkUpdateNotificationPreferences,
  useNotificationTemplates,
  useBulkArchiveNotifications,
  useBulkDeleteNotifications,
  useSnoozeNotification,
  useForwardNotification,
} from './useNotifications'

// Re-export query keys and utilities
export { queryKeys, queryKeyUtils } from '@/lib/queryKeys'
export type { QueryFilters } from '@/lib/queryKeys'

// Inventory hooks - Products
export {
  useProducts,
  useActiveProducts,
  useProductsWithStock,
  useLowStockProducts,
  useProduct,
  useProductSearch,
  useCreateProduct,
  useUpdateProduct,
  useDeleteProduct,
  useToggleProductStatus,
  useDuplicateProduct,
  useBulkProductOperations,
} from './useProducts'

// Inventory hooks - Product Categories
export {
  useProductCategories,
  useActiveProductCategories,
  useProductCategoryTree,
  useProductCategory,
  useCreateProductCategory,
  useUpdateProductCategory,
  useDeleteProductCategory,
  useToggleProductCategoryStatus,
} from './useProductCategories'

// Inventory hooks - Locations
export {
  useInventoryLocations,
  useActiveInventoryLocations,
  useInventoryLocation,
  useDefaultInventoryLocation,
  useCreateInventoryLocation,
  useUpdateInventoryLocation,
  useDeleteInventoryLocation,
  useToggleInventoryLocationStatus,
} from './useInventoryLocations'

// Inventory hooks - Stock Levels
export {
  useStockLevels,
  useStockLevelsByProduct,
  useStockLevelsByLocation,
  useLowStockLevels,
  useStockLevel,
  useStockAdjustment,
  useReserveStock,
  useReleaseStock,
} from './useStockLevels'

// Inventory hooks - Transactions
export {
  useInventoryTransactions,
  useInventoryTransactionsByProduct,
  useInventoryTransactionsByLocation,
  useRecentInventoryTransactions,
  useCreateInventoryTransaction,
  useInventoryMovementSummary,
  useInventoryValuation,
  useStockMovementReport,
  useABCAnalysis,
} from './useInventoryTransactions'

// Inventory hooks - Dashboard
export {
  useInventoryDashboardSummary,
  useInventoryAlerts,
  useTopSellingProducts,
  useInventoryTurnover,
  useSlowMovingProducts,
  useProductsRequiringReorder,
  useInventoryAging,
  useStockLevelTrends,
} from './useInventoryDashboard'

// Re-export types for convenience
export type { PaymentWithDetails } from './usePayments'
export type { JournalEntryWithLines } from './useJournalEntries'
