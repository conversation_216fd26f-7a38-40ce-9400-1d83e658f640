import { Clock, X, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import type { RecentSearch } from '@/types/search'

interface RecentSearchesProps {
  searches: RecentSearch[]
  onSearchSelect: (query: string) => void
  onRemoveSearch: (id: string) => void
  onClearAll: () => void
}

export function RecentSearches({ 
  searches, 
  onSearchSelect, 
  onRemoveSearch, 
  onClearAll 
}: RecentSearchesProps) {
  if (searches.length === 0) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No recent searches</p>
      </div>
    )
  }

  return (
    <div className="p-2">
      <div className="flex items-center justify-between px-2 py-1 mb-2">
        <span className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
          Recent Searches
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearAll}
          className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
        >
          <Trash2 className="h-3 w-3 mr-1" />
          Clear
        </Button>
      </div>
      
      <div className="space-y-1">
        {searches.map((search) => (
          <div
            key={search.id}
            className="group flex items-center justify-between px-2 py-2 rounded-md hover:bg-accent cursor-pointer"
            onClick={() => onSearchSelect(search.query)}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <Clock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{search.query}</p>
                <p className="text-xs text-muted-foreground">
                  {search.resultCount} result{search.resultCount !== 1 ? 's' : ''} • {' '}
                  {new Date(search.timestamp).toLocaleDateString('en-UG', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onRemoveSearch(search.id)
              }}
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}
