/**
 * Query Key Factory for React Query
 * 
 * This module provides standardized query key generation for consistent
 * caching and invalidation across the application.
 * 
 * Query Key Structure:
 * - Level 1: Entity type (e.g., 'customers', 'vendors')
 * - Level 2: Organization ID (for multi-tenant isolation)
 * - Level 3: Specific query type or filters
 * 
 * Examples:
 * - ['customers', 'org-123'] - All customers for org
 * - ['customers', 'org-123', 'active'] - Active customers for org
 * - ['customers', 'org-123', { search: 'john' }] - Filtered customers
 * - ['customer', 'org-123', 'customer-456'] - Single customer
 */

export type QueryKeyBase = readonly unknown[]

export interface QueryFilters {
  search?: string
  status?: string
  active?: boolean
  limit?: number
  offset?: number
  [key: string]: unknown
}

/**
 * Base query key factory
 */
export const queryKeys = {
  // Organization-level keys
  organization: (orgId: string) => ['organization', orgId] as const,
  
  // Customer-related keys
  customers: {
    all: (orgId: string) => ['customers', orgId] as const,
    active: (orgId: string) => ['customers', orgId, 'active'] as const,
    filtered: (orgId: string, filters: QueryFilters) => ['customers', orgId, filters] as const,
    detail: (orgId: string, customerId: string) => ['customer', orgId, customerId] as const,
    statements: (orgId: string, customerId: string, filters?: QueryFilters) =>
      ['customer-statements', orgId, customerId, filters] as const,
    canDelete: (orgId: string, customerId: string) => ['customer-can-delete', orgId, customerId] as const,
  },

  // Vendor-related keys
  vendors: {
    all: (orgId: string) => ['vendors', orgId] as const,
    active: (orgId: string) => ['vendors', orgId, 'active'] as const,
    filtered: (orgId: string, filters: QueryFilters) => ['vendors', orgId, filters] as const,
    detail: (orgId: string, vendorId: string) => ['vendor', orgId, vendorId] as const,
    statements: (orgId: string, vendorId: string, filters?: QueryFilters) => 
      ['vendor-statements', orgId, vendorId, filters] as const,
  },

  // Account-related keys
  accounts: {
    all: (orgId: string) => ['accounts', orgId] as const,
    active: (orgId: string) => ['accounts', orgId, 'active'] as const,
    byType: (orgId: string, accountType: string) => ['accounts', orgId, 'type', accountType] as const,
    detail: (orgId: string, accountId: string) => ['account', orgId, accountId] as const,
    balance: (orgId: string, accountId: string, date?: string) => 
      ['account-balance', orgId, accountId, date] as const,
  },

  // Invoice-related keys
  invoices: {
    all: (orgId: string) => ['invoices', orgId] as const,
    filtered: (orgId: string, filters: QueryFilters) => ['invoices', orgId, filters] as const,
    detail: (orgId: string, invoiceId: string) => ['invoice', orgId, invoiceId] as const,
    byCustomer: (orgId: string, customerId: string) => ['invoices', orgId, 'customer', customerId] as const,
    byStatus: (orgId: string, status: string) => ['invoices', orgId, 'status', status] as const,
  },

  // Bill-related keys
  bills: {
    all: (orgId: string) => ['bills', orgId] as const,
    filtered: (orgId: string, filters: QueryFilters) => ['bills', orgId, filters] as const,
    detail: (orgId: string, billId: string) => ['bill', orgId, billId] as const,
    byVendor: (orgId: string, vendorId: string) => ['bills', orgId, 'vendor', vendorId] as const,
    byStatus: (orgId: string, status: string) => ['bills', orgId, 'status', status] as const,
  },

  // Payment-related keys
  payments: {
    all: (orgId: string) => ['payments', orgId] as const,
    filtered: (orgId: string, filters: QueryFilters) => ['payments', orgId, filters] as const,
    detail: (orgId: string, paymentId: string) => ['payment', orgId, paymentId] as const,
    recent: (orgId: string, limit?: number) => ['payments', orgId, 'recent', limit] as const,
    byEntity: (orgId: string, entityType: 'customer' | 'vendor', entityId: string) => 
      ['payments', orgId, entityType, entityId] as const,
  },

  // Journal Entry-related keys
  journalEntries: {
    all: (orgId: string) => ['journal-entries', orgId] as const,
    filtered: (orgId: string, filters: QueryFilters) => ['journal-entries', orgId, filters] as const,
    detail: (orgId: string, entryId: string) => ['journal-entry', orgId, entryId] as const,
    byAccount: (orgId: string, accountId: string) => ['journal-entries', orgId, 'account', accountId] as const,
  },

  // Recurring Journal-related keys
  recurringJournals: {
    all: (orgId: string) => ['recurring-journals', orgId] as const,
    active: (orgId: string) => ['recurring-journals', orgId, 'active'] as const,
    detail: (orgId: string, recurringId: string) => ['recurring-journal', orgId, recurringId] as const,
  },

  // Budget-related keys
  budgets: {
    all: (orgId: string) => ['budgets', orgId] as const,
    filtered: (orgId: string, filters: QueryFilters) => ['budgets', orgId, filters] as const,
    detail: (orgId: string, budgetId: string) => ['budget', orgId, budgetId] as const,
    byStatus: (orgId: string, status: string) => ['budgets', orgId, 'status', status] as const,
    accountStatus: (orgId: string, accountId: string) => ['budget-status', orgId, accountId] as const,
    analysis: (orgId: string, budgetId: string) => ['budget-analysis', orgId, budgetId] as const,
    allAnalysis: (orgId: string) => ['budget-analysis-all', orgId] as const,
    alerts: (orgId: string) => ['budget-alerts', orgId] as const,
  },

  // Tax-related keys
  tax: {
    rates: (orgId: string) => ['tax-rates', orgId] as const,
    withholdingRates: (orgId: string) => ['withholding-rates', orgId] as const,
    filings: (orgId: string) => ['tax-filings', orgId] as const,
  },

  // Audit-related keys
  audit: {
    logs: (orgId: string, filters?: QueryFilters) => ['audit-logs', orgId, filters] as const,
    analytics: (orgId: string, filters?: QueryFilters) => ['audit-analytics', orgId, filters] as const,
  },

  // Dashboard and analytics keys
  dashboard: {
    recentTransactions: (orgId: string, limit?: number) => ['recent-transactions', orgId, limit] as const,
    accountPerformance: (orgId: string, accountId: string, period?: string) => 
      ['account-performance', orgId, accountId, period] as const,
    summary: (orgId: string, period?: string) => ['dashboard-summary', orgId, period] as const,
  },

  // Search-related keys
  search: {
    global: (orgId: string, query: string, filters?: QueryFilters) =>
      ['global-search', orgId, query, filters] as const,
    suggestions: (orgId: string, query: string) => ['search-suggestions', orgId, query] as const,
  },

  // Notifications
  notifications: {
    all: (userId: string) => ['notifications', userId] as const,
    unread: (userId: string) => ['notifications', userId, 'unread'] as const,
    count: (userId: string) => ['notifications', userId, 'count'] as const,
    filtered: (userId: string, filters: Record<string, unknown>) => ['notifications', userId, 'filtered', filters] as const,
    preferences: (userId: string) => ['notification-preferences', userId] as const,
    templates: () => ['notification-templates'] as const,
  },

  // Inventory-related keys
  products: {
    all: (orgId: string) => ['products', orgId] as const,
    active: (orgId: string) => ['products', orgId, 'active'] as const,
    byCategory: (orgId: string, categoryId: string) => ['products', orgId, 'category', categoryId] as const,
    detail: (orgId: string, productId: string) => ['product', orgId, productId] as const,
    withStock: (orgId: string) => ['products', orgId, 'with-stock'] as const,
    lowStock: (orgId: string) => ['products', orgId, 'low-stock'] as const,
    search: (orgId: string, query: string) => ['products', orgId, 'search', query] as const,
  },

  productCategories: {
    all: (orgId: string) => ['product-categories', orgId] as const,
    active: (orgId: string) => ['product-categories', orgId, 'active'] as const,
    tree: (orgId: string) => ['product-categories', orgId, 'tree'] as const,
    detail: (orgId: string, categoryId: string) => ['product-category', orgId, categoryId] as const,
  },

  inventoryLocations: {
    all: (orgId: string) => ['inventory-locations', orgId] as const,
    active: (orgId: string) => ['inventory-locations', orgId, 'active'] as const,
    detail: (orgId: string, locationId: string) => ['inventory-location', orgId, locationId] as const,
  },

  stockLevels: {
    all: (orgId: string) => ['stock-levels', orgId] as const,
    byProduct: (orgId: string, productId: string) => ['stock-levels', orgId, 'product', productId] as const,
    byLocation: (orgId: string, locationId: string) => ['stock-levels', orgId, 'location', locationId] as const,
    lowStock: (orgId: string) => ['stock-levels', orgId, 'low-stock'] as const,
  },

  inventoryTransactions: {
    all: (orgId: string) => ['inventory-transactions', orgId] as const,
    byProduct: (orgId: string, productId: string) => ['inventory-transactions', orgId, 'product', productId] as const,
    byLocation: (orgId: string, locationId: string) => ['inventory-transactions', orgId, 'location', locationId] as const,
    recent: (orgId: string) => ['inventory-transactions', orgId, 'recent'] as const,
  },

  inventoryDashboard: {
    summary: (orgId: string) => ['inventory-dashboard', orgId, 'summary'] as const,
    alerts: (orgId: string) => ['inventory-dashboard', orgId, 'alerts'] as const,
  },
} as const

/**
 * Utility functions for query key management
 */
export const queryKeyUtils = {
  /**
   * Invalidate all queries for a specific entity type
   */
  invalidateEntity: (queryClient: { invalidateQueries: (options: { queryKey: readonly unknown[] }) => void }, entityType: keyof typeof queryKeys, orgId: string) => {
    if (entityType === 'organization') {
      return queryClient.invalidateQueries({ queryKey: [entityType, orgId] })
    }
    
    const entityKeys = queryKeys[entityType]
    if (typeof entityKeys === 'object' && 'all' in entityKeys) {
      return queryClient.invalidateQueries({ queryKey: (entityKeys as { all: (orgId: string) => readonly unknown[] }).all(orgId) })
    }
  },

  /**
   * Remove all cached data for an organization
   */
  clearOrgCache: (queryClient: { removeQueries: (options: { predicate: (query: { queryKey: unknown[] }) => boolean }) => void }, orgId: string) => {
    queryClient.removeQueries({
      predicate: (query: { queryKey: unknown[] }) => {
        const queryKey = query.queryKey
        return Array.isArray(queryKey) && queryKey.includes(orgId)
      }
    })
  },

  /**
   * Prefetch related data when viewing an entity
   */
  prefetchRelated: async (queryClient: { prefetchQuery: (options: { queryKey: readonly unknown[]; staleTime: number }) => Promise<void> }, entityType: string, entityId: string, orgId: string) => {
    // Prefetch related data based on entity type
    switch (entityType) {
      case 'customer':
        // Prefetch customer invoices and payments
        await Promise.all([
          queryClient.prefetchQuery({
            queryKey: queryKeys.invoices.byCustomer(orgId, entityId),
            staleTime: 2 * 60 * 1000, // 2 minutes
          }),
          queryClient.prefetchQuery({
            queryKey: queryKeys.payments.byEntity(orgId, 'customer', entityId),
            staleTime: 2 * 60 * 1000,
          }),
        ])
        break
      
      case 'vendor':
        // Prefetch vendor bills and payments
        await Promise.all([
          queryClient.prefetchQuery({
            queryKey: queryKeys.bills.byVendor(orgId, entityId),
            staleTime: 2 * 60 * 1000,
          }),
          queryClient.prefetchQuery({
            queryKey: queryKeys.payments.byEntity(orgId, 'vendor', entityId),
            staleTime: 2 * 60 * 1000,
          }),
        ])
        break
    }
  },
}
