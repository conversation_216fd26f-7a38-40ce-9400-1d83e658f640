import * as React from "react"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button, ButtonProps } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

// Loading Spinner Component
interface LoadingSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "default" | "primary" | "muted"
  text?: string
  showText?: boolean
}

const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ className, size = "md", variant = "default", text, showText = false, ...props }, ref) => {
    const sizeClasses = {
      sm: "h-4 w-4",
      md: "h-6 w-6", 
      lg: "h-8 w-8",
      xl: "h-12 w-12"
    }

    const variantClasses = {
      default: "text-foreground",
      primary: "text-primary",
      muted: "text-muted-foreground"
    }

    return (
      <div
        ref={ref}
        className={cn("flex items-center justify-center", className)}
        {...props}
      >
        <div className="flex flex-col items-center gap-2">
          <Loader2 
            className={cn(
              "animate-spin",
              sizeClasses[size],
              variantClasses[variant]
            )}
          />
          {(showText || text) && (
            <p className={cn(
              "text-sm",
              variantClasses[variant]
            )}>
              {text || "Loading..."}
            </p>
          )}
        </div>
      </div>
    )
  }
)
LoadingSpinner.displayName = "LoadingSpinner"

// Loading Button Component
interface LoadingButtonProps extends ButtonProps {
  loading?: boolean
  loadingText?: string
}

const LoadingButton = React.forwardRef<HTMLButtonElement, LoadingButtonProps>(
  ({ children, loading = false, loadingText, disabled, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {loading ? (loadingText || "Loading...") : children}
      </Button>
    )
  }
)
LoadingButton.displayName = "LoadingButton"

// Loading Overlay Component
interface LoadingOverlayProps extends React.HTMLAttributes<HTMLDivElement> {
  show?: boolean
  text?: string
  backdrop?: boolean
}

const LoadingOverlay = React.forwardRef<HTMLDivElement, LoadingOverlayProps>(
  ({ className, show = true, text, backdrop = true, ...props }, ref) => {
    if (!show) return null

    return (
      <div
        ref={ref}
        className={cn(
          "absolute inset-0 z-50 flex items-center justify-center",
          backdrop && "bg-background/80 backdrop-blur-sm",
          className
        )}
        {...props}
      >
        <LoadingSpinner size="lg" text={text} showText />
      </div>
    )
  }
)
LoadingOverlay.displayName = "LoadingOverlay"

// Loading Card Component
interface LoadingCardProps extends React.HTMLAttributes<HTMLDivElement> {
  text?: string
  lines?: number
}

const LoadingCard = React.forwardRef<HTMLDivElement, LoadingCardProps>(
  ({ className, text, lines = 3, ...props }, ref) => {
    return (
      <Card ref={ref} className={cn("", className)} {...props}>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-center py-4">
              <LoadingSpinner text={text} showText />
            </div>
            <div className="space-y-2">
              {Array.from({ length: lines }).map((_, i) => (
                <Skeleton key={i} className="h-4 w-full" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }
)
LoadingCard.displayName = "LoadingCard"

// Loading Table Component
interface LoadingTableProps extends React.HTMLAttributes<HTMLDivElement> {
  rows?: number
  columns?: number
  text?: string
}

const LoadingTable = React.forwardRef<HTMLDivElement, LoadingTableProps>(
  ({ className, rows = 5, columns = 4, text, ...props }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-4", className)} {...props}>
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner text={text} showText />
        </div>
        <div className="space-y-2">
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <div key={rowIndex} className="flex space-x-4">
              {Array.from({ length: columns }).map((_, colIndex) => (
                <Skeleton key={colIndex} className="h-8 flex-1" />
              ))}
            </div>
          ))}
        </div>
      </div>
    )
  }
)
LoadingTable.displayName = "LoadingTable"

// Loading Page Component
interface LoadingPageProps extends React.HTMLAttributes<HTMLDivElement> {
  text?: string
  fullScreen?: boolean
}

const LoadingPage = React.forwardRef<HTMLDivElement, LoadingPageProps>(
  ({ className, text, fullScreen = true, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center justify-center",
          fullScreen ? "min-h-screen" : "min-h-[400px]",
          className
        )}
        {...props}
      >
        <LoadingSpinner size="xl" text={text} showText />
      </div>
    )
  }
)
LoadingPage.displayName = "LoadingPage"

export {
  LoadingSpinner,
  LoadingButton,
  LoadingOverlay,
  LoadingCard,
  LoadingTable,
  LoadingPage,
  type LoadingSpinnerProps,
  type LoadingButtonProps,
  type LoadingOverlayProps,
  type LoadingCardProps,
  type LoadingTableProps,
  type LoadingPageProps
}
