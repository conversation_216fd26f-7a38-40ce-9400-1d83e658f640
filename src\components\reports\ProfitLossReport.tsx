import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { LoadingSpinner } from '@/components/ui/loading'
import { Calendar } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import type { Account } from '@/types/database'

interface PLData {
  account: Account
  amount: number
}

interface PLSection {
  title: string
  accounts: PLData[]
  total: number
}

export const ProfitLossReport = () => {
  const { profile } = useAuth()
  const [data, setData] = useState<{
    revenue: PLSection
    expenses: PLSection
    netIncome: number
  }>({
    revenue: { title: 'Revenue', accounts: [], total: 0 },
    expenses: { title: 'Expenses', accounts: [], total: 0 },
    netIncome: 0
  })
  const [loading, setLoading] = useState(false)
  const [startDate, setStartDate] = useState(() => {
    const date = new Date()
    date.setMonth(0, 1) // January 1st of current year
    return date.toISOString().split('T')[0]
  })
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0])

  const fetchProfitLoss = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      
      // Fetch income and expense accounts (using 'income' instead of 'revenue')
      const { data: accounts, error: accountsError } = await supabase
        .from('accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .in('type', ['income', 'expense'])
        .eq('is_active', true)
        .order('code')

      if (accountsError) throw accountsError

      const revenueAccounts: PLData[] = []
      const expenseAccounts: PLData[] = []

      for (const account of accounts || []) {
        const { data: transactions, error: transError } = await supabase
          .from('transaction_lines')
          .select('debit, credit')
          .eq('org_id', profile.org_id)
          .eq('account_id', account.id)
          .gte('created_at', `${startDate}T00:00:00`)
          .lte('created_at', `${endDate}T23:59:59`)

        if (transError) throw transError

        const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
        const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0

        let amount = 0
        if (account.type === 'income') {
          // Income accounts have credit balances
          amount = creditTotal - debitTotal
        } else {
          // Expense accounts have debit balances
          amount = debitTotal - creditTotal
        }

        if (amount !== 0) {
          const accountData = { account, amount }
          if (account.type === 'income') {
            revenueAccounts.push(accountData)
          } else {
            expenseAccounts.push(accountData)
          }
        }
      }

      const revenueTotal = revenueAccounts.reduce((sum, item) => sum + item.amount, 0)
      const expenseTotal = expenseAccounts.reduce((sum, item) => sum + item.amount, 0)

      setData({
        revenue: {
          title: 'Revenue',
          accounts: revenueAccounts,
          total: revenueTotal
        },
        expenses: {
          title: 'Expenses',
          accounts: expenseAccounts,
          total: expenseTotal
        },
        netIncome: revenueTotal - expenseTotal
      })
    } catch (error) {
      console.error('Error fetching profit & loss:', error)
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, startDate, endDate])

  useEffect(() => {
    if (profile?.org_id) {
      fetchProfitLoss()
    }
  }, [profile?.org_id, startDate, endDate, fetchProfitLoss])

  if (loading) {
    return (
      <div className="p-8">
        <LoadingSpinner text="Loading profit & loss statement..." showText />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Label htmlFor="startDate">From:</Label>
          <Input
            id="startDate"
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="w-auto"
          />
        </div>
        <div className="flex items-center gap-2">
          <Label htmlFor="endDate">To:</Label>
          <Input
            id="endDate"
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            className="w-auto"
          />
        </div>
        <Button onClick={fetchProfitLoss} size="sm">
          <Calendar className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Account</TableHead>
              <TableHead className="text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {/* Revenue Section */}
            <TableRow className="font-semibold bg-muted/50">
              <TableCell>{data.revenue.title}</TableCell>
              <TableCell></TableCell>
            </TableRow>
            {data.revenue.accounts.map((item) => (
              <TableRow key={item.account.id}>
                <TableCell className="pl-6">
                  {item.account.code} - {item.account.name}
                </TableCell>
                <TableCell className="text-right font-mono">
                  {formatCurrency(item.amount)}
                </TableCell>
              </TableRow>
            ))}
            <TableRow className="font-semibold">
              <TableCell className="pl-6">Total Revenue</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.revenue.total)}
              </TableCell>
            </TableRow>

            {/* Expenses Section */}
            <TableRow className="font-semibold bg-muted/50">
              <TableCell>{data.expenses.title}</TableCell>
              <TableCell></TableCell>
            </TableRow>
            {data.expenses.accounts.map((item) => (
              <TableRow key={item.account.id}>
                <TableCell className="pl-6">
                  {item.account.code} - {item.account.name}
                </TableCell>
                <TableCell className="text-right font-mono">
                  {formatCurrency(item.amount)}
                </TableCell>
              </TableRow>
            ))}
            <TableRow className="font-semibold">
              <TableCell className="pl-6">Total Expenses</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.expenses.total)}
              </TableCell>
            </TableRow>

            {/* Net Income */}
            <TableRow className="font-bold border-t-2 bg-primary/10">
              <TableCell>Net Income</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.netIncome)}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
