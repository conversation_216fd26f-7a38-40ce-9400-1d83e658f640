import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { DatePicker } from '@/components/ui/date-picker'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { ApprovalFilters } from '@/types/approval-workflow'
import type { ApprovalStatus, DocumentType } from '@/types/database'

interface ApprovalFiltersDialogProps {
  open: boolean
  onClose: () => void
  filters: ApprovalFilters
  onFiltersChange: (filters: ApprovalFilters) => void
}

export function ApprovalFiltersDialog({
  open,
  onClose,
  filters,
  onFiltersChange
}: ApprovalFiltersDialogProps) {
  const [localFilters, setLocalFilters] = useState<ApprovalFilters>(filters)

  const statusOptions: ApprovalStatus[] = ['pending', 'approved', 'rejected', 'escalated', 'delegated', 'cancelled']
  const documentTypeOptions: DocumentType[] = ['invoice', 'bill', 'payment', 'budget']

  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
    onClose()
  }

  const handleClearFilters = () => {
    const emptyFilters: ApprovalFilters = {}
    setLocalFilters(emptyFilters)
    onFiltersChange(emptyFilters)
    onClose()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Filter Approval Instances</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Filter */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                {statusOptions.map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}`}
                      checked={localFilters.status?.includes(status) || false}
                      onCheckedChange={(checked) => {
                        const currentStatus = localFilters.status || []
                        if (checked) {
                          setLocalFilters({
                            ...localFilters,
                            status: [...currentStatus, status]
                          })
                        } else {
                          setLocalFilters({
                            ...localFilters,
                            status: currentStatus.filter(s => s !== status)
                          })
                        }
                      }}
                    />
                    <Label htmlFor={`status-${status}`} className="capitalize">
                      {status}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Document Type Filter */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Document Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                {documentTypeOptions.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type}`}
                      checked={localFilters.document_type?.includes(type) || false}
                      onCheckedChange={(checked) => {
                        const currentTypes = localFilters.document_type || []
                        if (checked) {
                          setLocalFilters({
                            ...localFilters,
                            document_type: [...currentTypes, type]
                          })
                        } else {
                          setLocalFilters({
                            ...localFilters,
                            document_type: currentTypes.filter(t => t !== type)
                          })
                        }
                      }}
                    />
                    <Label htmlFor={`type-${type}`} className="capitalize">
                      {type}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Amount Range Filter */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Amount Range</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="min-amount">Minimum Amount</Label>
                  <Input
                    id="min-amount"
                    type="number"
                    placeholder="0"
                    value={localFilters.amount_range?.min || ''}
                    onChange={(e) => {
                      const value = e.target.value ? parseFloat(e.target.value) : undefined
                      setLocalFilters({
                        ...localFilters,
                        amount_range: {
                          ...localFilters.amount_range,
                          min: value || 0,
                          max: localFilters.amount_range?.max || 0
                        }
                      })
                    }}
                  />
                </div>
                <div>
                  <Label htmlFor="max-amount">Maximum Amount</Label>
                  <Input
                    id="max-amount"
                    type="number"
                    placeholder="No limit"
                    value={localFilters.amount_range?.max || ''}
                    onChange={(e) => {
                      const value = e.target.value ? parseFloat(e.target.value) : undefined
                      setLocalFilters({
                        ...localFilters,
                        amount_range: {
                          min: localFilters.amount_range?.min || 0,
                          max: value || 0
                        }
                      })
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Date Range Filter */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Date Range</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Start Date</Label>
                  <DatePicker
                    date={localFilters.date_range?.start ? new Date(localFilters.date_range.start) : undefined}
                    onDateChange={(date) => {
                      setLocalFilters({
                        ...localFilters,
                        date_range: {
                          ...localFilters.date_range,
                          start: date?.toISOString() || '',
                          end: localFilters.date_range?.end || ''
                        }
                      })
                    }}
                  />
                </div>
                <div>
                  <Label>End Date</Label>
                  <DatePicker
                    date={localFilters.date_range?.end ? new Date(localFilters.date_range.end) : undefined}
                    onDateChange={(date) => {
                      setLocalFilters({
                        ...localFilters,
                        date_range: {
                          start: localFilters.date_range?.start || '',
                          end: date?.toISOString() || ''
                        }
                      })
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Options */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Additional Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="overdue-only"
                    checked={localFilters.overdue_only || false}
                    onCheckedChange={(checked) => {
                      setLocalFilters({
                        ...localFilters,
                        overdue_only: checked as boolean
                      })
                    }}
                  />
                  <Label htmlFor="overdue-only">Show overdue only</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="my-approvals-only"
                    checked={localFilters.my_approvals_only || false}
                    onCheckedChange={(checked) => {
                      setLocalFilters({
                        ...localFilters,
                        my_approvals_only: checked as boolean
                      })
                    }}
                  />
                  <Label htmlFor="my-approvals-only">My approvals only</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={handleClearFilters}>
            Clear All
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleApplyFilters}>
            Apply Filters
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
