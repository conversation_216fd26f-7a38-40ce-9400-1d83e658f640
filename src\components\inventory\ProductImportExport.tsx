import React, { useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Upload, 
  Download, 
  FileText, 
  AlertCircle, 
  CheckCircle, 
  X,
  FileSpreadsheet,
  Info
} from 'lucide-react'
import { useProducts, useCreateProduct, useActiveProductCategories } from '@/hooks/queries'
import type { ProductFormData } from '@/types/inventory'

interface ImportResult {
  success: boolean
  row: number
  data?: ProductFormData
  errors: string[]
}

interface ProductImportExportProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ProductImportExport({ open, onOpenChange }: ProductImportExportProps) {
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importResults, setImportResults] = useState<ImportResult[]>([])
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [showResults, setShowResults] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { data: products = [] } = useProducts()
  const { data: categories = [] } = useActiveProductCategories()
  const createProduct = useCreateProduct()

  // CSV Export functionality
  const handleExportProducts = () => {
    const csvHeaders = [
      'SKU',
      'Name',
      'Description',
      'Category',
      'Unit of Measure',
      'Cost Price',
      'Selling Price',
      'Track Inventory',
      'Reorder Level',
      'Reorder Quantity',
      'Barcode',
      'Weight',
      'Dimensions',
      'Is Active',
      'Is Sellable',
      'Is Purchasable'
    ]

    const csvData = products.map(product => [
      product.sku,
      product.name,
      product.description || '',
      product.category?.name || '',
      product.unit_of_measure,
      product.cost_price?.toString() || '0',
      product.selling_price?.toString() || '0',
      product.track_inventory ? 'TRUE' : 'FALSE',
      product.reorder_level?.toString() || '0',
      product.reorder_quantity?.toString() || '0',
      product.barcode || '',
      product.weight?.toString() || '',
      product.dimensions || '',
      product.is_active ? 'TRUE' : 'FALSE',
      product.is_sellable ? 'TRUE' : 'FALSE',
      product.is_purchasable ? 'TRUE' : 'FALSE'
    ])

    const csvContent = [
      csvHeaders.join(','),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `products-export-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // CSV Template download
  const handleDownloadTemplate = () => {
    const templateHeaders = [
      'SKU',
      'Name',
      'Description',
      'Category',
      'Unit of Measure',
      'Cost Price',
      'Selling Price',
      'Track Inventory',
      'Reorder Level',
      'Reorder Quantity',
      'Barcode',
      'Weight',
      'Dimensions',
      'Is Active',
      'Is Sellable',
      'Is Purchasable'
    ]

    const sampleData = [
      'SAMPLE-001',
      'Sample Product',
      'This is a sample product description',
      'Electronics',
      'each',
      '10.00',
      '15.00',
      'TRUE',
      '5',
      '20',
      '1234567890123',
      '0.5',
      '10x5x2 cm',
      'TRUE',
      'TRUE',
      'TRUE'
    ]

    const csvContent = [
      templateHeaders.join(','),
      sampleData.map(cell => `"${cell}"`).join(',')
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'product-import-template.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // File selection handler
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        alert('Please select a CSV file')
        return
      }
      setImportFile(file)
      setImportResults([])
      setShowResults(false)
    }
  }

  // Parse CSV content
  const parseCSV = (content: string): string[][] => {
    const lines = content.split('\n').filter(line => line.trim())
    return lines.map(line => {
      const result: string[] = []
      let current = ''
      let inQuotes = false
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i]
        if (char === '"') {
          inQuotes = !inQuotes
        } else if (char === ',' && !inQuotes) {
          result.push(current.trim())
          current = ''
        } else {
          current += char
        }
      }
      result.push(current.trim())
      return result
    })
  }

  // Validate and process import data
  const processImportData = async () => {
    if (!importFile) return

    setIsImporting(true)
    setImportProgress(0)

    try {
      const content = await importFile.text()
      const rows = parseCSV(content)
      
      if (rows.length < 2) {
        alert('CSV file must contain at least a header row and one data row')
        return
      }

      const headers = rows[0].map(h => h.toLowerCase().trim())
      const dataRows = rows.slice(1)
      const results: ImportResult[] = []

      // Process each row
      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i]
        const rowNumber = i + 2 // +2 because we skip header and arrays are 0-indexed
        const errors: string[] = []

        try {
          // Map CSV columns to product data
          const productData: Partial<ProductFormData> = {}

          // Required fields
          const sku = row[headers.indexOf('sku')]?.trim()
          const name = row[headers.indexOf('name')]?.trim()
          
          if (!sku) errors.push('SKU is required')
          if (!name) errors.push('Name is required')

          if (sku) productData.sku = sku
          if (name) productData.name = name

          // Optional fields with defaults
          productData.description = row[headers.indexOf('description')]?.trim() || ''
          productData.unit_of_measure = row[headers.indexOf('unit of measure')]?.trim() || 'each'
          productData.cost_price = parseFloat(row[headers.indexOf('cost price')] || '0') || 0
          productData.selling_price = parseFloat(row[headers.indexOf('selling price')] || '0') || 0
          productData.track_inventory = (row[headers.indexOf('track inventory')]?.toLowerCase() === 'true')
          productData.reorder_level = parseFloat(row[headers.indexOf('reorder level')] || '0') || 0
          productData.reorder_quantity = parseFloat(row[headers.indexOf('reorder quantity')] || '0') || 0
          productData.barcode = row[headers.indexOf('barcode')]?.trim() || ''
          productData.weight = parseFloat(row[headers.indexOf('weight')] || '0') || null
          productData.dimensions = row[headers.indexOf('dimensions')]?.trim() || ''
          productData.is_active = (row[headers.indexOf('is active')]?.toLowerCase() !== 'false')
          productData.is_sellable = (row[headers.indexOf('is sellable')]?.toLowerCase() !== 'false')
          productData.is_purchasable = (row[headers.indexOf('is purchasable')]?.toLowerCase() !== 'false')

          // Handle category
          const categoryName = row[headers.indexOf('category')]?.trim()
          if (categoryName) {
            const category = categories.find(c => 
              c.name.toLowerCase() === categoryName.toLowerCase()
            )
            if (category) {
              productData.category_id = category.id
            } else {
              errors.push(`Category "${categoryName}" not found`)
            }
          }

          // Check for duplicate SKU
          const existingProduct = products.find(p => p.sku.toLowerCase() === sku?.toLowerCase())
          if (existingProduct) {
            errors.push(`SKU "${sku}" already exists`)
          }

          results.push({
            success: errors.length === 0,
            row: rowNumber,
            data: productData as ProductFormData,
            errors
          })

        } catch (error) {
          results.push({
            success: false,
            row: rowNumber,
            errors: [`Failed to process row: ${error}`]
          })
        }

        setImportProgress(((i + 1) / dataRows.length) * 50) // First 50% for validation
      }

      setImportResults(results)
      setShowResults(true)

      // Import valid products
      const validResults = results.filter(r => r.success && r.data)
      for (let i = 0; i < validResults.length; i++) {
        try {
          await createProduct.mutateAsync(validResults[i].data!)
          setImportProgress(50 + ((i + 1) / validResults.length) * 50) // Second 50% for import
        } catch (error) {
          // Update result to show import error
          const resultIndex = results.findIndex(r => r === validResults[i])
          if (resultIndex >= 0) {
            results[resultIndex].success = false
            results[resultIndex].errors.push(`Import failed: ${error}`)
          }
        }
      }

      setImportResults([...results])

    } catch (error) {
      alert(`Failed to process file: ${error}`)
    } finally {
      setIsImporting(false)
      setImportProgress(100)
    }
  }

  const successCount = importResults.filter(r => r.success).length
  const errorCount = importResults.filter(r => !r.success).length

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Product Import/Export
          </DialogTitle>
          <DialogDescription>
            Import products from CSV or export your current product catalog
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Download className="h-4 w-4" />
                Export Products
              </CardTitle>
              <CardDescription>
                Download your current product catalog as a CSV file
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Button onClick={handleExportProducts} className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Export All Products ({products.length})
                </Button>
                <Button variant="outline" onClick={handleDownloadTemplate}>
                  <FileText className="h-4 w-4 mr-2" />
                  Download Template
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Import Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Import Products
              </CardTitle>
              <CardDescription>
                Upload a CSV file to import multiple products at once
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                <div className="text-center">
                  <FileSpreadsheet className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <label htmlFor="file-upload" className="cursor-pointer">
                      <span className="mt-2 block text-sm font-medium text-gray-900">
                        {importFile ? importFile.name : 'Choose CSV file to upload'}
                      </span>
                    </label>
                    <input
                      ref={fileInputRef}
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      accept=".csv"
                      className="sr-only"
                      onChange={handleFileSelect}
                    />
                  </div>
                  <div className="mt-4 flex justify-center gap-2">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isImporting}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Select File
                    </Button>
                    {importFile && (
                      <Button
                        onClick={processImportData}
                        disabled={isImporting}
                      >
                        {isImporting ? 'Importing...' : 'Import Products'}
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {/* Import Progress */}
              {isImporting && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Processing...</span>
                    <span>{Math.round(importProgress)}%</span>
                  </div>
                  <Progress value={importProgress} className="w-full" />
                </div>
              )}

              {/* Import Instructions */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-blue-700">
                    <p className="font-medium mb-2">Import Instructions:</p>
                    <ul className="space-y-1 text-xs">
                      <li>• Download the template to see the required format</li>
                      <li>• SKU and Name are required fields</li>
                      <li>• Category names must match existing categories</li>
                      <li>• Use TRUE/FALSE for boolean fields</li>
                      <li>• Duplicate SKUs will be rejected</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Import Results */}
          {showResults && importResults.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Import Results
                </CardTitle>
                <CardDescription>
                  <div className="flex items-center gap-4">
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      {successCount} Successful
                    </Badge>
                    {errorCount > 0 && (
                      <Badge variant="destructive">
                        {errorCount} Failed
                      </Badge>
                    )}
                  </div>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="max-h-60 overflow-y-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Row</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Product</TableHead>
                        <TableHead>Errors</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {importResults.map((result, index) => (
                        <TableRow key={index}>
                          <TableCell>{result.row}</TableCell>
                          <TableCell>
                            {result.success ? (
                              <Badge variant="default" className="bg-green-100 text-green-800">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Success
                              </Badge>
                            ) : (
                              <Badge variant="destructive">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Failed
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {result.data && (
                              <div>
                                <div className="font-medium">{result.data.name}</div>
                                <div className="text-sm text-gray-500">{result.data.sku}</div>
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {result.errors.length > 0 && (
                              <div className="space-y-1">
                                {result.errors.map((error, errorIndex) => (
                                  <div key={errorIndex} className="text-sm text-red-600">
                                    {error}
                                  </div>
                                ))}
                              </div>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
