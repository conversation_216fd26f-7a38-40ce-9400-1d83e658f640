import { auditLogger } from './auditLogger'
import type { AuditAction, AuditSeverity } from '@/types/database'

export interface InventoryAuditContext {
  product_id?: string
  product_name?: string
  product_sku?: string
  location_id?: string
  location_name?: string
  transaction_id?: string
  reference_type?: string
  reference_id?: string
  reference_number?: string
  costing_method?: string
  unit_cost?: number
  quantity?: number
  total_cost?: number
}

export interface StockMovementAuditData {
  old_quantity?: number
  new_quantity?: number
  quantity_change: number
  old_cost?: number
  new_cost?: number
  cost_change?: number
  movement_type: 'in' | 'out' | 'adjustment' | 'transfer'
  reason?: string
}

export interface CostAdjustmentAuditData {
  old_unit_cost?: number
  new_unit_cost?: number
  cost_change: number
  old_total_value?: number
  new_total_value?: number
  value_change: number
  adjustment_reason: string
  costing_method: string
}

export interface ReservationAuditData {
  quantity_reserved: number
  reservation_type: string
  reference_document: string
  expires_at?: string
  status: 'created' | 'released' | 'fulfilled' | 'expired'
}

/**
 * Specialized audit logger for inventory operations
 */
export class InventoryAuditLogger {
  /**
   * Log inventory transaction creation
   */
  async logInventoryTransaction(
    transactionId: string,
    transactionType: string,
    context: InventoryAuditContext,
    additionalData?: Record<string, unknown>
  ): Promise<void> {
    const description = this.buildTransactionDescription(transactionType, context)
    
    await auditLogger.log({
      entity_type: 'inventory_transaction',
      entity_id: transactionId,
      action: 'create',
      description,
      severity: this.getTransactionSeverity(transactionType),
      category: 'data_modification',
      changed_data: {
        transaction_type: transactionType,
        product_id: context.product_id,
        product_name: context.product_name,
        location_id: context.location_id,
        quantity: context.quantity,
        unit_cost: context.unit_cost,
        total_cost: context.total_cost,
        reference_type: context.reference_type,
        reference_id: context.reference_id,
        ...additionalData
      },
      metadata: {
        inventory_operation: true,
        transaction_type: transactionType,
        product_sku: context.product_sku,
        location_name: context.location_name,
        reference_number: context.reference_number
      }
    })
  }

  /**
   * Log stock level changes
   */
  async logStockMovement(
    productId: string,
    locationId: string,
    movementData: StockMovementAuditData,
    context: InventoryAuditContext
  ): Promise<void> {
    const description = this.buildStockMovementDescription(movementData, context)
    
    await auditLogger.log({
      entity_type: 'stock_movement',
      entity_id: `${productId}-${locationId}`,
      action: 'update',
      description,
      severity: this.getStockMovementSeverity(movementData),
      category: 'data_modification',
      changed_data: {
        old_values: {
          quantity: movementData.old_quantity,
          unit_cost: movementData.old_cost
        },
        new_values: {
          quantity: movementData.new_quantity,
          unit_cost: movementData.new_cost
        },
        changes: {
          quantity_change: movementData.quantity_change,
          cost_change: movementData.cost_change,
          movement_type: movementData.movement_type,
          reason: movementData.reason
        }
      },
      metadata: {
        inventory_operation: true,
        product_id: productId,
        product_name: context.product_name,
        product_sku: context.product_sku,
        location_id: locationId,
        location_name: context.location_name,
        movement_type: movementData.movement_type,
        transaction_id: context.transaction_id
      }
    })
  }

  /**
   * Log cost adjustments and revaluations
   */
  async logCostAdjustment(
    productId: string,
    locationId: string,
    adjustmentData: CostAdjustmentAuditData,
    context: InventoryAuditContext
  ): Promise<void> {
    const description = this.buildCostAdjustmentDescription(adjustmentData, context)
    
    await auditLogger.log({
      entity_type: 'cost_adjustment',
      entity_id: `${productId}-${locationId}`,
      action: 'update',
      description,
      severity: this.getCostAdjustmentSeverity(adjustmentData),
      category: 'financial_transaction',
      changed_data: {
        old_values: {
          unit_cost: adjustmentData.old_unit_cost,
          total_value: adjustmentData.old_total_value
        },
        new_values: {
          unit_cost: adjustmentData.new_unit_cost,
          total_value: adjustmentData.new_total_value
        },
        changes: {
          cost_change: adjustmentData.cost_change,
          value_change: adjustmentData.value_change,
          adjustment_reason: adjustmentData.adjustment_reason,
          costing_method: adjustmentData.costing_method
        }
      },
      metadata: {
        inventory_operation: true,
        cost_adjustment: true,
        product_id: productId,
        product_name: context.product_name,
        product_sku: context.product_sku,
        location_id: locationId,
        location_name: context.location_name,
        costing_method: adjustmentData.costing_method
      }
    })
  }

  /**
   * Log stock reservations
   */
  async logStockReservation(
    reservationId: string,
    reservationData: ReservationAuditData,
    context: InventoryAuditContext
  ): Promise<void> {
    const description = this.buildReservationDescription(reservationData, context)
    
    await auditLogger.log({
      entity_type: 'stock_reservation',
      entity_id: reservationId,
      action: reservationData.status === 'created' ? 'create' : 'update',
      description,
      severity: 'medium',
      category: 'data_modification',
      changed_data: {
        quantity_reserved: reservationData.quantity_reserved,
        reservation_type: reservationData.reservation_type,
        reference_document: reservationData.reference_document,
        status: reservationData.status,
        expires_at: reservationData.expires_at
      },
      metadata: {
        inventory_operation: true,
        reservation_operation: true,
        product_id: context.product_id,
        product_name: context.product_name,
        product_sku: context.product_sku,
        location_id: context.location_id,
        location_name: context.location_name,
        reservation_type: reservationData.reservation_type,
        reference_type: context.reference_type,
        reference_id: context.reference_id
      }
    })
  }

  /**
   * Log inventory valuation changes
   */
  async logInventoryValuation(
    productId: string,
    oldValuation: number,
    newValuation: number,
    reason: string,
    context: InventoryAuditContext
  ): Promise<void> {
    const valuationChange = newValuation - oldValuation
    const description = `Inventory valuation ${valuationChange >= 0 ? 'increased' : 'decreased'} by UGX ${Math.abs(valuationChange).toLocaleString()} for ${context.product_name || 'product'} (${context.product_sku || productId}). Reason: ${reason}`
    
    await auditLogger.log({
      entity_type: 'inventory_valuation',
      entity_id: productId,
      action: 'update',
      description,
      severity: Math.abs(valuationChange) > 1000000 ? 'high' : 'medium', // High severity for changes > 1M UGX
      category: 'financial_transaction',
      changed_data: {
        old_values: { total_valuation: oldValuation },
        new_values: { total_valuation: newValuation },
        changes: {
          valuation_change: valuationChange,
          reason: reason,
          costing_method: context.costing_method
        }
      },
      metadata: {
        inventory_operation: true,
        valuation_change: true,
        product_id: productId,
        product_name: context.product_name,
        product_sku: context.product_sku,
        costing_method: context.costing_method,
        valuation_change_amount: valuationChange
      }
    })
  }

  /**
   * Log purchase order operations
   */
  async logPurchaseOrderOperation(
    poId: string,
    action: AuditAction,
    description: string,
    context: InventoryAuditContext,
    additionalData?: Record<string, unknown>
  ): Promise<void> {
    await auditLogger.log({
      entity_type: 'purchase_order',
      entity_id: poId,
      action,
      description,
      severity: action === 'delete' ? 'high' : 'medium',
      category: 'data_modification',
      changed_data: additionalData,
      metadata: {
        inventory_operation: true,
        purchase_order_operation: true,
        reference_number: context.reference_number,
        total_cost: context.total_cost
      }
    })
  }

  // Helper methods for building descriptions
  private buildTransactionDescription(transactionType: string, context: InventoryAuditContext): string {
    const productInfo = context.product_name ? `${context.product_name} (${context.product_sku})` : context.product_id
    const locationInfo = context.location_name || context.location_id
    const quantityInfo = context.quantity ? `${context.quantity} units` : ''
    const costInfo = context.unit_cost ? `at UGX ${context.unit_cost.toLocaleString()} each` : ''
    
    return `${transactionType.charAt(0).toUpperCase() + transactionType.slice(1)} transaction: ${quantityInfo} of ${productInfo} ${costInfo} at ${locationInfo}`
  }

  private buildStockMovementDescription(movementData: StockMovementAuditData, context: InventoryAuditContext): string {
    const productInfo = context.product_name ? `${context.product_name} (${context.product_sku})` : context.product_id
    const changeInfo = movementData.quantity_change >= 0 ? 'increased' : 'decreased'
    const quantityInfo = `${Math.abs(movementData.quantity_change)} units`
    
    return `Stock ${changeInfo} by ${quantityInfo} for ${productInfo}. ${movementData.reason || ''}`
  }

  private buildCostAdjustmentDescription(adjustmentData: CostAdjustmentAuditData, context: InventoryAuditContext): string {
    const productInfo = context.product_name ? `${context.product_name} (${context.product_sku})` : context.product_id
    const costChange = adjustmentData.cost_change >= 0 ? 'increased' : 'decreased'
    const costInfo = `UGX ${Math.abs(adjustmentData.cost_change).toLocaleString()}`
    
    return `Cost ${costChange} by ${costInfo} for ${productInfo} using ${adjustmentData.costing_method} method. Reason: ${adjustmentData.adjustment_reason}`
  }

  private buildReservationDescription(reservationData: ReservationAuditData, context: InventoryAuditContext): string {
    const productInfo = context.product_name ? `${context.product_name} (${context.product_sku})` : context.product_id
    const action = reservationData.status === 'created' ? 'Reserved' : 
                  reservationData.status === 'released' ? 'Released' :
                  reservationData.status === 'fulfilled' ? 'Fulfilled' : 'Updated'
    
    return `${action} ${reservationData.quantity_reserved} units of ${productInfo} for ${reservationData.reference_document}`
  }

  // Helper methods for determining severity
  private getTransactionSeverity(transactionType: string): AuditSeverity {
    const highSeverityTypes = ['adjustment', 'write_off', 'transfer']
    return highSeverityTypes.includes(transactionType) ? 'high' : 'medium'
  }

  private getStockMovementSeverity(movementData: StockMovementAuditData): AuditSeverity {
    if (movementData.movement_type === 'adjustment' && Math.abs(movementData.quantity_change) > 100) {
      return 'high'
    }
    return 'medium'
  }

  private getCostAdjustmentSeverity(adjustmentData: CostAdjustmentAuditData): AuditSeverity {
    if (Math.abs(adjustmentData.value_change) > 1000000) { // > 1M UGX
      return 'high'
    }
    if (Math.abs(adjustmentData.value_change) > 100000) { // > 100K UGX
      return 'medium'
    }
    return 'low'
  }
}

// Create singleton instance
export const inventoryAuditLogger = new InventoryAuditLogger()

// Hook for React components
export function useInventoryAuditLogger() {
  return inventoryAuditLogger
}
