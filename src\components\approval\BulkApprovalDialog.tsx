import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react'

interface BulkApprovalDialogProps {
  open: boolean
  onClose: () => void
  selectedCount: number
  onApprove: () => void
  onReject: () => void
}

export function BulkApprovalDialog({
  open,
  onClose,
  selectedCount,
  onApprove,
  onReject
}: BulkApprovalDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Bulk Approval Action
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            You have selected <strong>{selectedCount}</strong> approval instances.
            Choose an action to apply to all selected items.
          </p>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <strong>Warning:</strong> This action cannot be undone. All selected 
                approval instances will be processed with the same action.
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={onApprove}
              className="flex-1"
              variant="default"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Approve All ({selectedCount})
            </Button>
            <Button
              onClick={onReject}
              className="flex-1"
              variant="destructive"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Reject All ({selectedCount})
            </Button>
          </div>

          <Button
            variant="outline"
            onClick={onClose}
            className="w-full"
          >
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
