import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/hooks/useAuthHook'
import { useCreateCustomer, useUpdateCustomer } from '@/hooks/queries'
import { secureApiClient } from '@/lib/secureApiClient'
import { Textarea } from '@/components/ui/textarea'
import {
  validatePhoneNumber,
  validateTinNumber,
  validateEmail,
  formatPhoneNumber,
  getPhoneNumberHelper,
  getTinNumberHelper
} from '@/lib/validators'
import type { Customer } from '@/types/extended-database'

interface CustomerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
  customer?: Customer | null // Optional customer for editing
}

export function CustomerDialog({ open, onOpenChange, onSuccess, customer }: CustomerDialogProps) {
  const { profile } = useAuth()
  const { toast } = useToast()
  const createCustomer = useCreateCustomer()
  const updateCustomer = useUpdateCustomer()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    tin_number: '',
    address: '',
    notes: '',
    payment_terms: 30,
  })
  const [validationErrors, setValidationErrors] = useState<{
    phone?: string
    email?: string
    tin_number?: string
  }>({})

  // Populate form when editing a customer
  useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name,
        email: customer.email || '',
        phone: customer.phone || '',
        tin_number: customer.tin_number || '',
        address: customer.address || '',
        notes: customer.notes || '',
        payment_terms: customer.payment_terms || 30,
      })
    } else {
      // Reset form for new customer
      setFormData({
        name: '',
        email: '',
        phone: '',
        tin_number: '',
        address: '',
        notes: '',
        payment_terms: 30,
      })
    }
    // Clear validation errors when customer changes
    setValidationErrors({})
  }, [customer, open])

  // Handle phone number input with auto-formatting
  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value)
    setFormData({ ...formData, phone: formatted })

    // Clear validation error when user starts typing
    if (validationErrors.phone) {
      setValidationErrors({ ...validationErrors, phone: undefined })
    }
  }

  // Handle email input with validation
  const handleEmailChange = (value: string) => {
    setFormData({ ...formData, email: value })

    // Clear validation error when user starts typing
    if (validationErrors.email) {
      setValidationErrors({ ...validationErrors, email: undefined })
    }
  }

  // Handle TIN input with validation
  const handleTinChange = (value: string) => {
    setFormData({ ...formData, tin_number: value })

    // Clear validation error when user starts typing
    if (validationErrors.tin_number) {
      setValidationErrors({ ...validationErrors, tin_number: undefined })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    // Validate all fields
    const phoneValidation = validatePhoneNumber(formData.phone, true) // Phone is required
    const emailValidation = validateEmail(formData.email)
    const tinValidation = validateTinNumber(formData.tin_number)

    const errors: { phone?: string; email?: string; tin_number?: string } = {}

    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.message
    }

    if (!emailValidation.isValid) {
      errors.email = emailValidation.message
    }

    if (!tinValidation.isValid) {
      errors.tin_number = tinValidation.message
    }

    // If there are validation errors, show them and return
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors)
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors below and try again',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)
    try {
      const customerData = {
        name: formData.name,
        email: formData.email || null,
        phone: phoneValidation.formatted || formData.phone || null,
        tin_number: tinValidation.formatted || formData.tin_number || null,
        address: formData.address || null,
        notes: formData.notes || null,
        payment_terms: Number(formData.payment_terms),
      }

      if (customer) {
        // Update existing customer using React Query
        await updateCustomer.mutateAsync({
          customerId: customer.id,
          customerData
        })
      } else {
        // Create new customer using React Query
        await createCustomer.mutateAsync(customerData)
      }

      // Reset form and validation errors
      setFormData({
        name: '',
        email: '',
        phone: '',
        tin_number: '',
        address: '',
        notes: '',
        payment_terms: 30,
      })
      setValidationErrors({})

      onOpenChange(false)
      onSuccess()
    } catch (error) {
      console.error('Error saving customer:', error)
      // Error handling is done by the React Query hooks
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{customer ? 'Edit Customer' : 'Create New Customer'}</DialogTitle>
          <DialogDescription>
            {customer ? 'Update customer information' : 'Add a new customer to your organization'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
                placeholder="Enter customer name"
              />
            </div>

            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleEmailChange(e.target.value)}
                placeholder="<EMAIL>"
                className={validationErrors.email ? 'border-red-500' : ''}
              />
              {validationErrors.email && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.email}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Optional. Customer's email address for invoices and communication
              </p>
            </div>

            <div>
              <Label htmlFor="phone">Phone Number *</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => handlePhoneChange(e.target.value)}
                placeholder="777123456"
                className={validationErrors.phone ? 'border-red-500' : ''}
                required
              />
              {validationErrors.phone && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.phone}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {getPhoneNumberHelper()}
              </p>
            </div>

            <div>
              <Label htmlFor="tin_number">TIN Number</Label>
              <Input
                id="tin_number"
                value={formData.tin_number}
                onChange={(e) => handleTinChange(e.target.value)}
                placeholder="1000123456"
                className={validationErrors.tin_number ? 'border-red-500' : ''}
              />
              {validationErrors.tin_number && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.tin_number}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {getTinNumberHelper()}
              </p>
            </div>
          </div>

          <div>
            <Label htmlFor="address">Address</Label>
            <Textarea
              id="address"
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              placeholder="Enter customer address"
              rows={2}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="payment_terms">Payment Terms (days)</Label>
              <Input
                id="payment_terms"
                type="number"
                value={formData.payment_terms}
                onChange={(e) => setFormData({ ...formData, payment_terms: Number(e.target.value) })}
                min="0"
                placeholder="30"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Number of days for payment terms
              </p>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Add any additional notes"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading
                ? (customer ? 'Updating...' : 'Creating...')
                : (customer ? 'Update Customer' : 'Create Customer')
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
} 
