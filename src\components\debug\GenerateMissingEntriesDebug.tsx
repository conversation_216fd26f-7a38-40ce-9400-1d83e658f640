import React, { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Play, AlertTriangle, CheckCircle } from 'lucide-react'

export function GenerateMissingEntriesDebug() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<unknown>(null)
  const [error, setError] = useState<string | null>(null)

  const testFunction = async () => {
    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('Testing generate_missing_journal_entries function...')
      
      // First, test if the function exists
      const { data: functions, error: functionsError } = await supabase.rpc('pg_get_functiondef', {
        funcid: 'generate_missing_journal_entries'
      })
      
      if (functionsError) {
        console.log('Function check error:', functionsError)
      }

      // Test the actual function call
      const { data, error } = await supabase.rpc('generate_missing_journal_entries')
      
      console.log('RPC Response:', { data, error })
      
      if (error) {
        throw new Error(`Database error: ${error.message} (Code: ${error.code})`)
      }
      
      setResult(data)
    } catch (err: unknown) {
      console.error('Test error:', err)
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const testDatabaseConnection = async () => {
    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('Testing database connection...')
      
      // Test basic connection
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, org_id')
        .limit(1)
      
      console.log('Connection test:', { data, error })
      
      if (error) {
        throw new Error(`Connection error: ${error.message}`)
      }
      
      setResult({ type: 'connection', data })
    } catch (err: unknown) {
      console.error('Connection test error:', err)
      setError(err instanceof Error ? err.message : 'Connection test failed')
    } finally {
      setIsLoading(false)
    }
  }

  const testTableExists = async () => {
    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('Testing if required tables exist...')
      
      // Test if key tables exist
      const tables = ['organizations', 'profiles', 'accounts', 'invoices', 'bills', 'journal_entries', 'transaction_lines']
      const results = []
      
      for (const table of tables) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1)
          
          results.push({
            table,
            exists: !error,
            error: error?.message,
            count: data?.length || 0
          })
        } catch (err: unknown) {
          results.push({
            table,
            exists: false,
            error: err instanceof Error ? err.message : 'Unknown error',
            count: 0
          })
        }
      }
      
      setResult({ type: 'tables', data: results })
    } catch (err: unknown) {
      console.error('Table test error:', err)
      setError(err instanceof Error ? err.message : 'Table test failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5" />
          Generate Missing Entries Debug
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button
            onClick={testDatabaseConnection}
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Test Connection
          </Button>

          <Button
            onClick={testTableExists}
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Test Tables
          </Button>

          <Button
            onClick={testFunction}
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Test Function
          </Button>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Error:</strong> {error}
            </AlertDescription>
          </Alert>
        )}

        {result && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <strong>Result:</strong>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-96">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
