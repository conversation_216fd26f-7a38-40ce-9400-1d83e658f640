import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, DollarSign } from 'lucide-react'
import { BudgetValidationResult } from '@/hooks/queries/useBudgetValidation'

interface BudgetEnforcementDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  validation: BudgetValidationResult
  billAmount: number
  onProceed: (justification: string, requiresApproval: boolean) => void
  onCancel: () => void
  allowOverride?: boolean
}

export function BudgetEnforcementDialog({
  open,
  onOpenChange,
  validation,
  billAmount,
  onProceed,
  onCancel,
  allowOverride = true
}: BudgetEnforcementDialogProps) {
  const [justification, setJustification] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const handleProceed = async (requiresApproval: boolean) => {
    if (requiresApproval && !justification.trim()) {
      return // Justification required for approval
    }

    setIsSubmitting(true)
    try {
      await onProceed(justification, requiresApproval)
      onOpenChange(false)
      setJustification('')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    onCancel()
    onOpenChange(false)
    setJustification('')
  }

  if (!validation.budgetStatus) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Budget Limit Exceeded
          </DialogTitle>
          <DialogDescription>
            This bill would exceed the approved budget for the selected account.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Budget Impact Summary */}
          <Alert className="border-red-200 bg-red-50">
            <DollarSign className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <div className="font-medium">Budget Impact Summary</div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Account:</div>
                  <div className="font-medium">{validation.budgetStatus.accountName}</div>
                  
                  <div>Current Budget:</div>
                  <div>{formatCurrency(validation.budgetStatus.budgetAmount)}</div>
                  
                  <div>Already Spent:</div>
                  <div>{formatCurrency(validation.budgetStatus.actualAmount)}</div>
                  
                  <div>Remaining Budget:</div>
                  <div className={validation.budgetStatus.remainingAmount < 0 ? 'text-red-600' : ''}>
                    {formatCurrency(validation.budgetStatus.remainingAmount)}
                  </div>
                  
                  <div>This Bill Amount:</div>
                  <div className="font-medium">{formatCurrency(billAmount)}</div>
                  
                  <div className="border-t pt-1 font-medium">Budget Exceedance:</div>
                  <div className="border-t pt-1 font-medium text-red-600">
                    {formatCurrency(validation.exceedanceAmount)}
                  </div>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Current Utilization */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium mb-2">Current Budget Utilization</div>
            <div className="text-2xl font-bold text-red-600">
              {((validation.budgetStatus.actualAmount + billAmount) / validation.budgetStatus.budgetAmount * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">
              of {formatCurrency(validation.budgetStatus.budgetAmount)} budget
            </div>
          </div>

          {/* Justification for Override */}
          {allowOverride && (
            <div className="space-y-2">
              <Label htmlFor="justification">
                Justification for Budget Override <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="justification"
                placeholder="Please provide a business justification for exceeding the budget..."
                value={justification}
                onChange={(e) => setJustification(e.target.value)}
                rows={3}
              />
              <div className="text-xs text-muted-foreground">
                This justification will be included in the approval request and audit trail.
              </div>
            </div>
          )}

          {/* Warning Message */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {allowOverride ? (
                <>
                  Proceeding will require additional approval from an administrator. 
                  The bill will be marked as "Pending Budget Approval" until reviewed.
                </>
              ) : (
                <>
                  Budget enforcement is enabled for this account. 
                  Please reduce the bill amount or request a budget increase.
                </>
              )}
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          
          {allowOverride && (
            <Button
              onClick={() => handleProceed(true)}
              disabled={isSubmitting || !justification.trim()}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isSubmitting ? 'Processing...' : 'Request Approval'}
            </Button>
          )}
          
          <Button
            variant="destructive"
            onClick={() => handleProceed(false)}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Processing...' : 'Proceed Anyway'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
