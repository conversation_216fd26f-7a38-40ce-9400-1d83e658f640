import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  Copy, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle,
  Tag,
  DollarSign,
  Package,
  AlertTriangle
} from 'lucide-react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  useBulkProductOperations,
  useDuplicateProduct,
  useToggleProductStatus,
  useDeleteProduct,
  useActiveProductCategories
} from '@/hooks/queries'
import type { ProductWithStock } from '@/types/inventory'

const bulkEditSchema = z.object({
  operation: z.enum(['category', 'prices', 'status']),
  category_id: z.string().optional(),
  cost_price_adjustment: z.number().optional(),
  selling_price_adjustment: z.number().optional(),
  adjustment_type: z.enum(['fixed', 'percentage']).optional(),
  status: z.boolean().optional(),
})

type BulkEditFormValues = z.infer<typeof bulkEditSchema>

interface ProductQuickActionsProps {
  selectedProducts: ProductWithStock[]
  onSelectionChange: (products: ProductWithStock[]) => void
  onProductUpdate?: () => void
}

export function ProductQuickActions({ 
  selectedProducts, 
  onSelectionChange, 
  onProductUpdate 
}: ProductQuickActionsProps) {
  const [showBulkEdit, setShowBulkEdit] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const { data: categories = [] } = useActiveProductCategories()
  const bulkOperations = useBulkProductOperations()
  const duplicateProduct = useDuplicateProduct()
  const toggleStatus = useToggleProductStatus()
  const deleteProduct = useDeleteProduct()

  const form = useForm<BulkEditFormValues>({
    resolver: zodResolver(bulkEditSchema),
    defaultValues: {
      operation: 'category',
      adjustment_type: 'percentage',
    },
  })

  const selectedCount = selectedProducts.length
  const activeCount = selectedProducts.filter(p => p.is_active).length
  const inactiveCount = selectedCount - activeCount

  // Quick action handlers
  const handleBulkActivate = async () => {
    if (selectedCount === 0) return
    
    setIsProcessing(true)
    try {
      await bulkOperations.mutateAsync({
        productIds: selectedProducts.map(p => p.id),
        operation: 'activate',
      })
      onSelectionChange([])
      onProductUpdate?.()
    } catch (error) {
      console.error('Failed to activate products:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBulkDeactivate = async () => {
    if (selectedCount === 0) return
    
    setIsProcessing(true)
    try {
      await bulkOperations.mutateAsync({
        productIds: selectedProducts.map(p => p.id),
        operation: 'deactivate',
      })
      onSelectionChange([])
      onProductUpdate?.()
    } catch (error) {
      console.error('Failed to deactivate products:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBulkDelete = async () => {
    if (selectedCount === 0) return
    
    const confirmMessage = `Are you sure you want to delete ${selectedCount} products? This action cannot be undone.`
    if (!window.confirm(confirmMessage)) return

    setIsProcessing(true)
    try {
      await Promise.all(selectedProducts.map(p => deleteProduct.mutateAsync(p.id)))
      onSelectionChange([])
      onProductUpdate?.()
    } catch (error) {
      console.error('Failed to delete products:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBulkDuplicate = async () => {
    if (selectedCount === 0) return
    
    setIsProcessing(true)
    try {
      await Promise.all(selectedProducts.map(p => duplicateProduct.mutateAsync(p.id)))
      onSelectionChange([])
      onProductUpdate?.()
    } catch (error) {
      console.error('Failed to duplicate products:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  // Bulk edit form submission
  const onBulkEditSubmit = async (values: BulkEditFormValues) => {
    if (selectedCount === 0) return

    setIsProcessing(true)
    try {
      const productIds = selectedProducts.map(p => p.id)

      switch (values.operation) {
        case 'category':
          if (values.category_id) {
            await bulkOperations.mutateAsync({
              productIds,
              operation: 'update_category',
              data: { category_id: values.category_id }
            })
          }
          break

        case 'prices':
          if (values.cost_price_adjustment !== undefined || values.selling_price_adjustment !== undefined) {
            // Calculate new prices for each product
            const updates = selectedProducts.map(product => {
              let newCostPrice = product.cost_price
              let newSellingPrice = product.selling_price

              if (values.cost_price_adjustment !== undefined) {
                if (values.adjustment_type === 'percentage') {
                  newCostPrice = product.cost_price * (1 + values.cost_price_adjustment / 100)
                } else {
                  newCostPrice = product.cost_price + values.cost_price_adjustment
                }
              }

              if (values.selling_price_adjustment !== undefined) {
                if (values.adjustment_type === 'percentage') {
                  newSellingPrice = product.selling_price * (1 + values.selling_price_adjustment / 100)
                } else {
                  newSellingPrice = product.selling_price + values.selling_price_adjustment
                }
              }

              return {
                productId: product.id,
                cost_price: Math.max(0, newCostPrice),
                selling_price: Math.max(0, newSellingPrice)
              }
            })

            // Apply updates individually since bulk price updates need per-product calculations
            await Promise.all(updates.map(update => 
              bulkOperations.mutateAsync({
                productIds: [update.productId],
                operation: 'update_prices',
                data: {
                  cost_price: update.cost_price,
                  selling_price: update.selling_price
                }
              })
            ))
          }
          break

        case 'status':
          if (values.status !== undefined) {
            await bulkOperations.mutateAsync({
              productIds,
              operation: values.status ? 'activate' : 'deactivate',
            })
          }
          break
      }

      setShowBulkEdit(false)
      onSelectionChange([])
      onProductUpdate?.()
    } catch (error) {
      console.error('Failed to apply bulk edit:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  if (selectedCount === 0) {
    return null
  }

  return (
    <>
      {/* Quick Actions Bar */}
      <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Package className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-900">
              {selectedCount} product{selectedCount !== 1 ? 's' : ''} selected
            </span>
          </div>
          
          {activeCount > 0 && inactiveCount > 0 && (
            <div className="flex items-center gap-2 text-sm text-blue-700">
              <Badge variant="default" className="bg-green-100 text-green-800">
                {activeCount} Active
              </Badge>
              <Badge variant="secondary">
                {inactiveCount} Inactive
              </Badge>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Quick Action Buttons */}
          {inactiveCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkActivate}
              disabled={isProcessing}
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              Activate ({inactiveCount})
            </Button>
          )}

          {activeCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkDeactivate}
              disabled={isProcessing}
            >
              <XCircle className="h-4 w-4 mr-1" />
              Deactivate ({activeCount})
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkDuplicate}
            disabled={isProcessing}
          >
            <Copy className="h-4 w-4 mr-1" />
            Duplicate
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBulkEdit(true)}
            disabled={isProcessing}
          >
            <Edit className="h-4 w-4 mr-1" />
            Bulk Edit
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkDelete}
            disabled={isProcessing}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Delete
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => onSelectionChange([])}
          >
            Clear Selection
          </Button>
        </div>
      </div>

      {/* Bulk Edit Dialog */}
      <Dialog open={showBulkEdit} onOpenChange={setShowBulkEdit}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Bulk Edit Products</DialogTitle>
            <DialogDescription>
              Apply changes to {selectedCount} selected product{selectedCount !== 1 ? 's' : ''}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onBulkEditSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="operation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Operation</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select operation" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="category">
                          <div className="flex items-center gap-2">
                            <Tag className="h-4 w-4" />
                            Update Category
                          </div>
                        </SelectItem>
                        <SelectItem value="prices">
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4" />
                            Adjust Prices
                          </div>
                        </SelectItem>
                        <SelectItem value="status">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4" />
                            Change Status
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Category Update */}
              {form.watch('operation') === 'category' && (
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Category</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No Category</SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Price Adjustments */}
              {form.watch('operation') === 'prices' && (
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="adjustment_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Adjustment Type</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="percentage">Percentage (%)</SelectItem>
                            <SelectItem value="fixed">Fixed Amount ($)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="cost_price_adjustment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Cost Price {form.watch('adjustment_type') === 'percentage' ? '(%)' : '($)'}
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="selling_price_adjustment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Selling Price {form.watch('adjustment_type') === 'percentage' ? '(%)' : '($)'}
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm text-yellow-700">
                        <p className="font-medium">Price Adjustment Preview:</p>
                        <p className="text-xs mt-1">
                          {form.watch('adjustment_type') === 'percentage'
                            ? 'Prices will be multiplied by (1 + percentage/100)'
                            : 'Fixed amount will be added to current prices'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Status Change */}
              {form.watch('operation') === 'status' && (
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Status</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(value === 'true')}
                        value={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="true">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              Active
                            </div>
                          </SelectItem>
                          <SelectItem value="false">
                            <div className="flex items-center gap-2">
                              <XCircle className="h-4 w-4 text-red-600" />
                              Inactive
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowBulkEdit(false)}
                  disabled={isProcessing}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isProcessing}>
                  {isProcessing ? 'Applying...' : `Apply to ${selectedCount} Products`}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}
