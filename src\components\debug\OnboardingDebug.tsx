import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuthHook';
import { supabase } from '@/lib/supabase';
import { AlertCircle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface AuthDebugInfo {
  userId: string | null;
  userEmail: string | null;
  isAuthenticated: boolean;
  sessionExists: boolean;
  canCreateOrg: boolean;
  canCreateProfile: boolean;
  error?: string;
  orgTestMessage?: string;
  profileTestMessage?: string;
}

export const OnboardingDebug = () => {
  const { user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null);
  const [loading, setLoading] = useState(false);

  const runDiagnostics = async () => {
    setLoading(true);
    try {
      // Get session info
      const { data: session } = await supabase.auth.getSession();
      
      // Test authentication context
      const { data: authContext, error: authError } = await supabase
        .rpc('debug_auth_context');

      // Test organization creation permission using the proper function
      let canCreateOrg = false;
      let orgTestMessage = '';

      // First check if user already has a profile/organization
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id, org_id, onboarding_completed_at')
        .eq('id', user?.id || '')
        .maybeSingle();

      if (existingProfile?.onboarding_completed_at) {
        canCreateOrg = true; // User already completed onboarding
        orgTestMessage = 'User already has completed onboarding';
      } else {
        // Test the function for users who haven't completed onboarding
        try {
          const { data: orgTestResult, error: orgTestError } = await supabase
            .rpc('create_organization_with_profile', {
              org_name: 'TEST_ORG_DELETE_ME',
              tin_number: null,
              business_reg_number: null,
              ura_tax_office: null,
              user_phone: null,
              user_role: 'accountant',
              country_code: 'UG',
              currency_code: 'UGX'
            });

          if (!orgTestError && orgTestResult?.[0]?.success) {
            canCreateOrg = true;
            orgTestMessage = 'Function test successful';
            // Clean up test organization and profile
            await supabase
              .from('organizations')
              .delete()
              .eq('id', orgTestResult[0].organization_id);
            await supabase
              .from('profiles')
              .delete()
              .eq('id', user?.id || '');
          } else if (orgTestError) {
            orgTestMessage = `Function error: ${orgTestError.message}`;
            console.log('Org creation test failed:', orgTestError.message);
          } else {
            orgTestMessage = `Function returned error: ${orgTestResult?.[0]?.error_message}`;
            console.log('Org creation test failed:', orgTestResult?.[0]?.error_message);
          }
        } catch (e) {
          orgTestMessage = `Test exception: ${e instanceof Error ? e.message : 'Unknown error'}`;
          console.log('Org creation test failed:', e);
        }
      }

      // Test profile access permission (check if user can read their own profile)
      let canCreateProfile = false;
      let profileTestMessage = '';
      try {
        // Test if user can access profiles table (for their own profile)
        const { error: profileTestError } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', user?.id || '')
          .maybeSingle();

        // If no error, user can access profiles table
        canCreateProfile = !profileTestError;
        profileTestMessage = profileTestError ?
          `Profile access failed: ${profileTestError.message}` :
          'Profile access successful';

        if (profileTestError) {
          console.log('Profile access test failed:', profileTestError.message);
        }
      } catch (e) {
        profileTestMessage = `Profile test exception: ${e instanceof Error ? e.message : 'Unknown error'}`;
        console.log('Profile test failed:', e);
      }

      setDebugInfo({
        userId: user?.id || null,
        userEmail: user?.email || null,
        isAuthenticated: !!user,
        sessionExists: !!session?.session,
        canCreateOrg,
        canCreateProfile,
        error: authError?.message,
        orgTestMessage,
        profileTestMessage: canCreateProfile ? 'Profile access successful' : 'Profile access failed'
      });

    } catch (error) {
      setDebugInfo({
        userId: user?.id || null,
        userEmail: user?.email || null,
        isAuthenticated: !!user,
        sessionExists: false,
        canCreateOrg: false,
        canCreateProfile: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testOnboardingFlow = async () => {
    if (!user) {
      alert('Please log in first');
      return;
    }

    try {
      const { data, error } = await supabase
        .rpc('create_organization_with_profile', {
          org_name: 'Test Organization (DELETE ME)',
          tin_number: null,
          business_reg_number: null,
          ura_tax_office: null,
          user_phone: null,
          user_role: 'accountant',
          country_code: 'UG',
          currency_code: 'UGX'
        });

      if (error) {
        alert(`Helper function failed: ${error.message}`);
      } else if (data?.[0]?.success) {
        alert(`Success! Organization ID: ${data[0].organization_id}`);
        // Clean up
        await supabase
          .from('organizations')
          .delete()
          .eq('id', data[0].organization_id);
        await supabase
          .from('profiles')
          .delete()
          .eq('id', user.id);
      } else {
        alert(`Helper function returned error: ${data?.[0]?.error_message}`);
      }
    } catch (error) {
      alert(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto mt-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          Onboarding Debug Panel
        </CardTitle>
        <CardDescription>
          Debug authentication and RLS issues for onboarding flow
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={runDiagnostics} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Running Diagnostics...
              </>
            ) : (
              'Run Diagnostics'
            )}
          </Button>
          <Button onClick={testOnboardingFlow} variant="outline" disabled={!user}>
            Test Helper Function
          </Button>
        </div>

        {debugInfo && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-semibold">Authentication Status</h4>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    {debugInfo.isAuthenticated ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span>User Authenticated</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {debugInfo.sessionExists ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span>Session Exists</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Permissions</h4>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    {debugInfo.canCreateOrg ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span>Can Create Organization (via function)</span>
                  </div>
                  {debugInfo.orgTestMessage && (
                    <div className="text-xs text-gray-600 ml-6">
                      {debugInfo.orgTestMessage}
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    {debugInfo.canCreateProfile ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span>Can Access Profile Table</span>
                  </div>
                  {debugInfo.profileTestMessage && (
                    <div className="text-xs text-gray-600 ml-6">
                      {debugInfo.profileTestMessage}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold">User Information</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">User ID:</span>
                  <Badge variant="outline">{debugInfo.userId || 'None'}</Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Email:</span>
                  <Badge variant="outline">{debugInfo.userEmail || 'None'}</Badge>
                </div>
              </div>
            </div>

            {debugInfo.error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <h4 className="font-semibold text-red-800">Error Details</h4>
                <p className="text-sm text-red-600">{debugInfo.error}</p>
              </div>
            )}

            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <h4 className="font-semibold text-blue-800">Troubleshooting Tips</h4>
              <ul className="text-sm text-blue-600 space-y-1 mt-2">
                <li>• If authentication fails, try logging out and back in</li>
                <li>• If permissions fail, check if RLS migration has been applied</li>
                <li>• If helper function fails, manual creation should still work</li>
                <li>• Check browser console for detailed error messages</li>
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
