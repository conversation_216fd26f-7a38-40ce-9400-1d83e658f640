import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys, type QueryFilters } from '@/lib/queryKeys'
import { toast } from '@/hooks/use-toast'
import type { BillWithVendor, BillFormData } from '@/types/bills'

/**
 * Hook to fetch all bills for the organization
 */
export function useBills(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: filters 
      ? queryKeys.bills.filtered(profile?.org_id || '', filters)
      : queryKeys.bills.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('bills')
        .select('*, vendors!inner(*)')
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`bill_number.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }
      
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      // Apply ordering
      query = query.order('created_at', { ascending: false })

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit)
      }
      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1)
      }

      const { data, error } = await query

      if (error) throw error

      // Transform the data to match our expected structure
      const transformedBills = data?.map(bill => ({
        ...bill,
        vendor: bill.vendors
      })) || []

      return transformedBills
    },
    enabled: !!profile?.org_id,
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

/**
 * Hook to fetch bills by vendor
 */
export function useBillsByVendor(vendorId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.bills.byVendor(profile?.org_id || '', vendorId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !vendorId) return []

      const { data, error } = await supabase
        .from('bills')
        .select('*, vendors!inner(*)')
        .eq('org_id', profile.org_id)
        .eq('vendor_id', vendorId)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Transform the data to match our expected structure
      const transformedBills = data?.map(bill => ({
        ...bill,
        vendor: bill.vendors
      })) || []

      return transformedBills
    },
    enabled: !!profile?.org_id && !!vendorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch bills by status
 */
export function useBillsByStatus(status: string) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.bills.byStatus(profile?.org_id || '', status),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('bills')
        .select('*, vendors!inner(*)')
        .eq('org_id', profile.org_id)
        .eq('status', status)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Transform the data to match our expected structure
      const transformedBills = data?.map(bill => ({
        ...bill,
        vendor: bill.vendors
      })) || []

      return transformedBills
    },
    enabled: !!profile?.org_id && !!status,
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

/**
 * Hook to fetch a single bill by ID
 */
export function useBill(billId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.bills.detail(profile?.org_id || '', billId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !billId) return null

      const { data, error } = await supabase
        .from('bills')
        .select(`
          *,
          vendors(*),
          bill_lines(*)
        `)
        .eq('id', billId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id && !!billId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to create a new bill
 */
export function useCreateBill() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (billData: BillFormData) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { lines, ...billFields } = billData

      // Create the bill (exclude account_id which belongs to bill_lines)
      const { account_id, ...validBillFields } = billFields
      const { data: bill, error: billError } = await supabase
        .from('bills')
        .insert({
          ...validBillFields,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (billError) throw billError

      // Create bill lines if provided
      if (lines && lines.length > 0) {
        const { error: linesError } = await supabase
          .from('bill_lines')
          .insert(
            lines.map(line => ({
              ...line,
              bill_id: bill.id,
              org_id: profile.org_id,
            }))
          )

        if (linesError) throw linesError
      }

      return bill
    },
    onSuccess: (newBill) => {
      // Invalidate and refetch bills list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.bills.all(profile?.org_id || '') 
      })
      
      // Invalidate vendor-specific bills
      if (newBill.vendor_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.bills.byVendor(profile?.org_id || '', newBill.vendor_id) 
        })
      }

      // Invalidate status-specific bills
      if (newBill.status) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.bills.byStatus(profile?.org_id || '', newBill.status)
        })
      }

      // Invalidate budget-related queries for real-time budget analysis
      queryClient.invalidateQueries({
        queryKey: queryKeys.budgets.alerts(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.budgets.allAnalysis(profile?.org_id || '')
      })

      toast({
        title: 'Success',
        description: 'Bill created successfully',
      })
    },
    onError: (error) => {
      console.error('Error creating bill:', error)
      toast({
        title: 'Error',
        description: 'Failed to create bill',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update an existing bill
 */
export function useUpdateBill() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      billId, 
      billData 
    }: { 
      billId: string
      billData: Partial<BillFormData>
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { lines, ...billFields } = billData

      // Update the bill (exclude account_id which belongs to bill_lines)
      const { account_id, ...validBillFields } = billFields
      const { data, error } = await supabase
        .from('bills')
        .update({
          ...validBillFields,
          updated_at: new Date().toISOString(),
        })
        .eq('id', billId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error

      // Update bill lines if provided
      if (lines) {
        // Delete existing lines
        await supabase
          .from('bill_lines')
          .delete()
          .eq('bill_id', billId)

        // Insert new lines
        if (lines.length > 0) {
          await supabase
            .from('bill_lines')
            .insert(
              lines.map(line => ({
                ...line,
                bill_id: billId,
                org_id: profile.org_id,
              }))
            )
        }
      }

      return data
    },
    onSuccess: (updatedBill) => {
      // Update the bill in the cache
      queryClient.setQueryData(
        queryKeys.bills.detail(profile?.org_id || '', updatedBill.id),
        updatedBill
      )

      // Invalidate bills list to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.bills.all(profile?.org_id || '') 
      })

      // Invalidate vendor-specific bills
      if (updatedBill.vendor_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.bills.byVendor(profile?.org_id || '', updatedBill.vendor_id) 
        })
      }

      // Invalidate status-specific bills
      if (updatedBill.status) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.bills.byStatus(profile?.org_id || '', updatedBill.status)
        })
      }

      // Invalidate budget-related queries for real-time budget analysis
      queryClient.invalidateQueries({
        queryKey: queryKeys.budgets.alerts(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.budgets.allAnalysis(profile?.org_id || '')
      })

      toast({
        title: 'Success',
        description: 'Bill updated successfully',
      })
    },
    onError: (error) => {
      console.error('Error updating bill:', error)
      toast({
        title: 'Error',
        description: 'Failed to update bill',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a bill
 */
export function useDeleteBill() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (billId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Delete bill lines first
      await supabase
        .from('bill_lines')
        .delete()
        .eq('bill_id', billId)

      // Delete the bill
      const { error } = await supabase
        .from('bills')
        .delete()
        .eq('id', billId)
        .eq('org_id', profile.org_id)

      if (error) throw error
      return billId
    },
    onSuccess: (deletedBillId) => {
      // Remove the bill from the cache
      queryClient.removeQueries({ 
        queryKey: queryKeys.bills.detail(profile?.org_id || '', deletedBillId) 
      })

      // Invalidate bills list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.bills.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: 'Bill deleted successfully',
      })
    },
    onError: (error) => {
      console.error('Error deleting bill:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete bill',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update bill status
 */
export function useUpdateBillStatus() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ billId, status }: { billId: string, status: string }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('bills')
        .update({ 
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', billId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (updatedBill) => {
      // Update the bill in the cache
      queryClient.setQueryData(
        queryKeys.bills.detail(profile?.org_id || '', updatedBill.id),
        updatedBill
      )

      // Invalidate all bill lists to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.bills.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: `Bill status updated to ${updatedBill.status}`,
      })
    },
    onError: (error) => {
      console.error('Error updating bill status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update bill status',
        variant: 'destructive',
      })
    },
  })
}
