import { useState, useEffect, useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import type { AuditLogStats, AuditLogAnalytics, AuditLogFilters, AuditLogWithProfile } from '@/types/audit'

export function useAuditAnalytics(filters?: AuditLogFilters) {
  const { profile } = useAuth()
  const [stats, setStats] = useState<AuditLogStats | null>(null)
  const [analytics, setAnalytics] = useState<AuditLogAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAuditStats = useCallback(async (): Promise<AuditLogStats> => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

    // Build query with filters
    let query = supabase
      .from('audit_logs')
      .select('*')
      .eq('org_id', profile!.org_id)

    if (filters?.entity_type) {
      query = query.eq('entity_type', filters.entity_type)
    }
    if (filters?.action) {
      query = query.eq('action', filters.action)
    }
    if (filters?.user_id) {
      query = query.eq('profile_id', filters.user_id)
    }
    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from)
    }
    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to)
    }

    const { data: allLogs, error } = await query

    if (error) throw error

    const logs = allLogs || []

    // Calculate stats
    const total_logs = logs.length
    const logs_today = logs.filter(log => new Date(log.created_at) >= today).length
    const logs_this_week = logs.filter(log => new Date(log.created_at) >= weekAgo).length
    const logs_this_month = logs.filter(log => new Date(log.created_at) >= monthAgo).length

    // Top actions
    const actionCounts = logs.reduce((acc, log) => {
      acc[log.action] = (acc[log.action] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const top_actions = Object.entries(actionCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([action, count]) => ({ action, count }))

    // Top entities
    const entityCounts = logs.reduce((acc, log) => {
      acc[log.entity_type] = (acc[log.entity_type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const top_entities = Object.entries(entityCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([entity_type, count]) => ({ entity_type, count }))

    // Fetch user emails for top users
    const userCounts = logs.reduce((acc, log) => {
      if (log.profile_id) {
        acc[log.profile_id] = (acc[log.profile_id] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    const topUserIds = Object.entries(userCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([userId]) => userId)

    const { data: userProfiles } = await supabase
      .from('profiles')
      .select('id, email')
      .in('id', topUserIds)

    const userEmailMap = userProfiles?.reduce((acc, profile) => {
      acc[profile.id] = profile.email
      return acc
    }, {} as Record<string, string>) || {}

    const top_users = Object.entries(userCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([userId, count]) => ({
        user_email: userEmailMap[userId] || 'Unknown',
        count
      }))

    // Severity breakdown (placeholder - would need to be added to schema)
    const severity_breakdown = {
      low: Math.floor(logs.length * 0.6),
      medium: Math.floor(logs.length * 0.25),
      high: Math.floor(logs.length * 0.12),
      critical: Math.floor(logs.length * 0.03)
    }

    // Category breakdown (placeholder - would need to be added to schema)
    const category_breakdown = {
      data_modification: Math.floor(logs.length * 0.4),
      financial_transaction: Math.floor(logs.length * 0.3),
      authentication: Math.floor(logs.length * 0.15),
      data_access: Math.floor(logs.length * 0.1),
      system_configuration: Math.floor(logs.length * 0.05)
    }

    return {
      total_logs,
      logs_today,
      logs_this_week,
      logs_this_month,
      top_actions,
      top_entities,
      top_users,
      severity_breakdown,
      category_breakdown
    }
  }, [profile, filters])

  // Helper functions wrapped in useCallback to prevent recreation on every render
  const getSeverityForAction = useCallback((action: string): string => {
    const criticalActions = ['delete', 'purge', 'permission_change']
    const highActions = ['approve', 'reject', 'reconcile', 'password_change']
    const mediumActions = ['create', 'update', 'send', 'cancel']

    if (criticalActions.includes(action)) return 'critical'
    if (highActions.includes(action)) return 'high'
    if (mediumActions.includes(action)) return 'medium'
    return 'low'
  }, [])

  const calculateUserRiskScore = useCallback((actions: string[]): number => {
    const riskScores = {
      delete: 8,
      purge: 10,
      permission_change: 9,
      password_change: 6,
      approve: 7,
      reject: 7,
      export: 5,
      update: 3,
      create: 2,
      view: 1
    }

    const totalScore = actions.reduce((sum, action) => {
      return sum + (riskScores[action as keyof typeof riskScores] || 1)
    }, 0)

    return Math.min(Math.round(totalScore / actions.length), 10)
  }, [])

  const generateActivityTimeline = useCallback((logs: AuditLogWithProfile[]) => {
    const timeline: Record<string, { count: number; severity_breakdown: Record<string, number> }> = {}

    logs.forEach(log => {
      const date = new Date(log.created_at).toISOString().split('T')[0]
      if (!timeline[date]) {
        timeline[date] = {
          count: 0,
          severity_breakdown: { low: 0, medium: 0, high: 0, critical: 0 }
        }
      }
      timeline[date].count++
      // Placeholder severity assignment
      const severity = getSeverityForAction(log.action)
      timeline[date].severity_breakdown[severity]++
    })

    return Object.entries(timeline)
      .map(([date, data]) => ({ date, ...data }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }, [getSeverityForAction])

  const generateUserActivity = useCallback((logs: AuditLogWithProfile[]) => {
    const userActivity: Record<string, { total_actions: number; last_activity: string; actions: string[] }> = {}

    logs.forEach(log => {
      const email = log.profiles?.email || 'Unknown'
      if (!userActivity[email]) {
        userActivity[email] = {
          total_actions: 0,
          last_activity: log.created_at,
          actions: []
        }
      }
      userActivity[email].total_actions++
      userActivity[email].actions.push(log.action)
      if (new Date(log.created_at) > new Date(userActivity[email].last_activity)) {
        userActivity[email].last_activity = log.created_at
      }
    })

    return Object.entries(userActivity)
      .map(([user_email, data]) => ({
        user_email,
        total_actions: data.total_actions,
        last_activity: data.last_activity,
        risk_score: calculateUserRiskScore(data.actions)
      }))
      .sort((a, b) => b.total_actions - a.total_actions)
  }, [calculateUserRiskScore])

  const generateEntityActivity = useCallback((logs: AuditLogWithProfile[]) => {
    const entityActivity: Record<string, { total_modifications: number; last_modified: string }> = {}

    logs.forEach(log => {
      if (!entityActivity[log.entity_type]) {
        entityActivity[log.entity_type] = {
          total_modifications: 0,
          last_modified: log.created_at
        }
      }
      entityActivity[log.entity_type].total_modifications++
      if (new Date(log.created_at) > new Date(entityActivity[log.entity_type].last_modified)) {
        entityActivity[log.entity_type].last_modified = log.created_at
      }
    })

    return Object.entries(entityActivity)
      .map(([entity_type, data]) => ({
        entity_type,
        ...data
      }))
      .sort((a, b) => b.total_modifications - a.total_modifications)
  }, [])

  const fetchDetailedAnalytics = useCallback(async (): Promise<AuditLogAnalytics> => {
    // Activity timeline (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const { data: recentLogs } = await supabase
      .from('audit_logs')
      .select('created_at, action')
      .eq('org_id', profile!.org_id)
      .gte('created_at', thirtyDaysAgo.toISOString())

    const activity_timeline = generateActivityTimeline(recentLogs || [])

    // User activity analysis
    const { data: userActivityData } = await supabase
      .from('audit_logs')
      .select(`
        profile_id,
        created_at,
        action,
        profiles:profile_id(email)
      `)
      .eq('org_id', profile!.org_id)
      .gte('created_at', thirtyDaysAgo.toISOString())

    const user_activity = generateUserActivity(userActivityData || [])

    // Entity activity analysis
    const { data: entityActivityData } = await supabase
      .from('audit_logs')
      .select('entity_type, created_at, action')
      .eq('org_id', profile!.org_id)
      .in('action', ['create', 'update', 'delete'])
      .gte('created_at', thirtyDaysAgo.toISOString())

    const entity_activity = generateEntityActivity(entityActivityData || [])

    // Security events (placeholder)
    const security_events = [
      {
        event_type: 'Failed Login Attempts',
        count: 5,
        severity: 'medium',
        last_occurrence: new Date().toISOString()
      },
      {
        event_type: 'Permission Changes',
        count: 2,
        severity: 'high',
        last_occurrence: new Date().toISOString()
      }
    ]

    return {
      activity_timeline,
      user_activity,
      entity_activity,
      security_events
    }
  }, [profile, generateActivityTimeline, generateUserActivity, generateEntityActivity])

  const fetchAuditAnalytics = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      setError(null)

      // Fetch basic stats
      const statsData = await fetchAuditStats()
      setStats(statsData)

      // Fetch detailed analytics
      const analyticsData = await fetchDetailedAnalytics()
      setAnalytics(analyticsData)

    } catch (err) {
      console.error('Error fetching audit analytics:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch audit analytics')
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, fetchAuditStats, fetchDetailedAnalytics])

  useEffect(() => {
    if (profile?.org_id) {
      fetchAuditAnalytics()
    }
  }, [profile?.org_id, filters, fetchAuditAnalytics])

  return {
    stats,
    analytics,
    loading,
    error,
    refetch: fetchAuditAnalytics
  }
}
