/**
 * Hook for invoice PDF export functionality
 */

import { useState } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/lib/supabase'
import { exportInvoiceToPdf, type InvoicePdfData } from '@/lib/invoicePdfExport'
import type { Organization } from '@/types/database'

export function useInvoicePdfExport() {
  const { profile } = useAuth()
  const { toast } = useToast()
  const [isExporting, setIsExporting] = useState(false)

  const exportToPdf = async (invoice: InvoicePdfData) => {
    if (!profile?.org_id) {
      toast({
        title: "Error",
        description: "Organization not found",
        variant: "destructive"
      })
      return false
    }

    setIsExporting(true)
    try {
      // Fetch organization details
      let organization: Organization | undefined
      try {
        const { data: orgData, error: orgError } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', profile.org_id)
          .single()

        if (!orgError && orgData) {
          organization = orgData
        }
      } catch (error) {
        console.warn('Could not fetch organization details:', error)
        // Continue without organization details
      }

      // Export to PDF
      await exportInvoiceToPdf(invoice, organization)
      
      toast({
        title: "Success",
        description: "Invoice PDF downloaded successfully",
      })
      
      return true
    } catch (error) {
      console.error('PDF export failed:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to export PDF",
        variant: "destructive"
      })
      return false
    } finally {
      setIsExporting(false)
    }
  }

  const printInvoice = async (invoice: InvoicePdfData) => {
    if (!profile?.org_id) {
      toast({
        title: "Error",
        description: "Organization not found",
        variant: "destructive"
      })
      return false
    }

    setIsExporting(true)
    try {
      // Fetch organization details
      let organization: Organization | undefined
      try {
        const { data: orgData, error: orgError } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', profile.org_id)
          .single()

        if (!orgError && orgData) {
          organization = orgData
        }
      } catch (error) {
        console.warn('Could not fetch organization details:', error)
      }

      // Create print window with invoice content
      const { createInvoiceHtmlTemplate } = await import('@/lib/invoicePdfExport')
      const htmlContent = createInvoiceHtmlTemplate(invoice, organization)
      
      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        throw new Error('Could not open print window. Please check your popup blocker.')
      }

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Invoice ${invoice.invoice_number}</title>
            <style>
              @media print {
                body { margin: 0; }
                @page { margin: 1cm; }
              }
            </style>
          </head>
          <body>
            ${htmlContent}
          </body>
        </html>
      `)
      
      printWindow.document.close()
      
      // Wait for content to load then print
      printWindow.onload = () => {
        printWindow.print()
        printWindow.close()
      }
      
      toast({
        title: "Success",
        description: "Print dialog opened",
      })
      
      return true
    } catch (error) {
      console.error('Print failed:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to print invoice",
        variant: "destructive"
      })
      return false
    } finally {
      setIsExporting(false)
    }
  }

  return {
    exportToPdf,
    printInvoice,
    isExporting
  }
}
