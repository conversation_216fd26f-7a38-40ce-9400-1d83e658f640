import type { Customer, Vendor } from './extended-database'
import type { Invoice, Bill, Payment } from './database'

export interface StatementTransaction {
  id: string
  date: string
  type: 'invoice' | 'bill' | 'payment' | 'credit_note' | 'opening_balance'
  reference: string
  description: string
  debit: number
  credit: number
  balance: number
  document_id?: string
  status?: string
}

export interface StatementData {
  entity: Customer | Vendor
  entity_type: 'customer' | 'vendor'
  period_start: string
  period_end: string
  opening_balance: number
  closing_balance: number
  transactions: StatementTransaction[]
  summary: {
    total_invoiced?: number
    total_billed?: number
    total_payments: number
    total_outstanding: number
  }
  aging?: {
    current: number
    days_30: number
    days_60: number
    days_90: number
    over_90: number
  }
}

export interface StatementFilters {
  entity_id: string
  entity_type: 'customer' | 'vendor'
  start_date: string
  end_date: string
  include_paid?: boolean
  include_draft?: boolean
}

export interface StatementProps {
  entity: Customer | Vendor
  entity_type: 'customer' | 'vendor'
  open: boolean
  onOpenChange: (open: boolean) => void
}
