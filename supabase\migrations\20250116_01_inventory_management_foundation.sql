-- =====================================================
-- INVENTORY MANAGEMENT - FOUNDATION MIGRATION
-- =====================================================
-- Date: 2025-01-16
-- Purpose: Create core inventory management tables and relationships
-- Dependencies: organizations, auth.users tables must exist
-- Rollback: See 20250116_01_inventory_management_rollback.sql

-- =====================================================
-- STEP 1: CREATE CORE TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🏗️  Creating inventory management foundation tables...';
END $$;

-- Product Categories Table
CREATE TABLE IF NOT EXISTS product_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES product_categories(id) ON DELETE SET NULL,
    code VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_category_name_per_org UNIQUE(org_id, name),
    CONSTRAINT unique_category_code_per_org UNIQUE(org_id, code),
    CONSTRAINT no_self_reference CHECK (id != parent_id)
);

-- Inventory Locations Table
CREATE TABLE IF NOT EXISTS inventory_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_location_name_per_org UNIQUE(org_id, name),
    CONSTRAINT unique_location_code_per_org UNIQUE(org_id, code)
);

-- Products Table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    sku VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES product_categories(id) ON DELETE SET NULL,
    unit_of_measure VARCHAR(50) NOT NULL DEFAULT 'each',
    
    -- Pricing
    cost_price DECIMAL(15,2) DEFAULT 0,
    selling_price DECIMAL(15,2) DEFAULT 0,
    
    -- Inventory Control
    track_inventory BOOLEAN DEFAULT true,
    reorder_level DECIMAL(15,3) DEFAULT 0,
    reorder_quantity DECIMAL(15,3) DEFAULT 0,
    
    -- Product Attributes
    barcode VARCHAR(255),
    weight DECIMAL(10,3),
    dimensions VARCHAR(100),
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_sellable BOOLEAN DEFAULT true,
    is_purchasable BOOLEAN DEFAULT true,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    CONSTRAINT unique_sku_per_org UNIQUE(org_id, sku),
    CONSTRAINT unique_barcode_per_org UNIQUE(org_id, barcode)
);

-- Stock Levels Table
CREATE TABLE IF NOT EXISTS stock_levels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES inventory_locations(id) ON DELETE CASCADE,
    
    quantity_on_hand DECIMAL(15,3) DEFAULT 0,
    quantity_reserved DECIMAL(15,3) DEFAULT 0,
    quantity_available DECIMAL(15,3) GENERATED ALWAYS AS (quantity_on_hand - quantity_reserved) STORED,
    
    -- Cost tracking
    average_cost DECIMAL(15,4) DEFAULT 0,
    last_cost DECIMAL(15,4) DEFAULT 0,
    
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_product_location UNIQUE(org_id, product_id, location_id),
    CONSTRAINT non_negative_quantities CHECK (
        quantity_on_hand >= 0 AND 
        quantity_reserved >= 0 AND 
        quantity_reserved <= quantity_on_hand
    )
);

-- Inventory Transactions Table
CREATE TABLE IF NOT EXISTS inventory_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES inventory_locations(id) ON DELETE CASCADE,
    
    -- Transaction Details
    transaction_type VARCHAR(50) NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,
    unit_cost DECIMAL(15,4),
    total_cost DECIMAL(15,2),
    
    -- Reference Information
    reference_type VARCHAR(50),
    reference_id UUID,
    reference_number VARCHAR(255),
    
    -- Additional Details
    reason_code VARCHAR(50),
    notes TEXT,
    batch_number VARCHAR(100),
    expiry_date DATE,
    
    -- Audit Information
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    CONSTRAINT valid_transaction_type CHECK (
        transaction_type IN ('purchase', 'sale', 'adjustment', 'transfer_in', 'transfer_out', 'opening_balance')
    )
);

-- =====================================================
-- STEP 2: MODIFY EXISTING TABLES
-- =====================================================

-- Add product references to line items
ALTER TABLE invoice_lines 
ADD COLUMN IF NOT EXISTS product_id UUID REFERENCES products(id) ON DELETE SET NULL;

ALTER TABLE bill_lines 
ADD COLUMN IF NOT EXISTS product_id UUID REFERENCES products(id) ON DELETE SET NULL;

-- Ensure item columns exist for backward compatibility
ALTER TABLE invoice_lines 
ADD COLUMN IF NOT EXISTS item VARCHAR(255);

ALTER TABLE bill_lines 
ADD COLUMN IF NOT EXISTS item VARCHAR(255);

-- =====================================================
-- STEP 3: CREATE FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to ensure single default location per organization
CREATE OR REPLACE FUNCTION ensure_single_default_location()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_default = true THEN
        UPDATE inventory_locations 
        SET is_default = false 
        WHERE org_id = NEW.org_id AND id != NEW.id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update stock levels timestamp
CREATE OR REPLACE FUNCTION update_stock_levels_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to auto-populate item from product
CREATE OR REPLACE FUNCTION populate_item_from_product()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.product_id IS NOT NULL AND (NEW.item IS NULL OR NEW.item = '') THEN
        SELECT name INTO NEW.item FROM products WHERE id = NEW.product_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update stock levels on transaction
CREATE OR REPLACE FUNCTION update_stock_levels_on_transaction()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO stock_levels (org_id, product_id, location_id, quantity_on_hand, last_updated)
    VALUES (NEW.org_id, NEW.product_id, NEW.location_id, NEW.quantity, NOW())
    ON CONFLICT (org_id, product_id, location_id)
    DO UPDATE SET 
        quantity_on_hand = stock_levels.quantity_on_hand + NEW.quantity,
        last_updated = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_ensure_single_default_location
    BEFORE INSERT OR UPDATE ON inventory_locations
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_default_location();

CREATE TRIGGER trigger_update_stock_levels_timestamp
    BEFORE UPDATE ON stock_levels
    FOR EACH ROW
    EXECUTE FUNCTION update_stock_levels_timestamp();

CREATE TRIGGER trigger_populate_invoice_item_from_product
    BEFORE INSERT OR UPDATE ON invoice_lines
    FOR EACH ROW
    EXECUTE FUNCTION populate_item_from_product();

CREATE TRIGGER trigger_populate_bill_item_from_product
    BEFORE INSERT OR UPDATE ON bill_lines
    FOR EACH ROW
    EXECUTE FUNCTION populate_item_from_product();

CREATE TRIGGER trigger_update_stock_levels_on_transaction
    AFTER INSERT ON inventory_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_stock_levels_on_transaction();

DO $$
BEGIN
    RAISE NOTICE '✅ Inventory management foundation tables created successfully!';
END $$;
