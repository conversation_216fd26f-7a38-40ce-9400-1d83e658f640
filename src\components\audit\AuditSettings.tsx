import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Settings, 
  Archive, 
  Trash2, 
  Shield, 
  Clock, 
  Database,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/loading'
import { useAuth } from '@/hooks/useAuthHook'
import { useToast } from '@/hooks/use-toast'
import { auditRetentionManager } from '@/lib/auditRetention'
import type { AuditLogRetentionPolicy, AuditRetentionStatus } from '@/types/audit'
import { ENTITY_TYPE_LABELS } from '@/types/audit'

export function AuditSettings() {
  const { profile } = useAuth()
  const { toast } = useToast()
  const [retentionStatus, setRetentionStatus] = useState<AuditRetentionStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [policies, setPolicies] = useState<Record<string, AuditLogRetentionPolicy>>({})
  const [selectedEntityType, setSelectedEntityType] = useState<string>('')
  const [isArchiving, setIsArchiving] = useState(false)
  const [isPurging, setIsPurging] = useState(false)

  const fetchRetentionStatus = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      const status = await auditRetentionManager.getRetentionStatus(profile.org_id)
      setRetentionStatus(status)

      // Load current policies
      const currentPolicies: Record<string, AuditLogRetentionPolicy> = {}
      Object.keys(status.logs_by_type).forEach(entityType => {
        currentPolicies[entityType] = auditRetentionManager.getRetentionPolicy(entityType)
      })
      setPolicies(currentPolicies)
    } catch (error) {
      console.error('Error fetching retention status:', error)
      toast({
        title: "Error",
        description: "Failed to load retention status",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, toast])

  useEffect(() => {
    if (profile?.org_id) {
      fetchRetentionStatus()
    }
  }, [profile?.org_id, fetchRetentionStatus])

  const handlePolicyUpdate = (entityType: string, policy: AuditLogRetentionPolicy) => {
    auditRetentionManager.setRetentionPolicy(entityType, policy)
    setPolicies(prev => ({ ...prev, [entityType]: policy }))
    toast({
      title: "Success",
      description: `Retention policy updated for ${ENTITY_TYPE_LABELS[entityType] || entityType}`,
    })
  }

  const handleArchive = async (entityType?: string) => {
    if (!profile?.org_id) return

    try {
      setIsArchiving(true)
      const results = await auditRetentionManager.archiveOldLogs(profile.org_id, entityType)
      
      if (results.errors.length > 0) {
        toast({
          title: "Archive Completed with Errors",
          description: `Archived ${results.archived} logs. ${results.errors.length} errors occurred.`,
          variant: "destructive"
        })
      } else {
        toast({
          title: "Archive Successful",
          description: `Successfully archived ${results.archived} audit logs`,
        })
      }
      
      await fetchRetentionStatus()
    } catch (error) {
      toast({
        title: "Archive Failed",
        description: "Failed to archive audit logs",
        variant: "destructive"
      })
    } finally {
      setIsArchiving(false)
    }
  }

  const handlePurge = async (entityType?: string) => {
    if (!profile?.org_id) return

    try {
      setIsPurging(true)
      const results = await auditRetentionManager.purgeExpiredLogs(profile.org_id, entityType)
      
      if (results.errors.length > 0) {
        toast({
          title: "Purge Completed with Errors",
          description: `Purged ${results.purged} logs. ${results.errors.length} errors occurred.`,
          variant: "destructive"
        })
      } else {
        toast({
          title: "Purge Successful",
          description: `Successfully purged ${results.purged} expired audit logs`,
        })
      }
      
      await fetchRetentionStatus()
    } catch (error) {
      toast({
        title: "Purge Failed",
        description: "Failed to purge audit logs",
        variant: "destructive"
      })
    } finally {
      setIsPurging(false)
    }
  }

  if (loading) {
    return (
      <div className="p-8">
        <LoadingSpinner text="Loading audit settings..." showText />
      </div>
    )
  }

  if (!retentionStatus) {
    return (
      <div className="text-center p-8 text-gray-500">
        No retention data available
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Audit Logs</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{retentionStatus.total_logs.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Can Archive</CardTitle>
            <Archive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(retentionStatus.logs_by_type).reduce((sum: number, type) => sum + type.can_archive, 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Can Purge</CardTitle>
            <Trash2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(retentionStatus.logs_by_type).reduce((sum: number, type) => sum + type.can_purge, 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Entity Types</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(retentionStatus.logs_by_type).length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Age Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Log Age Distribution
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(retentionStatus.logs_by_age).map(([age, count]) => {
            const percentage = (count / retentionStatus.total_logs) * 100
            const ageLabels = {
              current: 'Current (< 30 days)',
              recent: 'Recent (30-90 days)',
              old: 'Old (90-365 days)',
              very_old: 'Very Old (> 365 days)'
            }
            
            return (
              <div key={age} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{ageLabels[age as keyof typeof ageLabels]}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">{count}</span>
                    <span className="text-sm text-muted-foreground">({percentage.toFixed(1)}%)</span>
                  </div>
                </div>
                <Progress value={percentage} className="h-2" />
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Retention Policies by Entity Type */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Retention Policies by Entity Type
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(retentionStatus.logs_by_type).map(([entityType, typeData]) => {
              const policy = policies[entityType]
              if (!policy) return null

              return (
                <div key={entityType} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium">{ENTITY_TYPE_LABELS[entityType] || entityType}</h4>
                      <p className="text-sm text-muted-foreground">
                        {typeData.count} logs • Oldest: {new Date(typeData.oldest).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {typeData.can_archive > 0 && (
                        <Badge variant="secondary">
                          {typeData.can_archive} can archive
                        </Badge>
                      )}
                      {typeData.can_purge > 0 && (
                        <Badge variant="destructive">
                          {typeData.can_purge} can purge
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <Label>Retention Period</Label>
                      <p className="font-medium">{policy.retention_days} days</p>
                    </div>
                    <div>
                      <Label>Archive After</Label>
                      <p className="font-medium">{policy.archive_after_days} days</p>
                    </div>
                    <div>
                      <Label>Auto Purge</Label>
                      <div className="flex items-center gap-2">
                        {policy.auto_purge_enabled ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        )}
                        <span>{policy.auto_purge_enabled ? 'Enabled' : 'Disabled'}</span>
                      </div>
                    </div>
                    <div>
                      <Label>Compliance</Label>
                      <p className="font-medium">{policy.compliance_requirements.length} requirements</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 mt-4">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4 mr-2" />
                          Configure
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>
                            Configure Retention Policy - {ENTITY_TYPE_LABELS[entityType] || entityType}
                          </DialogTitle>
                        </DialogHeader>
                        <RetentionPolicyForm
                          entityType={entityType}
                          policy={policy}
                          onSave={(updatedPolicy) => handlePolicyUpdate(entityType, updatedPolicy)}
                        />
                      </DialogContent>
                    </Dialog>

                    {typeData.can_archive > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleArchive(entityType)}
                        disabled={isArchiving}
                      >
                        <Archive className="h-4 w-4 mr-2" />
                        Archive ({typeData.can_archive})
                      </Button>
                    )}

                    {typeData.can_purge > 0 && policy.auto_purge_enabled && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handlePurge(entityType)}
                        disabled={isPurging}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Purge ({typeData.can_purge})
                      </Button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Bulk Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button
              onClick={() => handleArchive()}
              disabled={isArchiving}
              variant="outline"
            >
              <Archive className="h-4 w-4 mr-2" />
              {isArchiving ? 'Archiving...' : 'Archive All Eligible'}
            </Button>

            <Button
              onClick={() => handlePurge()}
              disabled={isPurging}
              variant="destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isPurging ? 'Purging...' : 'Purge All Eligible'}
            </Button>

            <Button onClick={fetchRetentionStatus} variant="outline">
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Retention Policy Configuration Form
function RetentionPolicyForm({ 
  entityType, 
  policy, 
  onSave 
}: { 
  entityType: string
  policy: AuditLogRetentionPolicy
  onSave: (policy: AuditLogRetentionPolicy) => void 
}) {
  const [formData, setFormData] = useState(policy)

  const handleSave = () => {
    onSave(formData)
  }

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="retention_days">Retention Period (days)</Label>
        <Input
          id="retention_days"
          type="number"
          value={formData.retention_days}
          onChange={(e) => setFormData(prev => ({ ...prev, retention_days: parseInt(e.target.value) }))}
        />
      </div>

      <div>
        <Label htmlFor="archive_after_days">Archive After (days)</Label>
        <Input
          id="archive_after_days"
          type="number"
          value={formData.archive_after_days}
          onChange={(e) => setFormData(prev => ({ ...prev, archive_after_days: parseInt(e.target.value) }))}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="auto_purge"
          checked={formData.auto_purge_enabled}
          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, auto_purge_enabled: checked }))}
        />
        <Label htmlFor="auto_purge">Enable Auto Purge</Label>
      </div>

      <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
        <Info className="h-4 w-4 text-blue-500" />
        <p className="text-sm text-blue-700">
          Changes will apply to future retention operations. Existing logs are not affected immediately.
        </p>
      </div>

      <Button onClick={handleSave} className="w-full">
        Save Policy
      </Button>
    </div>
  )
}
