import { useState, useEffect, useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import type { StatementData, StatementFilters, StatementTransaction } from '@/types/statements'
import type { Customer, Vendor } from '@/types/database'

export function useStatementData(filters: StatementFilters | null) {
  const { profile } = useAuth()
  const [data, setData] = useState<StatementData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const calculateOpeningBalance = useCallback(async (filters: StatementFilters): Promise<number> => {
    let balance = 0

    if (filters.entity_type === 'customer') {
      // For customers: invoices are debits, payments are credits
      const { data: invoices } = await supabase
        .from('invoices')
        .select('total_amount')
        .eq('customer_id', filters.entity_id)
        .eq('org_id', profile!.org_id)
        .lt('date_issued', filters.start_date)

      const { data: payments } = await supabase
        .from('payments')
        .select('amount')
        .eq('payee_id', filters.entity_id)
        .eq('payee_type', 'customer')
        .eq('org_id', profile!.org_id)
        .lt('payment_date', filters.start_date)

      const totalInvoiced = invoices?.reduce((sum, inv) => sum + inv.total_amount, 0) || 0
      const totalPaid = payments?.reduce((sum, pay) => sum + pay.amount, 0) || 0
      balance = totalInvoiced - totalPaid
    } else {
      // For vendors: bills are credits, payments are debits
      const { data: bills } = await supabase
        .from('bills')
        .select('total_amount')
        .eq('vendor_id', filters.entity_id)
        .eq('org_id', profile!.org_id)
        .lt('date_issued', filters.start_date)

      const { data: payments } = await supabase
        .from('payments')
        .select('amount')
        .eq('payee_id', filters.entity_id)
        .eq('payee_type', 'vendor')
        .eq('org_id', profile!.org_id)
        .lt('payment_date', filters.start_date)

      const totalBilled = bills?.reduce((sum, bill) => sum + bill.total_amount, 0) || 0
      const totalPaid = payments?.reduce((sum, pay) => sum + pay.amount, 0) || 0
      balance = totalPaid - totalBilled
    }

    return balance
  }, [profile])

  const fetchTransactions = useCallback(async (filters: StatementFilters): Promise<StatementTransaction[]> => {
    const transactions: StatementTransaction[] = []

    if (filters.entity_type === 'customer') {
      // Fetch invoices
      const { data: invoices } = await supabase
        .from('invoices')
        .select('*')
        .eq('customer_id', filters.entity_id)
        .eq('org_id', profile!.org_id)
        .gte('date_issued', filters.start_date)
        .lte('date_issued', filters.end_date)
        .order('date_issued')

      invoices?.forEach(invoice => {
        if (filters.include_draft || invoice.status !== 'draft') {
          transactions.push({
            id: `invoice-${invoice.id}`,
            date: invoice.date_issued,
            type: 'invoice',
            reference: invoice.invoice_number,
            description: `Invoice ${invoice.invoice_number}`,
            debit: invoice.total_amount,
            credit: 0,
            balance: 0, // Will be calculated later
            document_id: invoice.id,
            status: invoice.status
          })
        }
      })

      // Fetch payments
      const { data: payments } = await supabase
        .from('payments')
        .select('*')
        .eq('payee_id', filters.entity_id)
        .eq('payee_type', 'customer')
        .eq('org_id', profile!.org_id)
        .gte('payment_date', filters.start_date)
        .lte('payment_date', filters.end_date)
        .order('payment_date')

      payments?.forEach(payment => {
        transactions.push({
          id: `payment-${payment.id}`,
          date: payment.payment_date,
          type: 'payment',
          reference: payment.transaction_id || `PAY-${payment.id.slice(0, 8)}`,
          description: `Payment received`,
          debit: 0,
          credit: payment.amount,
          balance: 0, // Will be calculated later
          document_id: payment.id,
          status: payment.status
        })
      })
    } else {
      // Fetch bills for vendors
      const { data: bills } = await supabase
        .from('bills')
        .select('*')
        .eq('vendor_id', filters.entity_id)
        .eq('org_id', profile!.org_id)
        .gte('date_issued', filters.start_date)
        .lte('date_issued', filters.end_date)
        .order('date_issued')

      bills?.forEach(bill => {
        if (filters.include_draft || bill.status !== 'draft') {
          transactions.push({
            id: `bill-${bill.id}`,
            date: bill.date_issued,
            type: 'bill',
            reference: bill.bill_number,
            description: `Bill ${bill.bill_number}`,
            debit: 0,
            credit: bill.total_amount,
            balance: 0, // Will be calculated later
            document_id: bill.id,
            status: bill.status
          })
        }
      })

      // Fetch payments to vendors
      const { data: payments } = await supabase
        .from('payments')
        .select('*')
        .eq('payee_id', filters.entity_id)
        .eq('payee_type', 'vendor')
        .eq('org_id', profile!.org_id)
        .gte('payment_date', filters.start_date)
        .lte('payment_date', filters.end_date)
        .order('payment_date')

      payments?.forEach(payment => {
        transactions.push({
          id: `payment-${payment.id}`,
          date: payment.payment_date,
          type: 'payment',
          reference: payment.transaction_id || `PAY-${payment.id.slice(0, 8)}`,
          description: `Payment made`,
          debit: payment.amount,
          credit: 0,
          balance: 0, // Will be calculated later
          document_id: payment.id,
          status: payment.status
        })
      })
    }

    // Sort all transactions by date
    return transactions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }, [profile])

  const calculateSummary = useCallback((transactions: StatementTransaction[], entityType: 'customer' | 'vendor', closingBalance: number) => {
    const summary = {
      total_payments: 0,
      total_outstanding: 0,
      ...(entityType === 'customer' ? { total_invoiced: 0 } : { total_billed: 0 })
    }

    transactions.forEach(transaction => {
      if (transaction.type === 'payment') {
        summary.total_payments += entityType === 'customer' ? transaction.credit : transaction.debit
      } else if (transaction.type === 'invoice') {
        summary.total_invoiced = (summary.total_invoiced || 0) + transaction.debit
      } else if (transaction.type === 'bill') {
        summary.total_billed = (summary.total_billed || 0) + transaction.credit
      }
    })

    // Calculate outstanding amount from the closing balance
    summary.total_outstanding = closingBalance

    return summary
  }, [])

  const calculateAging = useCallback(async (customerId: string) => {
    const today = new Date()
    const aging = {
      current: 0,
      days_30: 0,
      days_60: 0,
      days_90: 0,
      over_90: 0
    }

    const { data: invoices } = await supabase
      .from('invoices')
      .select('total_amount, amount_due, due_date, status')
      .eq('customer_id', customerId)
      .eq('org_id', profile!.org_id)
      .neq('status', 'paid')

    invoices?.forEach(invoice => {
      const dueDate = new Date(invoice.due_date)
      const daysPastDue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))
      const amount = invoice.amount_due || invoice.total_amount

      if (daysPastDue <= 0) {
        aging.current += amount
      } else if (daysPastDue <= 30) {
        aging.days_30 += amount
      } else if (daysPastDue <= 60) {
        aging.days_60 += amount
      } else if (daysPastDue <= 90) {
        aging.days_90 += amount
      } else {
        aging.over_90 += amount
      }
    })

    return aging
  }, [profile])

  useEffect(() => {
    if (!filters || !profile?.org_id) {
      setLoading(false)
      return
    }

    const fetchStatementData = async () => {
      setLoading(true)
      setError(null)

      try {
        // Fetch entity details
        const entityTable = filters.entity_type === 'customer' ? 'customers' : 'vendors'
        const { data: entity, error: entityError } = await supabase
          .from(entityTable)
          .select('*')
          .eq('id', filters.entity_id)
          .eq('org_id', profile.org_id)
          .single()

        if (entityError) throw entityError

        // Calculate opening balance (transactions before start date)
        const openingBalance = await calculateOpeningBalance(filters)

        // Fetch transactions within the period
        const transactions = await fetchTransactions(filters)

        // Calculate running balances
        let runningBalance = openingBalance
        const transactionsWithBalance = transactions.map(transaction => {
          if (filters.entity_type === 'customer') {
            runningBalance += transaction.debit - transaction.credit
          } else {
            runningBalance += transaction.credit - transaction.debit
          }
          return {
            ...transaction,
            balance: runningBalance
          }
        })

        // Calculate summary
        const summary = calculateSummary(transactionsWithBalance, filters.entity_type, runningBalance)

        // Calculate aging (for customers only)
        const aging = filters.entity_type === 'customer'
          ? await calculateAging(filters.entity_id)
          : undefined

        const statementData: StatementData = {
          entity: entity as Customer | Vendor,
          entity_type: filters.entity_type,
          period_start: filters.start_date,
          period_end: filters.end_date,
          opening_balance: openingBalance,
          closing_balance: runningBalance,
          transactions: transactionsWithBalance,
          summary,
          aging
        }

        setData(statementData)
      } catch (err) {
        console.error('Error fetching statement data:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch statement data')
      } finally {
        setLoading(false)
      }
    }

    fetchStatementData()
  }, [filters, profile?.org_id, calculateAging, calculateOpeningBalance, calculateSummary, fetchTransactions])

  const refetch = useCallback(() => {
    if (!filters || !profile?.org_id) return
    // Trigger re-fetch by updating a dependency
    setData(null)
  }, [filters, profile?.org_id])

  return {
    data,
    loading,
    error,
    refetch
  }
}
