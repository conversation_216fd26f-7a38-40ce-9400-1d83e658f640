
import { useState } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { useVendors, useCreateVendor, useUpdateVendor, useDeleteVendor } from '@/hooks/queries'
import { supabase } from '@/lib/supabase'
import { Vendor } from '@/types/extended-database'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, Edit, Trash2, Search, FileText } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { VendorDetailsModal } from '@/components/vendors/VendorDetailsModal'
import { VendorStatementDialog } from '@/components/statements/VendorStatementDialog'
import { LoadingPage } from '@/components/ui/loading'
import {
  validatePhoneNumber,
  validateTinNumber,
  validateEmail,
  formatPhoneNumber,
  getPhoneNumberHelper,
  getTinNumberHelper
} from '@/lib/validators'

const initialFormData = {
  name: '',
  email: '',
  phone: '',
  address: '',
  tin_number: '',
  notes: '',
  payment_terms: 30,
}

export const Vendors = () => {
  const { profile, loading: authLoading } = useAuth()
  const { data: vendors = [], isLoading: loading } = useVendors()
  const createVendor = useCreateVendor()
  const updateVendor = useUpdateVendor()
  const deleteVendor = useDeleteVendor()
  const [searchTerm, setSearchTerm] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingVendor, setEditingVendor] = useState<Vendor | null>(null)
  const [isStatementDialogOpen, setIsStatementDialogOpen] = useState(false)
  const [statementVendor, setStatementVendor] = useState<Vendor | null>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null)
  const [formData, setFormData] = useState(initialFormData)
  const [submitting, setSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{
    phone?: string
    email?: string
    tin_number?: string
  }>({})

  // Handle phone number input with auto-formatting
  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value)
    setFormData({ ...formData, phone: formatted })

    // Clear validation error when user starts typing
    if (validationErrors.phone) {
      setValidationErrors({ ...validationErrors, phone: undefined })
    }
  }

  // Handle email input with validation
  const handleEmailChange = (value: string) => {
    setFormData({ ...formData, email: value })

    // Clear validation error when user starts typing
    if (validationErrors.email) {
      setValidationErrors({ ...validationErrors, email: undefined })
    }
  }

  // Handle TIN input with validation
  const handleTinChange = (value: string) => {
    setFormData({ ...formData, tin_number: value })

    // Clear validation error when user starts typing
    if (validationErrors.tin_number) {
      setValidationErrors({ ...validationErrors, tin_number: undefined })
    }
  }

  // React Query handles data fetching automatically

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    // Validate all fields
    const phoneValidation = validatePhoneNumber(formData.phone, false) // Phone is optional for vendors
    const emailValidation = validateEmail(formData.email)
    const tinValidation = validateTinNumber(formData.tin_number)

    const errors: { phone?: string; email?: string; tin_number?: string } = {}

    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.message
    }

    if (!emailValidation.isValid) {
      errors.email = emailValidation.message
    }

    if (!tinValidation.isValid) {
      errors.tin_number = tinValidation.message
    }

    // If there are validation errors, show them and return
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors)
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors below and try again',
        variant: 'destructive',
      })
      setSubmitting(false)
      return
    }

    try {
      const vendorData = {
        ...formData,
        phone: phoneValidation.formatted || formData.phone || null,
        tin_number: tinValidation.formatted || formData.tin_number || null,
        payment_terms: Number(formData.payment_terms),
        org_id: profile?.org_id,
      }

      if (editingVendor) {
        await updateVendor.mutateAsync({
          vendorId: editingVendor.id,
          vendorData: {
            name: formData.name,
            email: formData.email || null,
            phone: phoneValidation.formatted || formData.phone || null,
            address: formData.address || null,
            tin_number: tinValidation.formatted || formData.tin_number || null,
            notes: formData.notes || null,
            payment_terms: Number(formData.payment_terms),
          }
        })
      } else {
        await createVendor.mutateAsync({
          name: formData.name,
          email: formData.email || null,
          phone: phoneValidation.formatted || formData.phone || null,
          address: formData.address || null,
          tin_number: tinValidation.formatted || formData.tin_number || null,
          notes: formData.notes || null,
          payment_terms: Number(formData.payment_terms),
        })
      }

      setIsDialogOpen(false)
      setEditingVendor(null)
      setFormData(initialFormData)
      setValidationErrors({})
    } catch (error) {
      console.error('Error saving vendor:', error)
      toast({
        title: "Error",
        description: "Failed to save vendor",
        variant: "destructive",
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleEdit = (vendor: Vendor) => {
    setEditingVendor(vendor)
    setFormData({
      name: vendor.name,
      email: vendor.email || '',
      phone: vendor.phone || '',
      address: vendor.address || '',
      tin_number: vendor.tin_number || '',
      notes: vendor.notes || '',
      payment_terms: vendor.payment_terms || 30,
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (vendor: Vendor) => {
    if (!confirm(`Are you sure you want to delete the vendor "${vendor.name}"? This action cannot be undone.`)) return

    try {
      await deleteVendor.mutateAsync(vendor.id)
    } catch (error) {
      console.error('Error deleting vendor:', error)
      // Error handling is done by the React Query hook
    }
  }

  const openCreateDialog = () => {
    setEditingVendor(null)
    setFormData(initialFormData)
    setValidationErrors({})
    setIsDialogOpen(true)
  }

  const handleViewStatement = (vendor: Vendor) => {
    setStatementVendor(vendor)
    setIsStatementDialogOpen(true)
  }

  const handleVendorClick = (vendor: Vendor) => {
    setSelectedVendor(vendor)
    setIsDetailsModalOpen(true)
  }

  const filteredVendors = vendors.filter(vendor =>
    vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.phone?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return <LoadingPage text="Loading vendors..." fullScreen={false} />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Vendors</h1>
          <p className="text-gray-600">Manage your vendors and suppliers</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search vendors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 w-64"
            />
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Add Vendor
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingVendor ? 'Edit Vendor' : 'Add New Vendor'}
            </DialogTitle>
          </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="name">Vendor Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>

              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleEmailChange(e.target.value)}
                  placeholder="<EMAIL>"
                  className={validationErrors.email ? 'border-red-500' : ''}
                />
                {validationErrors.email && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.email}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Optional. Vendor's email address for purchase orders and communication
                </p>
              </div>

              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handlePhoneChange(e.target.value)}
                  placeholder="777123456"
                  className={validationErrors.phone ? 'border-red-500' : ''}
                />
                {validationErrors.phone && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.phone}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  {getPhoneNumberHelper()}
                </p>
              </div>

              <div>
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  placeholder="Enter vendor address"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="tin_number">TIN Number</Label>
                <Input
                  id="tin_number"
                  value={formData.tin_number}
                  onChange={(e) => handleTinChange(e.target.value)}
                  placeholder="1000123456"
                  className={validationErrors.tin_number ? 'border-red-500' : ''}
                />
                {validationErrors.tin_number && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.tin_number}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  {getTinNumberHelper()}
                </p>
              </div>

              <div>
                <Label htmlFor="payment_terms">Payment Terms (days)</Label>
                <Input
                  id="payment_terms"
                  type="number"
                  value={formData.payment_terms}
                  onChange={(e) => setFormData({ ...formData, payment_terms: Number(e.target.value) })}
                  min="0"
                />
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  placeholder="Add any additional notes"
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? 'Saving...' : editingVendor ? 'Update' : 'Create'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

      {statementVendor && (
        <VendorStatementDialog
          entity={statementVendor}
          open={isStatementDialogOpen}
          onOpenChange={setIsStatementDialogOpen}
        />
      )}
      
      <Card>
        <CardHeader>
          <CardTitle>Vendors List</CardTitle>
          <CardDescription>
            {vendors.length} vendor{vendors.length !== 1 ? 's' : ''} total
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Payment Terms</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVendors.map((vendor) => (
                <TableRow
                  key={vendor.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => handleVendorClick(vendor)}
                >
                  <TableCell className="font-medium">{vendor.name}</TableCell>
                  <TableCell>{vendor.email || '-'}</TableCell>
                  <TableCell>{vendor.phone || '-'}</TableCell>
                  <TableCell>{vendor.payment_terms} days</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleViewStatement(vendor)
                        }}
                        title="View Statement"
                      >
                        <FileText className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEdit(vendor)
                        }}
                        title="Edit Vendor"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(vendor)
                        }}
                        title="Delete Vendor"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
              {filteredVendors.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    {searchTerm ? 'No vendors found matching your search.' : 'No vendors yet. Add your first vendor!'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Vendor Details Modal */}
      <VendorDetailsModal
        vendor={selectedVendor}
        open={isDetailsModalOpen}
        onOpenChange={setIsDetailsModalOpen}
      />

      {/* Vendor Statement Dialog */}
      {statementVendor && (
        <VendorStatementDialog
          entity={statementVendor}
          open={isStatementDialogOpen}
          onOpenChange={setIsStatementDialogOpen}
        />
      )}
    </div>
  )
}
