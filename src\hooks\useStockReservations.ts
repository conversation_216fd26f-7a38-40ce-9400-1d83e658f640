import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'
import { queryKeys } from '@/lib/queryKeys'

export type ReservationStatus = 'active' | 'fulfilled' | 'cancelled' | 'expired'
export type ReservationType = 'invoice' | 'quote' | 'manual'

export interface StockReservation {
  id: string
  org_id: string
  product_id: string
  location_id: string
  quantity_reserved: number
  reservation_type: ReservationType
  reference_type: string
  reference_id: string
  reference_number: string | null
  status: ReservationStatus
  expires_at: string | null
  notes: string | null
  created_at: string
  updated_at: string
  created_by: string | null
}

export interface StockReservationWithDetails extends StockReservation {
  product: {
    id: string
    name: string
    sku: string
  }
  location: {
    id: string
    name: string
  }
}

export interface CreateReservationData {
  product_id: string
  location_id?: string
  quantity_reserved: number
  reservation_type: ReservationType
  reference_type: string
  reference_id: string
  reference_number?: string
  expires_at?: string
  notes?: string
}

export interface ReservationSummary {
  total_reservations: number
  total_quantity_reserved: number
  active_reservations: number
  expired_reservations: number
  by_product: Array<{
    product_id: string
    product_name: string
    total_reserved: number
    active_reserved: number
  }>
}

/**
 * Hook for managing stock reservations
 */
export function useStockReservations() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  // Get all stock reservations for the organization
  const reservationsQuery = useQuery({
    queryKey: ['stock-reservations', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('stock_reservations')
        .select(`
          *,
          product:products(id, name, sku),
          location:inventory_locations(id, name)
        `)
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as StockReservationWithDetails[]
    },
    enabled: !!profile?.org_id,
  })

  // Get reservations for a specific product
  const getProductReservations = (productId: string) => {
    return useQuery({
      queryKey: ['product-reservations', profile?.org_id, productId],
      queryFn: async () => {
        if (!profile?.org_id) throw new Error('No organization ID')

        const { data, error } = await supabase
          .from('stock_reservations')
          .select(`
            *,
            product:products(id, name, sku),
            location:inventory_locations(id, name)
          `)
          .eq('org_id', profile.org_id)
          .eq('product_id', productId)
          .eq('status', 'active')
          .order('created_at', { ascending: false })

        if (error) throw error
        return data as StockReservationWithDetails[]
      },
      enabled: !!profile?.org_id && !!productId,
    })
  }

  // Get reservations for a specific reference (e.g., invoice)
  const getReferenceReservations = (referenceType: string, referenceId: string) => {
    return useQuery({
      queryKey: ['reference-reservations', profile?.org_id, referenceType, referenceId],
      queryFn: async () => {
        if (!profile?.org_id) throw new Error('No organization ID')

        const { data, error } = await supabase
          .from('stock_reservations')
          .select(`
            *,
            product:products(id, name, sku),
            location:inventory_locations(id, name)
          `)
          .eq('org_id', profile.org_id)
          .eq('reference_type', referenceType)
          .eq('reference_id', referenceId)
          .order('created_at', { ascending: false })

        if (error) throw error
        return data as StockReservationWithDetails[]
      },
      enabled: !!profile?.org_id && !!referenceType && !!referenceId,
    })
  }

  // Get reservation summary
  const reservationSummaryQuery = useQuery({
    queryKey: ['reservation-summary', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('stock_reservations')
        .select(`
          id,
          quantity_reserved,
          status,
          product_id,
          product:products(name)
        `)
        .eq('org_id', profile.org_id)

      if (error) throw error

      const summary: ReservationSummary = {
        total_reservations: data.length,
        total_quantity_reserved: data.reduce((sum, r) => sum + r.quantity_reserved, 0),
        active_reservations: data.filter(r => r.status === 'active').length,
        expired_reservations: data.filter(r => r.status === 'expired').length,
        by_product: []
      }

      // Group by product
      const productGroups = data.reduce((groups, reservation) => {
        const key = reservation.product_id
        if (!groups[key]) {
          groups[key] = {
            product_id: reservation.product_id,
            product_name: reservation.product?.name || 'Unknown',
            reservations: []
          }
        }
        groups[key].reservations.push(reservation)
        return groups
      }, {} as Record<string, any>)

      summary.by_product = Object.values(productGroups).map((group: any) => ({
        product_id: group.product_id,
        product_name: group.product_name,
        total_reserved: group.reservations.reduce((sum: number, r: any) => sum + r.quantity_reserved, 0),
        active_reserved: group.reservations
          .filter((r: any) => r.status === 'active')
          .reduce((sum: number, r: any) => sum + r.quantity_reserved, 0)
      }))

      return summary
    },
    enabled: !!profile?.org_id,
  })

  // Create manual reservation
  const createReservationMutation = useMutation({
    mutationFn: async (reservationData: CreateReservationData) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      // Get default location if not provided
      let locationId = reservationData.location_id
      if (!locationId) {
        const { data: defaultLocation } = await supabase
          .from('inventory_locations')
          .select('id')
          .eq('org_id', profile.org_id)
          .eq('is_default', true)
          .eq('is_active', true)
          .single()

        if (!defaultLocation) {
          throw new Error('No default location found')
        }
        locationId = defaultLocation.id
      }

      // Check available quantity
      const { data: stockLevel } = await supabase
        .from('stock_levels')
        .select('quantity_on_hand, quantity_reserved')
        .eq('org_id', profile.org_id)
        .eq('product_id', reservationData.product_id)
        .eq('location_id', locationId)
        .single()

      if (!stockLevel) {
        throw new Error('Product not found in stock')
      }

      const availableQuantity = stockLevel.quantity_on_hand - (stockLevel.quantity_reserved || 0)
      if (availableQuantity < reservationData.quantity_reserved) {
        throw new Error(`Insufficient stock. Available: ${availableQuantity}`)
      }

      // Create reservation
      const { data, error } = await supabase
        .from('stock_reservations')
        .insert({
          ...reservationData,
          org_id: profile.org_id,
          location_id: locationId,
          created_by: profile.id
        })
        .select()
        .single()

      if (error) throw error

      // Update stock level
      const { error: stockError } = await supabase
        .from('stock_levels')
        .update({
          quantity_reserved: (stockLevel.quantity_reserved || 0) + reservationData.quantity_reserved,
          last_updated: new Date().toISOString()
        })
        .eq('org_id', profile.org_id)
        .eq('product_id', reservationData.product_id)
        .eq('location_id', locationId)

      if (stockError) throw stockError

      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-reservations', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: ['reservation-summary', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      toast.success('Stock reservation created successfully')
    },
    onError: (error) => {
      console.error('Failed to create stock reservation:', error)
      toast.error(`Failed to create stock reservation: ${error.message}`)
    }
  })

  // Release reservation
  const releaseReservationMutation = useMutation({
    mutationFn: async (reservationId: string) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      // Get reservation details
      const { data: reservation, error: getError } = await supabase
        .from('stock_reservations')
        .select('*')
        .eq('id', reservationId)
        .eq('org_id', profile.org_id)
        .single()

      if (getError) throw getError

      // Update reservation status
      const { error: updateError } = await supabase
        .from('stock_reservations')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', reservationId)

      if (updateError) throw updateError

      // Update stock level
      const { error: stockError } = await supabase
        .from('stock_levels')
        .update({
          quantity_reserved: supabase.sql`GREATEST(0, quantity_reserved - ${reservation.quantity_reserved})`,
          last_updated: new Date().toISOString()
        })
        .eq('org_id', profile.org_id)
        .eq('product_id', reservation.product_id)
        .eq('location_id', reservation.location_id)

      if (stockError) throw stockError
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-reservations', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: ['reservation-summary', profile?.org_id] })
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      toast.success('Stock reservation released successfully')
    },
    onError: (error) => {
      console.error('Failed to release stock reservation:', error)
      toast.error('Failed to release stock reservation')
    }
  })

  return {
    // Queries
    reservations: reservationsQuery.data || [],
    reservationSummary: reservationSummaryQuery.data,
    
    // Query functions
    getProductReservations,
    getReferenceReservations,
    
    // Loading states
    isLoadingReservations: reservationsQuery.isLoading,
    isLoadingSummary: reservationSummaryQuery.isLoading,
    
    // Mutations
    createReservation: createReservationMutation.mutateAsync,
    releaseReservation: releaseReservationMutation.mutateAsync,
    
    // Mutation states
    isCreatingReservation: createReservationMutation.isPending,
    isReleasingReservation: releaseReservationMutation.isPending,
    
    // Refetch functions
    refetchReservations: reservationsQuery.refetch,
    refetchSummary: reservationSummaryQuery.refetch
  }
}
