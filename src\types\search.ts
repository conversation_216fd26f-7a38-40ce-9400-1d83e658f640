export interface SearchResult {
  id: string
  type: 'customer' | 'vendor' | 'invoice' | 'bill' | 'payment'
  title: string
  subtitle?: string
  description?: string
  metadata?: {
    status?: string
    amount?: number
    date?: string
    currency?: string
  }
  href: string
}

export interface SearchFilters {
  types?: Array<'customer' | 'vendor' | 'invoice' | 'bill' | 'payment'>
  status?: string[]
  dateRange?: {
    start: string
    end: string
  }
}

export interface RecentSearch {
  id: string
  query: string
  timestamp: number
  resultCount: number
}

export interface SearchState {
  query: string
  results: SearchResult[]
  recentSearches: RecentSearch[]
  loading: boolean
  error: string | null
  selectedIndex: number
  filters: SearchFilters
}

export interface SearchHookReturn {
  searchState: SearchState
  setQuery: (query: string) => void
  setFilters: (filters: SearchFilters) => void
  selectResult: (index: number) => void
  navigateToResult: (result: SearchResult) => void
  clearRecentSearches: () => void
  removeRecentSearch: (id: string) => void
}
