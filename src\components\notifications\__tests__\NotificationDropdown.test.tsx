/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { NotificationDropdown } from '../NotificationDropdown'
import { useNotifications, useNotificationCount } from '@/hooks/queries/useNotifications'
import { useAuth } from '@/hooks/useAuthHook'

// Mock the hooks
jest.mock('@/hooks/queries/useNotifications')
jest.mock('@/hooks/useAuthHook')
jest.mock('@/hooks/useNotificationRealtime')

const mockUseNotifications = useNotifications as jest.MockedFunction<typeof useNotifications>
const mockUseNotificationCount = useNotificationCount as jest.MockedFunction<typeof useNotificationCount>
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

// Mock notification data
const mockNotifications = [
  {
    id: '1',
    type: 'payment_pending_approval',
    category: 'financial',
    priority: 'high',
    title: 'Payment Approval Required',
    message: 'Payment of $1,000 to Vendor ABC requires approval',
    is_read: false,
    is_archived: false,
    created_at: '2025-06-28T10:00:00Z',
    updated_at: '2025-06-28T10:00:00Z',
    user_id: 'user-1',
    org_id: 'org-1',
    entity_type: 'payment',
    entity_id: 'payment-1',
    read_at: null,
    data: { amount: '$1,000', payee: 'Vendor ABC' }
  },
  {
    id: '2',
    type: 'invoice_overdue',
    category: 'financial',
    priority: 'urgent',
    title: 'Invoice Overdue',
    message: 'Invoice #INV-001 is 5 days overdue',
    is_read: true,
    is_archived: false,
    created_at: '2025-06-27T10:00:00Z',
    updated_at: '2025-06-28T09:00:00Z',
    user_id: 'user-1',
    org_id: 'org-1',
    entity_type: 'invoice',
    entity_id: 'invoice-1',
    read_at: '2025-06-28T09:00:00Z',
    data: { invoice_number: 'INV-001', days_overdue: 5 }
  }
]

const mockProfile = {
  id: 'user-1',
  org_id: 'org-1',
  email: '<EMAIL>',
  full_name: 'Test User'
}

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('NotificationDropdown', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()
    
    // Default mock implementations
    mockUseAuth.mockReturnValue({
      profile: mockProfile,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    mockUseNotificationCount.mockReturnValue({
      data: 1,
      isLoading: false,
      error: null,
      refetch: jest.fn()
    })

    mockUseNotifications.mockReturnValue({
      data: mockNotifications,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
      hasNextPage: false,
      fetchNextPage: jest.fn(),
      isFetchingNextPage: false
    })
  })

  it('renders notification bell with count badge', () => {
    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    expect(screen.getByRole('button')).toBeInTheDocument()
    expect(screen.getByText('1')).toBeInTheDocument()
  })

  it('shows loading state when notifications are loading', () => {
    mockUseNotifications.mockReturnValue({
      data: [],
      isLoading: true,
      error: null,
      refetch: jest.fn(),
      hasNextPage: false,
      fetchNextPage: jest.fn(),
      isFetchingNextPage: false
    })

    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))
    expect(screen.getByText(/loading/i)).toBeInTheDocument()
  })

  it('displays notifications when dropdown is opened', async () => {
    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))

    await waitFor(() => {
      expect(screen.getByText('Payment Approval Required')).toBeInTheDocument()
      expect(screen.getByText('Invoice Overdue')).toBeInTheDocument()
    })
  })

  it('shows empty state when no notifications', () => {
    mockUseNotifications.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
      refetch: jest.fn(),
      hasNextPage: false,
      fetchNextPage: jest.fn(),
      isFetchingNextPage: false
    })

    mockUseNotificationCount.mockReturnValue({
      data: 0,
      isLoading: false,
      error: null,
      refetch: jest.fn()
    })

    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))
    expect(screen.getByText(/no notifications/i)).toBeInTheDocument()
  })

  it('handles error state gracefully', () => {
    mockUseNotifications.mockReturnValue({
      data: [],
      isLoading: false,
      error: new Error('Failed to load notifications'),
      refetch: jest.fn(),
      hasNextPage: false,
      fetchNextPage: jest.fn(),
      isFetchingNextPage: false
    })

    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))
    expect(screen.getByText(/error loading notifications/i)).toBeInTheDocument()
  })

  it('shows correct priority indicators', async () => {
    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))

    await waitFor(() => {
      // Check for priority badges or indicators
      const urgentNotification = screen.getByText('Invoice Overdue')
      const highNotification = screen.getByText('Payment Approval Required')
      
      expect(urgentNotification).toBeInTheDocument()
      expect(highNotification).toBeInTheDocument()
    })
  })

  it('filters unread notifications correctly', async () => {
    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))

    await waitFor(() => {
      // Should show unread count badge
      expect(screen.getByText('1')).toBeInTheDocument()
    })
  })

  it('handles mark all as read action', async () => {
    const mockRefetch = jest.fn()
    mockUseNotifications.mockReturnValue({
      data: mockNotifications,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
      hasNextPage: false,
      fetchNextPage: jest.fn(),
      isFetchingNextPage: false
    })

    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))

    await waitFor(() => {
      const markAllButton = screen.getByText(/mark all as read/i)
      expect(markAllButton).toBeInTheDocument()
    })
  })

  it('navigates to notification center', async () => {
    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))

    await waitFor(() => {
      const viewAllButton = screen.getByText(/view all/i)
      expect(viewAllButton).toBeInTheDocument()
    })
  })

  it('handles keyboard navigation', async () => {
    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    const button = screen.getByRole('button')
    
    // Test Enter key
    fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' })
    
    await waitFor(() => {
      expect(screen.getByText('Payment Approval Required')).toBeInTheDocument()
    })

    // Test Escape key
    fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' })
  })

  it('displays correct timestamps', async () => {
    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))

    await waitFor(() => {
      // Check for relative time display
      expect(screen.getByText(/ago/)).toBeInTheDocument()
    })
  })

  it('handles notification click events', async () => {
    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    fireEvent.click(screen.getByRole('button'))

    await waitFor(() => {
      const notification = screen.getByText('Payment Approval Required')
      fireEvent.click(notification)
      // Should handle navigation or mark as read
    })
  })
})

// Integration tests
describe('NotificationDropdown Integration', () => {
  it('integrates with real-time updates', async () => {
    // Mock real-time hook
    const mockRealtime = {
      isConnected: true,
      connectionError: null,
      reconnect: jest.fn(),
      isUsingPolling: false
    }

    jest.doMock('@/hooks/useNotificationRealtime', () => ({
      useNotificationRealtime: () => mockRealtime
    }))

    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    // Test real-time connection status
    expect(mockRealtime.isConnected).toBe(true)
  })

  it('handles authentication state changes', () => {
    // Test with no authenticated user
    mockUseAuth.mockReturnValue({
      profile: null,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    // Should handle unauthenticated state gracefully
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('handles organization switching', () => {
    const newProfile = {
      ...mockProfile,
      org_id: 'org-2'
    }

    mockUseAuth.mockReturnValue({
      profile: newProfile,
      user: null,
      loading: false,
      signOut: jest.fn()
    })

    render(
      <TestWrapper>
        <NotificationDropdown />
      </TestWrapper>
    )

    // Should refetch notifications for new organization
    expect(mockUseNotifications).toHaveBeenCalled()
  })
})
