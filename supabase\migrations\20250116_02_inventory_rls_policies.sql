-- =====================================================
-- INVENTORY MANAGEMENT - RLS POLICIES
-- =====================================================
-- Date: 2025-01-16
-- Purpose: Create Row Level Security policies for inventory tables
-- Dependencies: 20250116_01_inventory_management_foundation.sql
-- Rollback: See 20250116_02_inventory_rls_rollback.sql

-- =====================================================
-- ENABLE RLS ON ALL INVENTORY TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Enabling Row Level Security on inventory tables...';
END $$;

ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_transactions ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- PRODUCTS RLS POLICIES
-- =====================================================

CREATE POLICY "Users can view products in their organization" ON products
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can insert products in their organization" ON products
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can update products in their organization" ON products
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can delete products in their organization" ON products
    FOR DELETE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

-- =====================================================
-- PRODUCT CATEGORIES RLS POLICIES
-- =====================================================

CREATE POLICY "Users can view categories in their organization" ON product_categories
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can insert categories in their organization" ON product_categories
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can update categories in their organization" ON product_categories
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can delete categories in their organization" ON product_categories
    FOR DELETE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

-- =====================================================
-- INVENTORY LOCATIONS RLS POLICIES
-- =====================================================

CREATE POLICY "Users can view locations in their organization" ON inventory_locations
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can insert locations in their organization" ON inventory_locations
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can update locations in their organization" ON inventory_locations
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can delete locations in their organization" ON inventory_locations
    FOR DELETE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

-- =====================================================
-- STOCK LEVELS RLS POLICIES
-- =====================================================

CREATE POLICY "Users can view stock levels in their organization" ON stock_levels
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can insert stock levels in their organization" ON stock_levels
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can update stock levels in their organization" ON stock_levels
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can delete stock levels in their organization" ON stock_levels
    FOR DELETE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

-- =====================================================
-- INVENTORY TRANSACTIONS RLS POLICIES
-- =====================================================

CREATE POLICY "Users can view transactions in their organization" ON inventory_transactions
    FOR SELECT USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can insert transactions in their organization" ON inventory_transactions
    FOR INSERT WITH CHECK (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can update transactions in their organization" ON inventory_transactions
    FOR UPDATE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can delete transactions in their organization" ON inventory_transactions
    FOR DELETE USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE id = auth.uid() AND is_active = true
        )
    );

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    -- Count policies created
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE schemaname = 'public'
    AND tablename IN ('products', 'product_categories', 'inventory_locations', 'stock_levels', 'inventory_transactions');
    
    RAISE NOTICE '✅ Created % RLS policies for inventory tables', policy_count;
    RAISE NOTICE '🔒 Row Level Security enabled for all inventory tables';
END $$;
