
import { useState } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import type { WithholdingTaxRate } from '@/types/database'

interface WithholdingTaxRateFormProps {
  withholdingTaxRate?: WithholdingTaxRate | null
  onClose: () => void
  onSave: () => void
}

interface WithholdingTaxRateFormData {
  name: string
  rate_pct: string
  description: string
  ura_code: string
  is_active: boolean
}

export function WithholdingTaxRateForm({ withholdingTaxRate, onClose, onSave }: WithholdingTaxRateFormProps) {
  const { profile } = useAuth()
  const [formData, setFormData] = useState<WithholdingTaxRateFormData>({
    name: withholdingTaxRate?.name || '',
    rate_pct: withholdingTaxRate?.rate_pct?.toString() || '',
    description: withholdingTaxRate?.description || '',
    ura_code: withholdingTaxRate?.ura_code || '',
    is_active: withholdingTaxRate?.is_active ?? true
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    setIsLoading(true)
    try {
      const withholdingTaxRateData = {
        name: formData.name,
        rate_pct: Number(formData.rate_pct),
        description: formData.description || null,
        ura_code: formData.ura_code || null,
        is_active: formData.is_active,
        org_id: profile.org_id
      }

      if (withholdingTaxRate) {
        const { error } = await supabase
          .from('withholding_tax_rates')
          .update(withholdingTaxRateData)
          .eq('id', withholdingTaxRate.id)

        if (error) throw error
        toast.success('Withholding tax rate updated successfully')
      } else {
        const { error } = await supabase
          .from('withholding_tax_rates')
          .insert([withholdingTaxRateData])

        if (error) throw error
        toast.success('Withholding tax rate created successfully')
      }

      onSave()
    } catch (error) {
      console.error('Error saving withholding tax rate:', error)
      toast.error('Failed to save withholding tax rate')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {withholdingTaxRate ? 'Edit Withholding Tax Rate' : 'New Withholding Tax Rate'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., Professional Services"
              required
            />
          </div>

          <div>
            <Label htmlFor="rate_pct">Rate (%)</Label>
            <Input
              id="rate_pct"
              type="number"
              step="0.01"
              value={formData.rate_pct}
              onChange={(e) => setFormData({ ...formData, rate_pct: e.target.value })}
              placeholder="e.g., 5.00"
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Description of when this rate applies"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="ura_code">URA Code</Label>
            <Input
              id="ura_code"
              value={formData.ura_code}
              onChange={(e) => setFormData({ ...formData, ura_code: e.target.value })}
              placeholder="e.g., WHT001"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked === true })}
            />
            <Label htmlFor="is_active">Active</Label>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : withholdingTaxRate ? 'Update Rate' : 'Create Rate'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
