
import { Outlet } from 'react-router-dom';
import { Sidebar } from '@/components/Sidebar';
import { Navbar } from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { useState } from 'react';
import { Sheet, SheetContent } from '@/components/ui/sheet';

interface LayoutProps {
  children?: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar onMenuClick={() => setIsMobileMenuOpen(true)} />
      <div className="flex-1 flex pt-16">
        {/* Desktop Sidebar - Fixed with scroll */}
        <div className="hidden md:block fixed left-0 top-16 bottom-0 w-60 bg-sidebar-background border-r border-sidebar-border">
          <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-sidebar-border scrollbar-track-transparent">
            <Sidebar />
          </div>
        </div>
        
        {/* Mobile Sidebar */}
        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetContent side="left" className="p-0 w-72">
            <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-sidebar-border scrollbar-track-transparent">
              <Sidebar />
            </div>
          </SheetContent>
        </Sheet>

        {/* Main Content */}
        <div className="flex-1 flex flex-col md:ml-60">
          <main className="flex-1 p-6 overflow-y-auto">
            {children || <Outlet />}
          </main>
          <Footer />
        </div>
      </div>
    </div>
  );
}
