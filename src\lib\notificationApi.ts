/**
 * Notification Management API
 * Comprehensive API for notification CRUD operations, bulk operations, and statistics
 */

import { supabase } from '@/lib/supabase'
import type {
  Notification,
  NotificationPreference,
  CreateNotificationPayload,
  UpdateNotificationPayload,
  NotificationFilters,
  NotificationStats,
  NotificationWithMeta
} from '@/types/notifications'

// Type for error handling
type ErrorWithMessage = Error | { message: string } | string

// Helper function to extract error message
function getErrorMessage(error: ErrorWithMessage): string {
  if (typeof error === 'string') return error
  if (error && typeof error === 'object' && 'message' in error) return getErrorMessage(error)
  return 'Unknown error occurred'
}

// Define analytics types
type NotificationAnalytics = {
  total_notifications: number
  read_notifications: number
  unread_notifications: number
  notifications_by_type: Record<string, number>
  notifications_by_priority: Record<string, number>
  engagement_rate: number
  avg_read_time: number
}

type UserEngagement = {
  user_id: string
  user_email: string
  total_notifications: number
  read_notifications: number
  engagement_rate: number
  last_activity: string
}

type EmailAnalytics = {
  total_sent: number
  delivered: number
  opened: number
  clicked: number
  bounced: number
  delivery_rate: number
  open_rate: number
  click_rate: number
}

type PushAnalytics = {
  total_sent: number
  delivered: number
  opened: number
  clicked: number
  delivery_rate: number
  open_rate: number
  click_rate: number
}

// API Response types
export interface ApiResponse<T> {
  data: T | null
  error: string | null
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  count: number
  page: number
  pageSize: number
  totalPages: number
  hasMore: boolean
}

export interface BulkOperationResult {
  success: number
  failed: number
  errors: string[]
}

/**
 * Notification CRUD Operations
 */
export class NotificationAPI {
  /**
   * Get notifications with pagination and filtering
   */
  static async getNotifications(
    userId: string,
    orgId: string,
    filters?: NotificationFilters,
    page: number = 1,
    pageSize: number = 20
  ): Promise<ApiResponse<PaginatedResponse<NotificationWithMeta>>> {
    try {
      const offset = (page - 1) * pageSize

      let query = supabase
        .from('notifications')
        .select('*', { count: 'exact' })
        .or(`user_id.eq.${userId},and(user_id.is.null,org_id.eq.${orgId})`)
        .order('created_at', { ascending: false })
        .range(offset, offset + pageSize - 1)

      // Apply filters
      if (filters) {
        if (filters.category) {
          query = query.eq('category', filters.category)
        }
        if (filters.priority) {
          query = query.eq('priority', filters.priority)
        }
        if (filters.is_read !== undefined) {
          query = query.eq('is_read', filters.is_read)
        }
        if (filters.is_archived !== undefined) {
          query = query.eq('is_archived', filters.is_archived)
        }
        if (filters.entity_type) {
          query = query.eq('entity_type', filters.entity_type)
        }
        if (filters.date_from) {
          query = query.gte('created_at', filters.date_from)
        }
        if (filters.date_to) {
          query = query.lte('created_at', filters.date_to)
        }
      }

      const { data, error, count } = await query

      if (error) throw error

      const totalPages = Math.ceil((count || 0) / pageSize)
      const hasMore = page < totalPages

      // Add computed properties
      const enhancedData = (data || []).map(notification => ({
        ...notification,
        isExpired: notification.expires_at ? new Date(notification.expires_at) < new Date() : false,
        timeAgo: this.getTimeAgo(notification.created_at)
      }))

      return {
        data: {
          data: enhancedData,
          count: count || 0,
          page,
          pageSize,
          totalPages,
          hasMore
        },
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Get single notification by ID
   */
  static async getNotification(
    notificationId: string,
    userId: string,
    orgId: string
  ): Promise<ApiResponse<NotificationWithMeta>> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('id', notificationId)
        .or(`user_id.eq.${userId},and(user_id.is.null,org_id.eq.${orgId})`)
        .single()

      if (error) throw error

      const enhancedData = {
        ...data,
        isExpired: data.expires_at ? new Date(data.expires_at) < new Date() : false,
        timeAgo: this.getTimeAgo(data.created_at)
      }

      return {
        data: enhancedData,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Create new notification
   */
  static async createNotification(
    payload: CreateNotificationPayload
  ): Promise<ApiResponse<Notification>> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .insert(payload)
        .select()
        .single()

      if (error) throw error

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Update notification
   */
  static async updateNotification(
    notificationId: string,
    payload: UpdateNotificationPayload,
    userId: string,
    orgId: string
  ): Promise<ApiResponse<Notification>> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .update(payload)
        .eq('id', notificationId)
        .or(`user_id.eq.${userId},and(user_id.is.null,org_id.eq.${orgId})`)
        .select()
        .single()

      if (error) throw error

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Delete notification
   */
  static async deleteNotification(
    notificationId: string,
    userId: string
  ): Promise<ApiResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', userId) // Only allow deleting own notifications

      if (error) throw error

      return {
        data: true,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: false,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(
    notificationId: string,
    userId: string,
    orgId: string
  ): Promise<ApiResponse<Notification>> {
    return this.updateNotification(
      notificationId,
      { is_read: true, read_at: new Date().toISOString() },
      userId,
      orgId
    )
  }

  /**
   * Mark notification as unread
   */
  static async markAsUnread(
    notificationId: string,
    userId: string,
    orgId: string
  ): Promise<ApiResponse<Notification>> {
    return this.updateNotification(
      notificationId,
      { is_read: false, read_at: null },
      userId,
      orgId
    )
  }

  /**
   * Archive notification
   */
  static async archiveNotification(
    notificationId: string,
    userId: string,
    orgId: string
  ): Promise<ApiResponse<Notification>> {
    return this.updateNotification(
      notificationId,
      { is_archived: true },
      userId,
      orgId
    )
  }

  /**
   * Unarchive notification
   */
  static async unarchiveNotification(
    notificationId: string,
    userId: string,
    orgId: string
  ): Promise<ApiResponse<Notification>> {
    return this.updateNotification(
      notificationId,
      { is_archived: false },
      userId,
      orgId
    )
  }

  /**
   * Bulk mark as read
   */
  static async bulkMarkAsRead(
    notificationIds: string[],
    userId: string,
    orgId: string
  ): Promise<ApiResponse<BulkOperationResult>> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .in('id', notificationIds)
        .or(`user_id.eq.${userId},and(user_id.is.null,org_id.eq.${orgId})`)
        .select('id')

      if (error) throw error

      return {
        data: {
          success: data?.length || 0,
          failed: notificationIds.length - (data?.length || 0),
          errors: []
        },
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: {
          success: 0,
          failed: notificationIds.length,
          errors: [getErrorMessage(error)]
        },
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Bulk archive
   */
  static async bulkArchive(
    notificationIds: string[],
    userId: string,
    orgId: string
  ): Promise<ApiResponse<BulkOperationResult>> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .update({ is_archived: true })
        .in('id', notificationIds)
        .or(`user_id.eq.${userId},and(user_id.is.null,org_id.eq.${orgId})`)
        .select('id')

      if (error) throw error

      return {
        data: {
          success: data?.length || 0,
          failed: notificationIds.length - (data?.length || 0),
          errors: []
        },
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: {
          success: 0,
          failed: notificationIds.length,
          errors: [getErrorMessage(error)]
        },
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Get notification statistics
   */
  static async getNotificationStats(
    userId: string,
    orgId: string
  ): Promise<ApiResponse<NotificationStats>> {
    try {
      const { data, error } = await supabase.rpc('get_notification_stats', {
        user_id_param: userId,
        org_id_param: orgId
      })

      if (error) throw error

      return {
        data: data || {
          total: 0,
          unread: 0,
          by_category: {},
          by_priority: {}
        },
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Helper function to calculate time ago
   */
  private static getTimeAgo(dateString: string): string {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }
}

/**
 * Notification Preferences API
 */
export class NotificationPreferencesAPI {
  /**
   * Get user notification preferences
   */
  static async getPreferences(userId: string): Promise<ApiResponse<NotificationPreference[]>> {
    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .order('notification_type')

      if (error) throw error

      return {
        data: data || [],
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Update notification preference
   */
  static async updatePreference(
    userId: string,
    notificationType: string,
    preferences: Partial<NotificationPreference>
  ): Promise<ApiResponse<NotificationPreference>> {
    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: userId,
          notification_type: notificationType,
          ...preferences
        }, { onConflict: 'user_id,notification_type' })
        .select()
        .single()

      if (error) throw error

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Bulk update preferences
   */
  static async bulkUpdatePreferences(
    userId: string,
    preferences: Array<{ notification_type: string; enabled?: boolean; email_enabled?: boolean; in_app_enabled?: boolean }>
  ): Promise<ApiResponse<BulkOperationResult>> {
    try {
      const updates = preferences.map(pref => ({
        user_id: userId,
        ...pref
      }))

      const { data, error } = await supabase
        .from('notification_preferences')
        .upsert(updates, { onConflict: 'user_id,notification_type' })
        .select('id')

      if (error) throw error

      return {
        data: {
          success: data?.length || 0,
          failed: preferences.length - (data?.length || 0),
          errors: []
        },
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: {
          success: 0,
          failed: preferences.length,
          errors: [getErrorMessage(error)]
        },
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Reset preferences to defaults
   */
  static async resetToDefaults(userId: string): Promise<ApiResponse<boolean>> {
    try {
      // Delete all existing preferences (will fall back to defaults)
      const { error } = await supabase
        .from('notification_preferences')
        .delete()
        .eq('user_id', userId)

      if (error) throw error

      return {
        data: true,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: false,
        error: getErrorMessage(error),
        success: false
      }
    }
  }
}

/**
 * Notification Analytics API
 */
export class NotificationAnalyticsAPI {
  /**
   * Get detailed analytics for organization
   */
  static async getAnalytics(
    orgId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<NotificationAnalytics>> {
    try {
      const { data, error } = await supabase.rpc('get_notification_analytics', {
        org_id_param: orgId,
        start_date: startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: endDate || new Date().toISOString().split('T')[0]
      })

      if (error) throw error

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Get user engagement metrics
   */
  static async getUserEngagement(orgId: string): Promise<ApiResponse<UserEngagement[]>> {
    try {
      const { data, error } = await supabase.rpc('get_user_engagement_metrics', {
        org_id_param: orgId
      })

      if (error) throw error

      return {
        data: data || [],
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Get email delivery analytics
   */
  static async getEmailAnalytics(
    orgId: string,
    days: number = 30
  ): Promise<ApiResponse<EmailAnalytics>> {
    try {
      const { data, error } = await supabase.rpc('get_email_analytics', {
        org_id_param: orgId,
        start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: new Date().toISOString().split('T')[0]
      })

      if (error) throw error

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Get push notification analytics
   */
  static async getPushAnalytics(
    orgId: string,
    days: number = 30
  ): Promise<ApiResponse<PushAnalytics>> {
    try {
      const { data, error } = await supabase.rpc('get_push_analytics', {
        org_id_param: orgId,
        start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: new Date().toISOString().split('T')[0]
      })

      if (error) throw error

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Export notifications to CSV
   */
  static async exportNotifications(
    userId: string,
    orgId: string,
    filters?: NotificationFilters
  ): Promise<ApiResponse<string>> {
    try {
      // Get all notifications matching filters
      const response = await NotificationAPI.getNotifications(
        userId,
        orgId,
        filters,
        1,
        10000 // Large page size for export
      )

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch notifications')
      }

      // Convert to CSV
      const notifications = response.data.data
      const headers = ['ID', 'Type', 'Category', 'Priority', 'Title', 'Message', 'Created At', 'Read', 'Archived']
      const csvRows = [
        headers.join(','),
        ...notifications.map(n => [
          n.id,
          n.type,
          n.category,
          n.priority,
          `"${n.title.replace(/"/g, '""')}"`,
          `"${n.message.replace(/"/g, '""')}"`,
          n.created_at,
          n.is_read ? 'Yes' : 'No',
          n.is_archived ? 'Yes' : 'No'
        ].join(','))
      ]

      return {
        data: csvRows.join('\n'),
        error: null,
        success: true
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }

  /**
   * Export analytics report
   */
  static async exportAnalyticsReport(
    orgId: string,
    startDate: string,
    endDate: string,
    format: 'csv' | 'json' = 'csv'
  ): Promise<ApiResponse<string>> {
    try {
      // Get comprehensive analytics data
      const [analytics, emailAnalytics, pushAnalytics] = await Promise.all([
        this.getAnalytics(orgId, startDate, endDate),
        this.getEmailAnalytics(orgId, 30),
        this.getPushAnalytics(orgId, 30)
      ])

      const reportData = {
        generated_at: new Date().toISOString(),
        period: { start: startDate, end: endDate },
        overview: analytics.data,
        email: emailAnalytics.data,
        push: pushAnalytics.data
      }

      if (format === 'json') {
        return {
          data: JSON.stringify(reportData, null, 2),
          error: null,
          success: true
        }
      } else {
        // Convert to CSV format
        const csvRows = [
          ['Metric', 'Value', 'Percentage'],
          ['Total Notifications', reportData.overview?.total_sent || 0, ''],
          ['Delivered', reportData.overview?.delivered || 0, `${(reportData.overview?.delivery_rate || 0).toFixed(1)}%`],
          ['Opened', reportData.overview?.opened || 0, `${(reportData.overview?.open_rate || 0).toFixed(1)}%`],
          ['Clicked', reportData.overview?.clicked || 0, `${(reportData.overview?.click_rate || 0).toFixed(1)}%`],
          ['Failed', reportData.overview?.failed || 0, `${(reportData.overview?.failure_rate || 0).toFixed(1)}%`],
          [''],
          ['Email Analytics'],
          ['Email Delivery Rate', '', `${(reportData.email?.delivery_rate || 0).toFixed(1)}%`],
          ['Email Open Rate', '', `${(reportData.email?.open_rate || 0).toFixed(1)}%`],
          ['Email Click Rate', '', `${(reportData.email?.click_rate || 0).toFixed(1)}%`],
          [''],
          ['Push Analytics'],
          ['Push Subscriptions', reportData.push?.total_subscriptions || 0, ''],
          ['Push Delivery Rate', '', `${(reportData.push?.delivery_rate || 0).toFixed(1)}%`],
          ['Push Click Rate', '', `${(reportData.push?.click_rate || 0).toFixed(1)}%`]
        ]

        return {
          data: csvRows.map(row => row.join(',')).join('\n'),
          error: null,
          success: true
        }
      }
    } catch (error: ErrorWithMessage) {
      return {
        data: null,
        error: getErrorMessage(error),
        success: false
      }
    }
  }
}
