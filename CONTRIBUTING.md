# Contributing to Kaya Finance

Thank you for your interest in contributing to Kaya Finance! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

## 🚀 Getting Started

### Prerequisites

Before contributing, ensure you have:
- Node.js 18+ installed
- pnpm package manager
- Git configured with your GitHub account
- A Supabase account for testing

### Development Setup

1. **Fork the repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/your-username/kaya-finance.git
   cd kaya-finance
   ```

2. **Set up development environment**
   ```bash
   # Install dependencies
   pnpm install
   
   # Copy environment file
   cp .env.example .env.local
   
   # Configure your environment variables
   # See docs/development/setup.md for details
   ```

3. **Start development server**
   ```bash
   pnpm dev
   ```

4. **Run tests to ensure everything works**
   ```bash
   pnpm test
   pnpm lint
   pnpm type-check
   ```

## 📋 How to Contribute

### Reporting Bugs

Before creating a bug report:
1. Check if the issue already exists in [GitHub Issues](https://github.com/your-username/kaya-finance/issues)
2. Try to reproduce the issue with the latest version
3. Gather relevant information (browser, OS, steps to reproduce)

**Bug Report Template:**
```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. See error

**Expected behavior**
What you expected to happen.

**Screenshots**
If applicable, add screenshots.

**Environment:**
- OS: [e.g. Windows 10, macOS 12]
- Browser: [e.g. Chrome 96, Firefox 95]
- Version: [e.g. 1.2.3]
```

### Suggesting Features

We welcome feature suggestions! Please:
1. Check existing [feature requests](https://github.com/your-username/kaya-finance/issues?q=is%3Aissue+is%3Aopen+label%3Aenhancement)
2. Create a detailed feature request with:
   - Clear description of the feature
   - Use cases and benefits
   - Possible implementation approach
   - Any relevant mockups or examples

### Contributing Code

#### 1. Choose an Issue

- Look for issues labeled `good first issue` for beginners
- Check issues labeled `help wanted` for areas needing assistance
- Comment on the issue to indicate you're working on it

#### 2. Create a Branch

```bash
# Create and switch to a new branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/issue-description
```

#### 3. Make Changes

- Follow our [coding standards](docs/development/coding-standards.md)
- Write tests for new functionality
- Update documentation as needed
- Keep commits focused and atomic

#### 4. Test Your Changes

```bash
# Run all tests
pnpm test

# Run linting
pnpm lint

# Check types
pnpm type-check

# Test build
pnpm build
```

#### 5. Commit Your Changes

We use [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# Feature
git commit -m "feat: add customer search functionality"

# Bug fix
git commit -m "fix: resolve invoice calculation error"

# Documentation
git commit -m "docs: update API documentation"

# Refactor
git commit -m "refactor: improve payment processing logic"

# Test
git commit -m "test: add unit tests for invoice validation"
```

#### 6. Push and Create Pull Request

```bash
# Push your branch
git push origin feature/your-feature-name

# Create pull request on GitHub
```

## 📝 Pull Request Guidelines

### Pull Request Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] New tests added for new functionality
- [ ] Manual testing completed

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings introduced
```

### Review Process

1. **Automated Checks**: CI/CD pipeline runs automatically
2. **Code Review**: Team members review your code
3. **Testing**: Changes are tested in staging environment
4. **Approval**: At least one maintainer approval required
5. **Merge**: Changes are merged to main branch

### Review Criteria

- Code quality and style
- Test coverage
- Documentation updates
- Performance impact
- Security considerations
- Backward compatibility

## 🎨 Coding Standards

### TypeScript

- Use TypeScript for all new code
- Define proper types and interfaces
- Avoid `any` type when possible
- Use strict mode settings

```typescript
// Good
interface Customer {
  id: string
  name: string
  email: string
  createdAt: Date
}

// Avoid
const customer: any = { ... }
```

### React Components

- Use functional components with hooks
- Implement proper prop types
- Use meaningful component names
- Keep components focused and small

```tsx
// Good
interface CustomerCardProps {
  customer: Customer
  onEdit: (id: string) => void
}

export function CustomerCard({ customer, onEdit }: CustomerCardProps) {
  return (
    <div className="customer-card">
      <h3>{customer.name}</h3>
      <button onClick={() => onEdit(customer.id)}>
        Edit
      </button>
    </div>
  )
}
```

### Styling

- Use Tailwind CSS for styling
- Follow mobile-first approach
- Use semantic class names
- Maintain consistent spacing

```tsx
// Good
<div className="flex flex-col space-y-4 p-6 bg-white rounded-lg shadow-md">
  <h2 className="text-xl font-semibold text-gray-900">Title</h2>
  <p className="text-gray-600">Description</p>
</div>
```

### Testing

- Write tests for all new functionality
- Use descriptive test names
- Test both happy path and edge cases
- Mock external dependencies

```typescript
describe('CustomerService', () => {
  it('should create a new customer with valid data', async () => {
    const customerData = {
      name: 'John Doe',
      email: '<EMAIL>'
    }

    const result = await customerService.create(customerData)

    expect(result.success).toBe(true)
    expect(result.data).toMatchObject(customerData)
  })
})
```

## 📚 Documentation

### Code Documentation

- Add JSDoc comments for functions and classes
- Document complex logic and algorithms
- Include usage examples for utilities

```typescript
/**
 * Calculates the total amount for an invoice including taxes
 * @param lineItems - Array of invoice line items
 * @param taxRate - Tax rate as a percentage (e.g., 18 for 18%)
 * @returns Total amount including taxes
 */
export function calculateInvoiceTotal(
  lineItems: InvoiceLineItem[],
  taxRate: number
): number {
  // Implementation...
}
```

### User Documentation

- Update user guides for new features
- Include screenshots for UI changes
- Provide step-by-step instructions
- Keep documentation current

## 🐛 Debugging

### Common Issues

1. **Build Errors**
   - Check TypeScript errors
   - Verify import paths
   - Ensure all dependencies are installed

2. **Test Failures**
   - Check test environment setup
   - Verify mock configurations
   - Review test data

3. **Linting Errors**
   - Run `pnpm lint --fix` for auto-fixes
   - Check ESLint configuration
   - Review code style guidelines

### Getting Help

- Check [documentation](docs/README.md)
- Search [existing issues](https://github.com/your-username/kaya-finance/issues)
- Ask in [GitHub Discussions](https://github.com/your-username/kaya-finance/discussions)
- Contact maintainers

## 🏆 Recognition

Contributors are recognized in:
- README.md contributors section
- Release notes for significant contributions
- Annual contributor appreciation

## 📞 Contact

- **General Questions**: [GitHub Discussions](https://github.com/your-username/kaya-finance/discussions)
- **Bug Reports**: [GitHub Issues](https://github.com/your-username/kaya-finance/issues)
- **Security Issues**: <EMAIL>
- **Maintainers**: <EMAIL>

## 📄 License

By contributing to Kaya Finance, you agree that your contributions will be licensed under the same [MIT License](LICENSE) that covers the project.

---

Thank you for contributing to Kaya Finance! Your efforts help make financial management better for SMEs across Africa. 🚀
