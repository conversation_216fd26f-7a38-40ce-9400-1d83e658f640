import { useState, useMemo } from 'react'
import { useCustomers, useDeleteCustomer, useCanDeleteCustomer } from '@/hooks/queries'
import { Customer } from '@/types/extended-database'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, Edit, Search, FileText, Trash2 } from 'lucide-react'
import { CustomerDialog } from '@/components/customers/CustomerDialog'
import { CustomerDetailsModal } from '@/components/customers/CustomerDetailsModal'
import { CustomerStatementDialog } from '@/components/statements/CustomerStatementDialog'
import { DeleteCustomerDialog } from '@/components/customers/DeleteCustomerDialog'
import { LoadingPage } from '@/components/ui/loading'

export const Customers = () => {
  const deleteCustomer = useDeleteCustomer()
  const [searchTerm, setSearchTerm] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [isStatementDialogOpen, setIsStatementDialogOpen] = useState(false)
  const [statementCustomer, setStatementCustomer] = useState<Customer | null>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  // Use React Query hook with search filter
  const { data: customers = [], isLoading, error } = useCustomers(
    searchTerm ? { search: searchTerm } : undefined
  )

  // Filter customers locally for immediate feedback while typing
  const filteredCustomers = useMemo(() => {
    if (!searchTerm) return customers

    const searchLower = searchTerm.toLowerCase()
    return customers.filter(customer =>
      customer.name?.toLowerCase().includes(searchLower) ||
      customer.email?.toLowerCase().includes(searchLower) ||
      customer.phone?.toLowerCase().includes(searchLower)
    )
  }, [customers, searchTerm])

  const handleDialogSuccess = () => {
    setIsDialogOpen(false)
    setEditingCustomer(null)
    // React Query will automatically refetch and update the cache
  }

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer)
    setIsDialogOpen(true)
  }

  const handleDelete = (customer: Customer) => {
    setCustomerToDelete(customer)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!customerToDelete) return

    try {
      await deleteCustomer.mutateAsync(customerToDelete.id)
      setIsDeleteDialogOpen(false)
      setCustomerToDelete(null)
    } catch (error) {
      console.error('Error deleting customer:', error)
      // Error handling is done by the React Query hook
    }
  }

  const openCreateDialog = () => {
    setEditingCustomer(null)
    setIsDialogOpen(true)
  }

  const handleViewStatement = (customer: Customer) => {
    setStatementCustomer(customer)
    setIsStatementDialogOpen(true)
  }

  const handleCustomerClick = (customer: Customer) => {
    setSelectedCustomer(customer)
    setIsDetailsModalOpen(true)
  }

  if (isLoading) {
    return <LoadingPage text="Loading customers..." fullScreen={false} />
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-2">Failed to load customers</p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-600">Manage your customers and their information</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 w-64"
            />
          </div>
          <Button onClick={openCreateDialog}>
            <Plus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </div>
      </div>

      <CustomerDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSuccess={handleDialogSuccess}
        customer={editingCustomer}
      />

      {statementCustomer && (
        <CustomerStatementDialog
          entity={statementCustomer}
          open={isStatementDialogOpen}
          onOpenChange={setIsStatementDialogOpen}
        />
      )}

      <Card>
        <CardHeader>
          <CardTitle>Customers List</CardTitle>
          <CardDescription>
            {customers.length} customer{customers.length !== 1 ? 's' : ''} total
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Payment Terms</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCustomers.map((customer) => (
                <TableRow
                  key={customer.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => handleCustomerClick(customer)}
                >
                  <TableCell className="font-medium">{customer.name}</TableCell>
                  <TableCell>{customer.email || '-'}</TableCell>
                  <TableCell>{customer.phone || '-'}</TableCell>
                  <TableCell>{customer.payment_terms} days</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleViewStatement(customer)
                        }}
                        title="View Statement"
                      >
                        <FileText className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEdit(customer)
                        }}
                        title="Edit Customer"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(customer)
                        }}
                        title="Delete Customer"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
              {filteredCustomers.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    {searchTerm ? 'No customers found matching your search.' : 'No customers yet. Add your first customer!'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Customer Details Modal */}
      <CustomerDetailsModal
        customer={selectedCustomer}
        open={isDetailsModalOpen}
        onOpenChange={setIsDetailsModalOpen}
      />

      {/* Customer Statement Dialog */}
      {statementCustomer && (
        <CustomerStatementDialog
          entity={statementCustomer}
          open={isStatementDialogOpen}
          onOpenChange={setIsStatementDialogOpen}
        />
      )}

      {/* Customer Dialog */}
      <CustomerDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSuccess={handleDialogSuccess}
        customer={editingCustomer}
      />

      {/* Delete Customer Dialog */}
      <DeleteCustomerDialog
        customer={customerToDelete}
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={confirmDelete}
      />
    </div>
  )
}
