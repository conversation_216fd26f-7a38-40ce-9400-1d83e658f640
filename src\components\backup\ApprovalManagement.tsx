// Backup Approval Management Component
// Handles approval requests and workflow management

import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/useAuthHook'
import { BackupRBACManager, type ApprovalRequest } from '@/lib/backup-rbac'
import { formatDate } from '@/lib/utils'

interface ApprovalManagementProps {
  className?: string
}

export function ApprovalManagement({ className }: ApprovalManagementProps) {
  const { profile } = useAuth()
  const queryClient = useQueryClient()
  const [selectedApproval, setSelectedApproval] = useState<ApprovalRequest | null>(null)
  const [rejectionReason, setRejectionReason] = useState('')
  const [showRejectDialog, setShowRejectDialog] = useState(false)

  // Fetch pending approvals
  const { data: pendingApprovals, isLoading } = useQuery({
    queryKey: ['backup-approvals', profile?.org_id],
    queryFn: async () => {
      if (!profile?.id || !profile?.org_id) return []
      return await BackupRBACManager.getPendingApprovals(profile.id, profile.org_id)
    },
    enabled: !!profile?.id && !!profile?.org_id,
    refetchInterval: 30000 // Refresh every 30 seconds
  })

  // Approve mutation
  const approveMutation = useMutation({
    mutationFn: async (approvalId: string) => {
      if (!profile?.id) throw new Error('User not authenticated')
      return await BackupRBACManager.processApproval(approvalId, profile.id, 'approve')
    },
    onSuccess: () => {
      toast.success('Request approved successfully')
      queryClient.invalidateQueries({ queryKey: ['backup-approvals'] })
    },
    onError: (error) => {
      toast.error(`Failed to approve request: ${error.message}`)
    }
  })

  // Reject mutation
  const rejectMutation = useMutation({
    mutationFn: async ({ approvalId, reason }: { approvalId: string; reason: string }) => {
      if (!profile?.id) throw new Error('User not authenticated')
      return await BackupRBACManager.processApproval(approvalId, profile.id, 'reject', reason)
    },
    onSuccess: () => {
      toast.success('Request rejected')
      queryClient.invalidateQueries({ queryKey: ['backup-approvals'] })
      setShowRejectDialog(false)
      setRejectionReason('')
      setSelectedApproval(null)
    },
    onError: (error) => {
      toast.error(`Failed to reject request: ${error.message}`)
    }
  })

  const handleApprove = (approval: ApprovalRequest) => {
    approveMutation.mutate(approval.id)
  }

  const handleReject = (approval: ApprovalRequest) => {
    setSelectedApproval(approval)
    setShowRejectDialog(true)
  }

  const confirmReject = () => {
    if (!selectedApproval) return
    rejectMutation.mutate({
      approvalId: selectedApproval.id,
      reason: rejectionReason
    })
  }

  const getOperationIcon = (operation: string) => {
    if (operation.includes('restore')) return <AlertTriangle className="h-4 w-4" />
    if (operation.includes('delete')) return <XCircle className="h-4 w-4" />
    return <Clock className="h-4 w-4" />
  }

  const getOperationColor = (operation: string) => {
    if (operation.includes('restore:replace')) return 'destructive'
    if (operation.includes('delete')) return 'destructive'
    if (operation.includes('restore')) return 'warning'
    return 'default'
  }

  const formatOperationDescription = (operation: string, operationData: Record<string, unknown>) => {
    switch (operation) {
      case 'restore:replace':
        return `Replace all data with backup from ${formatDate(operationData.backup_date as string)}`
      case 'restore:merge':
        return `Merge data with backup from ${formatDate(operationData.backup_date as string)}`
      case 'restore:preview':
        return `Preview restoration from backup ${operationData.backup_id}`
      case 'delete':
        return `Delete backup from ${formatDate(operationData.backup_date as string)}`
      default:
        return operation
    }
  }

  const isExpiringSoon = (expiresAt: string) => {
    const expiryTime = new Date(expiresAt).getTime()
    const now = Date.now()
    const hoursUntilExpiry = (expiryTime - now) / (1000 * 60 * 60)
    return hoursUntilExpiry <= 2
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Pending Approvals</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Pending Approvals
            {pendingApprovals && pendingApprovals.length > 0 && (
              <Badge variant="secondary">{pendingApprovals.length}</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!pendingApprovals || pendingApprovals.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No pending approval requests
            </div>
          ) : (
            <div className="space-y-4">
              {pendingApprovals.map((approval) => (
                <Card key={approval.id} className="border-l-4 border-l-orange-500">
                  <CardContent className="pt-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {getOperationIcon(approval.operation)}
                          <Badge variant={getOperationColor(approval.operation) as "default" | "secondary" | "destructive" | "outline"}>
                            {approval.operation.replace(':', ' ')}
                          </Badge>
                          {isExpiringSoon(approval.expiresAt) && (
                            <Badge variant="destructive">Expires Soon</Badge>
                          )}
                        </div>
                        
                        <p className="font-medium mb-1">
                          {formatOperationDescription(approval.operation, approval.operationData)}
                        </p>
                        
                        <div className="text-sm text-muted-foreground space-y-1">
                          <p>Requested by: {approval.requestedBy}</p>
                          <p>Requested: {formatDate(approval.requestedAt)}</p>
                          <p>Expires: {formatDate(approval.expiresAt)}</p>
                        </div>

                        {approval.operationData.notes && (
                          <div className="mt-2 p-2 bg-muted rounded text-sm">
                            <strong>Notes:</strong> {approval.operationData.notes}
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2 ml-4">
                        <Button
                          size="sm"
                          variant="default"
                          onClick={() => handleApprove(approval)}
                          disabled={approveMutation.isPending}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleReject(approval)}
                          disabled={rejectMutation.isPending}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Rejection Dialog */}
      {showRejectDialog && selectedApproval && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle>Reject Approval Request</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  You are about to reject the approval request for:{' '}
                  <strong>{selectedApproval.operation}</strong>
                </AlertDescription>
              </Alert>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Reason for rejection (required)
                </label>
                <Textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Please provide a reason for rejecting this request..."
                  rows={3}
                />
              </div>

              <div className="flex gap-2 justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowRejectDialog(false)
                    setRejectionReason('')
                    setSelectedApproval(null)
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={confirmReject}
                  disabled={!rejectionReason.trim() || rejectMutation.isPending}
                >
                  {rejectMutation.isPending ? 'Rejecting...' : 'Reject Request'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
