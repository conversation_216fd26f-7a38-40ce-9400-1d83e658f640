{"version": 2, "name": "kaya-finance", "alias": ["kayafinance.com", "www.kayafinance.com"], "regions": ["iad1", "fra1"], "build": {"env": {"NODE_VERSION": "18", "NPM_VERSION": "9"}}, "buildCommand": "npm run build:prod", "outputDirectory": "dist", "installCommand": "npm ci", "devCommand": "npm run dev", "framework": "vite", "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.ipify.org https://ipapi.co; frame-ancestors 'none'; base-uri 'self'; form-action 'self'"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}], "redirects": [{"source": "/dashboard", "destination": "/", "permanent": false}], "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}, "env": {"VITE_APP_ENV": "production", "NODE_ENV": "production"}, "github": {"enabled": true, "autoAlias": true, "autoJobCancelation": true, "silent": false}}