import { supabase } from '@/integrations/supabase/client'
import { config } from '@/lib/config'

export interface EmailRequest {
  type: 'user_invitation' | 'password_reset' | 'welcome' | 'notification'
  to: string
  data: Record<string, unknown>
  org_id?: string
}

export interface InvitationEmailData {
  inviter_name: string
  organization_name: string
  role: string
  invitation_url: string
  expires_at: string
}

export interface EmailResponse {
  success: boolean
  message: string
  email_id?: string
  error?: string
}

/**
 * Send an email via the email service Edge Function
 */
export async function sendEmail(request: EmailRequest): Promise<EmailResponse> {
  try {
    const { data, error } = await supabase.functions.invoke('email-service', {
      body: request
    })

    if (error) {
      throw new Error(error.message || 'Failed to send email')
    }

    return data as EmailResponse
  } catch (error) {
    console.error('Email service error:', error)
    return {
      success: false,
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Send a user invitation email
 */
export async function sendUserInvitation(
  email: string,
  role: string,
  inviterName: string,
  organizationName: string,
  invitationToken: string,
  orgId: string
): Promise<EmailResponse> {
  const baseUrl = window.location.origin
  const invitationUrl = `${baseUrl}/accept-invitation?token=${invitationToken}`
  
  // Calculate expiration date (7 days from now)
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + 7)

  const emailData: InvitationEmailData = {
    inviter_name: inviterName,
    organization_name: organizationName,
    role: role,
    invitation_url: invitationUrl,
    expires_at: expiresAt.toISOString()
  }

  return sendEmail({
    type: 'user_invitation',
    to: email,
    data: emailData,
    org_id: orgId
  })
}

/**
 * Send a password reset email
 */
export async function sendPasswordReset(
  email: string,
  resetToken: string,
  orgId?: string
): Promise<EmailResponse> {
  const baseUrl = window.location.origin
  const resetUrl = `${baseUrl}/reset-password?token=${resetToken}`

  return sendEmail({
    type: 'password_reset',
    to: email,
    data: {
      reset_url: resetUrl,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
    },
    org_id: orgId
  })
}

/**
 * Send a welcome email to new users
 */
export async function sendWelcomeEmail(
  email: string,
  userName: string,
  organizationName: string,
  orgId: string
): Promise<EmailResponse> {
  return sendEmail({
    type: 'welcome',
    to: email,
    data: {
      user_name: userName,
      organization_name: organizationName,
      login_url: `${window.location.origin}/login`
    },
    org_id: orgId
  })
}

/**
 * Send a notification email
 */
export async function sendNotificationEmail(
  email: string,
  subject: string,
  message: string,
  orgId?: string
): Promise<EmailResponse> {
  return sendEmail({
    type: 'notification',
    to: email,
    data: {
      subject,
      message,
      timestamp: new Date().toISOString()
    },
    org_id: orgId
  })
}

/**
 * Validate email address format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/
  return emailRegex.test(email.trim())
}

/**
 * Get email statistics for an organization
 */
export async function getEmailStats(orgId: string, daysBack: number = 30) {
  try {
    const { data, error } = await supabase.rpc('get_email_stats', {
      org_uuid: orgId,
      days_back: daysBack
    })

    if (error) {
      throw error
    }

    return data[0] || {
      total_sent: 0,
      total_delivered: 0,
      total_opened: 0,
      total_bounced: 0,
      delivery_rate: 0,
      open_rate: 0,
      bounce_rate: 0
    }
  } catch (error) {
    console.error('Error fetching email stats:', error)
    return null
  }
}

/**
 * Get email logs for an organization
 */
export async function getEmailLogs(orgId: string, limit: number = 50) {
  try {
    const { data, error } = await supabase
      .from('email_logs')
      .select('*')
      .eq('org_id', orgId)
      .order('sent_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    console.error('Error fetching email logs:', error)
    return []
  }
}

/**
 * Retry failed email
 */
export async function retryFailedEmail(emailLogId: string): Promise<EmailResponse> {
  try {
    // Get the original email log
    const { data: emailLog, error } = await supabase
      .from('email_logs')
      .select('*')
      .eq('id', emailLogId)
      .single()

    if (error || !emailLog) {
      throw new Error('Email log not found')
    }

    // Increment retry count
    await supabase
      .from('email_logs')
      .update({ retry_count: (emailLog.retry_count || 0) + 1 })
      .eq('id', emailLogId)

    // Resend the email (this would need the original email data)
    // For now, return a placeholder response
    return {
      success: false,
      message: 'Email retry functionality needs original email data to be stored'
    }
  } catch (error) {
    console.error('Error retrying email:', error)
    return {
      success: false,
      message: 'Failed to retry email',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Check if email service is configured
 */
export function isEmailServiceConfigured(): boolean {
  // In a real implementation, you might check if RESEND_API_KEY is set
  // For now, we'll assume it's configured if we're not in development
  return config.app.environment !== 'development'
}

/**
 * Get email service status
 */
export async function getEmailServiceStatus() {
  try {
    // Test the email service with a health check
    const { data, error } = await supabase.functions.invoke('email-service', {
      body: { type: 'health_check' }
    })

    return {
      available: !error,
      configured: isEmailServiceConfigured(),
      error: error?.message
    }
  } catch (error) {
    return {
      available: false,
      configured: isEmailServiceConfigured(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
