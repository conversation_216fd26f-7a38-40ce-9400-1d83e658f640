-- =====================================================
-- NOTIFICATION SYSTEM AUTOMATION & TRIGGERS
-- =====================================================
-- Migration: 20250628_notification_automation.sql
-- Description: Automated notification triggers and advanced functions
-- Author: Kaya Finance Team
-- Date: 2025-06-28

-- =====================================================
-- STEP 1: BUSINESS EVENT NOTIFICATION TRIGGERS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔔 Creating business event notification triggers...';
END $$;

-- Function to create invoice overdue notifications
CREATE OR REPLACE FUNCTION create_invoice_overdue_notifications()
RETURNS INTEGER AS $$
DECLARE
    invoice_record RECORD;
    notification_count INTEGER := 0;
    days_overdue INTEGER;
BEGIN
    -- Find overdue invoices that haven't been notified recently
    FOR invoice_record IN
        SELECT 
            i.id,
            i.org_id,
            i.invoice_number,
            i.due_date,
            i.total_amount,
            c.name as customer_name,
            EXTRACT(DAY FROM (CURRENT_DATE - i.due_date))::INTEGER as days_overdue
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.status IN ('sent', 'partial')
        AND i.due_date < CURRENT_DATE
        AND NOT EXISTS (
            -- Don't create duplicate notifications within 7 days
            SELECT 1 FROM notifications n
            WHERE n.entity_type = 'invoice'
            AND n.entity_id = i.id
            AND n.type = 'invoice_overdue'
            AND n.created_at > NOW() - INTERVAL '7 days'
        )
    LOOP
        -- Create overdue notification
        PERFORM create_notification_from_template(
            'invoice_overdue',
            invoice_record.org_id,
            NULL, -- Organization-wide notification
            jsonb_build_object(
                'invoice_number', invoice_record.invoice_number,
                'customer_name', invoice_record.customer_name,
                'days_overdue', invoice_record.days_overdue,
                'amount', invoice_record.total_amount::TEXT
            ),
            'invoice',
            invoice_record.id
        );
        
        notification_count := notification_count + 1;
    END LOOP;
    
    RETURN notification_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create bill due soon notifications
CREATE OR REPLACE FUNCTION create_bill_due_notifications()
RETURNS INTEGER AS $$
DECLARE
    bill_record RECORD;
    notification_count INTEGER := 0;
    days_until_due INTEGER;
BEGIN
    -- Find bills due in 3 days that haven't been notified
    FOR bill_record IN
        SELECT 
            b.id,
            b.org_id,
            b.bill_number,
            b.due_date,
            b.total_amount,
            v.name as vendor_name,
            EXTRACT(DAY FROM (b.due_date - CURRENT_DATE))::INTEGER as days_until_due
        FROM bills b
        JOIN vendors v ON b.vendor_id = v.id
        WHERE b.status IN ('pending', 'approved')
        AND b.due_date BETWEEN CURRENT_DATE + INTERVAL '1 day' AND CURRENT_DATE + INTERVAL '3 days'
        AND NOT EXISTS (
            -- Don't create duplicate notifications
            SELECT 1 FROM notifications n
            WHERE n.entity_type = 'bill'
            AND n.entity_id = b.id
            AND n.type = 'bill_due_soon'
            AND n.created_at > NOW() - INTERVAL '3 days'
        )
    LOOP
        -- Create due soon notification
        PERFORM create_notification_from_template(
            'bill_due_soon',
            bill_record.org_id,
            NULL, -- Organization-wide notification
            jsonb_build_object(
                'bill_number', bill_record.bill_number,
                'vendor_name', bill_record.vendor_name,
                'days_until_due', bill_record.days_until_due,
                'amount', bill_record.total_amount::TEXT
            ),
            'bill',
            bill_record.id
        );
        
        notification_count := notification_count + 1;
    END LOOP;
    
    RETURN notification_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create payment approval notifications
CREATE OR REPLACE FUNCTION notify_payment_approval_required()
RETURNS TRIGGER AS $$
DECLARE
    payee_name TEXT;
    amount_text TEXT;
BEGIN
    -- Only trigger for payments that require approval
    IF NEW.status = 'pending_approval' AND (OLD.status IS NULL OR OLD.status != 'pending_approval') THEN
        -- Get payee name based on payment type
        IF NEW.vendor_id IS NOT NULL THEN
            SELECT name INTO payee_name FROM vendors WHERE id = NEW.vendor_id;
        ELSIF NEW.customer_id IS NOT NULL THEN
            SELECT name INTO payee_name FROM customers WHERE id = NEW.customer_id;
        ELSE
            payee_name := 'Unknown';
        END IF;
        
        amount_text := NEW.amount::TEXT;
        
        -- Create approval notification for organization admins
        PERFORM create_notification_from_template(
            'payment_pending_approval',
            NEW.org_id,
            NULL, -- Organization-wide for admins
            jsonb_build_object(
                'amount', amount_text,
                'payee_name', payee_name,
                'reason', COALESCE(NEW.description, 'Payment approval required')
            ),
            'payment',
            NEW.id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create payment approval trigger
DROP TRIGGER IF EXISTS payment_approval_notification_trigger ON payments;
CREATE TRIGGER payment_approval_notification_trigger
    AFTER INSERT OR UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION notify_payment_approval_required();

-- Function to create invoice payment notifications
CREATE OR REPLACE FUNCTION notify_invoice_payment()
RETURNS TRIGGER AS $$
DECLARE
    invoice_record RECORD;
BEGIN
    -- Trigger when invoice status changes to paid
    IF NEW.status = 'paid' AND (OLD.status IS NULL OR OLD.status != 'paid') THEN
        -- Get invoice details
        SELECT 
            i.invoice_number,
            c.name as customer_name
        INTO invoice_record
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.id = NEW.id;
        
        -- Create payment notification
        PERFORM create_notification_from_template(
            'invoice_paid',
            NEW.org_id,
            NEW.created_by, -- Notify the creator
            jsonb_build_object(
                'invoice_number', invoice_record.invoice_number,
                'customer_name', invoice_record.customer_name,
                'amount', NEW.total_amount::TEXT
            ),
            'invoice',
            NEW.id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create invoice payment trigger
DROP TRIGGER IF EXISTS invoice_payment_notification_trigger ON invoices;
CREATE TRIGGER invoice_payment_notification_trigger
    AFTER UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION notify_invoice_payment();

-- =====================================================
-- STEP 2: NOTIFICATION CLEANUP AND MAINTENANCE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🧹 Creating cleanup and maintenance functions...';
END $$;

-- Enhanced cleanup function with detailed reporting
CREATE OR REPLACE FUNCTION cleanup_notifications_advanced(
    org_id_param UUID DEFAULT NULL,
    days_old INTEGER DEFAULT 90,
    cleanup_expired BOOLEAN DEFAULT true,
    cleanup_read BOOLEAN DEFAULT false
)
RETURNS JSONB AS $$
DECLARE
    expired_count INTEGER := 0;
    old_read_count INTEGER := 0;
    archived_count INTEGER := 0;
    result JSONB;
BEGIN
    -- Clean up expired notifications
    IF cleanup_expired THEN
        IF org_id_param IS NOT NULL THEN
            DELETE FROM notifications 
            WHERE org_id = org_id_param 
            AND expires_at < NOW();
        ELSE
            DELETE FROM notifications 
            WHERE expires_at < NOW();
        END IF;
        GET DIAGNOSTICS expired_count = ROW_COUNT;
    END IF;
    
    -- Clean up old read notifications
    IF cleanup_read THEN
        IF org_id_param IS NOT NULL THEN
            DELETE FROM notifications 
            WHERE org_id = org_id_param 
            AND is_read = true 
            AND read_at < NOW() - INTERVAL '1 day' * days_old;
        ELSE
            DELETE FROM notifications 
            WHERE is_read = true 
            AND read_at < NOW() - INTERVAL '1 day' * days_old;
        END IF;
        GET DIAGNOSTICS old_read_count = ROW_COUNT;
    END IF;
    
    -- Archive very old notifications instead of deleting
    IF org_id_param IS NOT NULL THEN
        UPDATE notifications 
        SET is_archived = true
        WHERE org_id = org_id_param 
        AND is_archived = false
        AND created_at < NOW() - INTERVAL '1 day' * (days_old * 2);
    ELSE
        UPDATE notifications 
        SET is_archived = true
        WHERE is_archived = false
        AND created_at < NOW() - INTERVAL '1 day' * (days_old * 2);
    END IF;
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    -- Return cleanup results
    result := jsonb_build_object(
        'expired_deleted', expired_count,
        'old_read_deleted', old_read_count,
        'archived', archived_count,
        'cleanup_date', NOW()
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create daily notification digest
CREATE OR REPLACE FUNCTION create_notification_digest(user_id_param UUID)
RETURNS UUID AS $$
DECLARE
    user_record RECORD;
    unread_count INTEGER;
    urgent_count INTEGER;
    digest_data JSONB;
    notification_id UUID;
BEGIN
    -- Get user details
    SELECT p.*, o.name as org_name
    INTO user_record
    FROM profiles p
    JOIN organizations o ON p.org_id = o.id
    WHERE p.id = user_id_param;
    
    IF NOT FOUND THEN
        RETURN NULL;
    END IF;
    
    -- Count unread notifications
    SELECT 
        COUNT(*) FILTER (WHERE is_read = false) as unread,
        COUNT(*) FILTER (WHERE priority = 'urgent' AND is_read = false) as urgent
    INTO unread_count, urgent_count
    FROM notifications
    WHERE (user_id = user_id_param OR (user_id IS NULL AND org_id = user_record.org_id))
    AND is_archived = false
    AND created_at > NOW() - INTERVAL '24 hours';
    
    -- Only create digest if there are notifications
    IF unread_count > 0 THEN
        digest_data := jsonb_build_object(
            'unread_count', unread_count,
            'urgent_count', urgent_count,
            'user_name', user_record.full_name,
            'org_name', user_record.org_name
        );
        
        -- Create digest notification
        SELECT create_notification_from_template(
            'daily_digest',
            user_record.org_id,
            user_id_param,
            digest_data
        ) INTO notification_id;
        
        RETURN notification_id;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 3: NOTIFICATION ANALYTICS FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📊 Creating analytics functions...';
END $$;

-- Function to get detailed notification analytics
CREATE OR REPLACE FUNCTION get_notification_analytics(
    org_id_param UUID,
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS JSONB AS $$
DECLARE
    analytics JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_notifications', COUNT(*),
        'read_rate', ROUND((COUNT(*) FILTER (WHERE is_read = true)::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 2),
        'avg_read_time_hours', ROUND(AVG(EXTRACT(EPOCH FROM (read_at - created_at)) / 3600), 2),
        'by_category', category_stats,
        'by_priority', priority_stats,
        'by_type', type_stats,
        'daily_volume', daily_stats
    ) INTO analytics
    FROM (
        SELECT 
            is_read,
            read_at,
            created_at,
            jsonb_object_agg(DISTINCT category, category_count) as category_stats,
            jsonb_object_agg(DISTINCT priority, priority_count) as priority_stats,
            jsonb_object_agg(DISTINCT type, type_count) as type_stats,
            jsonb_object_agg(DISTINCT date_created, daily_count) as daily_stats
        FROM (
            SELECT 
                is_read,
                read_at,
                created_at,
                category,
                priority,
                type,
                DATE(created_at) as date_created,
                COUNT(*) OVER (PARTITION BY category) as category_count,
                COUNT(*) OVER (PARTITION BY priority) as priority_count,
                COUNT(*) OVER (PARTITION BY type) as type_count,
                COUNT(*) OVER (PARTITION BY DATE(created_at)) as daily_count
            FROM notifications
            WHERE org_id = org_id_param
            AND DATE(created_at) BETWEEN start_date AND end_date
        ) subq
        GROUP BY is_read, read_at, created_at
    ) analytics_data;
    
    RETURN COALESCE(analytics, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user engagement metrics
CREATE OR REPLACE FUNCTION get_user_engagement_metrics(org_id_param UUID)
RETURNS TABLE(
    user_id UUID,
    user_name TEXT,
    total_notifications INTEGER,
    read_notifications INTEGER,
    read_rate DECIMAL,
    avg_read_time_hours DECIMAL,
    last_activity TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id as user_id,
        p.full_name as user_name,
        COUNT(n.id)::INTEGER as total_notifications,
        COUNT(n.id) FILTER (WHERE n.is_read = true)::INTEGER as read_notifications,
        ROUND((COUNT(n.id) FILTER (WHERE n.is_read = true)::DECIMAL / NULLIF(COUNT(n.id), 0)) * 100, 2) as read_rate,
        ROUND(AVG(EXTRACT(EPOCH FROM (n.read_at - n.created_at)) / 3600), 2) as avg_read_time_hours,
        MAX(n.read_at) as last_activity
    FROM profiles p
    LEFT JOIN notifications n ON (n.user_id = p.id OR (n.user_id IS NULL AND n.org_id = p.org_id))
    WHERE p.org_id = org_id_param
    AND (n.created_at IS NULL OR n.created_at > NOW() - INTERVAL '30 days')
    GROUP BY p.id, p.full_name
    ORDER BY read_rate DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 4: SCHEDULED NOTIFICATION JOBS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⏰ Creating scheduled job functions...';
END $$;

-- Master function to run all scheduled notification jobs
CREATE OR REPLACE FUNCTION run_notification_jobs()
RETURNS JSONB AS $$
DECLARE
    overdue_count INTEGER;
    due_count INTEGER;
    cleanup_result JSONB;
    job_result JSONB;
BEGIN
    -- Create overdue invoice notifications
    SELECT create_invoice_overdue_notifications() INTO overdue_count;
    
    -- Create bill due soon notifications
    SELECT create_bill_due_notifications() INTO due_count;
    
    -- Run cleanup
    SELECT cleanup_notifications_advanced() INTO cleanup_result;
    
    -- Compile results
    job_result := jsonb_build_object(
        'overdue_invoices_notified', overdue_count,
        'bills_due_notified', due_count,
        'cleanup_results', cleanup_result,
        'job_run_time', NOW()
    );
    
    RETURN job_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ NOTIFICATION AUTOMATION COMPLETED!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '';
    RAISE NOTICE '🔔 BUSINESS EVENT TRIGGERS:';
    RAISE NOTICE '  • Invoice overdue notifications';
    RAISE NOTICE '  • Bill due soon notifications';
    RAISE NOTICE '  • Payment approval notifications';
    RAISE NOTICE '  • Invoice payment notifications';
    RAISE NOTICE '';
    RAISE NOTICE '🧹 MAINTENANCE FUNCTIONS:';
    RAISE NOTICE '  • cleanup_notifications_advanced()';
    RAISE NOTICE '  • create_notification_digest()';
    RAISE NOTICE '';
    RAISE NOTICE '📊 ANALYTICS FUNCTIONS:';
    RAISE NOTICE '  • get_notification_analytics()';
    RAISE NOTICE '  • get_user_engagement_metrics()';
    RAISE NOTICE '';
    RAISE NOTICE '⏰ SCHEDULED JOBS:';
    RAISE NOTICE '  • run_notification_jobs()';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Notification system automation is ready!';
    RAISE NOTICE '';
END $$;
