/**
 * Notification Pagination Hook
 * Implements cursor-based pagination with infinite scroll and virtual scrolling
 */

import { useState, useCallback, useMemo, useRef, useEffect } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import type { 
  NotificationFilters, 
  NotificationWithMeta,
  NotificationPriority,
  NotificationCategory 
} from '@/types/notifications'
import { getNotificationTimeAgo, isNotificationExpired } from '@/types/notifications'

interface PaginationConfig {
  pageSize: number
  enableVirtualScrolling: boolean
  preloadPages: number
  maxCachedPages: number
}

interface NotificationPage {
  data: NotificationWithMeta[]
  nextCursor: string | null
  hasMore: boolean
  page: number
}

interface PaginationState {
  isLoading: boolean
  isLoadingMore: boolean
  hasMore: boolean
  error: string | null
  totalCount: number
  currentPage: number
}

const DEFAULT_CONFIG: PaginationConfig = {
  pageSize: 20,
  enableVirtualScrolling: true,
  preloadPages: 2,
  maxCachedPages: 10
}

export function useNotificationPagination(
  filters?: NotificationFilters,
  config: Partial<PaginationConfig> = {}
) {
  const { profile } = useAuth()
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  
  const [paginationState, setPaginationState] = useState<PaginationState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    error: null,
    totalCount: 0,
    currentPage: 0
  })

  // Infinite query for cursor-based pagination
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch
  } = useInfiniteQuery({
    queryKey: queryKeys.notifications.filtered(profile?.id || '', filters || {}),
    queryFn: async ({ pageParam = null }): Promise<NotificationPage> => {
      if (!profile?.id) {
        return { data: [], nextCursor: null, hasMore: false, page: 0 }
      }

      try {
        let query = supabase
          .from('notifications')
          .select('*', { count: 'exact' })
          .or(`user_id.eq.${profile.id},and(user_id.is.null,org_id.eq.${profile.org_id})`)
          .order('created_at', { ascending: false })
          .limit(finalConfig.pageSize)

        // Apply cursor-based pagination
        if (pageParam) {
          query = query.lt('created_at', pageParam)
        }

        // Apply filters
        if (filters) {
          if (filters.category) {
            query = query.eq('category', filters.category)
          }
          if (filters.priority) {
            query = query.eq('priority', filters.priority)
          }
          if (filters.is_read !== undefined) {
            query = query.eq('is_read', filters.is_read)
          }
          if (filters.is_archived !== undefined) {
            query = query.eq('is_archived', filters.is_archived)
          }
          if (filters.entity_type) {
            query = query.eq('entity_type', filters.entity_type)
          }
          if (filters.date_from) {
            query = query.gte('created_at', filters.date_from)
          }
          if (filters.date_to) {
            query = query.lte('created_at', filters.date_to)
          }
        }

        const { data: notifications, error: queryError, count } = await query

        if (queryError) {
          if (queryError.message?.includes('relation "notifications" does not exist')) {
            console.warn('Notifications table does not exist yet. Please run the migration.')
            return { data: [], nextCursor: null, hasMore: false, page: 0 }
          }
          throw queryError
        }

        // Add computed properties
        const enhancedData = (notifications || []).map(notification => ({
          ...notification,
          isExpired: isNotificationExpired(notification),
          timeAgo: getNotificationTimeAgo(notification.created_at),
        }))

        // Determine next cursor and if there are more pages
        const hasMore = enhancedData.length === finalConfig.pageSize
        const nextCursor = hasMore && enhancedData.length > 0 
          ? enhancedData[enhancedData.length - 1].created_at 
          : null

        return {
          data: enhancedData,
          nextCursor,
          hasMore,
          page: pageParam ? 1 : 0 // Simple page tracking
        }
      } catch (error: unknown) {
        console.error('Error fetching notifications page:', error)
        throw error
      }
    },
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    enabled: !!profile?.id,
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: false,
    retry: 2
  })

  // Flatten all pages into a single array
  const allNotifications = useMemo(() => {
    return data?.pages.flatMap(page => page.data) || []
  }, [data])

  // Update pagination state
  useEffect(() => {
    setPaginationState(prev => ({
      ...prev,
      isLoading,
      isLoadingMore: isFetchingNextPage,
      hasMore: hasNextPage || false,
      error: isError ? (error as Error)?.message || 'Unknown error' : null,
      totalCount: allNotifications.length
    }))
  }, [isLoading, isFetchingNextPage, hasNextPage, isError, error, allNotifications.length])

  // Load more notifications
  const loadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage()
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage])

  // Refresh notifications
  const refresh = useCallback(async () => {
    await refetch()
  }, [refetch])

  // Get notifications for virtual scrolling
  const getVirtualizedNotifications = useCallback((
    startIndex: number,
    endIndex: number
  ) => {
    return allNotifications.slice(startIndex, endIndex + 1)
  }, [allNotifications])

  // Preload next pages if needed
  useEffect(() => {
    if (finalConfig.preloadPages > 0 && hasNextPage && !isFetchingNextPage) {
      const currentPageCount = data?.pages.length || 0
      const totalNotifications = allNotifications.length
      const estimatedCurrentPage = Math.floor(totalNotifications / finalConfig.pageSize)
      
      // Preload if we're close to the end
      if (totalNotifications > 0 && 
          totalNotifications >= (estimatedCurrentPage - finalConfig.preloadPages) * finalConfig.pageSize) {
        loadMore()
      }
    }
  }, [allNotifications.length, finalConfig.preloadPages, finalConfig.pageSize, hasNextPage, isFetchingNextPage, loadMore, data?.pages.length])

  return {
    // Data
    notifications: allNotifications,
    
    // Pagination state
    ...paginationState,
    
    // Actions
    loadMore,
    refresh,
    getVirtualizedNotifications,
    
    // Utilities
    isEmpty: allNotifications.length === 0 && !isLoading,
    isFirstLoad: isLoading && allNotifications.length === 0,
    
    // Virtual scrolling helpers
    estimatedItemHeight: 80, // Estimated height of each notification item
    totalItems: allNotifications.length,
    
    // Configuration
    config: finalConfig
  }
}

/**
 * Hook for infinite scroll behavior
 */
export function useInfiniteScroll(
  loadMore: () => void,
  hasMore: boolean,
  isLoading: boolean,
  threshold: number = 200
) {
  const [isFetching, setIsFetching] = useState(false)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadingRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    if (!loadingRef.current || !hasMore || isLoading) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting && hasMore && !isLoading && !isFetching) {
          setIsFetching(true)
          loadMore()
        }
      },
      {
        rootMargin: `${threshold}px`,
        threshold: 0.1
      }
    )

    observerRef.current.observe(loadingRef.current)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [loadMore, hasMore, isLoading, threshold, isFetching])

  useEffect(() => {
    if (!isLoading) {
      setIsFetching(false)
    }
  }, [isLoading])

  return { loadingRef }
}

/**
 * Hook for virtual scrolling large notification lists
 */
export function useVirtualScrolling(
  totalItems: number,
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      totalItems - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )

    return { startIndex, endIndex }
  }, [scrollTop, itemHeight, containerHeight, totalItems, overscan])

  const totalHeight = totalItems * itemHeight
  const offsetY = visibleRange.startIndex * itemHeight

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop)
  }, [])

  return {
    visibleRange,
    totalHeight,
    offsetY,
    handleScroll,
    visibleItems: visibleRange.endIndex - visibleRange.startIndex + 1
  }
}
