import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  DollarSign,
  ChevronRight,
  RefreshCw
} from 'lucide-react'
import { useAllBudgetsAnalysis } from '@/hooks/queries/useBudgetAnalysis'
import { useHasCriticalBudgetAlerts } from '@/hooks/queries/useBudgetAlerts'

interface BudgetDashboardWidgetProps {
  className?: string
  onViewDetails?: () => void
}

export function BudgetDashboardWidget({ 
  className = "",
  onViewDetails 
}: BudgetDashboardWidgetProps) {
  const { data: budgetAnalyses = [], isLoading, refetch } = useAllBudgetsAnalysis()
  const { hasCritical, criticalCount } = useHasCriticalBudgetAlerts()
  const [isRefreshing, setIsRefreshing] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
      notation: 'compact',
      compactDisplay: 'short'
    }).format(amount)
  }

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await refetch()
    } finally {
      setIsRefreshing(false)
    }
  }

  // Aggregate data from all budgets
  const aggregatedData = budgetAnalyses.reduce((acc, analysis) => ({
    totalBudget: acc.totalBudget + analysis.totalBudget,
    totalActual: acc.totalActual + analysis.totalActual,
    totalVariance: acc.totalVariance + analysis.totalVariance,
    overBudgetAccounts: acc.overBudgetAccounts + analysis.overBudgetAccounts,
    criticalAccounts: acc.criticalAccounts + analysis.criticalAccounts,
    warningAccounts: acc.warningAccounts + analysis.warningAccounts,
    accountsAnalyzed: acc.accountsAnalyzed + analysis.accountsAnalyzed
  }), {
    totalBudget: 0,
    totalActual: 0,
    totalVariance: 0,
    overBudgetAccounts: 0,
    criticalAccounts: 0,
    warningAccounts: 0,
    accountsAnalyzed: 0
  })

  const utilizationPercent = aggregatedData.totalBudget > 0 
    ? (aggregatedData.totalActual / aggregatedData.totalBudget) * 100 
    : 0

  const getUtilizationColor = () => {
    if (utilizationPercent >= 100) return 'text-red-600'
    if (utilizationPercent >= 90) return 'text-orange-600'
    if (utilizationPercent >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  const getVarianceIcon = () => {
    if (aggregatedData.totalVariance > 0) {
      return <TrendingUp className="h-4 w-4 text-red-600" />
    }
    return <TrendingDown className="h-4 w-4 text-green-600" />
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Budget Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (budgetAnalyses.length === 0) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Budget Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <div className="text-sm text-muted-foreground">No active budgets found</div>
            <Button variant="outline" size="sm" className="mt-2" onClick={onViewDetails}>
              Create Budget
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Budget Overview
            {hasCritical && (
              <Badge variant="destructive" className="text-xs">
                {criticalCount} Alert{criticalCount !== 1 ? 's' : ''}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
            {onViewDetails && (
              <Button variant="ghost" size="sm" onClick={onViewDetails}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Overall Utilization */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Overall Utilization</span>
            <span className={`text-sm font-bold ${getUtilizationColor()}`}>
              {utilizationPercent.toFixed(1)}%
            </span>
          </div>
          <Progress 
            value={Math.min(utilizationPercent, 100)} 
            className="h-2"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Spent: {formatCurrency(aggregatedData.totalActual)}</span>
            <span>Budget: {formatCurrency(aggregatedData.totalBudget)}</span>
          </div>
        </div>

        {/* Variance Summary */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            {getVarianceIcon()}
            <span className="text-sm font-medium">
              {aggregatedData.totalVariance > 0 ? 'Over Budget' : 'Under Budget'}
            </span>
          </div>
          <div className={`text-sm font-bold ${
            aggregatedData.totalVariance > 0 ? 'text-red-600' : 'text-green-600'
          }`}>
            {formatCurrency(Math.abs(aggregatedData.totalVariance))}
          </div>
        </div>

        {/* Alert Summary */}
        {(aggregatedData.criticalAccounts > 0 || aggregatedData.warningAccounts > 0) && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Account Alerts</div>
            <div className="grid grid-cols-2 gap-2">
              {aggregatedData.criticalAccounts > 0 && (
                <div className="flex items-center gap-2 p-2 bg-red-50 rounded border border-red-200">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <div>
                    <div className="text-sm font-medium text-red-700">
                      {aggregatedData.criticalAccounts}
                    </div>
                    <div className="text-xs text-red-600">Critical</div>
                  </div>
                </div>
              )}
              
              {aggregatedData.warningAccounts > 0 && (
                <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded border border-yellow-200">
                  <TrendingUp className="h-4 w-4 text-yellow-600" />
                  <div>
                    <div className="text-sm font-medium text-yellow-700">
                      {aggregatedData.warningAccounts}
                    </div>
                    <div className="text-xs text-yellow-600">Warning</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Top Budget Issues */}
        {budgetAnalyses.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Top Issues</div>
            <div className="space-y-1">
              {budgetAnalyses
                .flatMap(analysis => analysis.items)
                .filter(item => item.alertLevel === 'exceeded' || item.alertLevel === 'critical')
                .slice(0, 3)
                .map((item, index) => (
                  <div key={`${item.account.id}-${index}`} className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                    <div className="flex items-center gap-2">
                      {item.alertLevel === 'exceeded' ? (
                        <AlertTriangle className="h-3 w-3 text-red-600" />
                      ) : (
                        <TrendingUp className="h-3 w-3 text-orange-600" />
                      )}
                      <span className="font-medium">{item.account.name}</span>
                    </div>
                    <div className={`font-medium ${
                      item.alertLevel === 'exceeded' ? 'text-red-600' : 'text-orange-600'
                    }`}>
                      {item.utilizationPercent.toFixed(0)}%
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-2 pt-2 border-t text-center">
          <div>
            <div className="text-lg font-bold">{budgetAnalyses.length}</div>
            <div className="text-xs text-muted-foreground">Active Budgets</div>
          </div>
          <div>
            <div className="text-lg font-bold">{aggregatedData.accountsAnalyzed}</div>
            <div className="text-xs text-muted-foreground">Accounts</div>
          </div>
          <div>
            <div className="text-lg font-bold text-red-600">{aggregatedData.overBudgetAccounts}</div>
            <div className="text-xs text-muted-foreground">Over Budget</div>
          </div>
        </div>

        {/* Action Button */}
        {onViewDetails && (
          <Button variant="outline" size="sm" className="w-full" onClick={onViewDetails}>
            View Detailed Reports
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
