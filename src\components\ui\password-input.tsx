/**
 * Password Input Component with Real-time Validation
 * Provides password input with strength indicator and requirements feedback
 */

import React, { useState } from 'react'
import { Eye, EyeOff, Check, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  validatePassword, 
  getPasswordStrengthColor, 
  getPasswordStrengthBgColor,
  type PasswordValidationResult 
} from '@/lib/passwordValidation'

interface PasswordInputProps {
  id?: string
  label?: string
  placeholder?: string
  value: string
  onChange: (value: string) => void
  onValidationChange?: (validation: PasswordValidationResult) => void
  showRequirements?: boolean
  showStrengthIndicator?: boolean
  required?: boolean
  disabled?: boolean
  className?: string
  error?: string
}

export function PasswordInput({
  id = 'password',
  label = 'Password',
  placeholder = 'Enter your password',
  value,
  onChange,
  onValidationChange,
  showRequirements = true,
  showStrengthIndicator = true,
  required = false,
  disabled = false,
  className = '',
  error
}: PasswordInputProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [isFocused, setIsFocused] = useState(false)

  const validation = validatePassword(value)

  // Notify parent of validation changes
  React.useEffect(() => {
    if (onValidationChange) {
      onValidationChange(validation)
    }
  }, [validation, onValidationChange])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const shouldShowFeedback = (isFocused || value.length > 0) && showRequirements

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label */}
      <Label htmlFor={id} className="text-sm font-medium">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>

      {/* Password Input with Toggle */}
      <div className="relative">
        <Input
          id={id}
          type={showPassword ? 'text' : 'password'}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          required={required}
          disabled={disabled}
          className={`pr-10 ${error ? 'border-red-500' : ''}`}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={togglePasswordVisibility}
          disabled={disabled}
          tabIndex={-1}
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4 text-gray-400" />
          ) : (
            <Eye className="h-4 w-4 text-gray-400" />
          )}
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {/* Strength Indicator */}
      {showStrengthIndicator && value.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Password strength:</span>
            <span className={`text-xs font-medium ${getPasswordStrengthColor(validation.strength)}`}>
              {validation.strength.charAt(0).toUpperCase() + validation.strength.slice(1)}
            </span>
          </div>
          <Progress 
            value={validation.score} 
            className="h-2"
            indicatorClassName={getPasswordStrengthBgColor(validation.strength)}
          />
        </div>
      )}

      {/* Requirements Feedback */}
      {shouldShowFeedback && (
        <div className="space-y-2">
          <p className="text-xs text-gray-600">Password requirements:</p>
          <div className="space-y-1">
            {validation.requirements.map((requirement) => (
              <div
                key={requirement.id}
                className="flex items-center gap-2 text-xs"
              >
                {requirement.met ? (
                  <Check className="h-3 w-3 text-green-500 flex-shrink-0" />
                ) : (
                  <X className="h-3 w-3 text-gray-400 flex-shrink-0" />
                )}
                <span
                  className={
                    requirement.met
                      ? 'text-green-600'
                      : 'text-gray-500'
                  }
                >
                  {requirement.label}
                </span>
              </div>
            ))}
          </div>

          {/* Additional Feedback */}
          {validation.feedback.length > 0 && (
            <div className="mt-2 space-y-1">
              {validation.feedback.map((feedback, index) => (
                <p key={index} className="text-xs text-gray-600">
                  {feedback}
                </p>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

/**
 * Password Confirmation Input Component
 */
interface PasswordConfirmInputProps {
  id?: string
  label?: string
  placeholder?: string
  value: string
  originalPassword: string
  onChange: (value: string) => void
  required?: boolean
  disabled?: boolean
  className?: string
  error?: string
}

export function PasswordConfirmInput({
  id = 'confirmPassword',
  label = 'Confirm Password',
  placeholder = 'Confirm your password',
  value,
  originalPassword,
  onChange,
  required = false,
  disabled = false,
  className = '',
  error
}: PasswordConfirmInputProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [isTouched, setIsTouched] = useState(false)

  const isMatching = value === originalPassword
  const showValidation = isTouched && value.length > 0

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label */}
      <Label htmlFor={id} className="text-sm font-medium">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>

      {/* Password Input with Toggle */}
      <div className="relative">
        <Input
          id={id}
          type={showPassword ? 'text' : 'password'}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onBlur={() => setIsTouched(true)}
          required={required}
          disabled={disabled}
          className={`pr-10 ${error || (showValidation && !isMatching) ? 'border-red-500' : showValidation && isMatching ? 'border-green-500' : ''}`}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={togglePasswordVisibility}
          disabled={disabled}
          tabIndex={-1}
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4 text-gray-400" />
          ) : (
            <Eye className="h-4 w-4 text-gray-400" />
          )}
        </Button>
      </div>

      {/* Validation Message */}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      
      {showValidation && !error && (
        <div className="flex items-center gap-2">
          {isMatching ? (
            <>
              <Check className="h-3 w-3 text-green-500" />
              <span className="text-xs text-green-600">Passwords match</span>
            </>
          ) : (
            <>
              <X className="h-3 w-3 text-red-500" />
              <span className="text-xs text-red-600">Passwords do not match</span>
            </>
          )}
        </div>
      )}
    </div>
  )
}
