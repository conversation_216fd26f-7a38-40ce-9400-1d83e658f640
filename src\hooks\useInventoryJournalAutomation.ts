import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'
import { queryKeys } from '@/lib/query<PERSON>eys'

export interface InventoryJournalSettings {
  enabled: boolean
  auto_post: boolean
  create_cogs_entries: boolean
  create_adjustment_entries: boolean
}

export interface InventoryAccountMapping {
  inventory_asset: string | null
  cost_of_goods_sold: string | null
  inventory_adjustment: string | null
}

export interface COGSCalculation {
  product_id: string
  quantity: number
  unit_cost: number
  total_cost: number
  method: 'fifo' | 'lifo' | 'average'
}

/**
 * Hook for managing inventory journal automation settings and operations
 */
export function useInventoryJournalAutomation() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  // Get inventory journal automation settings
  const settingsQuery = useQuery({
    queryKey: ['inventory-journal-settings', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('automation_settings')
        .select('enabled, setting_value')
        .eq('org_id', profile.org_id)
        .eq('setting_key', 'inventory_journal_automation')
        .single()

      if (error && error.code !== 'PGRST116') throw error
      
      if (!data) {
        // Return default settings if none exist
        return {
          enabled: true,
          auto_post: false,
          create_cogs_entries: true,
          create_adjustment_entries: true
        } as InventoryJournalSettings
      }

      return {
        enabled: data.enabled,
        ...data.setting_value
      } as InventoryJournalSettings
    },
    enabled: !!profile?.org_id,
  })

  // Get inventory account mappings
  const accountMappingsQuery = useQuery({
    queryKey: ['inventory-account-mappings', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('account_mappings')
        .select('mapping_type, account_id, account:accounts(name, code)')
        .eq('org_id', profile.org_id)
        .in('mapping_type', ['inventory_asset', 'cost_of_goods_sold', 'inventory_adjustment'])
        .eq('is_default', true)

      if (error) throw error

      const mappings: InventoryAccountMapping = {
        inventory_asset: null,
        cost_of_goods_sold: null,
        inventory_adjustment: null
      }

      data?.forEach(mapping => {
        mappings[mapping.mapping_type as keyof InventoryAccountMapping] = mapping.account_id
      })

      return mappings
    },
    enabled: !!profile?.org_id,
  })

  // Update inventory journal settings
  const updateSettingsMutation = useMutation({
    mutationFn: async (settings: Partial<InventoryJournalSettings>) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { error } = await supabase
        .from('automation_settings')
        .upsert({
          org_id: profile.org_id,
          setting_key: 'inventory_journal_automation',
          enabled: settings.enabled ?? true,
          setting_value: {
            auto_post: settings.auto_post ?? false,
            create_cogs_entries: settings.create_cogs_entries ?? true,
            create_adjustment_entries: settings.create_adjustment_entries ?? true
          },
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'org_id,setting_key'
        })

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory-journal-settings', profile?.org_id] })
      toast.success('Inventory journal settings updated successfully')
    },
    onError: (error) => {
      console.error('Failed to update inventory journal settings:', error)
      toast.error('Failed to update inventory journal settings')
    }
  })

  // Calculate COGS for a product sale
  const calculateCOGSMutation = useMutation({
    mutationFn: async ({
      product_id,
      quantity,
      method = 'average'
    }: {
      product_id: string
      quantity: number
      method?: 'fifo' | 'lifo' | 'average'
    }) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      // Get current stock levels and costs
      const { data: stockLevel, error: stockError } = await supabase
        .from('stock_levels')
        .select('average_cost, last_cost, quantity_on_hand')
        .eq('org_id', profile.org_id)
        .eq('product_id', product_id)
        .single()

      if (stockError) throw stockError

      let unit_cost = 0
      switch (method) {
        case 'average':
          unit_cost = stockLevel?.average_cost || 0
          break
        case 'fifo':
          // For FIFO, we'd need to implement a more complex calculation
          // For now, use average cost as fallback
          unit_cost = stockLevel?.average_cost || 0
          break
        case 'lifo':
          unit_cost = stockLevel?.last_cost || stockLevel?.average_cost || 0
          break
      }

      const total_cost = unit_cost * quantity

      return {
        product_id,
        quantity,
        unit_cost,
        total_cost,
        method
      } as COGSCalculation
    }
  })

  // Create manual inventory adjustment journal entry
  const createAdjustmentJournalMutation = useMutation({
    mutationFn: async ({
      product_id,
      quantity_change,
      unit_cost,
      reason,
      reference_number
    }: {
      product_id: string
      quantity_change: number
      unit_cost: number
      reason: string
      reference_number?: string
    }) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      // Create inventory transaction (this will trigger the journal entry automatically)
      const { data, error } = await supabase
        .from('inventory_transactions')
        .insert({
          org_id: profile.org_id,
          product_id,
          location_id: null, // Will use default location
          transaction_type: 'adjustment',
          quantity: quantity_change,
          unit_cost,
          reference_type: 'manual_adjustment',
          reference_id: null,
          reference_number: reference_number || `ADJ-${Date.now()}`,
          notes: reason,
          created_by: profile.id,
          transaction_date: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.journalEntries.all(profile?.org_id || '') })
      toast.success('Inventory adjustment journal entry created successfully')
    },
    onError: (error) => {
      console.error('Failed to create adjustment journal entry:', error)
      toast.error('Failed to create adjustment journal entry')
    }
  })

  // Get journal entries related to inventory transactions
  const inventoryJournalEntriesQuery = useQuery({
    queryKey: ['inventory-journal-entries', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const { data, error } = await supabase
        .from('journal_entries')
        .select(`
          *,
          transaction_lines(
            *,
            account:accounts(name, code, type)
          )
        `)
        .eq('org_id', profile.org_id)
        .eq('source_type', 'inventory_transaction')
        .order('date', { ascending: false })

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id,
  })

  return {
    // Queries
    settings: settingsQuery.data,
    accountMappings: accountMappingsQuery.data,
    inventoryJournalEntries: inventoryJournalEntriesQuery.data,
    
    // Loading states
    isLoadingSettings: settingsQuery.isLoading,
    isLoadingMappings: accountMappingsQuery.isLoading,
    isLoadingEntries: inventoryJournalEntriesQuery.isLoading,
    
    // Mutations
    updateSettings: updateSettingsMutation.mutateAsync,
    calculateCOGS: calculateCOGSMutation.mutateAsync,
    createAdjustmentJournal: createAdjustmentJournalMutation.mutateAsync,
    
    // Mutation states
    isUpdatingSettings: updateSettingsMutation.isPending,
    isCalculatingCOGS: calculateCOGSMutation.isPending,
    isCreatingAdjustment: createAdjustmentJournalMutation.isPending,
    
    // Refetch functions
    refetchSettings: settingsQuery.refetch,
    refetchMappings: accountMappingsQuery.refetch,
    refetchEntries: inventoryJournalEntriesQuery.refetch
  }
}
