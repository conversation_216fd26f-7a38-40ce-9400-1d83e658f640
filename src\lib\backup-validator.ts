// Comprehensive Backup Validation and Verification System
// Provides checksum validation, data integrity checks, and completeness verification

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'
import { BackupErrorHandler, BackupErrorType } from './backup-error-handler'

export interface ValidationResult {
  valid: boolean
  score: number // 0-100 validation score
  issues: ValidationIssue[]
  summary: ValidationSummary
}

export interface ValidationIssue {
  type: 'error' | 'warning' | 'info'
  category: string
  message: string
  details?: Record<string, unknown>
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface ValidationSummary {
  totalChecks: number
  passedChecks: number
  failedChecks: number
  warningChecks: number
  checksumValid: boolean
  dataIntegrityValid: boolean
  completenessValid: boolean
  encryptionValid: boolean
}

export interface BackupIntegrityCheck {
  checksum: string
  expectedChecksum: string
  sizeBytes: number
  expectedSizeBytes: number
  tableCount: number
  expectedTableCount: number
  recordCount: number
  expectedRecordCount: number
}

/**
 * Comprehensive Backup Validator
 */
export class BackupValidator {
  private static readonly REQUIRED_TABLES = [
    'organizations',
    'profiles',
    'customers',
    'vendors',
    'accounts',
    'invoices',
    'bills',
    'payments',
    'journal_entries',
    'transaction_lines',
    'audit_logs'
  ]

  /**
   * Validate backup integrity and completeness
   */
  static async validateBackup(
    backupId: string,
    orgId: string,
    options: {
      checkChecksum?: boolean
      checkDataIntegrity?: boolean
      checkCompleteness?: boolean
      checkEncryption?: boolean
      deepValidation?: boolean
    } = {}
  ): Promise<ValidationResult> {
    const {
      checkChecksum = true,
      checkDataIntegrity = true,
      checkCompleteness = true,
      checkEncryption = true,
      deepValidation = false
    } = options

    const issues: ValidationIssue[] = []
    let totalChecks = 0
    let passedChecks = 0
    let failedChecks = 0
    let warningChecks = 0

    try {
      // Get backup metadata
      const { data: metadata, error: metadataError } = await supabase
        .from('backup_metadata')
        .select('*')
        .eq('id', backupId)
        .eq('org_id', orgId)
        .single()

      if (metadataError || !metadata) {
        issues.push({
          type: 'error',
          category: 'metadata',
          message: 'Backup metadata not found',
          severity: 'critical'
        })
        return this.buildValidationResult(issues, 0, 1, 0, 0)
      }

      // Validate backup status
      totalChecks++
      if (metadata.status !== 'completed') {
        issues.push({
          type: 'error',
          category: 'status',
          message: `Backup status is '${metadata.status}', expected 'completed'`,
          severity: 'high'
        })
        failedChecks++
      } else {
        passedChecks++
      }

      // Download and validate backup data
      const backupData = await this.downloadBackupData(metadata)
      if (!backupData.success) {
        issues.push({
          type: 'error',
          category: 'download',
          message: backupData.error || 'Failed to download backup data',
          severity: 'critical'
        })
        return this.buildValidationResult(issues, totalChecks, passedChecks, failedChecks, warningChecks)
      }

      // Checksum validation
      if (checkChecksum) {
        const checksumResult = await this.validateChecksum(metadata, backupData.rawData!)
        totalChecks++
        if (checksumResult.valid) {
          passedChecks++
        } else {
          failedChecks++
          issues.push({
            type: 'error',
            category: 'checksum',
            message: 'Checksum validation failed',
            details: checksumResult.details,
            severity: 'critical'
          })
        }
      }

      // Data integrity validation
      if (checkDataIntegrity) {
        const integrityResult = await this.validateDataIntegrity(backupData.data!, metadata)
        totalChecks += integrityResult.totalChecks
        passedChecks += integrityResult.passedChecks
        failedChecks += integrityResult.failedChecks
        warningChecks += integrityResult.warningChecks
        issues.push(...integrityResult.issues)
      }

      // Completeness validation
      if (checkCompleteness) {
        const completenessResult = await this.validateCompleteness(backupData.data!, orgId)
        totalChecks += completenessResult.totalChecks
        passedChecks += completenessResult.passedChecks
        failedChecks += completenessResult.failedChecks
        warningChecks += completenessResult.warningChecks
        issues.push(...completenessResult.issues)
      }

      // Encryption validation
      if (checkEncryption && metadata.encryption_enabled) {
        const encryptionResult = await this.validateEncryption(metadata)
        totalChecks++
        if (encryptionResult.valid) {
          passedChecks++
        } else {
          failedChecks++
          issues.push({
            type: 'error',
            category: 'encryption',
            message: 'Encryption validation failed',
            details: encryptionResult.details,
            severity: 'high'
          })
        }
      }

      // Deep validation (optional, more thorough checks)
      if (deepValidation) {
        const deepResult = await this.performDeepValidation(backupData.data!, orgId)
        totalChecks += deepResult.totalChecks
        passedChecks += deepResult.passedChecks
        failedChecks += deepResult.failedChecks
        warningChecks += deepResult.warningChecks
        issues.push(...deepResult.issues)
      }

      // Log validation results
      await this.logValidationResults(backupId, orgId, {
        totalChecks,
        passedChecks,
        failedChecks,
        warningChecks,
        issues: issues.length
      })

      return this.buildValidationResult(issues, totalChecks, passedChecks, failedChecks, warningChecks)

    } catch (error) {
      await BackupErrorHandler.handleError(error, {
        operation: 'validate',
        orgId,
        backupId
      })

      issues.push({
        type: 'error',
        category: 'system',
        message: 'Validation process failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        severity: 'critical'
      })

      return this.buildValidationResult(issues, totalChecks, passedChecks, failedChecks + 1, warningChecks)
    }
  }

  /**
   * Download and decrypt backup data
   */
  private static async downloadBackupData(metadata: BackupMetadata): Promise<{
    success: boolean
    data?: Record<string, unknown[]>
    rawData?: string
    error?: string
  }> {
    try {
      // Download backup file
      const { data: backupFile, error: downloadError } = await supabase.storage
        .from('backups')
        .download(metadata.storage_path)

      if (downloadError) {
        return { success: false, error: `Download failed: ${downloadError.message}` }
      }

      const rawData = await backupFile.text()

      // Decrypt if encrypted
      if (metadata.encryption_enabled && metadata.encryption_key_id && metadata.encryption_iv) {
        try {
          // This would use the BackupEncryptionManager to decrypt
          // For now, we'll assume the data is already decrypted for validation
          console.log('Decrypting backup data...')
        } catch (decryptError) {
          return { success: false, error: 'Decryption failed' }
        }
      }

      // Parse JSON data
      const data = JSON.parse(rawData)

      return { success: true, data, rawData }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Validate backup checksum
   */
  private static async validateChecksum(metadata: BackupMetadata, rawData: string): Promise<{
    valid: boolean
    details?: Record<string, unknown>
  }> {
    try {
      // Calculate checksum of the raw data
      const calculatedChecksum = await this.calculateChecksum(rawData)
      const expectedChecksum = metadata.checksum

      const valid = calculatedChecksum === expectedChecksum

      return {
        valid,
        details: {
          calculated: calculatedChecksum,
          expected: expectedChecksum,
          match: valid
        }
      }
    } catch (error) {
      return {
        valid: false,
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }

  /**
   * Calculate SHA-256 checksum
   */
  private static async calculateChecksum(data: string): Promise<string> {
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(data)
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Validate data integrity
   */
  private static async validateDataIntegrity(
    backupData: Record<string, unknown[]>,
    metadata: BackupMetadata
  ): Promise<{
    totalChecks: number
    passedChecks: number
    failedChecks: number
    warningChecks: number
    issues: ValidationIssue[]
  }> {
    const issues: ValidationIssue[] = []
    let totalChecks = 0
    let passedChecks = 0
    let failedChecks = 0
    let warningChecks = 0

    // Validate table count
    totalChecks++
    const actualTableCount = Object.keys(backupData).length
    const expectedTableCount = metadata.table_count

    if (actualTableCount === expectedTableCount) {
      passedChecks++
    } else {
      failedChecks++
      issues.push({
        type: 'error',
        category: 'integrity',
        message: `Table count mismatch: expected ${expectedTableCount}, got ${actualTableCount}`,
        severity: 'high'
      })
    }

    // Validate record count
    totalChecks++
    const actualRecordCount = Object.values(backupData).reduce(
      (sum, table) => sum + (Array.isArray(table) ? table.length : 0),
      0
    )
    const expectedRecordCount = metadata.record_count

    if (actualRecordCount === expectedRecordCount) {
      passedChecks++
    } else {
      const difference = Math.abs(actualRecordCount - expectedRecordCount)
      const percentageDiff = (difference / expectedRecordCount) * 100

      if (percentageDiff > 5) {
        failedChecks++
        issues.push({
          type: 'error',
          category: 'integrity',
          message: `Record count mismatch: expected ${expectedRecordCount}, got ${actualRecordCount}`,
          severity: 'high'
        })
      } else {
        warningChecks++
        issues.push({
          type: 'warning',
          category: 'integrity',
          message: `Minor record count difference: expected ${expectedRecordCount}, got ${actualRecordCount}`,
          severity: 'low'
        })
      }
    }

    // Validate data structure for each table
    for (const [tableName, tableData] of Object.entries(backupData)) {
      totalChecks++
      
      if (!Array.isArray(tableData)) {
        failedChecks++
        issues.push({
          type: 'error',
          category: 'structure',
          message: `Table '${tableName}' data is not an array`,
          severity: 'high'
        })
        continue
      }

      // Check for required fields in organization-specific tables
      if (['customers', 'vendors', 'invoices', 'bills', 'payments', 'accounts'].includes(tableName)) {
        const recordsWithoutOrgId = tableData.filter(record => 
          !record || typeof record !== 'object' || !('org_id' in record)
        )

        if (recordsWithoutOrgId.length > 0) {
          failedChecks++
          issues.push({
            type: 'error',
            category: 'structure',
            message: `Table '${tableName}' has ${recordsWithoutOrgId.length} records without org_id`,
            severity: 'high'
          })
        } else {
          passedChecks++
        }
      } else {
        passedChecks++
      }
    }

    return { totalChecks, passedChecks, failedChecks, warningChecks, issues }
  }

  /**
   * Validate backup completeness
   */
  private static async validateCompleteness(
    backupData: Record<string, unknown[]>,
    orgId: string
  ): Promise<{
    totalChecks: number
    passedChecks: number
    failedChecks: number
    warningChecks: number
    issues: ValidationIssue[]
  }> {
    const issues: ValidationIssue[] = []
    let totalChecks = 0
    let passedChecks = 0
    let failedChecks = 0
    let warningChecks = 0

    // Check if all required tables are present
    for (const requiredTable of this.REQUIRED_TABLES) {
      totalChecks++
      
      if (!(requiredTable in backupData)) {
        failedChecks++
        issues.push({
          type: 'error',
          category: 'completeness',
          message: `Required table '${requiredTable}' is missing from backup`,
          severity: 'high'
        })
      } else {
        passedChecks++
      }
    }

    // Check for unexpected empty tables (warning)
    for (const [tableName, tableData] of Object.entries(backupData)) {
      if (Array.isArray(tableData) && tableData.length === 0) {
        totalChecks++
        warningChecks++
        issues.push({
          type: 'warning',
          category: 'completeness',
          message: `Table '${tableName}' is empty`,
          severity: 'low'
        })
      }
    }

    return { totalChecks, passedChecks, failedChecks, warningChecks, issues }
  }

  /**
   * Validate encryption settings
   */
  private static async validateEncryption(metadata: BackupMetadata): Promise<{
    valid: boolean
    details?: Record<string, unknown>
  }> {
    try {
      if (!metadata.encryption_enabled) {
        return { valid: true, details: { encrypted: false } }
      }

      // Check if encryption key exists
      const { data: keyRecord, error } = await supabase
        .from('backup_encryption_keys')
        .select('id, algorithm, version')
        .eq('id', metadata.encryption_key_id)
        .single()

      if (error || !keyRecord) {
        return {
          valid: false,
          details: { error: 'Encryption key not found' }
        }
      }

      return {
        valid: true,
        details: {
          encrypted: true,
          key_id: keyRecord.id,
          algorithm: keyRecord.algorithm,
          version: keyRecord.version
        }
      }
    } catch (error) {
      return {
        valid: false,
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }

  /**
   * Perform deep validation (optional thorough checks)
   */
  private static async performDeepValidation(
    backupData: Record<string, unknown[]>,
    orgId: string
  ): Promise<{
    totalChecks: number
    passedChecks: number
    failedChecks: number
    warningChecks: number
    issues: ValidationIssue[]
  }> {
    const issues: ValidationIssue[] = []
    let totalChecks = 0
    let passedChecks = 0
    const failedChecks = 0
    let warningChecks = 0

    // Deep validation would include:
    // - Cross-reference validation between tables
    // - Data consistency checks
    // - Business rule validation
    // - Foreign key integrity checks

    // For now, we'll implement basic cross-reference validation
    totalChecks++

    // Example: Check if all invoices reference valid customers
    const customers = backupData.customers || []
    const invoices = backupData.invoices || []
    const customerIds = new Set(customers.map((c: Record<string, unknown>) => c.id))

    const invalidInvoices = invoices.filter((invoice: Record<string, unknown>) =>
      invoice.customer_id && !customerIds.has(invoice.customer_id)
    )

    if (invalidInvoices.length === 0) {
      passedChecks++
    } else {
      warningChecks++
      issues.push({
        type: 'warning',
        category: 'consistency',
        message: `Found ${invalidInvoices.length} invoices with invalid customer references`,
        severity: 'medium'
      })
    }

    return { totalChecks, passedChecks, failedChecks, warningChecks, issues }
  }

  /**
   * Build validation result
   */
  private static buildValidationResult(
    issues: ValidationIssue[],
    totalChecks: number,
    passedChecks: number,
    failedChecks: number,
    warningChecks: number
  ): ValidationResult {
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 0
    const valid = failedChecks === 0

    const summary: ValidationSummary = {
      totalChecks,
      passedChecks,
      failedChecks,
      warningChecks,
      checksumValid: !issues.some(i => i.category === 'checksum' && i.type === 'error'),
      dataIntegrityValid: !issues.some(i => i.category === 'integrity' && i.type === 'error'),
      completenessValid: !issues.some(i => i.category === 'completeness' && i.type === 'error'),
      encryptionValid: !issues.some(i => i.category === 'encryption' && i.type === 'error')
    }

    return {
      valid,
      score,
      issues,
      summary
    }
  }

  /**
   * Log validation results
   */
  private static async logValidationResults(
    backupId: string,
    orgId: string,
    results: {
      totalChecks: number
      passedChecks: number
      failedChecks: number
      warningChecks: number
      issues: number
    }
  ): Promise<void> {
    try {
      await auditLogger.logActivity({
        entity_type: 'backup_validation',
        entity_id: backupId,
        action: 'validation_completed',
        description: `Backup validation completed: ${results.passedChecks}/${results.totalChecks} checks passed`,
        severity: results.failedChecks > 0 ? 'warning' : 'info',
        category: 'system',
        metadata: results
      })
    } catch (error) {
      console.error('Failed to log validation results:', error)
    }
  }
}
