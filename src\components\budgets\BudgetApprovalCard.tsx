import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { AlertTriangle, TrendingUp, DollarSign, Calendar } from 'lucide-react'
import { BudgetStatus } from '@/hooks/queries/useBudgetValidation'

interface BudgetApprovalCardProps {
  budgetStatus: BudgetStatus | null
  billAmount: number
  billDescription?: string
  vendorName?: string
  className?: string
}

export function BudgetApprovalCard({ 
  budgetStatus, 
  billAmount, 
  billDescription,
  vendorName,
  className = ""
}: BudgetApprovalCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  if (!budgetStatus) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Budget Impact
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertDescription>
              No active budget found for this expense account. This bill will be processed without budget constraints.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  const projectedAmount = budgetStatus.actualAmount + billAmount
  const projectedUtilization = budgetStatus.budgetAmount > 0 
    ? (projectedAmount / budgetStatus.budgetAmount) * 100 
    : 0
  const exceedanceAmount = projectedAmount > budgetStatus.budgetAmount 
    ? projectedAmount - budgetStatus.budgetAmount 
    : 0

  const getImpactLevel = () => {
    if (projectedUtilization > 100) return 'exceeded'
    if (projectedUtilization >= 90) return 'critical'
    if (projectedUtilization >= 75) return 'warning'
    return 'normal'
  }

  const getImpactColor = () => {
    const level = getImpactLevel()
    switch (level) {
      case 'exceeded': return 'border-red-200 bg-red-50'
      case 'critical': return 'border-orange-200 bg-orange-50'
      case 'warning': return 'border-yellow-200 bg-yellow-50'
      default: return 'border-green-200 bg-green-50'
    }
  }

  const getImpactIcon = () => {
    const level = getImpactLevel()
    switch (level) {
      case 'exceeded': return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'critical': return <TrendingUp className="h-4 w-4 text-orange-600" />
      case 'warning': return <TrendingUp className="h-4 w-4 text-yellow-600" />
      default: return <DollarSign className="h-4 w-4 text-green-600" />
    }
  }

  const getImpactBadge = () => {
    const level = getImpactLevel()
    switch (level) {
      case 'exceeded': return <Badge variant="destructive">Budget Exceeded</Badge>
      case 'critical': return <Badge variant="secondary">Critical Impact</Badge>
      case 'warning': return <Badge variant="outline">High Impact</Badge>
      default: return <Badge variant="default">Within Budget</Badge>
    }
  }

  return (
    <Card className={`${className} ${getImpactColor()}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm flex items-center gap-2">
            {getImpactIcon()}
            Budget Impact Analysis
          </CardTitle>
          {getImpactBadge()}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Bill Details */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Bill Details:</div>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {vendorName && (
              <>
                <div className="text-muted-foreground">Vendor:</div>
                <div>{vendorName}</div>
              </>
            )}
            {billDescription && (
              <>
                <div className="text-muted-foreground">Description:</div>
                <div>{billDescription}</div>
              </>
            )}
            <div className="text-muted-foreground">Amount:</div>
            <div className="font-medium">{formatCurrency(billAmount)}</div>
          </div>
        </div>

        {/* Budget Information */}
        <div className="space-y-2 pt-2 border-t">
          <div className="text-sm font-medium">Budget Information:</div>
          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span>Account:</span>
              <span className="font-medium">{budgetStatus.accountName}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Budget Period:</span>
              <span className="text-muted-foreground">{budgetStatus.budgetPeriod.budgetName}</span>
            </div>
            <div className="flex justify-between text-sm text-muted-foreground">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                Period:
              </span>
              <span>
                {new Date(budgetStatus.budgetPeriod.startDate).toLocaleDateString()} - {new Date(budgetStatus.budgetPeriod.endDate).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Current vs Projected */}
        <div className="space-y-3 pt-2 border-t">
          <div className="text-sm font-medium">Budget Utilization:</div>
          
          {/* Current Status */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current Usage:</span>
              <span>{budgetStatus.utilizationPercent.toFixed(1)}%</span>
            </div>
            <Progress value={Math.min(budgetStatus.utilizationPercent, 100)} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Spent: {formatCurrency(budgetStatus.actualAmount)}</span>
              <span>Budget: {formatCurrency(budgetStatus.budgetAmount)}</span>
            </div>
          </div>

          {/* Projected Status */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>After This Bill:</span>
              <span className={projectedUtilization > 100 ? 'text-red-600 font-medium' : 'text-blue-600'}>
                {projectedUtilization.toFixed(1)}%
              </span>
            </div>
            <Progress 
              value={Math.min(projectedUtilization, 100)} 
              className="h-2"
            />
            <div className="flex justify-between text-xs">
              <span>New Total: {formatCurrency(projectedAmount)}</span>
              <span className={projectedAmount > budgetStatus.budgetAmount ? 'text-red-600' : 'text-green-600'}>
                Remaining: {formatCurrency(budgetStatus.budgetAmount - projectedAmount)}
              </span>
            </div>
          </div>
        </div>

        {/* Impact Alert */}
        {exceedanceAmount > 0 && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <div className="font-medium text-red-700">Budget Exceedance Alert</div>
                <div className="text-sm">
                  This bill will exceed the approved budget by <span className="font-medium">{formatCurrency(exceedanceAmount)}</span>
                </div>
                <div className="text-xs text-red-600">
                  Requires additional approval before processing
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Approval Recommendation */}
        <div className="pt-2 border-t">
          <div className="text-sm font-medium mb-2">Approval Recommendation:</div>
          <Alert className={getImpactLevel() === 'exceeded' ? 'border-red-200 bg-red-50' : 'border-blue-200 bg-blue-50'}>
            <AlertDescription className="text-sm">
              {getImpactLevel() === 'exceeded' ? (
                <>
                  <strong>Requires Budget Override Approval:</strong> This bill exceeds the approved budget and requires additional authorization from a budget administrator.
                </>
              ) : getImpactLevel() === 'critical' ? (
                <>
                  <strong>Recommend Review:</strong> This bill will bring budget utilization to a critical level. Consider reviewing necessity and timing.
                </>
              ) : (
                <>
                  <strong>Within Acceptable Limits:</strong> This bill can be processed under normal approval workflow.
                </>
              )}
            </AlertDescription>
          </Alert>
        </div>
      </CardContent>
    </Card>
  )
}
