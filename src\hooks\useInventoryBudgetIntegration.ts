import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'
import { queryKeys } from '@/lib/queryKeys'

export interface InventoryBudgetAlert {
  id: string
  product_id: string
  product_name: string
  product_sku: string
  budget_id: string
  budget_name: string
  account_id: string
  account_name: string
  budgeted_amount: number
  actual_amount: number
  utilization_percent: number
  alert_level: 'warning' | 'critical' | 'exceeded'
  message: string
  period_start: string
  period_end: string
}

export interface InventoryBudgetSummary {
  total_inventory_budget: number
  total_inventory_spent: number
  total_inventory_value: number
  utilization_percent: number
  alerts: InventoryBudgetAlert[]
  by_category: Array<{
    category_id: string | null
    category_name: string
    budgeted_amount: number
    actual_amount: number
    utilization_percent: number
  }>
  by_product: Array<{
    product_id: string
    product_name: string
    product_sku: string
    budgeted_amount: number
    actual_amount: number
    current_value: number
    utilization_percent: number
  }>
}

export interface InventoryPurchaseValidation {
  is_valid: boolean
  budget_available: number
  amount_requested: number
  exceedance_amount: number
  requires_approval: boolean
  alert_level: 'none' | 'warning' | 'critical' | 'exceeded'
  message: string
  budget_details?: {
    budget_id: string
    budget_name: string
    account_id: string
    account_name: string
    period_start: string
    period_end: string
  }
}

/**
 * Hook for integrating inventory with budget system
 */
export function useInventoryBudgetIntegration() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  // Get inventory budget summary
  const inventoryBudgetSummaryQuery = useQuery({
    queryKey: ['inventory-budget-summary', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const currentDate = new Date().toISOString().split('T')[0]

      // Get active budgets for inventory-related accounts
      const { data: budgetData, error: budgetError } = await supabase
        .from('budget_lines')
        .select(`
          amount,
          budget_id,
          account_id,
          accounts!inner(name, code, type),
          budgets!inner(
            id,
            name,
            start_date,
            end_date,
            status
          )
        `)
        .eq('budgets.status', 'approved')
        .lte('budgets.start_date', currentDate)
        .gte('budgets.end_date', currentDate)
        .in('accounts.type', ['asset', 'expense']) // Inventory and COGS accounts

      if (budgetError) throw budgetError

      // Get inventory-related account mappings
      const { data: inventoryAccounts, error: accountsError } = await supabase
        .from('account_mappings')
        .select('account_id, mapping_type')
        .eq('org_id', profile.org_id)
        .in('mapping_type', ['inventory_asset', 'cost_of_goods_sold'])

      if (accountsError) throw accountsError

      const inventoryAccountIds = inventoryAccounts?.map(a => a.account_id) || []

      // Filter budget lines for inventory accounts
      const inventoryBudgetLines = budgetData?.filter(line => 
        inventoryAccountIds.includes(line.account_id)
      ) || []

      let totalBudget = 0
      let totalSpent = 0
      const alerts: InventoryBudgetAlert[] = []

      // Calculate actual spending for each budget line
      for (const line of inventoryBudgetLines) {
        const budget = line.budgets
        
        // Get actual transactions for this account in the budget period
        const { data: transactions, error: transError } = await supabase
          .from('transaction_lines')
          .select('debit, credit')
          .eq('org_id', profile.org_id)
          .eq('account_id', line.account_id)
          .gte('created_at', `${budget.start_date}T00:00:00`)
          .lte('created_at', `${budget.end_date}T23:59:59`)

        if (transError) continue

        const actualAmount = transactions?.reduce((sum, t) => {
          // For asset accounts (inventory), debits increase the balance
          // For expense accounts (COGS), debits increase the expense
          return sum + (t.debit || 0) - (t.credit || 0)
        }, 0) || 0

        totalBudget += line.amount || 0
        totalSpent += actualAmount

        const utilizationPercent = line.amount > 0 ? (actualAmount / line.amount) * 100 : 0

        // Create alerts for high utilization
        if (utilizationPercent >= 75) {
          let alertLevel: 'warning' | 'critical' | 'exceeded' = 'warning'
          let message = ''

          if (utilizationPercent >= 100) {
            alertLevel = 'exceeded'
            message = `Budget exceeded by ${(utilizationPercent - 100).toFixed(1)}%`
          } else if (utilizationPercent >= 90) {
            alertLevel = 'critical'
            message = `Budget utilization at ${utilizationPercent.toFixed(1)}% - Critical level`
          } else {
            message = `Budget utilization at ${utilizationPercent.toFixed(1)}% - Warning level`
          }

          alerts.push({
            id: `${line.budget_id}-${line.account_id}`,
            product_id: '', // Not product-specific at this level
            product_name: '',
            product_sku: '',
            budget_id: line.budget_id,
            budget_name: budget.name,
            account_id: line.account_id,
            account_name: line.accounts.name,
            budgeted_amount: line.amount || 0,
            actual_amount: actualAmount,
            utilization_percent: utilizationPercent,
            alert_level: alertLevel,
            message,
            period_start: budget.start_date,
            period_end: budget.end_date
          })
        }
      }

      // Get current inventory value
      const { data: stockLevels, error: stockError } = await supabase
        .from('stock_levels')
        .select(`
          quantity_on_hand,
          average_cost,
          product:products(name, sku, category_id)
        `)
        .eq('org_id', profile.org_id)

      if (stockError) throw stockError

      const totalInventoryValue = stockLevels?.reduce((sum, sl) => 
        sum + ((sl.quantity_on_hand || 0) * (sl.average_cost || 0)), 0) || 0

      const summary: InventoryBudgetSummary = {
        total_inventory_budget: totalBudget,
        total_inventory_spent: totalSpent,
        total_inventory_value: totalInventoryValue,
        utilization_percent: totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0,
        alerts,
        by_category: [], // TODO: Implement category breakdown
        by_product: []   // TODO: Implement product breakdown
      }

      return summary
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Validate inventory purchase against budget
  const validateInventoryPurchaseMutation = useMutation({
    mutationFn: async ({
      productId,
      quantity,
      unitCost,
      accountId
    }: {
      productId?: string
      quantity: number
      unitCost: number
      accountId: string
    }) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      const totalAmount = quantity * unitCost
      const currentDate = new Date().toISOString().split('T')[0]

      // Get active budget for this account
      const { data: budgetLine, error: budgetError } = await supabase
        .from('budget_lines')
        .select(`
          amount,
          budget_id,
          account_id,
          accounts!inner(name, code),
          budgets!inner(
            id,
            name,
            start_date,
            end_date,
            status
          )
        `)
        .eq('account_id', accountId)
        .eq('budgets.status', 'approved')
        .lte('budgets.start_date', currentDate)
        .gte('budgets.end_date', currentDate)
        .order('budgets.created_at', { ascending: false })
        .limit(1)
        .single()

      if (budgetError || !budgetLine) {
        return {
          is_valid: true, // No budget constraint
          budget_available: 0,
          amount_requested: totalAmount,
          exceedance_amount: 0,
          requires_approval: false,
          alert_level: 'none' as const,
          message: 'No active budget found for this account'
        } as InventoryPurchaseValidation
      }

      // Get current spending for this budget period
      const { data: transactions, error: transError } = await supabase
        .from('transaction_lines')
        .select('debit, credit')
        .eq('org_id', profile.org_id)
        .eq('account_id', accountId)
        .gte('created_at', `${budgetLine.budgets.start_date}T00:00:00`)
        .lte('created_at', `${budgetLine.budgets.end_date}T23:59:59`)

      if (transError) throw transError

      const currentSpending = transactions?.reduce((sum, t) => 
        sum + (t.debit || 0) - (t.credit || 0), 0) || 0

      const budgetAmount = budgetLine.amount || 0
      const availableAmount = budgetAmount - currentSpending
      const exceedanceAmount = Math.max(0, totalAmount - availableAmount)
      const newUtilization = budgetAmount > 0 ? 
        ((currentSpending + totalAmount) / budgetAmount) * 100 : 0

      let alertLevel: 'none' | 'warning' | 'critical' | 'exceeded' = 'none'
      let requiresApproval = false
      let message = 'Purchase within budget limits'

      if (newUtilization >= 100) {
        alertLevel = 'exceeded'
        requiresApproval = true
        message = `Purchase exceeds budget by UGX ${exceedanceAmount.toLocaleString()}`
      } else if (newUtilization >= 90) {
        alertLevel = 'critical'
        requiresApproval = true
        message = `Purchase will bring budget utilization to ${newUtilization.toFixed(1)}% - Requires approval`
      } else if (newUtilization >= 75) {
        alertLevel = 'warning'
        message = `Purchase will bring budget utilization to ${newUtilization.toFixed(1)}% - Warning level`
      }

      return {
        is_valid: exceedanceAmount === 0,
        budget_available: availableAmount,
        amount_requested: totalAmount,
        exceedance_amount: exceedanceAmount,
        requires_approval: requiresApproval,
        alert_level: alertLevel,
        message,
        budget_details: {
          budget_id: budgetLine.budget_id,
          budget_name: budgetLine.budgets.name,
          account_id: budgetLine.account_id,
          account_name: budgetLine.accounts.name,
          period_start: budgetLine.budgets.start_date,
          period_end: budgetLine.budgets.end_date
        }
      } as InventoryPurchaseValidation
    }
  })

  // Create inventory budget alert
  const createInventoryBudgetAlertMutation = useMutation({
    mutationFn: async ({
      productId,
      transactionId,
      alertLevel,
      message
    }: {
      productId: string
      transactionId: string
      alertLevel: 'warning' | 'critical' | 'exceeded'
      message: string
    }) => {
      if (!profile?.org_id) throw new Error('No organization ID')

      // This would create a custom inventory budget alert
      // For now, we'll just log it and show a toast
      console.log('Inventory budget alert:', {
        productId,
        transactionId,
        alertLevel,
        message
      })

      if (alertLevel === 'exceeded') {
        toast.error(`Budget Alert: ${message}`)
      } else if (alertLevel === 'critical') {
        toast.error(`Budget Warning: ${message}`)
      } else {
        toast.error(`Budget Notice: ${message}`)
      }
    }
  })

  return {
    // Queries
    inventoryBudgetSummary: inventoryBudgetSummaryQuery.data,
    
    // Loading states
    isLoadingSummary: inventoryBudgetSummaryQuery.isLoading,
    
    // Mutations
    validateInventoryPurchase: validateInventoryPurchaseMutation.mutateAsync,
    createInventoryBudgetAlert: createInventoryBudgetAlertMutation.mutateAsync,
    
    // Mutation states
    isValidatingPurchase: validateInventoryPurchaseMutation.isPending,
    isCreatingAlert: createInventoryBudgetAlertMutation.isPending,
    
    // Refetch functions
    refetchSummary: inventoryBudgetSummaryQuery.refetch
  }
}
