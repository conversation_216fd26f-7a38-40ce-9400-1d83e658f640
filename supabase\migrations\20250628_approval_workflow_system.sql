-- =====================================================
-- UNIFIED APPROVAL WORKFLOW SYSTEM
-- Migration: 20250628_approval_workflow_system.sql
-- =====================================================

-- Create enums for approval workflow system
CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected', 'escalated', 'delegated', 'cancelled');
CREATE TYPE workflow_step_type AS ENUM ('sequential', 'parallel', 'conditional');
CREATE TYPE document_type AS ENUM ('invoice', 'bill', 'payment', 'budget', 'journal');
CREATE TYPE escalation_type AS ENUM ('time_based', 'amount_based', 'role_based');
CREATE TYPE notification_channel AS ENUM ('email', 'in_app', 'sms', 'push');

-- =====================================================
-- WORKFLOW TEMPLATES TABLE
-- =====================================================
CREATE TABLE workflow_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    document_type document_type NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    version INTEGER DEFAULT 1,
    created_by UUID REFERENCES auth.users(id),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure only one default template per document type per organization
    CONSTRAINT unique_default_template UNIQUE (org_id, document_type, is_default) 
        DEFERRABLE INITIALLY DEFERRED
);

-- Add indexes for workflow templates
CREATE INDEX idx_workflow_templates_org_id ON workflow_templates(org_id);
CREATE INDEX idx_workflow_templates_document_type ON workflow_templates(document_type);
CREATE INDEX idx_workflow_templates_active ON workflow_templates(is_active);

-- =====================================================
-- APPROVAL RULES TABLE
-- =====================================================
CREATE TABLE approval_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_template_id UUID NOT NULL REFERENCES workflow_templates(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    priority INTEGER DEFAULT 0, -- Higher number = higher priority
    is_active BOOLEAN DEFAULT true,
    
    -- Rule conditions (JSON for flexibility)
    conditions JSONB NOT NULL DEFAULT '{}',
    -- Example: {"amount_min": 1000, "amount_max": 5000, "vendor_type": "critical"}
    
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for approval rules
CREATE INDEX idx_approval_rules_workflow_template ON approval_rules(workflow_template_id);
CREATE INDEX idx_approval_rules_org_id ON approval_rules(org_id);
CREATE INDEX idx_approval_rules_priority ON approval_rules(priority DESC);
CREATE INDEX idx_approval_rules_conditions ON approval_rules USING GIN(conditions);

-- =====================================================
-- APPROVAL STEPS TABLE
-- =====================================================
CREATE TABLE approval_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_template_id UUID NOT NULL REFERENCES workflow_templates(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    step_type workflow_step_type DEFAULT 'sequential',
    
    -- Required approver criteria
    required_role user_role[], -- Array of roles that can approve this step
    required_approvers INTEGER DEFAULT 1, -- Number of approvers needed
    allow_self_approval BOOLEAN DEFAULT false,
    
    -- Escalation settings
    escalation_enabled BOOLEAN DEFAULT true,
    escalation_timeout_hours INTEGER DEFAULT 24,
    escalation_type escalation_type DEFAULT 'time_based',
    escalation_to_role user_role,
    
    -- Delegation settings
    allow_delegation BOOLEAN DEFAULT true,
    
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique step order within workflow template
    CONSTRAINT unique_step_order UNIQUE (workflow_template_id, step_order)
);

-- Add indexes for approval steps
CREATE INDEX idx_approval_steps_workflow_template ON approval_steps(workflow_template_id);
CREATE INDEX idx_approval_steps_org_id ON approval_steps(org_id);
CREATE INDEX idx_approval_steps_order ON approval_steps(workflow_template_id, step_order);

-- =====================================================
-- ROLE APPROVAL LIMITS TABLE
-- =====================================================
CREATE TABLE role_approval_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role user_role NOT NULL,
    document_type document_type NOT NULL,
    currency_code VARCHAR(3) DEFAULT 'UGX',
    amount_limit DECIMAL(15,2), -- NULL means unlimited
    
    -- Time-based limits
    daily_limit DECIMAL(15,2),
    monthly_limit DECIMAL(15,2),
    
    -- Effective date range
    effective_from DATE DEFAULT CURRENT_DATE,
    effective_to DATE,
    
    is_active BOOLEAN DEFAULT true,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure no overlapping active limits for same role/document type
    CONSTRAINT unique_active_role_limits UNIQUE (org_id, role, document_type, currency_code, effective_from)
);

-- Add indexes for role approval limits
CREATE INDEX idx_role_approval_limits_org_id ON role_approval_limits(org_id);
CREATE INDEX idx_role_approval_limits_role ON role_approval_limits(role);
CREATE INDEX idx_role_approval_limits_document_type ON role_approval_limits(document_type);
CREATE INDEX idx_role_approval_limits_effective ON role_approval_limits(effective_from, effective_to);

-- =====================================================
-- APPROVAL INSTANCES TABLE
-- =====================================================
CREATE TABLE approval_instances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_template_id UUID NOT NULL REFERENCES workflow_templates(id),
    
    -- Document reference (polymorphic)
    document_type document_type NOT NULL,
    document_id UUID NOT NULL,
    document_amount DECIMAL(15,2),
    currency_code VARCHAR(3) DEFAULT 'UGX',
    
    -- Workflow state
    status approval_status DEFAULT 'pending',
    current_step_order INTEGER DEFAULT 1,
    total_steps INTEGER NOT NULL,
    
    -- Submitter information
    submitted_by UUID NOT NULL REFERENCES auth.users(id),
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Completion information
    completed_at TIMESTAMP WITH TIME ZONE,
    completed_by UUID REFERENCES auth.users(id),
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}',
    notes TEXT,
    
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for approval instances
CREATE INDEX idx_approval_instances_org_id ON approval_instances(org_id);
CREATE INDEX idx_approval_instances_document ON approval_instances(document_type, document_id);
CREATE INDEX idx_approval_instances_status ON approval_instances(status);
CREATE INDEX idx_approval_instances_submitted_by ON approval_instances(submitted_by);
CREATE INDEX idx_approval_instances_current_step ON approval_instances(current_step_order);
CREATE INDEX idx_approval_instances_submitted_at ON approval_instances(submitted_at);

-- =====================================================
-- APPROVAL ACTIONS TABLE
-- =====================================================
CREATE TABLE approval_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    approval_instance_id UUID NOT NULL REFERENCES approval_instances(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL,
    
    -- Approver information
    approver_id UUID NOT NULL REFERENCES auth.users(id),
    approver_role user_role NOT NULL,
    
    -- Action details
    action approval_status NOT NULL, -- approved, rejected, delegated
    comments TEXT,
    rejection_reason TEXT,
    
    -- Delegation information (if action = 'delegated')
    delegated_to UUID REFERENCES auth.users(id),
    delegation_reason TEXT,
    delegation_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Timing information
    action_taken_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- IP and audit information
    ip_address INET,
    user_agent TEXT,
    
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for approval actions
CREATE INDEX idx_approval_actions_instance ON approval_actions(approval_instance_id);
CREATE INDEX idx_approval_actions_approver ON approval_actions(approver_id);
CREATE INDEX idx_approval_actions_org_id ON approval_actions(org_id);
CREATE INDEX idx_approval_actions_action ON approval_actions(action);
CREATE INDEX idx_approval_actions_taken_at ON approval_actions(action_taken_at);
CREATE INDEX idx_approval_actions_step_order ON approval_actions(approval_instance_id, step_order);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all approval workflow tables
ALTER TABLE workflow_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_approval_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_actions ENABLE ROW LEVEL SECURITY;

-- Workflow Templates Policies
CREATE POLICY "Users can view workflow templates in their organization" ON workflow_templates
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Admins can manage workflow templates" ON workflow_templates
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles
        WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Approval Rules Policies
CREATE POLICY "Users can view approval rules in their organization" ON approval_rules
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Admins can manage approval rules" ON approval_rules
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles
        WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Approval Steps Policies
CREATE POLICY "Users can view approval steps in their organization" ON approval_steps
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Admins can manage approval steps" ON approval_steps
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles
        WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Role Approval Limits Policies
CREATE POLICY "Users can view role approval limits in their organization" ON role_approval_limits
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Admins can manage role approval limits" ON role_approval_limits
    FOR ALL USING (org_id IN (
        SELECT org_id FROM profiles
        WHERE id = auth.uid() AND role IN ('owner', 'admin')
    ));

-- Approval Instances Policies
CREATE POLICY "Users can view approval instances in their organization" ON approval_instances
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can create approval instances" ON approval_instances
    FOR INSERT WITH CHECK (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()) AND
        submitted_by = auth.uid()
    );

CREATE POLICY "Admins and approvers can update approval instances" ON approval_instances
    FOR UPDATE USING (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()) AND
        (
            -- Admin can update any instance
            EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('owner', 'admin')) OR
            -- User can update instances they submitted
            submitted_by = auth.uid() OR
            -- User can update if they have approval authority for current step
            EXISTS (
                SELECT 1 FROM approval_steps s
                WHERE s.workflow_template_id = approval_instances.workflow_template_id
                AND s.step_order = approval_instances.current_step_order
                AND (SELECT role FROM profiles WHERE id = auth.uid()) = ANY(s.required_role)
            )
        )
    );

-- Approval Actions Policies
CREATE POLICY "Users can view approval actions in their organization" ON approval_actions
    FOR SELECT USING (org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Authorized users can create approval actions" ON approval_actions
    FOR INSERT WITH CHECK (
        org_id IN (SELECT org_id FROM profiles WHERE id = auth.uid()) AND
        approver_id = auth.uid()
    );

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_workflow_templates_updated_at BEFORE UPDATE ON workflow_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_approval_rules_updated_at BEFORE UPDATE ON approval_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_approval_steps_updated_at BEFORE UPDATE ON approval_steps
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_role_approval_limits_updated_at BEFORE UPDATE ON role_approval_limits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_approval_instances_updated_at BEFORE UPDATE ON approval_instances
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get next approval step
CREATE OR REPLACE FUNCTION get_next_approval_step(
    p_workflow_template_id UUID,
    p_current_step_order INTEGER
)
RETURNS TABLE (
    step_id UUID,
    step_order INTEGER,
    name VARCHAR(255),
    required_role user_role[],
    required_approvers INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.id,
        s.step_order,
        s.name,
        s.required_role,
        s.required_approvers
    FROM approval_steps s
    WHERE s.workflow_template_id = p_workflow_template_id
    AND s.step_order > p_current_step_order
    ORDER BY s.step_order
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can approve at current step
CREATE OR REPLACE FUNCTION can_user_approve_step(
    p_user_id UUID,
    p_approval_instance_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    user_role user_role;
    step_roles user_role[];
    instance_record approval_instances%ROWTYPE;
    step_record approval_steps%ROWTYPE;
BEGIN
    -- Get user role
    SELECT role INTO user_role FROM profiles WHERE id = p_user_id;

    -- Get approval instance
    SELECT * INTO instance_record FROM approval_instances WHERE id = p_approval_instance_id;

    -- Get current step requirements
    SELECT * INTO step_record
    FROM approval_steps
    WHERE workflow_template_id = instance_record.workflow_template_id
    AND step_order = instance_record.current_step_order;

    -- Check if user role is in required roles
    RETURN user_role = ANY(step_record.required_role);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to advance approval workflow
CREATE OR REPLACE FUNCTION advance_approval_workflow(
    p_approval_instance_id UUID,
    p_action approval_status,
    p_approver_id UUID,
    p_comments TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    instance_record approval_instances%ROWTYPE;
    next_step_order INTEGER;
    total_steps INTEGER;
BEGIN
    -- Get current instance
    SELECT * INTO instance_record FROM approval_instances WHERE id = p_approval_instance_id;

    -- If rejected or cancelled, mark as complete
    IF p_action IN ('rejected', 'cancelled') THEN
        UPDATE approval_instances
        SET
            status = p_action,
            completed_at = NOW(),
            completed_by = p_approver_id
        WHERE id = p_approval_instance_id;
        RETURN TRUE;
    END IF;

    -- If approved, check if there are more steps
    IF p_action = 'approved' THEN
        SELECT COUNT(*) INTO total_steps
        FROM approval_steps
        WHERE workflow_template_id = instance_record.workflow_template_id;

        next_step_order := instance_record.current_step_order + 1;

        IF next_step_order > total_steps THEN
            -- Workflow complete
            UPDATE approval_instances
            SET
                status = 'approved',
                completed_at = NOW(),
                completed_by = p_approver_id
            WHERE id = p_approval_instance_id;
        ELSE
            -- Move to next step
            UPDATE approval_instances
            SET
                current_step_order = next_step_order,
                status = 'pending'
            WHERE id = p_approval_instance_id;
        END IF;

        RETURN TRUE;
    END IF;

    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- SEED DATA - DEFAULT APPROVAL LIMITS
-- =====================================================

-- Note: These will be inserted for each organization when they're created
-- This is a template for the default role approval limits

-- Function to create default approval limits for an organization
CREATE OR REPLACE FUNCTION create_default_approval_limits(p_org_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Default approval limits for different roles and document types
    INSERT INTO role_approval_limits (org_id, role, document_type, amount_limit, daily_limit, monthly_limit)
    VALUES
    -- Invoice limits
    (p_org_id, 'accountant', 'invoice', 1000000, 5000000, ********), -- 1M UGX per invoice, 5M daily, 20M monthly
    (p_org_id, 'admin', 'invoice', 5000000, ********, *********),     -- 5M UGX per invoice, 25M daily, 100M monthly
    (p_org_id, 'owner', 'invoice', NULL, NULL, NULL),                 -- Unlimited

    -- Bill limits
    (p_org_id, 'accountant', 'bill', 1000000, 5000000, ********),     -- 1M UGX per bill
    (p_org_id, 'admin', 'bill', 5000000, ********, *********),        -- 5M UGX per bill
    (p_org_id, 'owner', 'bill', NULL, NULL, NULL),                    -- Unlimited

    -- Payment limits
    (p_org_id, 'accountant', 'payment', 500000, 2500000, ********),   -- 500K UGX per payment
    (p_org_id, 'admin', 'payment', 2500000, ********, ********),      -- 2.5M UGX per payment
    (p_org_id, 'owner', 'payment', NULL, NULL, NULL),                 -- Unlimited

    -- Budget limits
    (p_org_id, 'admin', 'budget', NULL, NULL, NULL),                  -- Admin can approve any budget
    (p_org_id, 'owner', 'budget', NULL, NULL, NULL);                  -- Owner can approve any budget
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create default workflow templates for an organization
CREATE OR REPLACE FUNCTION create_default_workflow_templates(p_org_id UUID, p_created_by UUID)
RETURNS VOID AS $$
DECLARE
    invoice_template_id UUID;
    bill_template_id UUID;
    payment_template_id UUID;
    budget_template_id UUID;
BEGIN
    -- Create default invoice approval workflow
    INSERT INTO workflow_templates (name, description, document_type, is_default, org_id, created_by)
    VALUES ('Standard Invoice Approval', 'Default approval workflow for invoices', 'invoice', true, p_org_id, p_created_by)
    RETURNING id INTO invoice_template_id;

    -- Invoice approval steps
    INSERT INTO approval_steps (workflow_template_id, step_order, name, required_role, required_approvers, org_id)
    VALUES
    (invoice_template_id, 1, 'Accountant Review', ARRAY['accountant', 'admin', 'owner']::user_role[], 1, p_org_id),
    (invoice_template_id, 2, 'Manager Approval', ARRAY['admin', 'owner']::user_role[], 1, p_org_id);

    -- Create default bill approval workflow
    INSERT INTO workflow_templates (name, description, document_type, is_default, org_id, created_by)
    VALUES ('Standard Bill Approval', 'Default approval workflow for bills', 'bill', true, p_org_id, p_created_by)
    RETURNING id INTO bill_template_id;

    -- Bill approval steps
    INSERT INTO approval_steps (workflow_template_id, step_order, name, required_role, required_approvers, org_id)
    VALUES
    (bill_template_id, 1, 'Accountant Review', ARRAY['accountant', 'admin', 'owner']::user_role[], 1, p_org_id),
    (bill_template_id, 2, 'Manager Approval', ARRAY['admin', 'owner']::user_role[], 1, p_org_id);

    -- Create default payment approval workflow
    INSERT INTO workflow_templates (name, description, document_type, is_default, org_id, created_by)
    VALUES ('Standard Payment Approval', 'Default approval workflow for payments', 'payment', true, p_org_id, p_created_by)
    RETURNING id INTO payment_template_id;

    -- Payment approval steps (more restrictive)
    INSERT INTO approval_steps (workflow_template_id, step_order, name, required_role, required_approvers, org_id)
    VALUES
    (payment_template_id, 1, 'Accountant Review', ARRAY['accountant', 'admin', 'owner']::user_role[], 1, p_org_id),
    (payment_template_id, 2, 'Manager Approval', ARRAY['admin', 'owner']::user_role[], 1, p_org_id),
    (payment_template_id, 3, 'Final Authorization', ARRAY['owner']::user_role[], 1, p_org_id);

    -- Create default budget approval workflow
    INSERT INTO workflow_templates (name, description, document_type, is_default, org_id, created_by)
    VALUES ('Standard Budget Approval', 'Default approval workflow for budgets', 'budget', true, p_org_id, p_created_by)
    RETURNING id INTO budget_template_id;

    -- Budget approval steps
    INSERT INTO approval_steps (workflow_template_id, step_order, name, required_role, required_approvers, org_id)
    VALUES
    (budget_template_id, 1, 'Manager Review', ARRAY['admin', 'owner']::user_role[], 1, p_org_id),
    (budget_template_id, 2, 'Executive Approval', ARRAY['owner']::user_role[], 1, p_org_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Add comment to track migration
COMMENT ON SCHEMA public IS 'Unified Approval Workflow System - Migration 20250628 completed';
