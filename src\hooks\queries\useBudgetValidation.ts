import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'

export interface BudgetStatus {
  accountId: string
  accountName: string
  accountCode: string
  budgetAmount: number
  actualAmount: number
  remainingAmount: number
  utilizationPercent: number
  isOverBudget: boolean
  budgetPeriod: {
    startDate: string
    endDate: string
    budgetName: string
  }
}

export interface BudgetValidationResult {
  isValid: boolean
  budgetStatus: BudgetStatus | null
  wouldExceedBudget: boolean
  exceedanceAmount: number
  message: string
}

/**
 * Hook to get current budget status for an account
 */
export const useBudgetStatus = (accountId: string | null, enabled = true) => {
  const { profile } = useAuth()

  return useQuery({
    queryKey: queryKeys.budgets.accountStatus(profile?.org_id || '', accountId || ''),
    queryFn: async (): Promise<BudgetStatus | null> => {
      if (!profile?.org_id || !accountId) return null

      // Get current active budgets for this account
      const currentDate = new Date().toISOString().split('T')[0]
      
      const { data: budgetLines, error } = await supabase
        .from('budget_lines')
        .select(`
          amount,
          budget_id,
          account_id,
          accounts!inner(name, code),
          budgets!inner(
            name,
            start_date,
            end_date,
            status
          )
        `)
        .eq('account_id', accountId)
        .eq('budgets.status', 'approved')
        .lte('budgets.start_date', currentDate)
        .gte('budgets.end_date', currentDate)
        .order('budgets.created_at', { ascending: false })
        .limit(1)

      if (error) throw error
      if (!budgetLines || budgetLines.length === 0) return null

      const budgetLine = budgetLines[0]
      const budget = budgetLine.budgets
      const account = budgetLine.accounts

      // Get actual spending for this account in the budget period
      const { data: transactions, error: transError } = await supabase
        .from('transaction_lines')
        .select('debit, credit')
        .eq('org_id', profile.org_id)
        .eq('account_id', accountId)
        .gte('created_at', `${budget.start_date}T00:00:00`)
        .lte('created_at', `${budget.end_date}T23:59:59`)

      if (transError) throw transError

      // Calculate actual amount based on account type
      const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
      const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0
      
      // For expense accounts, actual = debits - credits
      const actualAmount = debitTotal - creditTotal
      const budgetAmount = budgetLine.amount || 0
      const remainingAmount = budgetAmount - actualAmount
      const utilizationPercent = budgetAmount > 0 ? (actualAmount / budgetAmount) * 100 : 0

      return {
        accountId,
        accountName: account.name,
        accountCode: account.code,
        budgetAmount,
        actualAmount,
        remainingAmount,
        utilizationPercent,
        isOverBudget: actualAmount > budgetAmount,
        budgetPeriod: {
          startDate: budget.start_date,
          endDate: budget.end_date,
          budgetName: budget.name
        }
      }
    },
    enabled: enabled && !!profile?.org_id && !!accountId,
    staleTime: 30 * 1000, // 30 seconds - frequent updates for budget monitoring
  })
}

/**
 * Hook to validate if a bill amount would exceed budget
 */
export const useBudgetValidation = (accountId: string | null, billAmount: number) => {
  const { data: budgetStatus, isLoading } = useBudgetStatus(accountId)

  const validateBudget = (): BudgetValidationResult => {
    if (!budgetStatus) {
      return {
        isValid: true, // No budget constraint
        budgetStatus: null,
        wouldExceedBudget: false,
        exceedanceAmount: 0,
        message: 'No active budget found for this account'
      }
    }

    const newTotal = budgetStatus.actualAmount + billAmount
    const wouldExceedBudget = newTotal > budgetStatus.budgetAmount
    const exceedanceAmount = wouldExceedBudget ? newTotal - budgetStatus.budgetAmount : 0

    return {
      isValid: !wouldExceedBudget,
      budgetStatus,
      wouldExceedBudget,
      exceedanceAmount,
      message: wouldExceedBudget 
        ? `This bill would exceed the budget by ${exceedanceAmount.toLocaleString()}`
        : `Budget remaining: ${budgetStatus.remainingAmount.toLocaleString()}`
    }
  }

  return {
    validation: validateBudget(),
    isLoading,
    budgetStatus
  }
}
