# Database Schema

Complete database schema documentation for the notification system.

## Table of Contents

1. [Overview](#overview)
2. [Core Tables](#core-tables)
3. [Preference Tables](#preference-tables)
4. [Analytics Tables](#analytics-tables)
5. [Integration Tables](#integration-tables)
6. [Functions](#functions)
7. [Triggers](#triggers)
8. [Indexes](#indexes)
9. [RLS Policies](#rls-policies)

## Overview

The notification system uses a PostgreSQL database with the following key features:

- **Row Level Security (RLS)**: Organization-based data isolation
- **Real-time Subscriptions**: Supabase real-time for live updates
- **Optimized Indexes**: Performance-tuned for common queries
- **Database Functions**: Server-side logic for complex operations
- **Audit Trails**: Comprehensive tracking of changes

## Core Tables

### notifications

Primary table storing all notification data.

```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type TEXT NOT NULL,
    category TEXT NOT NULL,
    priority TEXT NOT NULL DEFAULT 'normal',
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    entity_type TEXT,
    entity_id TEXT,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_type CHECK (type IN (
        'payment_pending_approval', 'payment_approved', 'payment_rejected',
        'invoice_overdue', 'invoice_due_soon', 'invoice_paid',
        'bill_due_soon', 'bill_overdue',
        'budget_exceeded', 'budget_warning',
        'user_invited', 'backup_completed', 'backup_failed',
        'system_maintenance', 'audit_alert'
    )),
    CONSTRAINT valid_category CHECK (category IN ('financial', 'system', 'security', 'user')),
    CONSTRAINT valid_priority CHECK (priority IN ('urgent', 'high', 'normal', 'low'))
);
```

**Key Features:**
- Supports both user-specific and organization-wide notifications
- JSONB data field for flexible metadata storage
- Audit trail with created/updated timestamps
- Entity linking for related business objects

### notification_templates

Templates for generating consistent notification content.

```sql
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type TEXT NOT NULL UNIQUE,
    title_template TEXT NOT NULL,
    message_template TEXT NOT NULL,
    email_subject_template TEXT,
    email_html_template TEXT,
    variables JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Template Variables:**
Templates support variable substitution using `{{variable_name}}` syntax:
- `{{user_name}}` - Recipient's name
- `{{org_name}}` - Organization name
- `{{amount}}` - Financial amounts
- `{{entity_name}}` - Related entity name
- Custom variables from notification data

## Preference Tables

### notification_preferences

User-specific notification preferences.

```sql
CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL,
    enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT true,
    in_app_enabled BOOLEAN DEFAULT true,
    push_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, notification_type)
);
```

### notification_schedules

Scheduled notification delivery settings.

```sql
CREATE TABLE notification_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    digest_frequency TEXT DEFAULT 'daily' CHECK (digest_frequency IN ('immediate', 'hourly', 'daily', 'weekly')),
    digest_time TIME DEFAULT '09:00:00',
    digest_timezone TEXT DEFAULT 'UTC',
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    weekend_delivery BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, org_id)
);
```

## Analytics Tables

### email_deliveries

Email delivery tracking and analytics.

```sql
CREATE TABLE email_deliveries (
    id TEXT PRIMARY KEY,
    email TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    bounced_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    message_id TEXT,
    template_type TEXT,
    recipient_data JSONB DEFAULT '{}',
    tracking_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_status CHECK (status IN ('pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed'))
);
```

### push_deliveries

Push notification delivery tracking.

```sql
CREATE TABLE push_deliveries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id UUID NOT NULL REFERENCES push_subscriptions(id) ON DELETE CASCADE,
    notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
    payload JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_status CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'expired'))
);
```

### notification_analytics

Aggregated analytics data for performance optimization.

```sql
CREATE TABLE notification_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    notification_type TEXT,
    total_sent INTEGER DEFAULT 0,
    total_delivered INTEGER DEFAULT 0,
    total_opened INTEGER DEFAULT 0,
    total_clicked INTEGER DEFAULT 0,
    total_failed INTEGER DEFAULT 0,
    avg_delivery_time INTERVAL,
    avg_read_time INTERVAL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(org_id, date, notification_type)
);
```

## Integration Tables

### push_subscriptions

Push notification subscriptions.

```sql
CREATE TABLE push_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    endpoint TEXT NOT NULL,
    p256dh_key TEXT NOT NULL,
    auth_key TEXT NOT NULL,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, endpoint)
);
```

### notification_integrations

External service integrations.

```sql
CREATE TABLE notification_integrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    name TEXT NOT NULL,
    config JSONB NOT NULL DEFAULT '{}',
    notification_types TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_type CHECK (type IN ('slack', 'teams', 'webhook', 'sms')),
    UNIQUE(org_id, name)
);
```

### integration_deliveries

Integration delivery tracking.

```sql
CREATE TABLE integration_deliveries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID NOT NULL REFERENCES notification_integrations(id) ON DELETE CASCADE,
    notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    response_code INTEGER,
    response_body TEXT,
    error_message TEXT,
    sent_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_status CHECK (status IN ('pending', 'sent', 'failed', 'retrying'))
);
```

## Functions

### get_notification_analytics()

Generates comprehensive analytics for an organization.

```sql
CREATE OR REPLACE FUNCTION get_notification_analytics(
    org_id_param UUID,
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS JSONB AS $$
DECLARE
    analytics JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_sent', COUNT(*),
        'delivered', COUNT(*) FILTER (WHERE is_read = true OR delivered_at IS NOT NULL),
        'opened', COUNT(*) FILTER (WHERE is_read = true),
        'failed', COUNT(*) FILTER (WHERE status = 'failed'),
        'delivery_rate', ROUND((COUNT(*) FILTER (WHERE is_read = true)::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 2),
        'avg_read_time', AVG(EXTRACT(EPOCH FROM (read_at - created_at))) FILTER (WHERE read_at IS NOT NULL),
        'by_type', jsonb_object_agg(type, type_count),
        'by_priority', jsonb_object_agg(priority, priority_count)
    ) INTO analytics
    FROM (
        SELECT 
            type,
            priority,
            is_read,
            read_at,
            created_at,
            COUNT(*) OVER (PARTITION BY type) as type_count,
            COUNT(*) OVER (PARTITION BY priority) as priority_count
        FROM notifications
        WHERE org_id = org_id_param
        AND DATE(created_at) BETWEEN start_date AND end_date
    ) subq;
    
    RETURN COALESCE(analytics, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### cleanup_old_notifications()

Automated cleanup of old notifications.

```sql
CREATE OR REPLACE FUNCTION cleanup_old_notifications(
    days_old INTEGER DEFAULT 365,
    batch_size INTEGER DEFAULT 1000
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    batch_deleted INTEGER;
BEGIN
    LOOP
        DELETE FROM notifications 
        WHERE id IN (
            SELECT id FROM notifications
            WHERE created_at < NOW() - INTERVAL '1 day' * days_old
            AND is_archived = true
            LIMIT batch_size
        );
        
        GET DIAGNOSTICS batch_deleted = ROW_COUNT;
        deleted_count := deleted_count + batch_deleted;
        
        EXIT WHEN batch_deleted = 0;
        
        -- Pause between batches to avoid blocking
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Triggers

### update_notification_updated_at

Automatically updates the `updated_at` timestamp.

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### notification_read_trigger

Automatically sets `read_at` when `is_read` becomes true.

```sql
CREATE OR REPLACE FUNCTION set_notification_read_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_read = true AND OLD.is_read = false THEN
        NEW.read_at = NOW();
    ELSIF NEW.is_read = false THEN
        NEW.read_at = NULL;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER notification_read_trigger
    BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION set_notification_read_at();
```

## Indexes

Performance-optimized indexes for common query patterns:

```sql
-- Core notification queries
CREATE INDEX idx_notifications_user_org ON notifications(user_id, org_id);
CREATE INDEX idx_notifications_org_created ON notifications(org_id, created_at DESC);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = false;
CREATE INDEX idx_notifications_type_org ON notifications(type, org_id);
CREATE INDEX idx_notifications_entity ON notifications(entity_type, entity_id);

-- Analytics queries
CREATE INDEX idx_notifications_analytics ON notifications(org_id, created_at, type, is_read);
CREATE INDEX idx_email_deliveries_org_date ON email_deliveries(org_id, created_at DESC);
CREATE INDEX idx_push_deliveries_date ON push_deliveries(created_at DESC);

-- Real-time queries
CREATE INDEX idx_notifications_realtime ON notifications(org_id, updated_at DESC) WHERE is_archived = false;

-- Cleanup queries
CREATE INDEX idx_notifications_cleanup ON notifications(created_at, is_archived) WHERE is_archived = true;
```

## RLS Policies

Row Level Security policies ensure data isolation:

```sql
-- Notifications policies
CREATE POLICY "notifications_select_policy" ON notifications
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        (user_id = auth.uid()) OR
        (user_id IS NULL AND org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid()))
    );

CREATE POLICY "notifications_insert_policy" ON notifications
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role' OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "notifications_update_policy" ON notifications
    FOR UPDATE USING (
        auth.role() = 'service_role' OR
        (user_id = auth.uid()) OR
        (user_id IS NULL AND org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid()))
    );

-- Preferences policies
CREATE POLICY "notification_preferences_policy" ON notification_preferences
    FOR ALL USING (user_id = auth.uid());

-- Analytics policies (admin only)
CREATE POLICY "analytics_policy" ON notification_analytics
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        org_id IN (
            SELECT p.org_id FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role IN ('admin', 'owner')
        )
    );
```

## Migration Scripts

Database migrations are located in `supabase/migrations/` and should be applied in order:

1. `20250628_notifications_system.sql` - Core notification system
2. `20250628_email_delivery_tracking.sql` - Email analytics
3. `20250628_push_notifications.sql` - Push notification support
4. `20250628_external_integrations.sql` - External integrations

**Apply migrations:**
```bash
# Using Supabase CLI
supabase db push

# Or manually
psql -h your_host -U your_user -d your_db -f supabase/migrations/filename.sql
```
