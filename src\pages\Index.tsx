import { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  ArrowRight,
  CheckCircle,
  Cloud,
  Database,
  Laptop,
  Menu,
  Plus,
  X
} from 'lucide-react';
import { LoginModal } from '@/components/auth/LoginModal';
import { FeaturesModal } from '@/components/features/FeaturesModal';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function Index() {
  const [isHeroHovered, setIsHeroHovered] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [featuresModalOpen, setFeaturesModalOpen] = useState(false);
  const currentYear = new Date().getFullYear();

  const toggleMobileMenu = (): void => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      {/* Fixed Navigation Header */}
      <header className="fixed top-0 left-0 right-0 bg-background/95 backdrop-blur border-b border-border z-50 shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo/Name */}
            <div className="flex items-center">
              <Link to="/" className="flex items-center">
                <span className="text-xl font-bold text-primary">KAYA<span className="text-green-600"> Finance</span></span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              <LoginModal
                trigger={
                  <Button variant="outline">
                    Login
                  </Button>
                }
              />
              <Button asChild>
                <a href="mailto:<EMAIL>?subject=Get%20Started%20with%20KAYA%20Finance">
                  Book a Demo
                </a>
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <Button variant="ghost" size="icon" onClick={toggleMobileMenu} aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}>
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden py-4 border-t border-border bg-background">
              <div className="flex flex-col space-y-3">
                <LoginModal
                  trigger={
                    <Button variant="outline" className="w-full">
                      Login
                    </Button>
                  }
                />
                <Button asChild className="w-full">
                  <a href="mailto:<EMAIL>?subject=Get%20Started%20with%20KAYA%20Finance">
                    Book a Demo
                  </a>
                </Button>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content with padding-top to account for fixed header */}
      <main className="flex-grow pt-16">
        {/* Hero Section */}
        <section
          className="min-h-[calc(100vh-4rem)] flex flex-col items-center justify-center px-4 py-16 bg-gradient-to-b from-background to-muted/20 relative overflow-hidden"
          onMouseEnter={() => setIsHeroHovered(true)}
          onMouseLeave={() => setIsHeroHovered(false)}
        >
          <div className="container mx-auto text-center z-10">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-4">
              Accounting, Wherever You Work
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-muted-foreground">
              Cloud flexibility with offline reliability. Keep your books updated even when the internet isn't.
            </p>
            {/* Hero illustration */}
            <div
              className={`relative max-w-4xl mx-auto transition-transform duration-500 ease-in-out ${isHeroHovered ? 'translate-y-[-8px]' : ''}`}
              aria-hidden="true"
            >
              <img
                src="/guest/kaya3.png"
                alt=""
                className="w-full h-auto rounded-lg shadow-xl"
              />
              <div className="absolute -top-4 -right-4 bg-off-white p-2 rounded-full shadow-lg border border-cream">
                <Cloud className="h-8 w-8 text-dark-blue" />
              </div>
              <div className="absolute -bottom-4 -left-4 bg-off-white p-2 rounded-full shadow-lg border border-cream">
                <Database className="h-8 w-8 text-dark-blue" />
              </div>
            </div>
          </div>
        </section>

        {/* Feature Trio */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center mb-12">
              <h2 className="text-3xl font-bold text-foreground mb-4 md:mb-0">
                Flexible Solutions for Your Business
              </h2>
              <Button
                onClick={() => setFeaturesModalOpen(true)}
                className="gap-2"
                variant="outline"
              >
                <Plus className="h-4 w-4" />
                More Features
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  icon: Cloud,
                  title: "Cloud Freedom",
                  description: "Access your financial data from anywhere with secure, encrypted backups.",
                },
                {
                  icon: Laptop,
                  title: "Offline Capture",
                  description: "Continue working without internet and sync your data with one click later.",
                },
                {
                  icon: Database,
                  title: "On-Prem Installation",
                  description: "Run on your own local server with no monthly hosting fees.",
                }
              ].map((feature, index) => (
                <Card
                  key={index}
                  className="border border-border bg-card transition-all duration-300 hover:translate-y-[-8px] hover:shadow-lg"
                >
                  <CardHeader className="text-center">
                    <div className="mx-auto bg-primary/10 p-4 rounded-full mb-4">
                      <feature.icon className="h-8 w-8 text-primary" />
                    </div>
                    <CardTitle>{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-center text-muted-foreground mb-4">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Deep-Dive Strip */}
        <section className="py-20 bg-muted/30 border-y border-border">
          <div className="max-w-6xl mx-auto px-6">
            <h2 className="text-center text-4xl font-bold text-foreground mb-12">
              Why Accountants Love KAYA Finance
            </h2>

            <div className="flex flex-col lg:flex-row items-start gap-12">
              {/* Left: Single illustrative image */}
                <div className="flex-1">
                  <img
                    src="/guest/kaya1.jpg"
                    alt="Overview of Kaya Finance Flow interface"
                    className="w-full rounded-md shadow-soft border border-border"
                  />
                </div>

              {/* Right: Feature bullets */}
              <div className="flex-1">
                <ul className="space-y-5">
                  {[
                    {
                      title: "Automatic Double-Entry System",
                      description: "Our comprehensive system implements rigorous double-entry accounting principles across all financial transactions, maintaining matched debit and credit entries with full auditability. Every transaction is automatically validated against accounting rules, with real-time reconciliation alerts and complete audit trails showing before/after balances for each affected account."
                    },
                    {
                      title: "Comprehensive Audit Trail",
                      description: "Detailed activity logging for all system actions with user activity tracking and timestamps. Includes change history for all financial records, entity-specific audit logs, action-based filtering and searching, and complete audit trail for compliance and accountability."
                    },
                    {
                      title: "Advanced Budget Management",
                      description: "Multi-level budget approval workflow with budget vs. actual performance tracking. Features variance analysis with visual indicators, budget line item tracking, approval deadlines and notifications, budget status tracking (draft, pending_approval, approved, rejected), and budget period management (monthly, quarterly, yearly)."
                    },
                    {
                      title: "Pre-configured Chart of Accounts",
                      description: "Start instantly with our pre-configured Ugandan Chart of Accounts, or tailor your own to match your business needs."
                    }
                  ].map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <CheckCircle className="mt-1 h-6 w-6 text-green-600 flex-shrink-0" />
                      <div className="ml-3">
                        <button
                          onClick={() => {
                            const element = document.getElementById(`feature-${i}`);
                            if (element) {
                              element.classList.toggle('hidden');
                            }
                          }}
                          className="text-left font-medium hover:text-primary transition-colors flex items-center gap-2 group"
                        >
                          {feature.title}
                          <svg 
                            className={`w-4 h-4 transition-transform duration-200 ${i === 0 ? 'rotate-180' : ''} group-hover:translate-y-0.5`} 
                            fill="none" 
                            viewBox="0 0 24 24" 
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                        <p 
                          id={`feature-${i}`} 
                          className={`text-muted-foreground mt-2 ${i === 0 ? '' : 'hidden'}`}
                        >
                          {feature.description}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-muted/50 border-t border-border py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <p className="text-muted-foreground text-sm">
                &copy; {currentYear} KAYA Finance. All rights reserved.
              </p>
              {/* <p className="text-muted-foreground/70 text-xs mt-1">
                Version 1.0.0
              </p> */}
            </div>

            <div className="flex flex-wrap justify-center gap-x-8 gap-y-2">
              <Link
                to="/terms-of-service"
                className="text-muted-foreground hover:text-foreground text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 rounded-sm"
                aria-label="Terms of Service"
              >
                Terms of Service
              </Link>
              <Link
                to="/privacy-policy"
                className="text-muted-foreground hover:text-foreground text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 rounded-sm"
                aria-label="Privacy Policy"
              >
                Privacy Policy
              </Link>
              <Link
                to="/support"
                className="text-muted-foreground hover:text-foreground text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 rounded-sm"
                aria-label="Support"
              >
                Support
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Features Modal */}
      <FeaturesModal
        open={featuresModalOpen}
        onOpenChange={setFeaturesModalOpen}
      />
    </div>
  );
}
