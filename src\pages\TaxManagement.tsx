
import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus } from 'lucide-react'
import { toast } from 'sonner'
import { TaxRatesTable } from '@/components/tax/TaxRatesTable'
import { WithholdingTaxRatesTable } from '@/components/tax/WithholdingTaxRatesTable'
import { UraTaxFilingsTable } from '@/components/tax/UraTaxFilingsTable'
import { TaxRateForm } from '@/components/tax/TaxRateForm'
import { WithholdingTaxRateForm } from '@/components/tax/WithholdingTaxRateForm'
import { UraTaxFilingForm } from '@/components/tax/UraTaxFilingForm'
import type { TaxRate, WithholdingTaxRate } from '@/types/database'

// Define UraTaxFiling type based on database schema
interface UraTaxFiling {
  id: string
  org_id: string
  filing_type: string
  period: string
  filing_date: string
  due_date: string
  amount: number
  total_paid: number
  penalty: number | null
  interest: number | null
  status: string
  ura_ref_no: string | null
  payment_ref_no: string | null
  filed_at: string | null
  filed_by: string | null
  created_at: string
  updated_at: string | null
}

export default function TaxManagement() {
  const { profile } = useAuth()
  const [taxRates, setTaxRates] = useState<TaxRate[]>([])
  const [withholdingTaxRates, setWithholdingTaxRates] = useState<WithholdingTaxRate[]>([])
  const [uraTaxFilings, setUraTaxFilings] = useState<UraTaxFiling[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showTaxRateForm, setShowTaxRateForm] = useState(false)
  const [showWithholdingTaxRateForm, setShowWithholdingTaxRateForm] = useState(false)
  const [showUraTaxFilingForm, setShowUraTaxFilingForm] = useState(false)
  const [editingTaxRate, setEditingTaxRate] = useState<TaxRate | null>(null)
  const [editingWithholdingTaxRate, setEditingWithholdingTaxRate] = useState<WithholdingTaxRate | null>(null)
  const [editingUraTaxFiling, setEditingUraTaxFiling] = useState<UraTaxFiling | null>(null)

  const fetchTaxRates = useCallback(async () => {
    if (!profile?.org_id) return

    const { data, error } = await supabase
      .from('tax_rates')
      .select('*')
      .eq('org_id', profile.org_id)
      .order('name')

    if (error) throw error
    setTaxRates(data || [])
  }, [profile?.org_id])

  const fetchWithholdingTaxRates = useCallback(async () => {
    if (!profile?.org_id) return

    const { data, error } = await supabase
      .from('withholding_tax_rates')
      .select('*')
      .eq('org_id', profile.org_id)
      .order('name')

    if (error) throw error
    setWithholdingTaxRates(data || [])
  }, [profile?.org_id])

  const fetchUraTaxFilings = useCallback(async () => {
    if (!profile?.org_id) return

    const { data, error } = await supabase
      .from('ura_tax_filings')
      .select('*')
      .eq('org_id', profile.org_id)
      .order('filing_date', { ascending: false })

    if (error) throw error
    setUraTaxFilings(data || [])
  }, [profile?.org_id])

  const fetchAllData = useCallback(async () => {
    setIsLoading(true)
    try {
      await Promise.all([
        fetchTaxRates(),
        fetchWithholdingTaxRates(),
        fetchUraTaxFilings()
      ])
    } catch (error) {
      console.error('Error fetching tax data:', error)
      toast.error('Failed to load tax data')
    } finally {
      setIsLoading(false)
    }
  }, [fetchTaxRates, fetchWithholdingTaxRates, fetchUraTaxFilings])

  useEffect(() => {
    if (profile?.org_id) {
      fetchAllData()
    }
  }, [profile?.org_id, fetchAllData])

  const handleEditTaxRate = (taxRate: TaxRate) => {
    setEditingTaxRate(taxRate)
    setShowTaxRateForm(true)
  }

  const handleEditWithholdingTaxRate = (withholdingTaxRate: WithholdingTaxRate) => {
    setEditingWithholdingTaxRate(withholdingTaxRate)
    setShowWithholdingTaxRateForm(true)
  }

  const handleEditUraTaxFiling = (uraTaxFiling: UraTaxFiling) => {
    setEditingUraTaxFiling(uraTaxFiling)
    setShowUraTaxFilingForm(true)
  }

  const handleFormClose = () => {
    setShowTaxRateForm(false)
    setShowWithholdingTaxRateForm(false)
    setShowUraTaxFilingForm(false)
    setEditingTaxRate(null)
    setEditingWithholdingTaxRate(null)
    setEditingUraTaxFiling(null)
  }

  const handleFormSave = async () => {
    await fetchAllData()
    handleFormClose()
  }

  if (isLoading) {
    return <div className="p-6">Loading tax management data...</div>
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Tax Management</h1>
          <p className="text-muted-foreground">
            Manage tax rates, withholding tax rates, and URA tax filings
          </p>
        </div>
      </div>

      <Tabs defaultValue="tax-rates" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tax-rates">Tax Rates</TabsTrigger>
          <TabsTrigger value="withholding-tax">Withholding Tax</TabsTrigger>
          <TabsTrigger value="ura-filings">URA Filings</TabsTrigger>
        </TabsList>

        <TabsContent value="tax-rates" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Tax Rates</CardTitle>
                  <CardDescription>
                    Configure VAT and other tax rates for your organization
                  </CardDescription>
                </div>
                <Button onClick={() => setShowTaxRateForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Tax Rate
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <TaxRatesTable 
                taxRates={taxRates} 
                onEdit={handleEditTaxRate}
                onRefresh={fetchTaxRates}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="withholding-tax" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Withholding Tax Rates</CardTitle>
                  <CardDescription>
                    Configure withholding tax rates for different services and supplies
                  </CardDescription>
                </div>
                <Button onClick={() => setShowWithholdingTaxRateForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Withholding Tax Rate
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <WithholdingTaxRatesTable 
                withholdingTaxRates={withholdingTaxRates} 
                onEdit={handleEditWithholdingTaxRate}
                onRefresh={fetchWithholdingTaxRates}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ura-filings" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>URA Tax Filings</CardTitle>
                  <CardDescription>
                    Track and manage your URA tax filing records
                  </CardDescription>
                </div>
                <Button onClick={() => setShowUraTaxFilingForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Tax Filing
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <UraTaxFilingsTable 
                uraTaxFilings={uraTaxFilings} 
                onEdit={handleEditUraTaxFiling}
                onRefresh={fetchUraTaxFilings}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {showTaxRateForm && (
        <TaxRateForm
          taxRate={editingTaxRate}
          onClose={handleFormClose}
          onSave={handleFormSave}
        />
      )}

      {showWithholdingTaxRateForm && (
        <WithholdingTaxRateForm
          withholdingTaxRate={editingWithholdingTaxRate}
          onClose={handleFormClose}
          onSave={handleFormSave}
        />
      )}

      {showUraTaxFilingForm && (
        <UraTaxFilingForm
          uraTaxFiling={editingUraTaxFiling}
          onClose={handleFormClose}
          onSave={handleFormSave}
        />
      )}
    </div>
  )
}
