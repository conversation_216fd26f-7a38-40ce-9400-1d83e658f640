-- =====================================================
-- NOTIFICATION SYSTEM COMPLETE MIGRATION
-- =====================================================
-- Migration: 20250628_notification_system_complete.sql
-- Description: Complete notification system with tables, indexes, RLS, functions, and triggers
-- Author: Kaya Finance Team
-- Date: 2025-06-28

-- =====================================================
-- STEP 1: CREATE NOTIFICATION TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔔 Creating notification system tables...';
END $$;

-- Notification templates table
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL UNIQUE,
    category VARCHAR(20) NOT NULL CHECK (category IN ('financial', 'system', 'approval', 'reminder', 'security')),
    title_template TEXT NOT NULL,
    message_template TEXT NOT NULL,
    default_priority VARCHAR(10) DEFAULT 'normal' CHECK (default_priority IN ('low', 'normal', 'high', 'urgent')),
    expires_after_hours INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- NULL for org-wide notifications
    type VARCHAR(50) NOT NULL,
    category VARCHAR(20) NOT NULL CHECK (category IN ('financial', 'system', 'approval', 'reminder', 'security')),
    priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    entity_type VARCHAR(50),
    entity_id UUID,
    is_read BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT true,
    in_app_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, notification_type)
);

-- =====================================================
-- STEP 2: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📊 Creating performance indexes...';
END $$;

-- Notifications indexes
CREATE INDEX IF NOT EXISTS idx_notifications_org_id ON notifications(org_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_category ON notifications(category);
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_is_archived ON notifications(is_archived);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON notifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_notifications_entity ON notifications(entity_type, entity_id);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, is_read, is_archived) WHERE is_read = false AND is_archived = false;
CREATE INDEX IF NOT EXISTS idx_notifications_org_unread ON notifications(org_id, is_read, is_archived) WHERE user_id IS NULL AND is_read = false AND is_archived = false;

-- Notification preferences indexes
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_type ON notification_preferences(notification_type);

-- Notification templates indexes
CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(type);
CREATE INDEX IF NOT EXISTS idx_notification_templates_category ON notification_templates(category);
CREATE INDEX IF NOT EXISTS idx_notification_templates_active ON notification_templates(is_active) WHERE is_active = true;

-- =====================================================
-- STEP 3: INSERT DEFAULT NOTIFICATION TEMPLATES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📝 Inserting default notification templates...';
END $$;

INSERT INTO notification_templates (type, category, title_template, message_template, default_priority, expires_after_hours) VALUES
-- Financial notifications
('payment_pending_approval', 'approval', 'Payment Approval Required', 'Payment of {{amount}} to {{payee_name}} requires your approval. Reason: {{reason}}', 'high', 72),
('payment_approved', 'financial', 'Payment Approved', 'Your payment of {{amount}} to {{payee_name}} has been approved.', 'normal', 24),
('payment_rejected', 'financial', 'Payment Rejected', 'Your payment of {{amount}} to {{payee_name}} has been rejected. Reason: {{reason}}', 'high', 48),
('invoice_overdue', 'reminder', 'Invoice Overdue', 'Invoice {{invoice_number}} for {{customer_name}} is {{days_overdue}} days overdue.', 'high', NULL),
('invoice_due_soon', 'reminder', 'Invoice Due Soon', 'Invoice {{invoice_number}} for {{customer_name}} is due in {{days_until_due}} days.', 'normal', 24),
('invoice_paid', 'financial', 'Invoice Paid', 'Invoice {{invoice_number}} for {{customer_name}} has been paid.', 'normal', 24),
('bill_due_soon', 'reminder', 'Bill Due Soon', 'Bill {{bill_number}} from {{vendor_name}} is due in {{days_until_due}} days.', 'normal', 24),
('bill_overdue', 'reminder', 'Bill Overdue', 'Bill {{bill_number}} from {{vendor_name}} is {{days_overdue}} days overdue.', 'high', NULL),

-- Budget notifications
('budget_exceeded', 'financial', 'Budget Exceeded', 'Budget for {{entity_name}} has been exceeded by {{amount}}.', 'urgent', NULL),
('budget_warning', 'financial', 'Budget Warning', 'Budget for {{entity_name}} is at {{percentage}}% of limit.', 'high', 48),

-- System notifications
('user_invited', 'system', 'User Invited', 'User {{email}} has been invited to join the organization.', 'normal', 168),
('backup_completed', 'system', 'Backup Completed', 'System backup completed successfully. {{details}}', 'low', 24),
('backup_failed', 'system', 'Backup Failed', 'System backup failed. {{details}}', 'urgent', NULL),
('system_maintenance', 'system', 'System Maintenance', 'Scheduled system maintenance: {{details}}', 'normal', 48),

-- Security notifications
('audit_alert', 'security', 'Audit Alert', 'Security audit alert: {{details}}', 'urgent', NULL)

ON CONFLICT (type) DO UPDATE SET
    title_template = EXCLUDED.title_template,
    message_template = EXCLUDED.message_template,
    default_priority = EXCLUDED.default_priority,
    expires_after_hours = EXCLUDED.expires_after_hours,
    updated_at = NOW();

-- =====================================================
-- STEP 4: CREATE UTILITY FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating utility functions...';
END $$;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_notifications_updated_at ON notifications;
CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_notification_preferences_updated_at ON notification_preferences;
CREATE TRIGGER update_notification_preferences_updated_at 
    BEFORE UPDATE ON notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_notification_templates_updated_at ON notification_templates;
CREATE TRIGGER update_notification_templates_updated_at 
    BEFORE UPDATE ON notification_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically set read_at when is_read is set to true
CREATE OR REPLACE FUNCTION set_notification_read_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_read = true AND OLD.is_read = false THEN
        NEW.read_at = NOW();
    ELSIF NEW.is_read = false THEN
        NEW.read_at = NULL;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS set_notification_read_at_trigger ON notifications;
CREATE TRIGGER set_notification_read_at_trigger
    BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION set_notification_read_at();

-- =====================================================
-- STEP 5: CREATE NOTIFICATION MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to create notification from template
CREATE OR REPLACE FUNCTION create_notification_from_template(
    template_type TEXT,
    org_id_param UUID,
    user_id_param UUID DEFAULT NULL,
    template_data JSONB DEFAULT '{}',
    entity_type_param TEXT DEFAULT NULL,
    entity_id_param UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    template_record RECORD;
    notification_id UUID;
    processed_title TEXT;
    processed_message TEXT;
    expires_at_value TIMESTAMP WITH TIME ZONE;
    key TEXT;
    value TEXT;
BEGIN
    -- Get template
    SELECT * INTO template_record
    FROM notification_templates
    WHERE type = template_type AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Template not found: %', template_type;
    END IF;
    
    -- Process template variables
    processed_title := template_record.title_template;
    processed_message := template_record.message_template;
    
    -- Replace template variables
    FOR key, value IN SELECT * FROM jsonb_each_text(template_data)
    LOOP
        processed_title := REPLACE(processed_title, '{{' || key || '}}', value);
        processed_message := REPLACE(processed_message, '{{' || key || '}}', value);
    END LOOP;
    
    -- Calculate expiry
    IF template_record.expires_after_hours IS NOT NULL THEN
        expires_at_value := NOW() + INTERVAL '1 hour' * template_record.expires_after_hours;
    END IF;
    
    -- Create notification
    INSERT INTO notifications (
        org_id, user_id, type, category, priority,
        title, message, data, entity_type, entity_id, expires_at
    ) VALUES (
        org_id_param, user_id_param, template_type, template_record.category, template_record.default_priority,
        processed_title, processed_message, template_data, entity_type_param, entity_id_param, expires_at_value
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications(org_id_param UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    IF org_id_param IS NOT NULL THEN
        DELETE FROM notifications 
        WHERE org_id = org_id_param 
        AND expires_at < NOW();
    ELSE
        DELETE FROM notifications 
        WHERE expires_at < NOW();
    END IF;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get notification statistics
CREATE OR REPLACE FUNCTION get_notification_stats(user_id_param UUID, org_id_param UUID)
RETURNS JSONB AS $$
DECLARE
    stats JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total', COUNT(*),
        'unread', COUNT(*) FILTER (WHERE is_read = false),
        'by_category', jsonb_object_agg(category, category_count),
        'by_priority', jsonb_object_agg(priority, priority_count)
    ) INTO stats
    FROM (
        SELECT 
            category,
            priority,
            COUNT(*) OVER (PARTITION BY category) as category_count,
            COUNT(*) OVER (PARTITION BY priority) as priority_count
        FROM notifications
        WHERE (user_id = user_id_param OR (user_id IS NULL AND org_id = org_id_param))
        AND is_archived = false
    ) subq;
    
    RETURN COALESCE(stats, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ NOTIFICATION SYSTEM MIGRATION COMPLETED!';
    RAISE NOTICE '================================================';
    RAISE NOTICE '';
    RAISE NOTICE '📋 CREATED TABLES:';
    RAISE NOTICE '  • notification_templates - Template definitions';
    RAISE NOTICE '  • notifications - Main notification records';
    RAISE NOTICE '  • notification_preferences - User preferences';
    RAISE NOTICE '';
    RAISE NOTICE '📊 CREATED INDEXES:';
    RAISE NOTICE '  • Performance indexes for all common queries';
    RAISE NOTICE '  • Composite indexes for unread notifications';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ CREATED FUNCTIONS:';
    RAISE NOTICE '  • create_notification_from_template()';
    RAISE NOTICE '  • cleanup_expired_notifications()';
    RAISE NOTICE '  • get_notification_stats()';
    RAISE NOTICE '';
    RAISE NOTICE '🔔 INSERTED TEMPLATES:';
    RAISE NOTICE '  • 13 default notification templates';
    RAISE NOTICE '';
    RAISE NOTICE 'Next: Run RLS security policies migration';
    RAISE NOTICE '';
END $$;
