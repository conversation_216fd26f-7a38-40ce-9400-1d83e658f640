import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'
import type { 
  InventoryLocation, 
  InventoryLocationFormData 
} from '@/types/inventory'

/**
 * Hook to fetch all inventory locations for an organization
 */
export function useInventoryLocations() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryLocations.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('inventory_locations')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('is_default', { ascending: false })
        .order('name', { ascending: true })

      if (error) throw error
      return data as InventoryLocation[]
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch active inventory locations only
 */
export function useActiveInventoryLocations() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryLocations.active(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('inventory_locations')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('is_default', { ascending: false })
        .order('name', { ascending: true })

      if (error) throw error
      return data as InventoryLocation[]
    },
    enabled: !!profile?.org_id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook to fetch a single inventory location by ID
 */
export function useInventoryLocation(locationId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.inventoryLocations.detail(profile?.org_id || '', locationId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !locationId) return null

      const { data, error } = await supabase
        .from('inventory_locations')
        .select(`
          *,
          stock_levels(
            *,
            product:products(id, name, sku)
          )
        `)
        .eq('id', locationId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data as InventoryLocation & {
        stock_levels: Array<{
          id: string
          quantity_on_hand: number
          quantity_reserved: number
          quantity_available: number
          product: {
            id: string
            name: string
            sku: string
          } | null
        }>
      }
    },
    enabled: !!profile?.org_id && !!locationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get the default inventory location
 */
export function useDefaultInventoryLocation() {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: ['default-inventory-location', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return null

      const { data, error } = await supabase
        .from('inventory_locations')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_default', true)
        .eq('is_active', true)
        .single()

      if (error) {
        // If no default location found, return the first active location
        const { data: firstLocation, error: firstError } = await supabase
          .from('inventory_locations')
          .select('*')
          .eq('org_id', profile.org_id)
          .eq('is_active', true)
          .order('created_at', { ascending: true })
          .limit(1)
          .single()

        if (firstError) return null
        return firstLocation as InventoryLocation
      }

      return data as InventoryLocation
    },
    enabled: !!profile?.org_id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Hook to create a new inventory location
 */
export function useCreateInventoryLocation() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (locationData: InventoryLocationFormData) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // If this is being set as default, unset other defaults first
      if (locationData.is_default) {
        await supabase
          .from('inventory_locations')
          .update({ is_default: false })
          .eq('org_id', profile.org_id)
          .eq('is_default', true)
      }

      const { data, error } = await supabase
        .from('inventory_locations')
        .insert({
          ...locationData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data as InventoryLocation
    },
    onSuccess: () => {
      // Invalidate all location-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: ['default-inventory-location', profile?.org_id] })
    },
  })
}

/**
 * Hook to update an inventory location
 */
export function useUpdateInventoryLocation() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ locationId, locationData }: { locationId: string; locationData: Partial<InventoryLocationFormData> }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // If this is being set as default, unset other defaults first
      if (locationData.is_default) {
        await supabase
          .from('inventory_locations')
          .update({ is_default: false })
          .eq('org_id', profile.org_id)
          .eq('is_default', true)
          .neq('id', locationId)
      }

      const { data, error } = await supabase
        .from('inventory_locations')
        .update({
          ...locationData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', locationId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data as InventoryLocation
    },
    onSuccess: (data) => {
      // Invalidate all location-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.detail(profile?.org_id || '', data.id) })
      queryClient.invalidateQueries({ queryKey: ['default-inventory-location', profile?.org_id] })
    },
  })
}

/**
 * Hook to delete an inventory location
 */
export function useDeleteInventoryLocation() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (locationId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Check if location has stock levels
      const { data: stockLevels } = await supabase
        .from('stock_levels')
        .select('id')
        .eq('location_id', locationId)
        .eq('org_id', profile.org_id)
        .limit(1)

      if (stockLevels && stockLevels.length > 0) {
        throw new Error('Cannot delete location that has stock records')
      }

      // Check if this is the default location
      const { data: location } = await supabase
        .from('inventory_locations')
        .select('is_default')
        .eq('id', locationId)
        .eq('org_id', profile.org_id)
        .single()

      if (location?.is_default) {
        throw new Error('Cannot delete the default location')
      }

      const { error } = await supabase
        .from('inventory_locations')
        .delete()
        .eq('id', locationId)
        .eq('org_id', profile.org_id)

      if (error) throw error
    },
    onSuccess: () => {
      // Invalidate all location-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.active(profile?.org_id || '') })
    },
  })
}

/**
 * Hook to toggle inventory location active status
 */
export function useToggleInventoryLocationStatus() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ locationId, isActive }: { locationId: string; isActive: boolean }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Don't allow deactivating the default location
      if (!isActive) {
        const { data: location } = await supabase
          .from('inventory_locations')
          .select('is_default')
          .eq('id', locationId)
          .eq('org_id', profile.org_id)
          .single()

        if (location?.is_default) {
          throw new Error('Cannot deactivate the default location')
        }
      }

      const { data, error } = await supabase
        .from('inventory_locations')
        .update({ 
          is_active: isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('id', locationId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data as InventoryLocation
    },
    onSuccess: (data) => {
      // Invalidate all location-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.active(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryLocations.detail(profile?.org_id || '', data.id) })
    },
  })
}
