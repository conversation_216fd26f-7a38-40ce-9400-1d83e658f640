
import React, { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { useAuditLogger } from '@/hooks/useAuditLogger'
import { supabase } from '@/lib/supabase'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import { useActiveCustomers, useActiveVendors, useCreatePayment, useUpdatePayment } from '@/hooks/queries'
import { useQueryClient } from '@tanstack/react-query'
import { queryKeys } from '@/lib/queryKeys'
import { usePaymentInventoryIntegration } from '@/hooks/usePaymentInventoryIntegration'
import type { Payment, Customer, Vendor, BankAccount, PaymentChannel, PaymentStatus, Invoice, Bill } from '@/types/database'
import {
  validatePhoneNumber,
  formatPhoneNumber,
  getPhoneNumberHelper
} from '@/lib/validators'
import { formatCurrency } from '@/lib/utils'

interface PaymentFormProps {
  payment?: Payment | null
  customers?: Customer[]
  vendors?: Vendor[]
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
  preselectedPayeeType?: 'customer' | 'vendor'
  preselectedPayeeId?: string
}

interface PaymentFormData {
  payment_date: string
  payee_type: string
  payee_id: string
  amount: string
  channel: PaymentChannel
  bank_account_id: string
  mobile_money_number: string
  transaction_id: string
  notes: string
  status: PaymentStatus
  is_reconciled: boolean
  invoice_id?: string
  bill_id?: string
}

export function PaymentForm({
  payment,
  customers: propCustomers = [],
  vendors: propVendors = [],
  open,
  onOpenChange,
  onSuccess,
  preselectedPayeeType,
  preselectedPayeeId
}: PaymentFormProps) {
  const { profile } = useAuth()
  const { logFormSubmission } = useAuditLogger()
  const createPayment = useCreatePayment()
  const updatePayment = useUpdatePayment()
  const queryClient = useQueryClient()
  const { processPaymentInventory } = usePaymentInventoryIntegration()

  // Use hooks to fetch data if not provided via props
  const { data: hookCustomers = [] } = useActiveCustomers()
  const { data: hookVendors = [] } = useActiveVendors()

  // Use prop data if available, otherwise use hook data
  const customers = propCustomers.length > 0 ? propCustomers : hookCustomers
  const vendors = propVendors.length > 0 ? propVendors : hookVendors

  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([])
  const [availableInvoices, setAvailableInvoices] = useState<Invoice[]>([])
  const [availableBills, setAvailableBills] = useState<Bill[]>([])
  const [selectedDocument, setSelectedDocument] = useState<Invoice | Bill | null>(null)
  const [amountValidationError, setAmountValidationError] = useState<string>('')
  const [formData, setFormData] = useState<PaymentFormData>({
    payment_date: payment?.payment_date || new Date().toISOString().split('T')[0],
    payee_type: preselectedPayeeType || payment?.payee_type || 'vendor',
    payee_id: preselectedPayeeId || payment?.payee_id || '',
    amount: payment?.amount?.toString() || '',
    channel: payment?.channel || 'bank',
    bank_account_id: payment?.bank_account_id || '',
    mobile_money_number: payment?.mobile_money_number || '',
    transaction_id: payment?.transaction_id || '',
    notes: payment?.notes || '',
    status: payment?.status || 'pending',
    is_reconciled: payment?.is_reconciled || false,
    invoice_id: '',
    bill_id: ''
  })
  const [validationErrors, setValidationErrors] = useState<{
    mobile_money_number?: string
  }>({})

  // Update form data when preselected values change
  useEffect(() => {
    if (preselectedPayeeType && preselectedPayeeId) {
      setFormData(prev => ({
        ...prev,
        payee_type: preselectedPayeeType,
        payee_id: preselectedPayeeId
      }))
    }
  }, [preselectedPayeeType, preselectedPayeeId])

  // Handle mobile money number input with auto-formatting
  const handleMobileMoneyChange = (value: string) => {
    const formatted = formatPhoneNumber(value)
    setFormData({ ...formData, mobile_money_number: formatted })

    // Clear validation error when user starts typing
    if (validationErrors.mobile_money_number) {
      setValidationErrors({ ...validationErrors, mobile_money_number: undefined })
    }
  }
  const [isLoading, setIsLoading] = useState(false)

  const fetchBankAccounts = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      const { data, error } = await supabase
        .from('bank_accounts')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)

      if (error) throw error
      setBankAccounts(data || [])
    } catch (error) {
      console.error('Error fetching bank accounts:', error)
    }
  }, [profile?.org_id])

  const fetchAvailableInvoices = useCallback(async (customerId: string) => {
    try {
      const { data, error } = await supabase
        .from('invoices')
        .select('*')
        .eq('customer_id', customerId)
        .eq('org_id', profile?.org_id)
        .in('status', ['sent', 'overdue']) // Exclude 'paid' status
        .gt('amount_due', 0)
        .order('date_issued', { ascending: false })

      if (error) throw error
      setAvailableInvoices(data || [])
    } catch (error) {
      console.error('Error fetching invoices:', error)
      setAvailableInvoices([])
    }
  }, [profile?.org_id])

  const fetchAvailableBills = useCallback(async (vendorId: string) => {
    try {
      const { data, error } = await supabase
        .from('bills')
        .select('*')
        .eq('vendor_id', vendorId)
        .eq('org_id', profile?.org_id)
        .in('status', ['draft', 'approved']) // Exclude 'paid' status
        .gt('amount_due', 0)
        .order('date_issued', { ascending: false })

      if (error) throw error
      setAvailableBills(data || [])
    } catch (error) {
      console.error('Error fetching bills:', error)
      setAvailableBills([])
    }
  }, [profile?.org_id])

  useEffect(() => {
    if (profile?.org_id) {
      fetchBankAccounts()
    }
  }, [profile?.org_id, fetchBankAccounts])

  useEffect(() => {
    if (formData.payee_id) {
      if (formData.payee_type === 'customer') {
        fetchAvailableInvoices(formData.payee_id)
      } else {
        fetchAvailableBills(formData.payee_id)
      }
    }
  }, [formData.payee_id, formData.payee_type, fetchAvailableInvoices, fetchAvailableBills])

  // Handle document selection (invoice or bill)
  const handleDocumentSelection = (documentId: string) => {
    if (formData.payee_type === 'customer') {
      const invoice = availableInvoices.find(inv => inv.id === documentId)
      if (invoice) {
        setSelectedDocument(invoice)
        const amount = invoice.amount_due?.toString() || invoice.total_amount.toString()
        setFormData({
          ...formData,
          invoice_id: documentId,
          bill_id: '',
          amount
        })
        // Validate the auto-filled amount
        validateAmount(amount)
      }
    } else {
      const bill = availableBills.find(b => b.id === documentId)
      if (bill) {
        setSelectedDocument(bill)
        const amount = bill.amount_due?.toString() || bill.total_amount.toString()
        setFormData({
          ...formData,
          bill_id: documentId,
          invoice_id: '',
          amount
        })
        // Validate the auto-filled amount
        validateAmount(amount)
      }
    }
  }

  // Clear document selection
  const clearDocumentSelection = () => {
    setSelectedDocument(null)
    setAmountValidationError('')
    setFormData({
      ...formData,
      invoice_id: '',
      bill_id: '',
      amount: ''
    })
  }

  // Real-time amount validation
  const validateAmount = (amount: string) => {
    setAmountValidationError('')

    if (!selectedDocument || !amount) {
      return
    }

    const paymentAmount = Number(amount)
    const documentAmountDue = selectedDocument.amount_due || selectedDocument.total_amount || 0

    if (paymentAmount <= 0) {
      setAmountValidationError('Payment amount must be greater than 0')
    } else if (paymentAmount > documentAmountDue) {
      setAmountValidationError(`Payment amount cannot exceed ${formatCurrency(documentAmountDue)} (amount due)`)
    } else if (selectedDocument.status === 'paid') {
      setAmountValidationError(`This ${formData.invoice_id ? 'invoice' : 'bill'} is already marked as paid`)
    }
  }

  // Handle amount change with validation
  const handleAmountChange = (value: string) => {
    setFormData({ ...formData, amount: value })
    validateAmount(value)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profile?.org_id) return

    // Enhanced validation
    const validationErrors: string[] = []

    // Validate required fields
    if (!formData.payee_id || formData.payee_id.trim() === '') {
      validationErrors.push('Please select a payee')
    }

    if (!formData.amount || Number(formData.amount) <= 0) {
      validationErrors.push('Please enter a valid payment amount greater than 0')
    }

    if (!formData.payment_date) {
      validationErrors.push('Please select a payment date')
    }

    // Validate channel-specific requirements
    if (formData.channel === 'bank' && (!formData.bank_account_id || formData.bank_account_id.trim() === '')) {
      validationErrors.push('Please select a bank account for bank transfers')
    }

    if (['mtn_momo', 'airtel_money'].includes(formData.channel)) {
      if (!formData.mobile_money_number || formData.mobile_money_number.trim() === '') {
        validationErrors.push('Please enter a mobile money number')
      } else {
        const phoneValidation = validatePhoneNumber(formData.mobile_money_number, true)
        if (!phoneValidation.isValid) {
          validationErrors.push(phoneValidation.message || 'Invalid mobile money number')
        }
      }
    }

    // Validate payment amount against selected invoice/bill
    if (selectedDocument && (formData.invoice_id || formData.bill_id)) {
      const paymentAmount = Number(formData.amount)
      const documentAmountDue = selectedDocument.amount_due || selectedDocument.total_amount || 0

      if (paymentAmount <= 0) {
        validationErrors.push('Payment amount must be greater than 0')
      } else if (paymentAmount > documentAmountDue) {
        validationErrors.push(`Payment amount (${formatCurrency(paymentAmount)}) cannot exceed the amount due (${formatCurrency(documentAmountDue)}) for the selected ${formData.invoice_id ? 'invoice' : 'bill'}`)
      } else if (paymentAmount < documentAmountDue) {
        // Allow partial payments but warn the user
        const confirmPartial = confirm(
          `You are making a partial payment of ${formatCurrency(paymentAmount)} against an amount due of ${formatCurrency(documentAmountDue)}. ` +
          `This will leave a remaining balance of ${formatCurrency(documentAmountDue - paymentAmount)}. ` +
          `Do you want to continue?`
        )
        if (!confirmPartial) {
          setIsLoading(false)
          return
        }
      }

      // Check if the document is already fully paid
      if (selectedDocument.status === 'paid') {
        validationErrors.push(`The selected ${formData.invoice_id ? 'invoice' : 'bill'} is already marked as paid. Please select a different document or create a payment without linking to a specific document.`)
      }
    }

    // Show validation errors
    if (validationErrors.length > 0) {
      toast.error(validationErrors.join('. '))
      return
    }

    setIsLoading(true)
    try {
      // Prepare payment data with proper null handling
      // Note: Exclude status field if it doesn't exist in database yet
      const basePaymentData = {
        payment_date: formData.payment_date,
        payee_type: formData.payee_type,
        payee_id: formData.payee_id,
        amount: Number(formData.amount),
        channel: formData.channel,
        bank_account_id: (formData.channel === 'bank' && formData.bank_account_id && formData.bank_account_id.trim() !== '')
          ? formData.bank_account_id
          : null,
        mobile_money_number: (['mtn_momo', 'airtel_money'].includes(formData.channel) && formData.mobile_money_number && formData.mobile_money_number.trim() !== '')
          ? formData.mobile_money_number
          : null,
        transaction_id: (formData.transaction_id && formData.transaction_id.trim() !== '')
          ? formData.transaction_id
          : null,
        notes: (formData.notes && formData.notes.trim() !== '')
          ? formData.notes
          : null,
        is_reconciled: formData.is_reconciled,
        org_id: profile.org_id,
        currency_code: 'UGX'
      }

      // Try to include status field, but handle gracefully if it doesn't exist
      const paymentData = basePaymentData as Record<string, unknown>
      // Only include status if we're updating an existing payment that has it
      if (payment?.status !== undefined) {
        paymentData.status = formData.status
      }

      console.log('Submitting payment data:', paymentData)

      let paymentId = payment?.id

      if (payment) {
        // Store old data for audit logging
        const oldData = {
          payment_date: payment.payment_date,
          payee_type: payment.payee_type,
          payee_id: payment.payee_id,
          amount: payment.amount,
          channel: payment.channel,
          status: payment.status,
          is_reconciled: payment.is_reconciled
        }

        const { error } = await supabase
          .from('payments')
          .update(paymentData)
          .eq('id', payment.id)

        if (error) {
          console.error('Payment update error:', error)
          throw new Error(`Failed to update payment: ${error.message}`)
        }

        // Log the update
        await logFormSubmission('payments', payment.id, 'update', paymentData, oldData)
        toast.success('Payment updated successfully')

        // Manually invalidate cache for customer/vendor-specific queries
        if (formData.payee_type && formData.payee_id) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.payments.byEntity(profile?.org_id || '', formData.payee_type as 'customer' | 'vendor', formData.payee_id)
          })
        }
      } else {
        const { data: newPayment, error } = await supabase
          .from('payments')
          .insert([paymentData])
          .select()
          .single()

        if (error) {
          console.error('Payment creation error:', error)
          throw new Error(`Failed to create payment: ${error.message}`)
        }

        paymentId = newPayment.id

        // Log the creation
        await logFormSubmission('payments', newPayment.id, 'create', paymentData)
        toast.success('Payment created successfully')

        // Manually invalidate cache for customer/vendor-specific queries
        if (formData.payee_type && formData.payee_id) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.payments.byEntity(profile?.org_id || '', formData.payee_type as 'customer' | 'vendor', formData.payee_id)
          })
        }
      }

      // Create payment application if invoice/bill is selected
      if (paymentId && (formData.invoice_id || formData.bill_id)) {
        const applicationData = {
          payment_id: paymentId,
          applied_to_type: formData.invoice_id ? 'invoice' : 'bill',
          applied_to_id: formData.invoice_id || formData.bill_id,
          amount_applied: Number(formData.amount)
        }

        const { error: appError } = await supabase
          .from('payment_applications')
          .insert([applicationData])

        if (appError) {
          console.error('Error creating payment application:', appError)
          toast.error('Payment created but failed to link to document')
        } else {
          // Update invoice/bill status if fully paid
          const documentType = formData.invoice_id ? 'invoices' : 'bills'
          const documentId = formData.invoice_id || formData.bill_id
          const amountDue = selectedDocument?.amount_due || selectedDocument?.total_amount || 0

          if (Number(formData.amount) >= amountDue) {
            const { error: statusError } = await supabase
              .from(documentType)
              .update({ status: 'paid' })
              .eq('id', documentId)

            if (statusError) {
              console.error('Error updating document status:', statusError)
            } else {
              // Process inventory movements when document is fully paid
              try {
                await processPaymentInventory({
                  paymentId: paymentId,
                  documentType: documentType as 'invoice' | 'bill',
                  documentId: documentId,
                  documentNumber: selectedDocument?.invoice_number || selectedDocument?.bill_number || 'Unknown',
                  amount: Number(formData.amount)
                })
              } catch (inventoryError) {
                console.error('Error processing payment inventory:', inventoryError)
                // Don't fail the payment if inventory processing fails
                toast.error('Payment processed but inventory update failed')
              }
            }
          }
        }
      }

      onSuccess()
    } catch (error: Error | unknown) {
      console.error('Error saving payment:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save payment')
    } finally {
      setIsLoading(false)
    }
  }

  const payeeOptions = formData.payee_type === 'customer' ? customers : vendors

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{payment ? 'Edit Payment' : 'New Payment'}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Payee Selection */}
          <div className="space-y-4">
            <div className="border-b pb-2">
              <h3 className="text-lg font-medium">Select Payee</h3>
              <p className="text-sm text-muted-foreground">Choose who you're making the payment to</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="payee_type">Payee Type</Label>
                <Select
                  value={formData.payee_type}
                  onValueChange={(value) => setFormData({ ...formData, payee_type: value, payee_id: '', invoice_id: '', bill_id: '' })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="customer">Customer</SelectItem>
                    <SelectItem value="vendor">Vendor</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="payee_id">
                  {formData.payee_type === 'customer' ? 'Customer' : 'Vendor'} *
                </Label>
                <Select
                  value={formData.payee_id}
                  onValueChange={(value) => setFormData({ ...formData, payee_id: value, invoice_id: '', bill_id: '' })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={`Select ${formData.payee_type}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {payeeOptions.map((payee) => (
                      <SelectItem key={payee.id} value={payee.id}>
                        {payee.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Related Document Selection */}
          {formData.payee_id && (
            <div className="space-y-4">
              <div className="border-b pb-2">
                <h3 className="text-lg font-medium">
                  Select {formData.payee_type === 'customer' ? 'Invoice' : 'Bill'} to Pay
                </h3>
                <p className="text-sm text-muted-foreground">
                  Choose the specific {formData.payee_type === 'customer' ? 'invoice' : 'bill'} you're paying for, or skip to make a general payment
                </p>
              </div>

              {formData.payee_type === 'customer' ? (
                availableInvoices.length > 0 ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="invoice_select" className="text-base font-medium">
                        Available Invoices
                      </Label>
                      {selectedDocument && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={clearDocumentSelection}
                        >
                          Clear Selection
                        </Button>
                      )}
                    </div>

                    <Select
                      value={formData.invoice_id}
                      onValueChange={handleDocumentSelection}
                    >
                      <SelectTrigger className="h-12">
                        <SelectValue placeholder="Select an invoice to pay (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableInvoices.length === 0 ? (
                          <div className="p-3 text-sm text-muted-foreground text-center">
                            No unpaid invoices available for this customer
                          </div>
                        ) : (
                          availableInvoices.map((invoice) => (
                            <SelectItem key={invoice.id} value={invoice.id} className="py-3">
                              <div className="flex flex-col">
                                <span className="font-medium">{invoice.invoice_number}</span>
                                <span className="text-sm text-muted-foreground">
                                  Due: UGX {(invoice.amount_due || invoice.total_amount).toLocaleString()} •
                                  Due Date: {new Date(invoice.due_date).toLocaleDateString()}
                                </span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                ) : (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
                    <p className="text-sm text-muted-foreground">
                      No unpaid invoices found for this customer. You can still create a general payment.
                    </p>
                  </div>
                )
              ) : (
                availableBills.length > 0 ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="bill_select" className="text-base font-medium">
                        Available Bills
                      </Label>
                      {selectedDocument && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={clearDocumentSelection}
                        >
                          Clear Selection
                        </Button>
                      )}
                    </div>

                    <Select
                      value={formData.bill_id}
                      onValueChange={handleDocumentSelection}
                    >
                      <SelectTrigger className="h-12">
                        <SelectValue placeholder="Select a bill to pay (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableBills.length === 0 ? (
                          <div className="p-3 text-sm text-muted-foreground text-center">
                            No unpaid bills available for this vendor
                          </div>
                        ) : (
                          availableBills.map((bill) => (
                            <SelectItem key={bill.id} value={bill.id} className="py-3">
                              <div className="flex flex-col">
                                <span className="font-medium">{bill.bill_number}</span>
                                <span className="text-sm text-muted-foreground">
                                  Due: UGX {(bill.amount_due || bill.total_amount).toLocaleString()} •
                                  Due Date: {new Date(bill.due_date).toLocaleDateString()}
                                </span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                ) : (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
                    <p className="text-sm text-muted-foreground">
                      No unpaid bills found for this vendor. You can still create a general payment.
                    </p>
                  </div>
                )
              )}

              {selectedDocument && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <h4 className="font-medium text-blue-900">
                        {'invoice_number' in selectedDocument ? selectedDocument.invoice_number : selectedDocument.bill_number}
                      </h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-blue-700">Total Amount:</span>
                          <p className="font-medium">UGX {selectedDocument.total_amount.toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="text-blue-700">Amount Due:</span>
                          <p className="font-medium">UGX {(selectedDocument.amount_due || selectedDocument.total_amount).toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="text-blue-700">Due Date:</span>
                          <p className="font-medium">{new Date(selectedDocument.due_date).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <span className="text-blue-700">Status:</span>
                          <p className="font-medium capitalize">{selectedDocument.status}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Payment Amount and Date */}
          <div className="space-y-4">
            <div className="border-b pb-2">
              <h3 className="text-lg font-medium">Payment Details</h3>
              <p className="text-sm text-muted-foreground">
                {selectedDocument
                  ? `Amount has been auto-filled based on the selected ${formData.payee_type === 'customer' ? 'invoice' : 'bill'}`
                  : 'Enter the payment amount and date'
                }
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="payment_date">Payment Date *</Label>
                <Input
                  id="payment_date"
                  type="date"
                  value={formData.payment_date}
                  onChange={(e) => setFormData({ ...formData, payment_date: e.target.value })}
                  required
                />
              </div>

              <div>
                <Label htmlFor="amount">
                  Payment Amount *
                  {selectedDocument && (
                    <span className="text-sm text-muted-foreground ml-2">
                      (Auto-filled from {formData.payee_type === 'customer' ? 'invoice' : 'bill'})
                    </span>
                  )}
                </Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => handleAmountChange(e.target.value)}
                  placeholder={selectedDocument ? "Amount auto-filled" : "Enter payment amount"}
                  className={amountValidationError ? 'border-red-500 focus:border-red-500' : ''}
                  required
                />
                {amountValidationError && (
                  <p className="text-xs text-red-600 mt-1 flex items-center gap-1">
                    <span>⚠️</span>
                    {amountValidationError}
                  </p>
                )}
                {selectedDocument && !amountValidationError && (
                  <div className="text-xs text-muted-foreground mt-1">
                    <p>Amount due: {formatCurrency(selectedDocument.amount_due || selectedDocument.total_amount)}</p>
                    <p>You can modify this amount if needed (e.g., for partial payments)</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Payment Method */}
          <div className="space-y-4">
            <div className="border-b pb-2">
              <h3 className="text-lg font-medium">Payment Method</h3>
              <p className="text-sm text-muted-foreground">Choose how the payment was made</p>
            </div>

            <div>
              <Label htmlFor="channel">Payment Channel</Label>
              <Select
                value={formData.channel}
                onValueChange={(value: PaymentChannel) => setFormData({
                  ...formData,
                  channel: value,
                  // Clear mobile money number if not mobile money channel
                  mobile_money_number: ['mtn_momo', 'airtel_money'].includes(value) ? formData.mobile_money_number : '',
                  // Clear bank account if not bank channel
                  bank_account_id: value === 'bank' ? formData.bank_account_id : ''
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bank">Bank Transfer</SelectItem>
                  <SelectItem value="mtn_momo">MTN Mobile Money</SelectItem>
                  <SelectItem value="airtel_money">Airtel Money</SelectItem>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          {formData.channel === 'bank' && (
            <div>
              <Label htmlFor="bank_account_id">Bank Account *</Label>
              <Select
                value={formData.bank_account_id}
                onValueChange={(value) => setFormData({ ...formData, bank_account_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select bank account" />
                </SelectTrigger>
                <SelectContent>
                  {bankAccounts.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name} - {account.account_no}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {['mtn_momo', 'airtel_money'].includes(formData.channel) && (
            <div>
              <Label htmlFor="mobile_money_number">Mobile Money Number *</Label>
              <Input
                id="mobile_money_number"
                type="tel"
                value={formData.mobile_money_number}
                onChange={(e) => handleMobileMoneyChange(e.target.value)}
                placeholder="*********"
                className={validationErrors.mobile_money_number ? 'border-red-500' : ''}
                required
              />
              {validationErrors.mobile_money_number && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.mobile_money_number}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {getPhoneNumberHelper()}
              </p>
            </div>
          )}

          <div>
            <Label htmlFor="transaction_id">Transaction ID</Label>
            <Input
              id="transaction_id"
              value={formData.transaction_id}
              onChange={(e) => setFormData({ ...formData, transaction_id: e.target.value })}
              placeholder="Reference number or transaction ID"
            />
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <div className="border-b pb-2">
              <h3 className="text-lg font-medium">Additional Information</h3>
              <p className="text-sm text-muted-foreground">Optional notes and status</p>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Additional notes about the payment"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_reconciled"
                checked={formData.is_reconciled}
                onCheckedChange={(checked) => setFormData({ ...formData, is_reconciled: checked === true })}
              />
              <Label htmlFor="is_reconciled">Mark as paid</Label>
            </div>
          </div>

          {/* Payment Summary */}
          {(formData.payee_id && formData.amount) && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium mb-3">Payment Summary</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Payee:</span>
                  <p className="font-medium">
                    {payeeOptions.find(p => p.id === formData.payee_id)?.name || 'Unknown'}
                  </p>
                </div>
                <div>
                  <span className="text-muted-foreground">Amount:</span>
                  <p className="font-medium">UGX {Number(formData.amount).toLocaleString()}</p>
                </div>
                {selectedDocument && (
                  <>
                    <div>
                      <span className="text-muted-foreground">Related Document:</span>
                      <p className="font-medium">
                        {'invoice_number' in selectedDocument ? selectedDocument.invoice_number : selectedDocument.bill_number}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Document Due:</span>
                      <p className="font-medium">
                        UGX {(selectedDocument.amount_due || selectedDocument.total_amount).toLocaleString()}
                      </p>
                    </div>
                  </>
                )}
                <div>
                  <span className="text-muted-foreground">Payment Method:</span>
                  <p className="font-medium capitalize">{formData.channel.replace('_', ' ')}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Date:</span>
                  <p className="font-medium">{new Date(formData.payment_date).toLocaleDateString()}</p>
                </div>
              </div>
              {selectedDocument && (
                <div className={`mt-3 p-2 rounded text-xs ${
                  amountValidationError
                    ? 'bg-red-50 border border-red-200'
                    : 'bg-blue-50 border border-blue-200'
                }`}>
                  {amountValidationError ? (
                    <p className="text-red-800">
                      ⚠️ {amountValidationError}
                    </p>
                  ) : (
                    <p className="text-blue-800">
                      ✓ This payment will be automatically linked to {formData.payee_type === 'customer' ? 'invoice' : 'bill'} {' '}
                      {'invoice_number' in selectedDocument ? selectedDocument.invoice_number : selectedDocument.bill_number}
                      {Number(formData.amount) >= (selectedDocument.amount_due || selectedDocument.total_amount) &&
                        ` and mark it as paid`
                      }
                      {Number(formData.amount) < (selectedDocument.amount_due || selectedDocument.total_amount) &&
                        ` (partial payment - remaining balance: ${formatCurrency((selectedDocument.amount_due || selectedDocument.total_amount) - Number(formData.amount))})`
                      }
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !formData.payee_id || !formData.amount || !!amountValidationError}
            >
              {isLoading ? 'Saving...' : payment ? 'Update Payment' : 'Create Payment'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
