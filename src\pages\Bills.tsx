import { useEffect, useState, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Bill, BillStatus, Vendor, Account, WithholdingTaxRate } from '@/types/database'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Search, FileText, Check, X, Send } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { BillForm } from '@/components/bills/BillForm'
import { useSubmitForApproval } from '@/hooks/queries/useApprovalWorkflow'
import { useBudgetValidation } from '@/hooks/queries/useBudgetValidation'
import { useBudgetAwareApprovalSubmission } from '@/hooks/queries/useBudgetApprovalWorkflow'
import { BudgetStatusCard } from '@/components/budgets/BudgetStatusCard'
import { BudgetEnforcementDialog } from '@/components/budgets/BudgetEnforcementDialog'
import { useInventoryIntegration } from '@/hooks/useInventoryIntegration'

import { calculateBillLineTotals, type TaxCalculationResult } from '@/utils/taxCalculations'
import { BillList } from '@/components/bills/BillList'
import { LoadingPage } from '@/components/ui/loading'
import type { BillFormData, BillLineData, BillWithVendor } from '@/types/bills'

export const Bills = () => {
  const { profile } = useAuth()
  const [bills, setBills] = useState<BillWithVendor[]>([])
  const [vendors, setVendors] = useState<Vendor[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [withholdingRates, setWithholdingRates] = useState<WithholdingTaxRate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingBill, setEditingBill] = useState<Bill | null>(null)
  const [showBudgetDialog, setShowBudgetDialog] = useState(false)
  const [pendingBillData, setPendingBillData] = useState<{
    formData: BillFormData
    billLines: BillLineData[]
    totals: TaxCalculationResult
  } | null>(null)

  // Approval workflow mutations
  const submitForApprovalMutation = useSubmitForApproval()
  const budgetAwareApprovalMutation = useBudgetAwareApprovalSubmission()
  const { processBillInventory } = useInventoryIntegration()

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)

      const [billsRes, vendorsRes, accountsRes, withholdingRes] = await Promise.all([
        supabase
          .from('bills')
          .select('*, vendors!inner(*)')
          .eq('org_id', profile?.org_id)
          .order('created_at', { ascending: false }),
        supabase
          .from('vendors')
          .select('*')
          .eq('org_id', profile?.org_id)
          .order('name'),
        supabase
          .from('accounts')
          .select('*')
          .eq('org_id', profile?.org_id)
          .eq('is_active', true)
          .order('name'),
        supabase
          .from('withholding_tax_rates')
          .select('*')
          .eq('org_id', profile?.org_id)
          .eq('is_active', true)
          .order('name')
      ])

      if (billsRes.error) throw billsRes.error
      if (vendorsRes.error) throw vendorsRes.error
      if (accountsRes.error) throw accountsRes.error
      if (withholdingRes.error) throw withholdingRes.error

      // Transform the data to match our expected structure
      const transformedBills = billsRes.data?.map(bill => ({
        ...bill,
        vendor: bill.vendors
      })) || []

      setBills(transformedBills)
      setVendors(vendorsRes.data || [])
      setAccounts(accountsRes.data || [])
      setWithholdingRates(withholdingRes.data || [])
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Error",
        description: "Failed to fetch data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id])

  useEffect(() => {
    if (profile?.org_id) {
      fetchData()
    }
  }, [profile?.org_id, fetchData])

  const updateBillStatus = async (bill: Bill, newStatus: BillStatus) => {
    try {
      const { error } = await supabase
        .from('bills')
        .update({ status: newStatus })
        .eq('id', bill.id)

      if (error) throw error

      toast({
        title: "Success",
        description: `Bill ${newStatus}`,
      })
      fetchData()
    } catch (error) {
      console.error('Error updating bill status:', error)
      toast({
        title: "Error",
        description: "Failed to update bill status",
        variant: "destructive",
      })
    }
  }

  const generateBillNumber = () => {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const timestamp = Date.now().toString().slice(-4)
    return `BILL-${year}${month}-${timestamp}`
  }



  const handleSubmit = async (formData: BillFormData, billLines: BillLineData[], bypassBudgetCheck = false) => {
    if (!formData.vendor_id || !formData.account_id || billLines.length === 0) {
      toast({
        title: "Error",
        description: "Please select a vendor, account, and add at least one line item",
        variant: "destructive",
      })
      return
    }

    try {
      const withholdingTaxRateId = formData.withholding_tax_rate_id && formData.withholding_tax_rate_id !== '' ? formData.withholding_tax_rate_id : null
      const totals = calculateBillLineTotals(billLines, withholdingTaxRateId, withholdingRates)

      // Store pending data for potential budget validation
      setPendingBillData({ formData, billLines, totals })
      
      const billData = {
        vendor_id: formData.vendor_id,
        bill_number: formData.bill_number || generateBillNumber(),
        total_amount: totals.totalAmount,
        tax_amount: totals.taxAmount,
        withholding_amount: totals.withholdingAmount,
        withholding_tax_rate_id: withholdingTaxRateId,
        org_id: profile?.org_id,
        status: formData.status || 'draft',
        date_issued: formData.date_issued,
        due_date: formData.due_date,
        notes: formData.notes,
      }

      let billId: string

      if (editingBill) {
        const { error } = await supabase
          .from('bills')
          .update(billData)
          .eq('id', editingBill.id)

        if (error) throw error
        billId = editingBill.id

        // Delete existing lines
        await supabase
          .from('bill_lines')
          .delete()
          .eq('bill_id', billId)
      } else {
        const { data, error } = await supabase
          .from('bills')
          .insert([billData])
          .select()
          .single()

        if (error) throw error
        if (!data) throw new Error('No data returned from insert')
        billId = data.id
      }

      // Insert bill lines
      const linesData = billLines.map(line => ({
        bill_id: billId,
        account_id: formData.account_id && formData.account_id !== '' ? formData.account_id : null, // Use form-level account_id
        description: `${line.item}${line.description ? ' - ' + line.description : ''}`, // Combine item and description
        quantity: line.quantity,
        unit_price: line.unit_price,
        tax_rate_pct: line.tax_rate_pct
      }))

      const { error: linesError } = await supabase
        .from('bill_lines')
        .insert(linesData)

      if (linesError) throw linesError

      // Process inventory transactions for products
      if (!editingBill) { // Only process inventory for new bills
        try {
          await processBillInventory.mutateAsync({
            billId: billData.id,
            billNumber: formData.bill_number,
            lines: billLines
          })
        } catch (inventoryError) {
          console.error('Failed to process inventory transactions:', inventoryError)
          toast({
            title: "Warning",
            description: "Bill created but inventory tracking failed. Please check inventory manually.",
            variant: "destructive",
          })
        }
      }

      toast({
        title: "Success",
        description: editingBill ? "Bill updated successfully" : "Bill created successfully",
      })

      setIsDialogOpen(false)
      setEditingBill(null)
      fetchData()
    } catch (error) {
      console.error('Error saving bill:', error)
      toast({
        title: "Error",
        description: "Failed to save bill",
        variant: "destructive",
      })
      throw error
    }
  }

  const handleEdit = async (bill: Bill) => {
    setEditingBill(bill)
    setIsDialogOpen(true)
  }

  const openCreateDialog = () => {
    setEditingBill(null)
    setIsDialogOpen(true)
  }

  const handleSubmitForApproval = async (bill: BillWithVendor) => {
    try {
      // Get the account ID from bill lines (assuming all lines use the same account)
      const { data: billLines } = await supabase
        .from('bill_lines')
        .select('account_id')
        .eq('bill_id', bill.id)
        .limit(1)

      const accountId = billLines?.[0]?.account_id

      if (accountId) {
        // Use budget-aware approval submission
        await budgetAwareApprovalMutation.mutateAsync({
          documentType: 'bill',
          documentId: bill.id,
          documentAmount: bill.total_amount,
          accountId: accountId,
          currencyCode: 'UGX',
          metadata: {
            vendor_id: bill.vendor_id,
            bill_number: bill.bill_number,
            vendor_name: bill.vendor?.name
          }
        })
      } else {
        // Fallback to standard approval if no account found
        await submitForApprovalMutation.mutateAsync({
          documentType: 'bill',
          documentId: bill.id,
          documentAmount: bill.total_amount,
          currencyCode: 'UGX',
          metadata: {
            vendor_id: bill.vendor_id,
            bill_number: bill.bill_number,
            vendor_name: bill.vendor?.name
          }
        })
      }

      // Refresh bills list
      fetchData()
    } catch (error) {
      console.error('Error submitting bill for approval:', error)
      toast({
        title: "Error",
        description: "Failed to submit bill for approval",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return <LoadingPage text="Loading bills..." fullScreen={false} />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Bills</h1>
          <p className="text-gray-600">Create and manage vendor bills</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search bills..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 w-64"
            />
          </div>
          <Button onClick={openCreateDialog}>
            <Plus className="h-4 w-4 mr-2" />
            Create Bill
          </Button>
        </div>
      </div>

      <BillList
        bills={bills}
        onEdit={handleEdit}
        onStatusChange={updateBillStatus}
        onSubmitForApproval={handleSubmitForApproval}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
      />

      <BillForm
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        editingBill={editingBill}
        vendors={vendors}
        accounts={accounts}
        withholdingRates={withholdingRates}
        onSubmit={handleSubmit}
      />

      {/* Budget Enforcement Dialog */}
      {pendingBillData && (
        <BudgetEnforcementDialog
          open={showBudgetDialog}
          onOpenChange={setShowBudgetDialog}
          validation={{
            isValid: false,
            budgetStatus: null, // Will be populated by the dialog
            wouldExceedBudget: true,
            exceedanceAmount: 0, // Will be calculated
            message: 'Budget validation required'
          }}
          billAmount={pendingBillData.totals.totalAmount}
          onProceed={async (justification, requiresApproval) => {
            if (pendingBillData) {
              // Continue with bill creation, marking for approval if needed
              const modifiedFormData = {
                ...pendingBillData.formData,
                status: requiresApproval ? 'pending_budget_approval' : pendingBillData.formData.status,
                notes: pendingBillData.formData.notes +
                  (justification ? `\n\nBudget Override Justification: ${justification}` : '')
              }
              await handleSubmit(modifiedFormData, pendingBillData.billLines, true)
              setPendingBillData(null)
            }
          }}
          onCancel={() => {
            setPendingBillData(null)
          }}
        />
      )}
    </div>
  )
}
