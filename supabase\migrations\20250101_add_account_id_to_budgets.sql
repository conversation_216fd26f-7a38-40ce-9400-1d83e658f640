-- Add account_id column to budgets table to follow invoice/bill pattern
-- This allows one account per budget instead of per budget line

DO $$
BEGIN
    RAISE NOTICE '🔧 Adding account_id column to budgets table...';
END $$;

-- Add account_id column to budgets table
ALTER TABLE budgets 
ADD COLUMN IF NOT EXISTS account_id UUID REFERENCES accounts(id);

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_budgets_account_id ON budgets(account_id);

-- Update the foreign key constraint
DO $$
BEGIN
    -- Add foreign key constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'budgets_account_id_fkey' 
        AND table_name = 'budgets'
    ) THEN
        ALTER TABLE budgets 
        ADD CONSTRAINT budgets_account_id_fkey 
        FOREIGN KEY (account_id) REFERENCES accounts(id);
    END IF;
END $$;

-- Remove account_id column from budget_lines table since it will be inherited from budget
-- First, let's check if there are any existing budget lines with different accounts
DO $$
DECLARE
    conflict_count INTEGER;
BEGIN
    RAISE NOTICE '📋 Checking for account conflicts in existing budget lines...';
    
    -- Count budget lines that have different accounts within the same budget
    SELECT COUNT(*) INTO conflict_count
    FROM (
        SELECT budget_id, COUNT(DISTINCT account_id) as account_count
        FROM budget_lines
        GROUP BY budget_id
        HAVING COUNT(DISTINCT account_id) > 1
    ) conflicts;
    
    IF conflict_count > 0 THEN
        RAISE NOTICE '⚠️  Found % budgets with mixed accounts in lines. These will need manual review.', conflict_count;
        
        -- Log the conflicting budgets for manual review
        RAISE NOTICE 'Conflicting budget IDs:';
        FOR budget_id IN 
            SELECT bl.budget_id
            FROM budget_lines bl
            GROUP BY bl.budget_id
            HAVING COUNT(DISTINCT bl.account_id) > 1
        LOOP
            RAISE NOTICE '  - Budget ID: %', budget_id;
        END LOOP;
    ELSE
        RAISE NOTICE '✅ No account conflicts found in budget lines.';
    END IF;
END $$;

-- Migrate existing data: set budget.account_id from the first budget line
DO $$
DECLARE
    budget_record RECORD;
    first_account_id UUID;
BEGIN
    RAISE NOTICE '📋 Migrating account_id from budget lines to budgets...';
    
    FOR budget_record IN 
        SELECT DISTINCT b.id, b.name
        FROM budgets b
        INNER JOIN budget_lines bl ON bl.budget_id = b.id
        WHERE b.account_id IS NULL
    LOOP
        -- Get the first account_id from budget lines for this budget
        SELECT account_id INTO first_account_id
        FROM budget_lines
        WHERE budget_id = budget_record.id
        LIMIT 1;
        
        -- Update the budget with this account_id
        UPDATE budgets 
        SET account_id = first_account_id
        WHERE id = budget_record.id;
        
        RAISE NOTICE '  ✅ Updated budget "%" with account_id', budget_record.name;
    END LOOP;
    
    RAISE NOTICE '✅ Account migration completed.';
END $$;

-- Add comment to document the change
COMMENT ON COLUMN budgets.account_id IS 'Account associated with this budget. All budget lines inherit this account.';

-- Final status messages
DO $$
BEGIN
    RAISE NOTICE '📋 Keeping account_id in budget_lines for backward compatibility during transition.';
    RAISE NOTICE '    This can be removed in a future migration once all applications are updated.';
    RAISE NOTICE '';
    RAISE NOTICE '✅ BUDGET ACCOUNT MIGRATION COMPLETED!';
    RAISE NOTICE '================================================';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 CHANGES MADE:';
    RAISE NOTICE '  • Added account_id column to budgets table';
    RAISE NOTICE '  • Migrated existing account data from budget_lines';
    RAISE NOTICE '  • Added foreign key constraint and index';
    RAISE NOTICE '  • Kept budget_lines.account_id for backward compatibility';
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '  • Update application code to use budget.account_id';
    RAISE NOTICE '  • Test the new budget form implementation';
    RAISE NOTICE '  • Remove budget_lines.account_id in future migration';
    RAISE NOTICE '';
END $$;
