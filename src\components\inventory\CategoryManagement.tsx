import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  ChevronRight, 
  ChevronDown,
  Folder,
  FolderOpen,
  Tag,
  Move,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  useProductCategories,
  useCreateProductCategory,
  useUpdateProductCategory,
  useDeleteProductCategory,
  useToggleProductCategoryStatus
} from '@/hooks/queries'
import type { ProductCategory } from '@/types/extended-database'

const categoryFormSchema = z.object({
  name: z.string().min(1, 'Category name is required').max(255, 'Name must be 255 characters or less'),
  description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
  code: z.string().max(50, 'Code must be 50 characters or less').optional(),
  parent_id: z.string().optional(),
  sort_order: z.number().min(0, 'Sort order must be positive').default(0),
  is_active: z.boolean().default(true),
})

type CategoryFormValues = z.infer<typeof categoryFormSchema>

interface CategoryTreeNode extends ProductCategory {
  children: CategoryTreeNode[]
  level: number
  productCount?: number
}

interface CategoryManagementProps {
  onCategorySelect?: (category: ProductCategory | null) => void
  selectedCategoryId?: string | null
}

export function CategoryManagement({ onCategorySelect, selectedCategoryId }: CategoryManagementProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  const [showForm, setShowForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<ProductCategory | null>(null)
  const [showInactive, setShowInactive] = useState(false)

  const { data: categories = [], isLoading } = useProductCategories()
  const createCategory = useCreateProductCategory()
  const updateCategory = useUpdateProductCategory()
  const deleteCategory = useDeleteProductCategory()
  const toggleStatus = useToggleProductCategoryStatus()

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: '',
      description: '',
      code: '',
      parent_id: 'none',
      sort_order: 0,
      is_active: true,
    },
  })

  // Build hierarchical tree structure
  const categoryTree = useMemo(() => {
    const buildTree = (parentId: string | null = null, level: number = 0): CategoryTreeNode[] => {
      return categories
        .filter(cat => cat.parent_id === parentId)
        .filter(cat => showInactive || cat.is_active)
        .filter(cat => {
          if (!searchTerm) return true
          return cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 cat.code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 cat.description?.toLowerCase().includes(searchTerm.toLowerCase())
        })
        .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0) || a.name.localeCompare(b.name))
        .map(cat => ({
          ...cat,
          children: buildTree(cat.id, level + 1),
          level,
          productCount: 0 // TODO: Add product count from API
        }))
    }
    return buildTree()
  }, [categories, showInactive, searchTerm])

  const handleToggleExpand = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const handleCreateCategory = () => {
    setEditingCategory(null)
    form.reset({
      name: '',
      description: '',
      code: '',
      parent_id: 'none',
      sort_order: 0,
      is_active: true,
    })
    setShowForm(true)
  }

  const handleEditCategory = (category: ProductCategory) => {
    setEditingCategory(category)
    form.reset({
      name: category.name,
      description: category.description || '',
      code: category.code || '',
      parent_id: category.parent_id || 'none',
      sort_order: category.sort_order || 0,
      is_active: category.is_active,
    })
    setShowForm(true)
  }

  const handleDeleteCategory = async (category: ProductCategory) => {
    if (window.confirm(`Are you sure you want to delete "${category.name}"? This action cannot be undone.`)) {
      try {
        await deleteCategory.mutateAsync(category.id)
      } catch (error) {
        console.error('Failed to delete category:', error)
      }
    }
  }

  const handleToggleStatus = async (category: ProductCategory) => {
    try {
      await toggleStatus.mutateAsync({
        categoryId: category.id,
        isActive: !category.is_active
      })
    } catch (error) {
      console.error('Failed to toggle category status:', error)
    }
  }

  const onSubmit = async (values: CategoryFormValues) => {
    try {
      const formData = {
        ...values,
        parent_id: values.parent_id === 'none' ? null : values.parent_id,
        description: values.description || null,
        code: values.code || null,
      }

      if (editingCategory) {
        await updateCategory.mutateAsync({
          categoryId: editingCategory.id,
          categoryData: formData,
        })
      } else {
        await createCategory.mutateAsync(formData)
      }

      setShowForm(false)
      setEditingCategory(null)
    } catch (error) {
      console.error('Failed to save category:', error)
    }
  }

  const renderCategoryNode = (node: CategoryTreeNode) => {
    const hasChildren = node.children.length > 0
    const isExpanded = expandedCategories.has(node.id)
    const isSelected = selectedCategoryId === node.id

    return (
      <div key={node.id} className="select-none">
        <div 
          className={`flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 cursor-pointer ${
            isSelected ? 'bg-blue-50 border border-blue-200' : ''
          }`}
          style={{ paddingLeft: `${node.level * 20 + 8}px` }}
          onClick={() => onCategorySelect?.(node)}
        >
          {hasChildren ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation()
                handleToggleExpand(node.id)
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          ) : (
            <div className="w-6" />
          )}

          {hasChildren ? (
            isExpanded ? (
              <FolderOpen className="h-4 w-4 text-blue-600" />
            ) : (
              <Folder className="h-4 w-4 text-blue-600" />
            )
          ) : (
            <Tag className="h-4 w-4 text-gray-500" />
          )}

          <span className={`flex-1 ${!node.is_active ? 'text-gray-500' : ''}`}>
            {node.name}
          </span>

          {node.code && (
            <Badge variant="outline" className="text-xs">
              {node.code}
            </Badge>
          )}

          {!node.is_active && (
            <Badge variant="secondary" className="text-xs">
              Inactive
            </Badge>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditCategory(node)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Category
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleToggleStatus(node)}>
                {node.is_active ? (
                  <>
                    <XCircle className="mr-2 h-4 w-4" />
                    Deactivate
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Activate
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDeleteCategory(node)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {hasChildren && isExpanded && (
          <div>
            {node.children.map(child => renderCategoryNode(child))}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                Product Categories
                <Badge variant="secondary" className="ml-2">
                  {categories.length}
                </Badge>
              </CardTitle>
              <CardDescription>
                Organize your products into categories and subcategories
              </CardDescription>
            </div>
            <Button onClick={handleCreateCategory}>
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search and Filters */}
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-inactive"
                  checked={showInactive}
                  onCheckedChange={setShowInactive}
                />
                <label htmlFor="show-inactive" className="text-sm font-medium">
                  Show inactive
                </label>
              </div>
            </div>

            {/* Category Tree */}
            <div className="border rounded-lg">
              {isLoading ? (
                <div className="p-8 text-center text-gray-500">
                  Loading categories...
                </div>
              ) : categoryTree.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  {searchTerm || !showInactive && categories.some(c => !c.is_active)
                    ? 'No categories match your search criteria.'
                    : 'No categories found. Create your first category to get started.'
                  }
                </div>
              ) : (
                <div className="p-2">
                  {categoryTree.map(node => renderCategoryNode(node))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Form Dialog */}
      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? 'Edit Category' : 'Create New Category'}
            </DialogTitle>
            <DialogDescription>
              {editingCategory
                ? 'Update the category information below.'
                : 'Add a new category to organize your products.'
              }
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter category name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category Code</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., ELEC, FURN" {...field} />
                    </FormControl>
                    <FormDescription>
                      Short code for SKU generation and reporting
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter category description"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="parent_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Parent Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select parent category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No Parent (Top Level)</SelectItem>
                        {categories
                          .filter(cat => cat.id !== editingCategory?.id) // Prevent self-parent
                          .filter(cat => cat.is_active)
                          .map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose a parent to create a subcategory
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="sort_order"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sort Order</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>
                        Lower numbers appear first
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 pt-6">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active</FormLabel>
                        <FormDescription>
                          Category is active and visible
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowForm(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {editingCategory ? 'Update Category' : 'Create Category'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}
