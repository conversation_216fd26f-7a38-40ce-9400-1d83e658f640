/**
 * User Authentication Test Component
 * Tests authentication and onboarding status for the specific user
 */

import React, { useState } from 'react'
import { supabase } from '@/integrations/supabase/client'
import { checkOnboardingStatus } from '@/lib/onboardingUtils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle, XCircle, User } from 'lucide-react'

interface TestResult {
  test: string
  status: 'success' | 'error' | 'pending'
  message: string
  data?: Record<string, unknown>
}

export const UserAuthTest: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('')
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [testing, setTesting] = useState(false)

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const runAuthTest = async () => {
    setTesting(true)
    setTestResults([])

    try {
      // Test 1: Sign in the user
      addTestResult({
        test: 'User Authentication',
        status: 'pending',
        message: 'Attempting to sign in...'
      })

      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (authError) {
        addTestResult({
          test: 'User Authentication',
          status: 'error',
          message: `Authentication failed: ${authError.message}`,
          data: authError
        })
        setTesting(false)
        return
      }

      addTestResult({
        test: 'User Authentication',
        status: 'success',
        message: 'User authenticated successfully',
        data: {
          userId: authData.user?.id,
          email: authData.user?.email
        }
      })

      const userId = authData.user?.id
      if (!userId) {
        addTestResult({
          test: 'User ID Check',
          status: 'error',
          message: 'No user ID found in auth response'
        })
        setTesting(false)
        return
      }

      // Test 2: Direct profile query
      addTestResult({
        test: 'Direct Profile Query',
        status: 'pending',
        message: 'Querying profile directly...'
      })

      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle()

      if (profileError) {
        addTestResult({
          test: 'Direct Profile Query',
          status: 'error',
          message: `Profile query failed: ${profileError.message}`,
          data: profileError
        })
      } else {
        addTestResult({
          test: 'Direct Profile Query',
          status: 'success',
          message: profileData ? 'Profile found' : 'No profile found',
          data: profileData
        })
      }

      // Test 3: Onboarding status check
      addTestResult({
        test: 'Onboarding Status Check',
        status: 'pending',
        message: 'Checking onboarding status...'
      })

      try {
        const onboardingStatus = await checkOnboardingStatus(userId)
        addTestResult({
          test: 'Onboarding Status Check',
          status: 'success',
          message: `Onboarding check completed. Needs onboarding: ${onboardingStatus.needsOnboarding}`,
          data: onboardingStatus
        })
      } catch (onboardingError) {
        addTestResult({
          test: 'Onboarding Status Check',
          status: 'error',
          message: `Onboarding check failed: ${(onboardingError as Error).message}`,
          data: onboardingError
        })
      }

      // Test 4: Organization query
      if (profileData?.org_id) {
        addTestResult({
          test: 'Organization Query',
          status: 'pending',
          message: 'Querying organization...'
        })

        const { data: orgData, error: orgError } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', profileData.org_id)
          .maybeSingle()

        if (orgError) {
          addTestResult({
            test: 'Organization Query',
            status: 'error',
            message: `Organization query failed: ${orgError.message}`,
            data: orgError
          })
        } else {
          addTestResult({
            test: 'Organization Query',
            status: 'success',
            message: orgData ? 'Organization found' : 'No organization found',
            data: orgData
          })
        }
      }

    } catch (error) {
      addTestResult({
        test: 'General Error',
        status: 'error',
        message: `Unexpected error: ${(error as Error).message}`,
        data: error
      })
    }

    setTesting(false)
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: TestResult['status']) => {
    const variant = status === 'success' ? 'default' : status === 'error' ? 'destructive' : 'secondary'
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5 text-blue-500" />
          User Authentication Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Test Form */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email"
            />
          </div>
          <div>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
            />
          </div>
        </div>

        <Button
          onClick={runAuthTest}
          disabled={testing || !email || !password}
          className="w-full"
        >
          {testing ? 'Running Tests...' : 'Run Authentication Test'}
        </Button>

        {/* Test Results */}
        <div className="space-y-4">
          {testResults.map((result, index) => (
            <div key={index} className="flex items-start justify-between p-3 border rounded-lg">
              <div className="flex items-start gap-3 flex-1">
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <div className="font-medium">{result.test}</div>
                  <div className="text-sm text-muted-foreground">{result.message}</div>
                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-xs text-muted-foreground cursor-pointer">Data</summary>
                      <pre className="text-xs mt-1 p-2 bg-muted rounded overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
              {getStatusBadge(result.status)}
            </div>
          ))}
        </div>

        {testResults.length === 0 && !testing && (
          <div className="text-center text-muted-foreground py-8">
            Enter credentials and click "Run Authentication Test" to start
          </div>
        )}
      </CardContent>
    </Card>
  )
}
