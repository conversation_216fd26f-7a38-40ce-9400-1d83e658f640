// Get version from package.json
// In a real production app, this would be injected at build time
export const getAppVersion = (): string => {
  // For now, we'll use a static version that matches our app
  // In production, this could be injected via environment variables or build process
  return '1.0.0';
};

// Get build information
export const getBuildInfo = () => {
  const buildDate = new Date().toISOString().split('T')[0]; // Current date as placeholder
  return {
    version: getAppVersion(),
    buildDate,
    environment: import.meta.env.MODE || 'development',
  };
};

// Get system status information
export const getSystemStatus = () => {
  const isOnline = navigator.onLine;
  return {
    online: isOnline,
    status: isOnline ? 'Connected' : 'Offline',
    mode: isOnline ? 'Cloud Sync Active' : 'Offline Mode',
    dataSync: isOnline ? 'Real-time updates' : 'Local storage only',
    lastSync: isOnline ? 'Just now' : 'Unavailable',
  };
};

// Format version for display
export const getDisplayVersion = (): string => {
  const version = getAppVersion();
  const env = import.meta.env.MODE;

  if (env === 'development') {
    return `${version}-dev`;
  } else if (env === 'staging') {
    return `${version}-staging`;
  }

  return version;
};
