/**
 * Health Check API Endpoint
 * Provides system health status for monitoring and load balancers
 */

import { NextApiRequest, NextApiResponse } from 'next'
import { notificationHealthChecker, NotificationHealthChecks } from '@/lib/monitoring'

export interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  version: string
  uptime: number
  checks: {
    name: string
    status: 'healthy' | 'degraded' | 'unhealthy'
    message?: string
    responseTime?: number
    details?: Record<string, unknown>
  }[]
  summary: {
    total: number
    healthy: number
    degraded: number
    unhealthy: number
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<HealthResponse | { error: string }>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const startTime = Date.now()

    // Run all health checks
    const [dbHealth, realtimeHealth, perfHealth] = await Promise.all([
      notificationHealthChecker.runCheck('database', NotificationHealthChecks.database),
      notificationHealthChecker.runCheck('realtime', NotificationHealthChecks.realtime),
      notificationHealthChecker.runCheck('performance', NotificationHealthChecks.notificationPerformance)
    ])

    const checks = [dbHealth, realtimeHealth, perfHealth]
    
    // Calculate summary
    const summary = {
      total: checks.length,
      healthy: checks.filter(c => c.status === 'healthy').length,
      degraded: checks.filter(c => c.status === 'degraded').length,
      unhealthy: checks.filter(c => c.status === 'unhealthy').length
    }

    // Determine overall status
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy'
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded'
    }

    const response: HealthResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      checks: checks.map(check => ({
        name: check.name,
        status: check.status,
        message: check.message,
        responseTime: check.responseTime,
        details: check.details
      })),
      summary
    }

    // Set appropriate HTTP status code
    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503

    // Add response time header
    res.setHeader('X-Response-Time', `${Date.now() - startTime}ms`)
    
    // Cache control headers
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
    res.setHeader('Pragma', 'no-cache')
    res.setHeader('Expires', '0')

    res.status(statusCode).json(response)
  } catch (error) {
    console.error('Health check failed:', error)
    
    res.status(503).json({
      error: 'Health check failed',
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      checks: [],
      summary: { total: 0, healthy: 0, degraded: 0, unhealthy: 1 }
    })
  }
}
