import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Plus } from 'lucide-react'
import { ProductLineItem } from '@/components/inventory/ProductLineItem'
import { useInventoryIntegration } from '@/hooks/useInventoryIntegration'
import { supabase } from '@/lib/supabase'
import { useBudgetValidation } from '@/hooks/queries/useBudgetValidation'
import { BudgetStatusCard } from '@/components/budgets/BudgetStatusCard'
import type { BillFormProps, BillFormData, BillLineData } from '@/types/bills'
import type { BillStatus } from '@/types/database'

const initialFormData: BillFormData = {
  vendor_id: '',
  account_id: '',
  bill_number: '',
  date_issued: new Date().toISOString().split('T')[0],
  due_date: '',
  withholding_tax_rate_id: '',
  notes: '',
  status: 'draft',
}

const initialLineData: BillLineData = {
  product_id: '',
  item: '',
  description: '',
  quantity: 1,
  unit_price: 0,
  tax_rate_pct: 0,
}

export function BillForm({
  open,
  onOpenChange,
  editingBill,
  vendors,
  accounts,
  withholdingRates,
  onSubmit,
  preselectedVendorId
}: BillFormProps) {
  const [formData, setFormData] = useState<BillFormData>(
    editingBill ? {
      vendor_id: editingBill.vendor_id,
      account_id: '', // Will be populated from existing bill lines
      bill_number: editingBill.bill_number,
      date_issued: editingBill.date_issued,
      due_date: editingBill.due_date,
      withholding_tax_rate_id: editingBill.withholding_tax_rate_id || '',
      notes: editingBill.notes || '',
      status: editingBill.status,
    } : {
      ...initialFormData,
      vendor_id: preselectedVendorId || ''
    }
  )
  const [billLines, setBillLines] = useState<BillLineData[]>(
    editingBill ? [] : [{ ...initialLineData }]
  )
  const [submitting, setSubmitting] = useState(false)
  const { processBillInventory } = useInventoryIntegration()

  // Calculate total amount for budget validation
  const totalAmount = billLines.reduce((total, line) => {
    return total + (line.quantity * line.unit_price)
  }, 0)

  // Budget validation
  const { validation, isLoading: budgetLoading, budgetStatus } = useBudgetValidation(
    formData.account_id || null,
    totalAmount
  )

  // Update form data when preselected vendor changes
  useEffect(() => {
    if (preselectedVendorId && !editingBill) {
      setFormData(prev => ({ ...prev, vendor_id: preselectedVendorId }))
    }
  }, [preselectedVendorId, editingBill])

  // Load existing bill lines when editing
  useEffect(() => {
    const loadBillLines = async () => {
      if (!editingBill) return

      try {
        const { data: lines, error } = await supabase
          .from('bill_lines')
          .select('*')
          .eq('bill_id', editingBill.id)
          .order('created_at')

        if (error) throw error

        if (lines && lines.length > 0) {
          // Set the account_id from the first line (since all lines should have the same account)
          const firstLine = lines[0]
          if (firstLine.account_id) {
            setFormData(prev => ({ ...prev, account_id: firstLine.account_id }))
          }

          // Convert database lines to form format
          const formLines: BillLineData[] = lines.map(line => {
            // Split the description to extract item and description
            const description = line.description || ''
            const parts = description.split(' - ')
            const item = parts[0] || ''
            const itemDescription = parts.length > 1 ? parts.slice(1).join(' - ') : ''

            return {
              product_id: line.product_id || '',
              item,
              description: itemDescription,
              quantity: line.quantity,
              unit_price: line.unit_price,
              tax_rate_pct: line.tax_rate_pct
            }
          })

          setBillLines(formLines)
        } else {
          setBillLines([{ ...initialLineData }])
        }
      } catch (error) {
        console.error('Error loading bill lines:', error)
        setBillLines([{ ...initialLineData }])
      }
    }

    loadBillLines()
  }, [editingBill])

  const addBillLine = () => {
    setBillLines([...billLines, { ...initialLineData }])
  }

  const updateBillLine = (index: number, field: keyof BillLineData, value: string | number) => {
    const updatedLines = [...billLines]
    updatedLines[index] = { ...updatedLines[index], [field]: value }
    setBillLines(updatedLines)
  }

  const removeBillLine = (index: number) => {
    setBillLines(billLines.filter((_, i) => i !== index))
  }

  const calculateLineTotals = () => {
    let subtotal = 0
    let taxAmount = 0

    billLines.forEach(line => {
      const lineTotal = line.quantity * line.unit_price
      subtotal += lineTotal
      taxAmount += lineTotal * (line.tax_rate_pct / 100)
    })

    const totalBeforeWithholding = subtotal + taxAmount
    const withholdingRate = withholdingRates.find(r => r.id === formData.withholding_tax_rate_id)
    const withholdingAmount = withholdingRate ? totalBeforeWithholding * (withholdingRate.rate_pct / 100) : 0
    const totalAmount = totalBeforeWithholding - withholdingAmount

    return {
      subtotal,
      taxAmount,
      withholdingAmount,
      totalAmount
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    try {
      await onSubmit(formData, billLines)
      setFormData(initialFormData)
      setBillLines([{ ...initialLineData }])
    } finally {
      setSubmitting(false)
    }
  }

  const totals = calculateLineTotals()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingBill ? 'Edit Bill' : 'Create New Bill'}
          </DialogTitle>
          <DialogDescription>
            {editingBill ? 'Update the bill details' : 'Create a new bill for a vendor'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vendor_id">Vendor *</Label>
              <Select value={formData.vendor_id || ''} onValueChange={(value) => setFormData({ ...formData, vendor_id: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="bill_number">Bill Number</Label>
              <Input
                id="bill_number"
                value={formData.bill_number}
                onChange={(e) => setFormData({ ...formData, bill_number: e.target.value })}
                placeholder="Auto-generated if empty"
              />
            </div>

            <div>
              <Label htmlFor="date_issued">Date Issued *</Label>
              <Input
                id="date_issued"
                type="date"
                value={formData.date_issued}
                onChange={(e) => setFormData({ ...formData, date_issued: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="due_date">Due Date *</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => setFormData({ ...formData, due_date: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="withholding_tax_rate_id">Withholding Tax Rate</Label>
              <Select value={formData.withholding_tax_rate_id || 'none'} onValueChange={(value) => setFormData({ ...formData, withholding_tax_rate_id: value === 'none' ? '' : value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select withholding tax rate" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No withholding tax</SelectItem>
                  {withholdingRates.map((rate) => (
                    <SelectItem key={rate.id} value={rate.id}>
                      {rate.name} ({rate.rate_pct}%)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value as BillStatus })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Additional notes for the bill"
              rows={3}
            />
          </div>

          <div>
            <div className="flex items-center justify-between mb-4">
              <Label>Bill Lines</Label>
              <div className="flex items-center gap-4">
                <div className="min-w-[200px]">
                  <Label htmlFor="account_id">Account *</Label>
                  <Select value={formData.account_id || ''} onValueChange={(value) => setFormData({ ...formData, account_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select account" />
                    </SelectTrigger>
                    <SelectContent>
                      {accounts.map((account) => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.name} ({account.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Button type="button" variant="outline" onClick={addBillLine} className="mt-6">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Line
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              {billLines.map((line, index) => (
                <ProductLineItem
                  key={index}
                  line={line}
                  index={index}
                  onUpdate={updateBillLine}
                  onRemove={removeBillLine}
                  isLast={billLines.length === 1}
                  showProductSelection={true}
                />
              ))}
            </div>

            {billLines.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>UGX {totals.subtotal.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>UGX {totals.taxAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Withholding Tax:</span>
                    <span>-UGX {totals.withholdingAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-bold border-t pt-2">
                    <span>Total:</span>
                    <span>UGX {totals.totalAmount.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Budget Status Card */}
          {formData.account_id && (
            <BudgetStatusCard
              budgetStatus={budgetStatus}
              validation={validation}
              billAmount={totalAmount}
              isLoading={budgetLoading}
              className="mb-4"
            />
          )}

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={submitting}>
              {submitting ? 'Saving...' : editingBill ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
} 
