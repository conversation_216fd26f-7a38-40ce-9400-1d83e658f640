import { useState, useCallback } from 'react'

interface UseLoadingOptions {
  initialState?: boolean
}

interface UseLoadingReturn {
  loading: boolean
  setLoading: (loading: boolean) => void
  startLoading: () => void
  stopLoading: () => void
  withLoading: <T>(asyncFn: () => Promise<T>) => Promise<T>
}

/**
 * Custom hook for managing loading states
 * 
 * @param options - Configuration options
 * @returns Loading state and control functions
 * 
 * @example
 * ```tsx
 * const { loading, withLoading } = useLoading()
 * 
 * const handleSubmit = async () => {
 *   await withLoading(async () => {
 *     await submitForm()
 *   })
 * }
 * ```
 */
export function useLoading(options: UseLoadingOptions = {}): UseLoadingReturn {
  const { initialState = false } = options
  const [loading, setLoading] = useState(initialState)

  const startLoading = useCallback(() => {
    setLoading(true)
  }, [])

  const stopLoading = useCallback(() => {
    setLoading(false)
  }, [])

  const withLoading = useCallback(async <T>(asyncFn: () => Promise<T>): Promise<T> => {
    try {
      setLoading(true)
      const result = await asyncFn()
      return result
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    loading,
    setLoading,
    startLoading,
    stopLoading,
    withLoading
  }
}

/**
 * Hook for managing multiple loading states
 * 
 * @example
 * ```tsx
 * const { loading, setLoading } = useMultipleLoading({
 *   submit: false,
 *   delete: false,
 *   fetch: true
 * })
 * 
 * // loading.submit, loading.delete, loading.fetch
 * // setLoading('submit', true)
 * ```
 */
export function useMultipleLoading<T extends Record<string, boolean>>(
  initialStates: T
): {
  loading: T
  setLoading: (key: keyof T, value: boolean) => void
  startLoading: (key: keyof T) => void
  stopLoading: (key: keyof T) => void
  withLoading: <R>(key: keyof T, asyncFn: () => Promise<R>) => Promise<R>
} {
  const [loading, setLoadingState] = useState<T>(initialStates)

  const setLoading = useCallback((key: keyof T, value: boolean) => {
    setLoadingState(prev => ({ ...prev, [key]: value }))
  }, [])

  const startLoading = useCallback((key: keyof T) => {
    setLoading(key, true)
  }, [setLoading])

  const stopLoading = useCallback((key: keyof T) => {
    setLoading(key, false)
  }, [setLoading])

  const withLoading = useCallback(async <R>(
    key: keyof T, 
    asyncFn: () => Promise<R>
  ): Promise<R> => {
    try {
      setLoading(key, true)
      const result = await asyncFn()
      return result
    } finally {
      setLoading(key, false)
    }
  }, [setLoading])

  return {
    loading,
    setLoading,
    startLoading,
    stopLoading,
    withLoading
  }
}

/**
 * Hook for managing loading states with error handling
 */
export function useLoadingWithError<E = Error>(options: UseLoadingOptions = {}) {
  const { loading, setLoading, startLoading, stopLoading, withLoading } = useLoading(options)
  const [error, setError] = useState<E | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const withLoadingAndError = useCallback(async <T>(
    asyncFn: () => Promise<T>
  ): Promise<T | null> => {
    try {
      setError(null)
      const result = await withLoading(asyncFn)
      return result
    } catch (err) {
      setError(err as E)
      return null
    }
  }, [withLoading])

  return {
    loading,
    error,
    setLoading,
    startLoading,
    stopLoading,
    clearError,
    withLoading: withLoadingAndError
  }
}
