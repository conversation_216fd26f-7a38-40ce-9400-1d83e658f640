/**
 * Invoice PDF Test Component
 * For testing the PDF export functionality
 */

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Printer, Download, FileText } from 'lucide-react'
import { useInvoicePdfExport } from '@/hooks/useInvoicePdfExport'
import type { InvoicePdfData } from '@/lib/invoicePdfExport'

// Test data
const testInvoice: InvoicePdfData = {
  id: 'test-invoice-1',
  org_id: 'test-org-1',
  customer_id: 'test-customer-1',
  invoice_number: 'INV-2025-001',
  date_issued: '2025-06-29',
  due_date: '2025-07-29',
  total_amount: 12000,
  tax_amount: 2000,
  notes: 'Thank you for your business! Payment is due within 30 days.',
  status: 'sent',
  created_at: '2025-06-29T10:00:00Z',
  updated_at: '2025-06-29T10:00:00Z',
  customers: {
    id: 'test-customer-1',
    org_id: 'test-org-1',
    name: 'Acme Corporation Ltd',
    email: '<EMAIL>',
    phone: '+*********** 456',
    address: '123 Business Street, Kampala, Uganda',
    is_active: true,
    created_at: '2025-06-01T00:00:00Z',
    updated_at: '2025-06-01T00:00:00Z'
  },
  invoice_lines: [
    {
      id: 'line-1',
      account_id: 'acc-1',
      description: 'Web Development Services - Custom E-commerce Platform',
      quantity: 1,
      unit_price: 8000,
      tax_rate_pct: 18,
      line_total: 8000,
      tax_amount: 1440,
      accounts: {
        code: '4000',
        name: 'Sales Revenue'
      }
    },
    {
      id: 'line-2',
      account_id: 'acc-2',
      description: 'Monthly Hosting & Maintenance',
      quantity: 3,
      unit_price: 1000,
      tax_rate_pct: 18,
      line_total: 3000,
      tax_amount: 540,
      accounts: {
        code: '4100',
        name: 'Service Revenue'
      }
    },
    {
      id: 'line-3',
      account_id: 'acc-3',
      description: 'SSL Certificate Setup',
      quantity: 1,
      unit_price: 200,
      tax_rate_pct: 0,
      line_total: 200,
      tax_amount: 0,
      accounts: {
        code: '4200',
        name: 'Other Revenue'
      }
    }
  ]
}

export function InvoicePdfTest() {
  const { exportToPdf, printInvoice, isExporting } = useInvoicePdfExport()
  const [testResult, setTestResult] = useState<string | null>(null)

  const handleExportTest = async () => {
    setTestResult(null)
    try {
      const success = await exportToPdf(testInvoice)
      setTestResult(success ? 'PDF export successful!' : 'PDF export failed')
    } catch (error) {
      setTestResult(`PDF export error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const handlePrintTest = async () => {
    setTestResult(null)
    try {
      const success = await printInvoice(testInvoice)
      setTestResult(success ? 'Print dialog opened successfully!' : 'Print failed')
    } catch (error) {
      setTestResult(`Print error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6 max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Invoice PDF Export Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Invoice Preview */}
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="font-semibold mb-4">Test Invoice Preview</h3>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p><strong>Invoice Number:</strong> {testInvoice.invoice_number}</p>
                <p><strong>Customer:</strong> {testInvoice.customers?.name}</p>
                <p><strong>Email:</strong> {testInvoice.customers?.email}</p>
              </div>
              <div>
                <p><strong>Date Issued:</strong> {new Date(testInvoice.date_issued).toLocaleDateString()}</p>
                <p><strong>Due Date:</strong> {new Date(testInvoice.due_date).toLocaleDateString()}</p>
                <p><strong>Status:</strong> <Badge className={getStatusColor(testInvoice.status)}>{testInvoice.status}</Badge></p>
              </div>
            </div>
            
            <div className="mb-4">
              <h4 className="font-medium mb-2">Line Items:</h4>
              <div className="space-y-2">
                {testInvoice.invoice_lines?.map((line, index) => (
                  <div key={index} className="flex justify-between items-center text-sm bg-white p-2 rounded">
                    <span>{line.description}</span>
                    <span className="font-medium">UGX {(line.line_total + line.tax_amount).toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="border-t pt-2">
              <div className="flex justify-between font-bold">
                <span>Total Amount:</span>
                <span>UGX {testInvoice.total_amount.toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Test Buttons */}
          <div className="flex gap-4">
            <Button
              onClick={handlePrintTest}
              disabled={isExporting}
              variant="outline"
            >
              <Printer className="h-4 w-4 mr-2" />
              Test Print
            </Button>
            
            <Button
              onClick={handleExportTest}
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Test PDF Export
                </>
              )}
            </Button>
          </div>

          {/* Test Result */}
          {testResult && (
            <div className={`p-4 rounded-lg ${
              testResult.includes('successful') || testResult.includes('opened') 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {testResult}
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Test Instructions:</h4>
            <ul className="text-sm space-y-1 list-disc list-inside">
              <li><strong>Test Print:</strong> Opens a print dialog with the formatted invoice</li>
              <li><strong>Test PDF Export:</strong> Downloads a PDF file of the invoice</li>
              <li>The PDF will include organization name if available in your profile</li>
              <li>Check browser downloads folder for the exported PDF</li>
              <li>Verify that all invoice details, line items, and totals are correctly formatted</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
