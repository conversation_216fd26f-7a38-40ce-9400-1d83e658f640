import { useState, useRef } from 'react'
import { format } from 'date-fns'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Separator } from '@/components/ui/separator'
import { LoadingSpinner, LoadingButton } from '@/components/ui/loading'
import { Calendar, Download, Printer, FileText } from 'lucide-react'
import { useStatementData } from '@/hooks/useStatementData'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency } from '@/lib/utils'
import { useAuth } from '@/hooks/useAuthHook'
import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import type { StatementFilters, StatementProps } from '@/types/statements'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

export function StatementOfAccount({ entity, entity_type }: Omit<StatementProps, 'open' | 'onOpenChange'>) {
  const [filters, setFilters] = useState<StatementFilters>({
    entity_id: entity.id,
    entity_type,
    start_date: format(new Date(new Date().getFullYear(), 0, 1), 'yyyy-MM-dd'), // Start of year
    end_date: format(new Date(), 'yyyy-MM-dd'), // Today
    include_paid: true,
    include_draft: false
  })

  const { data, loading, error } = useStatementData(filters)
  const { toast } = useToast()
  const { user, profile } = useAuth()
  const statementRef = useRef<HTMLDivElement>(null)
  const [isExporting, setIsExporting] = useState(false)

  // Fetch organization data
  const { data: organization } = useQuery({
    queryKey: ['organization', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return null;
      const { data, error } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', profile.org_id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!profile?.org_id,
  })

  const handleDateChange = (field: 'start_date' | 'end_date', value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }))
  }

  const handlePrint = () => {
    window.print()
  }

  const handleExport = async () => {
    if (!statementRef.current || !data) {
      toast({
        title: "Error",
        description: "Statement data not available for export",
        variant: "destructive"
      })
      return
    }

    setIsExporting(true)

    try {
      // Create a clone of the statement element for PDF generation
      const element = statementRef.current.cloneNode(true) as HTMLElement

      // Apply styles for better PDF rendering
      element.style.width = '190mm' // Slightly smaller than A4 to ensure margins
      element.style.backgroundColor = 'white'
      element.style.padding = '15mm' // Increased padding for better margins
      element.style.fontFamily = 'Arial, sans-serif'
      element.style.fontSize = '11px'
      element.style.color = 'black'
      element.style.lineHeight = '1.4'

      // Remove any interactive elements that shouldn't be in PDF
      const buttons = element.querySelectorAll('button')
      buttons.forEach(button => button.remove())

      const inputs = element.querySelectorAll('input')
      inputs.forEach(input => input.remove())

      // Remove print-hidden elements
      const printHidden = element.querySelectorAll('.print\\:hidden')
      printHidden.forEach(el => el.remove())

      // Add audit information at the top
      const auditInfo = document.createElement('div')
      auditInfo.style.cssText = `
        margin-bottom: 20px;
        padding: 10px;
        border: 1px solid #e5e7eb;
        background-color: #f9fafb;
        font-size: 10px;
        color: #6b7280;
        border-radius: 4px;
      `

      const currentDate = new Date()
      const exportedBy = user?.email || 'Unknown User'
      const companyName = organization?.name || 'Unknown Organization'

      auditInfo.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <strong>Export Information:</strong><br>
            Company: ${companyName}<br>
            Period: ${format(new Date(filters.start_date), 'dd MMM yyyy')} to ${format(new Date(filters.end_date), 'dd MMM yyyy')}
          </div>
          <div style="text-align: right;">
            Exported by: ${exportedBy}<br>
            Export Date: ${format(currentDate, 'dd MMM yyyy HH:mm:ss')}<br>
            System: KAYA Finance
          </div>
        </div>
      `

      element.insertBefore(auditInfo, element.firstChild)

      // Add audit footer
      const auditFooter = document.createElement('div')
      auditFooter.style.cssText = `
        margin-top: 20px;
        padding: 10px;
        border-top: 1px solid #e5e7eb;
        font-size: 9px;
        color: #9ca3af;
        text-align: center;
      `
      auditFooter.innerHTML = `
        This statement was generated by KAYA Finance on ${format(currentDate, 'dd MMM yyyy')} at ${format(currentDate, 'HH:mm:ss')} by ${exportedBy}
      `
      element.appendChild(auditFooter)

      // Temporarily add to DOM for rendering
      element.style.position = 'absolute'
      element.style.left = '-9999px'
      element.style.top = '0'
      document.body.appendChild(element)

      // Generate canvas from HTML with better settings
      const canvas = await html2canvas(element, {
        scale: 1.5, // Reduced scale for better performance while maintaining quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: element.scrollWidth,
        height: element.scrollHeight,
        scrollX: 0,
        scrollY: 0
      })

      // Remove the temporary element
      document.body.removeChild(element)

      // Create PDF with proper margins
      const pdf = new jsPDF('p', 'mm', 'a4')
      const imgData = canvas.toDataURL('image/png', 0.95) // Slightly compressed for smaller file size

      const pdfWidth = pdf.internal.pageSize.getWidth()
      const pdfHeight = pdf.internal.pageSize.getHeight()
      const margin = 10 // 10mm margin on all sides
      const availableWidth = pdfWidth - (2 * margin)
      const availableHeight = pdfHeight - (2 * margin)

      const imgWidth = canvas.width
      const imgHeight = canvas.height
      const ratio = Math.min(availableWidth / (imgWidth * 0.264583), availableHeight / (imgHeight * 0.264583)) // Convert pixels to mm

      const scaledWidth = (imgWidth * 0.264583) * ratio
      const scaledHeight = (imgHeight * 0.264583) * ratio

      const imgX = margin + (availableWidth - scaledWidth) / 2
      const imgY = margin

      pdf.addImage(imgData, 'PNG', imgX, imgY, scaledWidth, scaledHeight)

      // Generate filename
      const entityName = entity.name.replace(/[^a-zA-Z0-9]/g, '_')
      const dateRange = `${filters.start_date}_to_${filters.end_date}`
      const filename = `${entity_type}_statement_${entityName}_${dateRange}.pdf`

      // Save the PDF
      pdf.save(filename)

      toast({
        title: "Success",
        description: "Statement exported to PDF successfully",
      })
    } catch (error) {
      console.error('Error exporting PDF:', error)
      toast({
        title: "Error",
        description: "Failed to export statement to PDF",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }



  const getStatusBadge = (status: string) => {
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800',
      sent: 'bg-blue-100 text-blue-800',
      paid: 'bg-green-100 text-green-800',
      overdue: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
    }
    
    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="p-8">
        <LoadingSpinner size="lg" text="Loading statement..." showText />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center p-8 text-red-600">
        <p>Error loading statement: {error}</p>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center p-8 text-gray-500">
        <p>No data available</p>
      </div>
    )
  }

  return (
    <div ref={statementRef} className="space-y-6 print:space-y-4">
      {/* Header Controls - Hidden in print */}
      <div className="print:hidden">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Statement Period
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={filters.start_date}
                  onChange={(e) => handleDateChange('start_date', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={filters.end_date}
                  onChange={(e) => handleDateChange('end_date', e.target.value)}
                />
              </div>
              <div className="flex items-end gap-2">
                <Button onClick={handlePrint} variant="outline" size="sm">
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </Button>
                <LoadingButton
                  onClick={handleExport}
                  variant="outline"
                  size="sm"
                  loading={isExporting}
                  loadingText="Exporting..."
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export PDF
                </LoadingButton>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statement Header */}
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Statement of Account</CardTitle>
          <div className="text-lg font-semibold text-primary">
            {data.entity.name}
          </div>
          <div className="text-sm text-muted-foreground">
            Period: {format(new Date(data.period_start), 'dd MMM yyyy')} to {format(new Date(data.period_end), 'dd MMM yyyy')}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">{entity_type === 'customer' ? 'Customer' : 'Vendor'} Details:</h4>
              <p><strong>Name:</strong> {data.entity.name}</p>
              {data.entity.email && <p><strong>Email:</strong> {data.entity.email}</p>}
              {data.entity.phone && <p><strong>Phone:</strong> {data.entity.phone}</p>}
              {data.entity.address && <p><strong>Address:</strong> {data.entity.address}</p>}
              {data.entity.tin_number && <p><strong>TIN:</strong> {data.entity.tin_number}</p>}
            </div>
            <div>
              <h4 className="font-semibold mb-2">Statement Summary:</h4>
              <p><strong>Opening Balance:</strong> {formatCurrency(data.opening_balance)}</p>
              <p><strong>Closing Balance:</strong> {formatCurrency(data.closing_balance)}</p>
              {entity_type === 'customer' && data.summary.total_invoiced && (
                <p><strong>Total Invoiced:</strong> {formatCurrency(data.summary.total_invoiced)}</p>
              )}
              {entity_type === 'vendor' && data.summary.total_billed && (
                <p><strong>Total Billed:</strong> {formatCurrency(data.summary.total_billed)}</p>
              )}
              <p><strong>Total Payments:</strong> {formatCurrency(data.summary.total_payments)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transaction History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Transaction History
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.opening_balance !== 0 && (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Debit</TableHead>
                    <TableHead className="text-right">Credit</TableHead>
                    <TableHead className="text-right">Balance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow className="bg-muted/50">
                    <TableCell>{format(new Date(data.period_start), 'dd MMM yyyy')}</TableCell>
                    <TableCell>-</TableCell>
                    <TableCell className="font-medium">Opening Balance</TableCell>
                    <TableCell>-</TableCell>
                    <TableCell className="text-right">-</TableCell>
                    <TableCell className="text-right">-</TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(data.opening_balance)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
              <Separator className="my-4" />
            </>
          )}

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Reference</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Debit</TableHead>
                <TableHead className="text-right">Credit</TableHead>
                <TableHead className="text-right">Balance</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.transactions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center text-muted-foreground py-8">
                    No transactions found for the selected period
                  </TableCell>
                </TableRow>
              ) : (
                data.transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>{format(new Date(transaction.date), 'dd MMM yyyy')}</TableCell>
                    <TableCell className="font-mono text-sm">{transaction.reference}</TableCell>
                    <TableCell>{transaction.description}</TableCell>
                    <TableCell>
                      {transaction.status && getStatusBadge(transaction.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      {transaction.debit > 0 ? formatCurrency(transaction.debit) : '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      {transaction.credit > 0 ? formatCurrency(transaction.credit) : '-'}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(transaction.balance)}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Closing Balance Row */}
          <Separator className="my-4" />
          <div className="flex justify-end">
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Closing Balance</p>
                <p className="text-lg font-bold">
                  {formatCurrency(data.closing_balance)}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>


    </div>
  )
}
