import type { Vendor } from './database'

export type PurchaseOrderStatus = 'draft' | 'sent' | 'confirmed' | 'partially_received' | 'received' | 'cancelled'
export type QualityStatus = 'accepted' | 'rejected' | 'pending'

export interface PurchaseOrder {
  id: string
  org_id: string
  vendor_id: string
  po_number: string
  date_issued: string
  expected_delivery_date: string | null
  subtotal: number
  tax_amount: number
  total_amount: number
  status: PurchaseOrderStatus
  notes: string | null
  terms_and_conditions: string | null
  delivery_address: string | null
  created_at: string
  updated_at: string
  created_by: string | null
}

export interface PurchaseOrderLine {
  id: string
  org_id: string
  purchase_order_id: string
  product_id: string | null
  item: string
  description: string | null
  quantity: number
  unit_price: number
  tax_rate_pct: number
  line_total: number
  quantity_received: number
  quantity_remaining: number
  created_at: string
}

export interface PurchaseOrderReceipt {
  id: string
  org_id: string
  purchase_order_id: string
  receipt_number: string
  receipt_date: string
  received_by: string | null
  bill_id: string | null
  notes: string | null
  created_at: string
}

export interface PurchaseOrderReceiptLine {
  id: string
  org_id: string
  receipt_id: string
  po_line_id: string
  quantity_received: number
  unit_cost: number | null
  quality_status: QualityStatus
  quality_notes: string | null
  created_at: string
}

// Extended types with relationships
export interface PurchaseOrderWithVendor extends PurchaseOrder {
  vendor: Vendor
}

export interface PurchaseOrderWithDetails extends PurchaseOrderWithVendor {
  lines: PurchaseOrderLineWithProduct[]
  receipts?: PurchaseOrderReceiptWithLines[]
}

export interface PurchaseOrderLineWithProduct extends PurchaseOrderLine {
  product?: {
    id: string
    name: string
    sku: string
    cost_price: number
    selling_price: number
    track_inventory: boolean
  }
}

export interface PurchaseOrderReceiptWithLines extends PurchaseOrderReceipt {
  lines: PurchaseOrderReceiptLineWithDetails[]
  received_by_user?: {
    id: string
    email: string
    full_name: string | null
  }
}

export interface PurchaseOrderReceiptLineWithDetails extends PurchaseOrderReceiptLine {
  po_line: PurchaseOrderLineWithProduct
}

// Form data types
export interface PurchaseOrderFormData {
  vendor_id: string
  po_number: string
  date_issued: string
  expected_delivery_date: string
  notes: string
  terms_and_conditions: string
  delivery_address: string
  status: PurchaseOrderStatus
}

export interface PurchaseOrderLineData {
  product_id?: string
  item: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number
}

export interface PurchaseOrderReceiptFormData {
  purchase_order_id: string
  receipt_number: string
  receipt_date: string
  notes: string
  lines: PurchaseOrderReceiptLineData[]
}

export interface PurchaseOrderReceiptLineData {
  po_line_id: string
  quantity_received: number
  unit_cost?: number
  quality_status: QualityStatus
  quality_notes?: string
}

// Component props types
export interface PurchaseOrderFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingPO: PurchaseOrderWithDetails | null
  vendors: Vendor[]
  onSubmit: (formData: PurchaseOrderFormData, lines: PurchaseOrderLineData[]) => Promise<void>
  preselectedVendorId?: string
}

export interface PurchaseOrderListProps {
  purchaseOrders: PurchaseOrderWithVendor[]
  onEdit: (po: PurchaseOrderWithDetails) => void
  onStatusChange: (po: PurchaseOrder, newStatus: PurchaseOrderStatus) => void
  onReceive: (po: PurchaseOrderWithDetails) => void
  onCreateBill: (po: PurchaseOrderWithDetails) => void
  searchTerm: string
  onSearchChange: (term: string) => void
}

export interface PurchaseOrderLineItemProps {
  line: PurchaseOrderLineData
  index: number
  onUpdate: (index: number, field: keyof PurchaseOrderLineData, value: string | number) => void
  onRemove: (index: number) => void
  isLast: boolean
}

export interface PurchaseOrderReceiptFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  purchaseOrder: PurchaseOrderWithDetails
  onSubmit: (formData: PurchaseOrderReceiptFormData) => Promise<void>
}

// Summary and analytics types
export interface PurchaseOrderSummary {
  total_orders: number
  total_value: number
  pending_orders: number
  pending_value: number
  received_orders: number
  received_value: number
  overdue_orders: number
  overdue_value: number
}

export interface VendorPurchaseOrderSummary {
  vendor_id: string
  vendor_name: string
  total_orders: number
  total_value: number
  average_order_value: number
  on_time_delivery_rate: number
  last_order_date: string | null
}

// Search and filter types
export interface PurchaseOrderFilters {
  status?: PurchaseOrderStatus[]
  vendor_id?: string
  date_from?: string
  date_to?: string
  amount_min?: number
  amount_max?: number
}

export interface PurchaseOrderSearchParams {
  search?: string
  filters?: PurchaseOrderFilters
  sort_by?: 'date_issued' | 'total_amount' | 'po_number' | 'vendor_name'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}
