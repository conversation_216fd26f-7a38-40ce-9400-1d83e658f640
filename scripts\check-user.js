#!/usr/bin/env node

/**
 * User Status Checker for Supabase
 * Checks if a user exists and their onboarding status
 */

import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://kmejequnwwngmzwkszqs.supabase.co'
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImttZWplcXVud3duZ216d2tzenFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDk3ODQsImV4cCI6MjA2Mzc4NTc4NH0.ROGVGPdlDh_o1TJJJijM1BTubWUhUXUh5oZWxOKDdjw'

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function checkUserByEmail(email) {
  console.log(`🔍 Checking user status for: ${email}`)
  console.log('=' .repeat(60))

  try {
    // Check in auth.users table (this requires service role, so we'll check profiles instead)
    console.log('📋 Checking profiles table...')
    
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .maybeSingle()

    if (profileError) {
      console.error('❌ Error checking profiles:', profileError.message)
      return
    }

    if (!profile) {
      console.log('❌ User not found in profiles table')
      console.log('📝 Status: NEW USER - needs to complete registration')
      return
    }

    console.log('✅ User found in profiles table')
    console.log('📊 Profile Information:')
    console.log(`   • ID: ${profile.id}`)
    console.log(`   • Email: ${profile.email}`)
    console.log(`   • Role: ${profile.role || 'Not set'}`)
    console.log(`   • Organization ID: ${profile.org_id || 'Not set'}`)
    console.log(`   • Created: ${profile.created_at}`)
    console.log(`   • Onboarding Completed: ${profile.onboarding_completed_at || 'Not completed'}`)
    console.log(`   • Active: ${profile.is_active !== false ? 'Yes' : 'No'}`)

    // Check organization if user has one
    if (profile.org_id) {
      console.log('\n🏢 Checking organization...')
      
      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', profile.org_id)
        .maybeSingle()

      if (orgError) {
        console.log('❌ Error fetching organization:', orgError.message)
      } else if (organization) {
        console.log('✅ Organization found:')
        console.log(`   • Name: ${organization.name}`)
        console.log(`   • TIN: ${organization.tin_number || 'Not set'}`)
        console.log(`   • Created: ${organization.created_at}`)
      } else {
        console.log('❌ Organization not found (orphaned profile)')
      }
    }

    // Determine onboarding status
    console.log('\n🎯 Onboarding Status Analysis:')
    
    const hasProfile = !!profile
    const hasOrganization = !!profile.org_id
    const hasCompletedOnboarding = !!profile.onboarding_completed_at
    const needsOnboarding = !hasOrganization || !hasCompletedOnboarding

    console.log(`   • Has Profile: ${hasProfile ? '✅' : '❌'}`)
    console.log(`   • Has Organization: ${hasOrganization ? '✅' : '❌'}`)
    console.log(`   • Completed Onboarding: ${hasCompletedOnboarding ? '✅' : '❌'}`)
    console.log(`   • Needs Onboarding: ${needsOnboarding ? '❌ YES' : '✅ NO'}`)

    // Final status
    console.log('\n📋 FINAL STATUS:')
    if (needsOnboarding) {
      if (!hasOrganization) {
        console.log('🔄 EXISTING USER - Needs to complete organization setup')
      } else {
        console.log('🔄 EXISTING USER - Needs to mark onboarding as completed')
      }
    } else {
      console.log('✅ EXISTING USER - Fully onboarded, should go to dashboard')
    }

    // Check for recent activity
    console.log('\n📈 Recent Activity:')
    const { data: auditLogs, error: auditError } = await supabase
      .from('audit_logs')
      .select('action, created_at')
      .eq('profile_id', profile.id)
      .order('created_at', { ascending: false })
      .limit(5)

    if (auditError) {
      console.log('❌ Could not fetch audit logs:', auditError.message)
    } else if (auditLogs && auditLogs.length > 0) {
      console.log('✅ Recent activity found:')
      auditLogs.forEach((log, index) => {
        console.log(`   ${index + 1}. ${log.action} - ${new Date(log.created_at).toLocaleString()}`)
      })
    } else {
      console.log('ℹ️  No recent activity found')
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error.message)
  }
}

// Get email from command line arguments
const email = process.argv[2]

if (!email) {
  console.log('Usage: node scripts/check-user.js <email>')
  console.log('Example: node scripts/check-user.js <EMAIL>')
  process.exit(1)
}

// Validate email format
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
if (!emailRegex.test(email)) {
  console.log('❌ Invalid email format')
  process.exit(1)
}

// Run the check
checkUserByEmail(email)
  .then(() => {
    console.log('\n✅ Check completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Script failed:', error.message)
    process.exit(1)
  })
