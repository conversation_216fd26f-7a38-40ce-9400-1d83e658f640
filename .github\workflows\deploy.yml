name: Deploy to Production/Staging

on:
  push:
    branches:
      - main      # Deploy to production
      - develop   # Deploy to staging
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      skip_tests:
        description: 'Skip tests (emergency deployment only)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Determine deployment environment
  setup:
    name: Setup Deployment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      is_production: ${{ steps.env.outputs.is_production }}
      skip_tests: ${{ steps.env.outputs.skip_tests }}
    steps:
      - name: Determine environment
        id: env
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
            echo "skip_tests=${{ github.event.inputs.skip_tests }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "environment=production" >> $GITHUB_OUTPUT
            echo "skip_tests=false" >> $GITHUB_OUTPUT
          else
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "skip_tests=false" >> $GITHUB_OUTPUT
          fi
          
          if [ "${{ steps.env.outputs.environment }}" = "production" ]; then
            echo "is_production=true" >> $GITHUB_OUTPUT
          else
            echo "is_production=false" >> $GITHUB_OUTPUT
          fi

  # Pre-deployment tests (can be skipped for emergency deployments)
  pre-deployment-tests:
    name: Pre-deployment Tests
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.skip_tests == 'false'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run critical tests
        run: pnpm test:critical
        env:
          CI: true

      - name: Run smoke tests
        run: pnpm test:smoke
        env:
          CI: true

  # Build for deployment
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [setup, pre-deployment-tests]
    if: always() && (needs.pre-deployment-tests.result == 'success' || needs.setup.outputs.skip_tests == 'true')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build for staging
        if: needs.setup.outputs.environment == 'staging'
        run: pnpm build
        env:
          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL_STAGING }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY_STAGING }}
          VITE_ENVIRONMENT: staging
          VITE_SENTRY_DSN: ${{ secrets.VITE_SENTRY_DSN_STAGING }}

      - name: Build for production
        if: needs.setup.outputs.environment == 'production'
        run: pnpm build
        env:
          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL_PRODUCTION }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY_PRODUCTION }}
          VITE_ENVIRONMENT: production
          VITE_SENTRY_DSN: ${{ secrets.VITE_SENTRY_DSN_PRODUCTION }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-${{ needs.setup.outputs.environment }}
          path: dist/
          retention-days: 30

  # Database migrations (if needed)
  migrate:
    name: Database Migration
    runs-on: ubuntu-latest
    needs: [setup, build]
    if: needs.setup.outputs.environment == 'production'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Run database migrations
        run: |
          echo "Running database migrations for production..."
          # Add your migration commands here
          # Example: npx supabase db push --db-url ${{ secrets.DATABASE_URL_PRODUCTION }}
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL_PRODUCTION }}

  # Deploy to Vercel/Netlify/Other hosting
  deploy:
    name: Deploy to ${{ needs.setup.outputs.environment }}
    runs-on: ubuntu-latest
    needs: [setup, build, migrate]
    if: always() && needs.build.result == 'success' && (needs.migrate.result == 'success' || needs.migrate.result == 'skipped')
    environment: ${{ needs.setup.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-${{ needs.setup.outputs.environment }}
          path: dist/

      - name: Deploy to Vercel (Staging)
        if: needs.setup.outputs.environment == 'staging'
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          vercel-args: '--prod'
          alias-domains: staging.kaya-finance.com

      - name: Deploy to Vercel (Production)
        if: needs.setup.outputs.environment == 'production'
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          vercel-args: '--prod'
          alias-domains: app.kaya-finance.com

      - name: Get deployment URL
        id: deployment
        run: |
          if [ "${{ needs.setup.outputs.environment }}" = "production" ]; then
            echo "url=https://app.kaya-finance.com" >> $GITHUB_OUTPUT
          else
            echo "url=https://staging.kaya-finance.com" >> $GITHUB_OUTPUT
          fi

      - name: Update deployment status
        uses: bobheadxi/deployments@v1
        with:
          step: finish
          token: ${{ secrets.GITHUB_TOKEN }}
          status: success
          env: ${{ needs.setup.outputs.environment }}
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}
          env_url: ${{ steps.deployment.outputs.url }}

  # Post-deployment tests
  post-deployment-tests:
    name: Post-deployment Tests
    runs-on: ubuntu-latest
    needs: [setup, deploy]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run health checks
        run: |
          if [ "${{ needs.setup.outputs.environment }}" = "production" ]; then
            pnpm test:health --url=https://app.kaya-finance.com
          else
            pnpm test:health --url=https://staging.kaya-finance.com
          fi

      - name: Run smoke tests against deployed app
        run: |
          if [ "${{ needs.setup.outputs.environment }}" = "production" ]; then
            pnpm test:smoke:deployed --url=https://app.kaya-finance.com
          else
            pnpm test:smoke:deployed --url=https://staging.kaya-finance.com
          fi

  # Rollback on failure
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    needs: [setup, deploy, post-deployment-tests]
    if: failure() && needs.setup.outputs.is_production == 'true'
    environment: production
    steps:
      - name: Rollback production deployment
        run: |
          echo "Rolling back production deployment..."
          # Add rollback commands here
          # Example: vercel rollback --token ${{ secrets.VERCEL_TOKEN }}

      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: "🔄 Production deployment rolled back due to post-deployment test failures"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Notifications
  notify:
    name: Deployment Notifications
    runs-on: ubuntu-latest
    needs: [setup, deploy, post-deployment-tests]
    if: always()
    steps:
      - name: Notify successful deployment
        if: needs.post-deployment-tests.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: |
            🚀 Kaya Finance successfully deployed to ${{ needs.setup.outputs.environment }}!
            
            Environment: ${{ needs.setup.outputs.environment }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}
            
            ${{ needs.setup.outputs.environment == 'production' && 'URL: https://app.kaya-finance.com' || 'URL: https://staging.kaya-finance.com' }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify failed deployment
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: |
            ❌ Kaya Finance deployment to ${{ needs.setup.outputs.environment }} failed!
            
            Environment: ${{ needs.setup.outputs.environment }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}
            
            Please check the GitHub Actions logs for details.
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Create GitHub release (Production only)
        if: needs.setup.outputs.environment == 'production' && needs.post-deployment-tests.result == 'success'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ github.run_number }}
          release_name: Release v${{ github.run_number }}
          body: |
            ## Changes in this Release
            
            Deployed to production on ${{ github.event.head_commit.timestamp }}
            
            **Commit:** ${{ github.sha }}
            **Author:** ${{ github.actor }}
            
            ### Deployment Details
            - Environment: Production
            - URL: https://app.kaya-finance.com
            - Build Number: ${{ github.run_number }}
            
            For detailed changes, see the commit history.
          draft: false
          prerelease: false
