
import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { LoadingSpinner } from '@/components/ui/loading'
import { Calendar } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface CashFlowData {
  operatingActivities: number
  investingActivities: number
  financingActivities: number
  netCashFlow: number
  beginningCash: number
  endingCash: number
}

export const CashFlowReport = () => {
  const { profile } = useAuth()
  const [data, setData] = useState<CashFlowData>({
    operatingActivities: 0,
    investingActivities: 0,
    financingActivities: 0,
    netCashFlow: 0,
    beginningCash: 0,
    endingCash: 0
  })
  const [loading, setLoading] = useState(false)
  const [startDate, setStartDate] = useState(() => {
    const date = new Date()
    date.setMonth(0, 1) // January 1st of current year
    return date.toISOString().split('T')[0]
  })
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0])

  const fetchCashFlow = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setLoading(true)
      
      // Get cash accounts
      const { data: cashAccounts, error: cashError } = await supabase
        .from('accounts')
        .select('id')
        .eq('org_id', profile.org_id)
        .ilike('name', '%cash%')
        .eq('type', 'asset')

      if (cashError) throw cashError

      const cashAccountIds = cashAccounts?.map(acc => acc.id) || []

      if (cashAccountIds.length === 0) {
        console.warn('No cash accounts found')
        return
      }

      // Calculate beginning cash balance
      const beginningDate = new Date(startDate)
      beginningDate.setDate(beginningDate.getDate() - 1)

      const { data: beginningTransactions } = await supabase
        .from('transaction_lines')
        .select('debit, credit')
        .eq('org_id', profile.org_id)
        .in('account_id', cashAccountIds)
        .lte('created_at', `${beginningDate.toISOString().split('T')[0]}T23:59:59`)

      const beginningCash = beginningTransactions?.reduce((sum, t) => 
        sum + (t.debit || 0) - (t.credit || 0), 0) || 0

      // Get all cash transactions for the period
      const { data: periodTransactions } = await supabase
        .from('transaction_lines')
        .select(`
          debit,
          credit,
          journal_entries!inner(description, reference)
        `)
        .eq('org_id', profile.org_id)
        .in('account_id', cashAccountIds)
        .gte('created_at', `${startDate}T00:00:00`)
        .lte('created_at', `${endDate}T23:59:59`)

      // Simplified categorization based on description/reference
      let operatingActivities = 0
      let investingActivities = 0
      let financingActivities = 0

      periodTransactions?.forEach(transaction => {
        const amount = (transaction.debit || 0) - (transaction.credit || 0)
        const description = transaction.journal_entries?.description?.toLowerCase() || ''
        const reference = transaction.journal_entries?.reference?.toLowerCase() || ''
        
        // Simple categorization logic
        if (description.includes('sale') || description.includes('revenue') || 
            description.includes('customer') || description.includes('expense') ||
            description.includes('vendor') || description.includes('payroll')) {
          operatingActivities += amount
        } else if (description.includes('equipment') || description.includes('asset') ||
                   description.includes('investment') || description.includes('property')) {
          investingActivities += amount
        } else if (description.includes('loan') || description.includes('capital') ||
                   description.includes('dividend') || description.includes('equity')) {
          financingActivities += amount
        } else {
          // Default to operating activities
          operatingActivities += amount
        }
      })

      const netCashFlow = operatingActivities + investingActivities + financingActivities
      const endingCash = beginningCash + netCashFlow

      setData({
        operatingActivities,
        investingActivities,
        financingActivities,
        netCashFlow,
        beginningCash,
        endingCash
      })
    } catch (error) {
      console.error('Error fetching cash flow:', error)
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, startDate, endDate])

  useEffect(() => {
    if (profile?.org_id) {
      fetchCashFlow()
    }
  }, [profile?.org_id, startDate, endDate, fetchCashFlow])

  if (loading) {
    return (
      <div className="p-8">
        <LoadingSpinner text="Loading cash flow statement..." showText />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Label htmlFor="startDate">From:</Label>
          <Input
            id="startDate"
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="w-auto"
          />
        </div>
        <div className="flex items-center gap-2">
          <Label htmlFor="endDate">To:</Label>
          <Input
            id="endDate"
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            className="w-auto"
          />
        </div>
        <Button onClick={fetchCashFlow} size="sm">
          <Calendar className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="rounded-md border max-w-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Cash Flow Statement</TableHead>
              <TableHead className="text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow className="font-semibold bg-muted/50">
              <TableCell>Beginning Cash Balance</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.beginningCash)}
              </TableCell>
            </TableRow>
            
            <TableRow className="font-semibold">
              <TableCell>Operating Activities</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.operatingActivities)}
              </TableCell>
            </TableRow>
            
            <TableRow className="font-semibold">
              <TableCell>Investing Activities</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.investingActivities)}
              </TableCell>
            </TableRow>
            
            <TableRow className="font-semibold">
              <TableCell>Financing Activities</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.financingActivities)}
              </TableCell>
            </TableRow>
            
            <TableRow className="font-bold border-t">
              <TableCell>Net Cash Flow</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.netCashFlow)}
              </TableCell>
            </TableRow>
            
            <TableRow className="font-bold border-t-2 bg-primary/10">
              <TableCell>Ending Cash Balance</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(data.endingCash)}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>

      <div className="p-4 bg-muted/50 rounded-md text-sm">
        <p className="font-medium">Note:</p>
        <p>This cash flow statement uses simplified categorization based on transaction descriptions. 
        For more accurate reporting, consider implementing detailed cash flow categorization in your chart of accounts.</p>
      </div>
    </div>
  )
}
