import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys, type QueryFilters } from '@/lib/queryKeys'
import { toast } from '@/hooks/use-toast'
import type { Vendor } from '@/types/database'

/**
 * Hook to fetch all vendors for the organization
 */
export function useVendors(filters?: QueryFilters) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: filters 
      ? queryKeys.vendors.filtered(profile?.org_id || '', filters)
      : queryKeys.vendors.all(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      let query = supabase
        .from('vendors')
        .select('*')
        .eq('org_id', profile.org_id)

      // Apply filters
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`)
      }

      if (filters?.active !== undefined) {
        query = query.eq('is_active', filters.active)
      }

      // Apply ordering
      query = query.order('created_at', { ascending: false })

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit)
      }
      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch active vendors only
 */
export function useActiveVendors() {
  const { profile } = useAuth()

  return useQuery({
    queryKey: queryKeys.vendors.active(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) return []

      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('is_active', true)
        .order('name')

      if (error) throw error
      return data || []
    },
    enabled: !!profile?.org_id,
    staleTime: 10 * 60 * 1000, // 10 minutes - active vendors change less frequently
  })
}

/**
 * Hook to fetch a single vendor by ID
 */
export function useVendor(vendorId: string | undefined) {
  const { profile } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.vendors.detail(profile?.org_id || '', vendorId || ''),
    queryFn: async () => {
      if (!profile?.org_id || !vendorId) return null

      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('id', vendorId)
        .eq('org_id', profile.org_id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!profile?.org_id && !!vendorId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to create a new vendor
 */
export function useCreateVendor() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (vendorData: Omit<Vendor, 'id' | 'org_id' | 'created_at' | 'updated_at'>) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('vendors')
        .insert({
          ...vendorData,
          org_id: profile.org_id,
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (newVendor) => {
      // Invalidate all vendor-related queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.vendors.all(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.vendors.active(profile?.org_id || '')
      })
      // Invalidate any filtered queries
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === 'vendors' &&
          query.queryKey[1] === profile?.org_id
      })

      // Add the new vendor to the cache
      queryClient.setQueryData(
        queryKeys.vendors.detail(profile?.org_id || '', newVendor.id),
        newVendor
      )

      toast({
        title: 'Success',
        description: 'Vendor created successfully',
      })
    },
    onError: (error) => {
      console.error('Error creating vendor:', error)
      toast({
        title: 'Error',
        description: 'Failed to create vendor',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update an existing vendor
 */
export function useUpdateVendor() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ 
      vendorId, 
      vendorData 
    }: { 
      vendorId: string
      vendorData: Partial<Omit<Vendor, 'id' | 'org_id' | 'created_at'>>
    }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('vendors')
        .update({
          ...vendorData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', vendorId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (updatedVendor) => {
      // Update the vendor in the cache
      queryClient.setQueryData(
        queryKeys.vendors.detail(profile?.org_id || '', updatedVendor.id),
        updatedVendor
      )

      // Invalidate all vendor-related queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.vendors.all(profile?.org_id || '')
      })
      queryClient.invalidateQueries({
        queryKey: queryKeys.vendors.active(profile?.org_id || '')
      })
      // Invalidate any filtered queries
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey[0] === 'vendors' &&
          query.queryKey[1] === profile?.org_id
      })

      toast({
        title: 'Success',
        description: 'Vendor updated successfully',
      })
    },
    onError: (error) => {
      console.error('Error updating vendor:', error)
      toast({
        title: 'Error',
        description: 'Failed to update vendor',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a vendor
 */
export function useDeleteVendor() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (vendorId: string) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // First, check if vendor has any related records
      const [billsCheck, paymentsCheck] = await Promise.all([
        supabase
          .from('bills')
          .select('id')
          .eq('vendor_id', vendorId)
          .eq('org_id', profile.org_id)
          .limit(1),
        supabase
          .from('payments')
          .select('id')
          .eq('vendor_id', vendorId)
          .eq('org_id', profile.org_id)
          .limit(1)
      ])

      // Check for related records
      const hasBills = billsCheck.data && billsCheck.data.length > 0
      const hasPayments = paymentsCheck.data && paymentsCheck.data.length > 0

      if (hasBills || hasPayments) {
        const relatedItems = []
        if (hasBills) relatedItems.push('bills')
        if (hasPayments) relatedItems.push('payments')

        throw new Error(`Cannot delete vendor. Vendor has existing ${relatedItems.join(' and ')}.`)
      }

      // If no related records, proceed with deletion
      const { error } = await supabase
        .from('vendors')
        .delete()
        .eq('id', vendorId)
        .eq('org_id', profile.org_id)

      if (error) throw error
      return vendorId
    },
    onSuccess: (deletedVendorId) => {
      // Remove the vendor from the cache
      queryClient.removeQueries({ 
        queryKey: queryKeys.vendors.detail(profile?.org_id || '', deletedVendorId) 
      })

      // Invalidate vendors list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.vendors.all(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: 'Vendor deleted successfully',
      })
    },
    onError: (error: Error) => {
      console.error('Error deleting vendor:', error)

      // Provide specific error message based on the error type
      let errorMessage = 'Failed to delete vendor'

      if (error.message && error.message.includes('Cannot delete vendor')) {
        errorMessage = error.message
      } else if (error.code === '23503') {
        errorMessage = 'Cannot delete vendor. Vendor has existing bills or payments.'
      } else if (error.message) {
        errorMessage = error.message
      }

      toast({
        title: 'Cannot Delete Vendor',
        description: errorMessage,
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to toggle vendor active status
 */
export function useToggleVendorStatus() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ vendorId, isActive }: { vendorId: string, isActive: boolean }) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      const { data, error } = await supabase
        .from('vendors')
        .update({ 
          is_active: isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('id', vendorId)
        .eq('org_id', profile.org_id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (updatedVendor) => {
      // Update the vendor in the cache
      queryClient.setQueryData(
        queryKeys.vendors.detail(profile?.org_id || '', updatedVendor.id),
        updatedVendor
      )

      // Invalidate both all vendors and active vendors lists
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.vendors.all(profile?.org_id || '') 
      })
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.vendors.active(profile?.org_id || '') 
      })

      toast({
        title: 'Success',
        description: `Vendor ${updatedVendor.is_active ? 'activated' : 'deactivated'} successfully`,
      })
    },
    onError: (error) => {
      console.error('Error toggling vendor status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update vendor status',
        variant: 'destructive',
      })
    },
  })
}
