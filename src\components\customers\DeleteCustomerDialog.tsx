import React from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, Info } from 'lucide-react'
import { useCanDeleteCustomer } from '@/hooks/queries'
import { Customer } from '@/types/extended-database'

interface DeleteCustomerDialogProps {
  customer: Customer | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
}

export function DeleteCustomerDialog({
  customer,
  open,
  onOpenChange,
  onConfirm
}: DeleteCustomerDialogProps) {
  const { data: deleteCheck, isLoading } = useCanDeleteCustomer(customer?.id || null)

  if (!customer) return null

  const canDelete = deleteCheck?.canDelete ?? false
  const reasons = deleteCheck?.reasons ?? []

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Delete Customer
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the customer "{customer.name}"?
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
              <span className="ml-2 text-sm text-muted-foreground">Checking dependencies...</span>
            </div>
          ) : !canDelete ? (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <div className="font-medium text-red-700">
                    Cannot delete this customer
                  </div>
                  <div className="text-sm">
                    This customer cannot be deleted because they have:
                  </div>
                  <ul className="text-sm list-disc list-inside space-y-1">
                    {reasons.map((reason, index) => (
                      <li key={index}>{reason}</li>
                    ))}
                  </ul>
                  <div className="text-sm mt-2">
                    To delete this customer, you must first remove or reassign all related records.
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          ) : (
            <Alert className="border-orange-200 bg-orange-50">
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <div className="font-medium text-orange-700">
                    This action cannot be undone
                  </div>
                  <div className="text-sm">
                    Deleting this customer will permanently remove:
                  </div>
                  <ul className="text-sm list-disc list-inside space-y-1">
                    <li>Customer contact information</li>
                    <li>Customer profile and settings</li>
                    <li>All associated metadata</li>
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          {canDelete && !isLoading && (
            <AlertDialogAction
              onClick={onConfirm}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Delete Customer
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
