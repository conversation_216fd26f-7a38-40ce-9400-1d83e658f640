import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuthHook';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, ChevronDown, User, Search, Menu, Settings, LogOut, FileText, HelpCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { useQuery } from '@tanstack/react-query';
import { useActiveCustomers, useActiveVendors, useActiveAccounts, useActiveWithholdingTaxRates } from '@/hooks/queries';
import { BillForm } from '@/components/bills/BillForm';
import { InvoiceDialog } from '@/components/invoices/InvoiceDialog';
import { CustomerDialog } from '@/components/customers/CustomerDialog';
import { GlobalSearchDialog } from '@/components/search/GlobalSearchDialog';
import { NotificationDropdown } from '@/components/notifications';
import { useDebounce } from '@/hooks/use-debounce';
import { validatePhoneNumber, validateTinNumber } from '@/lib/validators';
import { BudgetAlertIndicator } from '@/components/budgets/BudgetAlertIndicator';
import { useInventoryIntegration } from '@/hooks/useInventoryIntegration';
import type { BillFormData, BillLineData } from '@/types/bills';

interface NavbarProps {
  onMenuClick: () => void;
}

type NewItemType = 'customer' | 'invoice' | 'bill';

export function Navbar({ onMenuClick }: NavbarProps) {
  const { profile, signOut } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isNewMenuOpen, setIsNewMenuOpen] = useState(false);
  const [activeDialog, setActiveDialog] = useState<NewItemType | null>(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // Fetch organization data
  const { data: organization } = useQuery({
    queryKey: ['organization', profile?.org_id],
    queryFn: async () => {
      if (!profile?.org_id) return null;
      const { data, error } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', profile.org_id)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!profile?.org_id,
  });

  // Use React Query hooks for data fetching
  const { data: customers } = useActiveCustomers()
  const { data: vendors } = useActiveVendors()
  const { data: accounts } = useActiveAccounts()
  const { data: withholdingRates } = useActiveWithholdingTaxRates()
  const { processBillInventory, processInvoiceInventory } = useInventoryIntegration()

  // Handle new item creation
  const handleNewItem = (type: NewItemType) => {
    setIsNewMenuOpen(false);
    setActiveDialog(type);
  };

  // Handle dialog success
  const handleSuccess = (route?: string) => {
    if (route) {
      navigate(route);
    }
    setActiveDialog(null);
  };

  // Generate bill number
  const generateBillNumber = () => {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const timestamp = Date.now().toString().slice(-4)
    return `BILL-${year}${month}-${timestamp}`
  }

  // Calculate bill totals
  const calculateLineTotals = (billLines: BillLineData[], withholdingTaxRateId: string | null) => {
    let subtotal = 0
    let taxAmount = 0

    billLines.forEach(line => {
      const lineTotal = line.quantity * line.unit_price
      subtotal += lineTotal
      taxAmount += lineTotal * (line.tax_rate_pct / 100)
    })

    const totalBeforeWithholding = subtotal + taxAmount
    const withholdingRate = withholdingRates?.find(r => r.id === withholdingTaxRateId)
    const withholdingAmount = withholdingRate ? totalBeforeWithholding * (withholdingRate.rate_pct / 100) : 0
    const totalAmount = totalBeforeWithholding - withholdingAmount

    return {
      subtotal,
      taxAmount,
      withholdingAmount,
      totalAmount
    }
  }

  // Handle bill submission
  const handleBillSubmit = async (formData: BillFormData, billLines: BillLineData[]) => {
    if (!formData.vendor_id || billLines.length === 0) {
      toast({
        title: "Error",
        description: "Please select a vendor and add at least one line item",
        variant: "destructive",
      })
      return
    }

    try {
      const withholdingTaxRateId = formData.withholding_tax_rate_id && formData.withholding_tax_rate_id !== '' ? formData.withholding_tax_rate_id : null
      const totals = calculateLineTotals(billLines, withholdingTaxRateId)

      const billData = {
        vendor_id: formData.vendor_id,
        bill_number: formData.bill_number || generateBillNumber(),
        total_amount: totals.totalAmount,
        tax_amount: totals.taxAmount,
        withholding_amount: totals.withholdingAmount,
        withholding_tax_rate_id: withholdingTaxRateId,
        org_id: profile?.org_id,
        status: formData.status || 'draft',
        date_issued: formData.date_issued,
        due_date: formData.due_date,
        notes: formData.notes,
      }

      const { data, error } = await supabase
        .from('bills')
        .insert([billData])
        .select()
        .single()

      if (error) throw error
      if (!data) throw new Error('No data returned from insert')

      // Insert bill lines
      const linesData = billLines.map(line => ({
        bill_id: data.id,
        account_id: formData.account_id && formData.account_id !== '' ? formData.account_id : null,
        description: `${line.item}${line.description ? ' - ' + line.description : ''}`, // Combine item and description
        quantity: line.quantity,
        unit_price: line.unit_price,
        tax_rate_pct: line.tax_rate_pct
      }))

      const { error: linesError } = await supabase
        .from('bill_lines')
        .insert(linesData)

      if (linesError) throw linesError

      // Process inventory transactions for products
      try {
        await processBillInventory.mutateAsync({
          billId: data.id,
          billNumber: billData.bill_number,
          lines: billLines
        })
      } catch (inventoryError) {
        console.error('Failed to process inventory transactions:', inventoryError)
        toast({
          title: "Warning",
          description: "Bill created but inventory tracking failed. Please check inventory manually.",
          variant: "destructive",
        })
      }

      toast({
        title: "Success",
        description: "Bill created successfully",
      })

      handleSuccess('/bills')
    } catch (error) {
      console.error('Error saving bill:', error)
      toast({
        title: "Error",
        description: "Failed to save bill",
        variant: "destructive",
      })
      throw error
    }
  };

  // Global keyboard shortcut for search (Ctrl+K / Cmd+K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setIsSearchOpen(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // New item dropdown items
  const newItemOptions = [
    { type: 'customer', label: 'New Customer', icon: User },
    { type: 'invoice', label: 'New Invoice', icon: FileText },
    { type: 'bill', label: 'New Bill', icon: FileText },
  ] as const;

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-40 flex items-center justify-between px-6 bg-background border-b h-16">
        <div className="flex items-center gap-4 flex-1">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={onMenuClick}
            aria-label="Toggle menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          <div className="relative flex-1 max-w-2xl">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search customers, vendors, invoices, bills, payments... (⌘K)"
              onClick={() => setIsSearchOpen(true)}
              readOnly
              className="pl-9 w-full cursor-pointer"
              aria-label="Search"
            />
          </div>
          
          <DropdownMenu open={isNewMenuOpen} onOpenChange={setIsNewMenuOpen}>
            <DropdownMenuTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                New
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {newItemOptions.map((item) => (
                <DropdownMenuItem 
                  key={item.type}
                  onClick={() => handleNewItem(item.type)}
                  className="flex items-center gap-2"
                >
                  <item.icon className="h-4 w-4" />
                  {item.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            asChild
            className="relative"
            title="Getting Started Guide"
          >
            <Link to="/help">
              <HelpCircle className="h-5 w-5" />
            </Link>
          </Button>

          <BudgetAlertIndicator />

          <NotificationDropdown />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>
                    {profile?.email?.charAt(0).toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">
                    {organization?.name || 'Organization'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {profile?.role || 'User'}
                  </span>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link to="/settings" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => signOut()} 
                className="text-destructive focus:text-destructive"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      {/* Global Search Dialog */}
      <GlobalSearchDialog
        open={isSearchOpen}
        onOpenChange={setIsSearchOpen}
      />

      {/* Dynamic Dialog Rendering */}
      {activeDialog === 'customer' && (
        <CustomerDialog
          open={true}
          onOpenChange={(open) => !open && setActiveDialog(null)}
          onSuccess={() => handleSuccess('/customers')}
        />
      )}

      {activeDialog === 'invoice' && (
        <InvoiceDialog
          open={true}
          onOpenChange={(open) => !open && setActiveDialog(null)}
          customers={customers || []}
          accounts={accounts || []}
          onSuccess={() => handleSuccess('/invoices')}
        />
      )}

      {activeDialog === 'bill' && (
        <BillForm
          open={true}
          onOpenChange={(open) => !open && setActiveDialog(null)}
          editingBill={null}
          vendors={vendors || []}
          accounts={accounts || []}
          withholdingRates={withholdingRates || []}
          onSubmit={handleBillSubmit}
        />
      )}
    </>
  );
}
