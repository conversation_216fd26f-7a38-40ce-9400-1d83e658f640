/**
 * Supabase Client Test Component
 * Tests the Supabase client functionality to debug the method chaining issue
 */

import React, { useState, useEffect, useCallback } from 'react'
import { supabase } from '@/integrations/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle, XCircle } from 'lucide-react'

interface TestResult {
  test: string
  status: 'success' | 'error' | 'pending'
  message: string
  details?: Record<string, unknown>
}

export const SupabaseClientTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [testing, setTesting] = useState(false)

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const runTests = useCallback(async () => {
    setTesting(true)
    setTestResults([])

    // Test 1: Check if supabase client exists
    addTestResult({
      test: 'Supabase Client Existence',
      status: supabase ? 'success' : 'error',
      message: supabase ? 'Supabase client is defined' : 'Supabase client is undefined',
      details: { type: typeof supabase }
    })

    if (!supabase) {
      setTesting(false)
      return
    }

    // Test 2: Check if from method exists
    addTestResult({
      test: 'From Method Existence',
      status: typeof supabase.from === 'function' ? 'success' : 'error',
      message: typeof supabase.from === 'function' ? 'from() method exists' : 'from() method missing',
      details: { fromType: typeof supabase.from }
    })

    if (typeof supabase.from !== 'function') {
      setTesting(false)
      return
    }

    // Test 3: Test method chaining
    try {
      const fromResult = supabase.from('profiles')
      addTestResult({
        test: 'From Method Call',
        status: 'success',
        message: 'from() method executed successfully',
        details: { 
          type: typeof fromResult,
          methods: Object.getOwnPropertyNames(fromResult).filter(name => typeof fromResult[name] === 'function')
        }
      })

      // Test 4: Test select method
      const selectResult = fromResult.select('*')
      addTestResult({
        test: 'Select Method Call',
        status: 'success',
        message: 'select() method executed successfully',
        details: { 
          type: typeof selectResult,
          methods: Object.getOwnPropertyNames(selectResult).filter(name => typeof selectResult[name] === 'function')
        }
      })

      // Test 5: Test eq method
      const eqResult = selectResult.eq('id', 'test-id')
      addTestResult({
        test: 'Eq Method Call',
        status: 'success',
        message: 'eq() method executed successfully',
        details: { 
          type: typeof eqResult,
          methods: Object.getOwnPropertyNames(eqResult).filter(name => typeof eqResult[name] === 'function')
        }
      })

      // Test 6: Test maybeSingle method
      const maybeSingleResult = eqResult.maybeSingle()
      addTestResult({
        test: 'MaybeSingle Method Call',
        status: 'success',
        message: 'maybeSingle() method executed successfully',
        details: { 
          type: typeof maybeSingleResult,
          isPromise: maybeSingleResult instanceof Promise
        }
      })

    } catch (error) {
      addTestResult({
        test: 'Method Chaining',
        status: 'error',
        message: `Method chaining failed: ${(error as Error).message}`,
        details: { 
          error: (error as Error).message,
          stack: (error as Error).stack
        }
      })
    }

    // Test 7: Test actual query execution (with error handling)
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email')
        .limit(1)

      if (error) {
        addTestResult({
          test: 'Query Execution',
          status: 'error',
          message: `Query failed: ${error.message}`,
          details: { error }
        })
      } else {
        addTestResult({
          test: 'Query Execution',
          status: 'success',
          message: 'Query executed successfully',
          details: { dataCount: data?.length || 0 }
        })
      }
    } catch (error) {
      addTestResult({
        test: 'Query Execution',
        status: 'error',
        message: `Query execution failed: ${(error as Error).message}`,
        details: { 
          error: (error as Error).message,
          stack: (error as Error).stack
        }
      })
    }

    setTesting(false)
  }, [])

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: TestResult['status']) => {
    const variant = status === 'success' ? 'default' : status === 'error' ? 'destructive' : 'secondary'
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>
  }

  useEffect(() => {
    // Run tests automatically on mount
    runTests()
  }, [runTests])

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-blue-500" />
            Supabase Client Test
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={runTests}
            disabled={testing}
          >
            {testing ? 'Testing...' : 'Run Tests'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {testResults.map((result, index) => (
          <div key={index} className="flex items-start justify-between p-3 border rounded-lg">
            <div className="flex items-start gap-3">
              {getStatusIcon(result.status)}
              <div>
                <div className="font-medium">{result.test}</div>
                <div className="text-sm text-muted-foreground">{result.message}</div>
                {result.details && (
                  <details className="mt-2">
                    <summary className="text-xs text-muted-foreground cursor-pointer">Details</summary>
                    <pre className="text-xs mt-1 p-2 bg-muted rounded overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            </div>
            {getStatusBadge(result.status)}
          </div>
        ))}
        
        {testResults.length === 0 && !testing && (
          <div className="text-center text-muted-foreground py-8">
            Click "Run Tests" to start testing the Supabase client
          </div>
        )}
      </CardContent>
    </Card>
  )
}
