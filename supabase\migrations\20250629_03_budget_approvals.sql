-- Create budget approvals table for tracking budget override approvals
-- This table links approval instances with budget validation information

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating budget approvals table...';
END $$;

-- Create enum for escalation levels
CREATE TYPE budget_escalation_level AS ENUM (
    'normal',
    'budget_override', 
    'critical_override'
);

-- Create enum for budget approval status
CREATE TYPE budget_approval_status AS ENUM (
    'pending',
    'approved',
    'rejected'
);

-- Create budget_approvals table
CREATE TABLE IF NOT EXISTS budget_approvals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Link to approval workflow
    approval_instance_id UUID NOT NULL,
    
    -- Document information
    document_type TEXT NOT NULL,
    document_id UUID NOT NULL,
    
    -- Budget information
    account_id UUID NOT NULL REFERENCES accounts(id),
    exceedance_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    escalation_level budget_escalation_level NOT NULL DEFAULT 'normal',
    
    -- Approval details
    status budget_approval_status NOT NULL DEFAULT 'pending',
    justification TEXT,
    budget_adjustment DECIMAL(15,2) DEFAULT 0,
    
    -- Approval tracking
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMPTZ,
    rejection_reason TEXT,
    
    -- Audit fields
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_budget_approvals_org_id ON budget_approvals(org_id);
CREATE INDEX IF NOT EXISTS idx_budget_approvals_approval_instance ON budget_approvals(approval_instance_id);
CREATE INDEX IF NOT EXISTS idx_budget_approvals_account_id ON budget_approvals(account_id);
CREATE INDEX IF NOT EXISTS idx_budget_approvals_status ON budget_approvals(status);
CREATE INDEX IF NOT EXISTS idx_budget_approvals_escalation ON budget_approvals(escalation_level);
CREATE INDEX IF NOT EXISTS idx_budget_approvals_document ON budget_approvals(document_type, document_id);

-- Add RLS policies
ALTER TABLE budget_approvals ENABLE ROW LEVEL SECURITY;

-- Policy for organization members to view their org's budget approvals
CREATE POLICY "Users can view budget approvals in their organization"
    ON budget_approvals FOR SELECT
    USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE user_id = auth.uid()
        )
    );

-- Policy for organization members to create budget approvals
CREATE POLICY "Users can create budget approvals in their organization"
    ON budget_approvals FOR INSERT
    WITH CHECK (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE user_id = auth.uid()
        )
        AND created_by = auth.uid()
    );

-- Policy for budget approvers to update budget approvals
CREATE POLICY "Budget approvers can update budget approvals"
    ON budget_approvals FOR UPDATE
    USING (
        org_id IN (
            SELECT org_id FROM profiles 
            WHERE user_id = auth.uid()
        )
        AND (
            -- Allow creator to update their own pending approvals
            (created_by = auth.uid() AND status = 'pending')
            OR
            -- Allow admins to approve/reject
            EXISTS (
                SELECT 1 FROM profiles p
                WHERE p.user_id = auth.uid() 
                AND p.org_id = budget_approvals.org_id
                AND p.role IN ('admin', 'owner')
            )
        )
    );

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_budget_approvals_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER budget_approvals_updated_at
    BEFORE UPDATE ON budget_approvals
    FOR EACH ROW
    EXECUTE FUNCTION update_budget_approvals_updated_at();

-- Create view for budget approval details with related information
CREATE OR REPLACE VIEW budget_approval_details AS
SELECT 
    ba.*,
    a.name as account_name,
    a.code as account_code,
    a.type as account_type,
    p_created.email as created_by_email,
    p_created.full_name as created_by_name,
    p_approved.email as approved_by_email,
    p_approved.full_name as approved_by_name
FROM budget_approvals ba
LEFT JOIN accounts a ON ba.account_id = a.id
LEFT JOIN profiles p_created ON ba.created_by = p_created.user_id
LEFT JOIN profiles p_approved ON ba.approved_by = p_approved.user_id;

-- Grant permissions
GRANT SELECT ON budget_approval_details TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE budget_approvals IS 'Tracks budget override approvals for documents that exceed budget limits';
COMMENT ON COLUMN budget_approvals.approval_instance_id IS 'Links to the main approval workflow instance';
COMMENT ON COLUMN budget_approvals.exceedance_amount IS 'Amount by which the document exceeds the budget';
COMMENT ON COLUMN budget_approvals.escalation_level IS 'Level of escalation required based on budget impact';
COMMENT ON COLUMN budget_approvals.budget_adjustment IS 'Optional budget adjustment approved along with the document';

-- Create function to get budget approval summary for an organization
CREATE OR REPLACE FUNCTION get_budget_approval_summary(org_uuid UUID)
RETURNS TABLE (
    total_pending INTEGER,
    total_approved INTEGER,
    total_rejected INTEGER,
    total_exceedance_amount DECIMAL,
    critical_overrides INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) FILTER (WHERE status = 'pending')::INTEGER as total_pending,
        COUNT(*) FILTER (WHERE status = 'approved')::INTEGER as total_approved,
        COUNT(*) FILTER (WHERE status = 'rejected')::INTEGER as total_rejected,
        COALESCE(SUM(exceedance_amount) FILTER (WHERE status = 'pending'), 0) as total_exceedance_amount,
        COUNT(*) FILTER (WHERE escalation_level = 'critical_override' AND status = 'pending')::INTEGER as critical_overrides
    FROM budget_approvals
    WHERE org_id = org_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_budget_approval_summary(UUID) TO authenticated;

DO $$
BEGIN
    RAISE NOTICE '✅ Budget approvals table created successfully';
END $$;
