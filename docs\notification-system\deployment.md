# Deployment Guide

Complete guide for deploying the Kaya Finance notification system to production.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Database Deployment](#database-deployment)
4. [Application Deployment](#application-deployment)
5. [External Services](#external-services)
6. [Monitoring Setup](#monitoring-setup)
7. [Security Configuration](#security-configuration)
8. [Performance Optimization](#performance-optimization)
9. [Backup and Recovery](#backup-and-recovery)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

**Minimum Requirements:**
- Node.js 18.0+ with npm/yarn
- PostgreSQL 14+ (Supabase recommended)
- Redis 6+ (for caching and rate limiting)
- SSL certificate for HTTPS

**Recommended Production Setup:**
- Load balancer (AWS ALB, Cloudflare, etc.)
- CDN for static assets
- Container orchestration (Docker + Kubernetes)
- Monitoring stack (Prometheus, Grafana)

### Required Accounts and Services

1. **Supabase Project** (or PostgreSQL database)
2. **Email Service Provider** (SendGrid, Mailgun, AWS SES)
3. **Push Notification Service** (Firebase, OneSignal, or custom VAPID)
4. **Monitoring Service** (DataDog, New Relic, or self-hosted)
5. **Error Tracking** (Sentry, Bugsnag)

## Environment Setup

### Production Environment Variables

Create a `.env.production` file with the following variables:

```bash
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
PORT=3000

# Database (Supabase)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
DATABASE_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres

# Authentication
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_nextauth_secret

# Email Service
EMAIL_SERVICE_PROVIDER=sendgrid
EMAIL_API_KEY=your_email_api_key
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME="Kaya Finance"

# Push Notifications
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your_vapid_public_key
VAPID_PRIVATE_KEY=your_vapid_private_key
VAPID_SUBJECT=mailto:<EMAIL>

# External Integrations
SLACK_CLIENT_ID=your_slack_client_id
SLACK_CLIENT_SECRET=your_slack_client_secret
TEAMS_CLIENT_ID=your_teams_client_id
TEAMS_CLIENT_SECRET=your_teams_client_secret

# Monitoring and Logging
SENTRY_DSN=your_sentry_dsn
DATADOG_API_KEY=your_datadog_api_key
LOG_LEVEL=info

# Security
RATE_LIMIT_REDIS_URL=redis://your-redis-url:6379
CORS_ORIGINS=https://your-domain.com,https://app.your-domain.com

# Performance
CACHE_TTL=300
MAX_NOTIFICATION_BATCH_SIZE=100
REAL_TIME_CONNECTION_TIMEOUT=30000
```

### Docker Configuration

**Dockerfile:**
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN yarn build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
```

## Database Deployment

### Migration Process

1. **Backup Current Database:**
   ```bash
   # Create backup
   pg_dump -h your-host -U your-user -d your-db > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Apply Migrations:**
   ```bash
   # Using Supabase CLI
   supabase db push

   # Or manually apply each migration
   psql -h your-host -U your-user -d your-db -f supabase/migrations/20250628_notifications_system.sql
   psql -h your-host -U your-user -d your-db -f supabase/migrations/20250628_email_delivery_tracking.sql
   psql -h your-host -U your-user -d your-db -f supabase/migrations/20250628_push_notifications.sql
   ```

3. **Verify Migration:**
   ```sql
   -- Check tables exist
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name LIKE '%notification%';

   -- Check RLS policies
   SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
   FROM pg_policies 
   WHERE tablename LIKE '%notification%';

   -- Check indexes
   SELECT indexname, tablename FROM pg_indexes 
   WHERE tablename LIKE '%notification%';
   ```

### Database Configuration

**Production PostgreSQL Settings:**
```sql
-- Connection settings
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

-- Performance settings
random_page_cost = 1.1
effective_io_concurrency = 200
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

-- Logging
log_statement = 'mod'
log_duration = on
log_min_duration_statement = 1000
```

## Application Deployment

### Vercel Deployment

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Configure Project:**
   ```bash
   vercel
   vercel env add NODE_ENV production
   vercel env add NEXT_PUBLIC_SUPABASE_URL
   # Add all environment variables
   ```

3. **Deploy:**
   ```bash
   vercel --prod
   ```

### AWS Deployment

**Using AWS App Runner:**

1. **Create apprunner.yaml:**
   ```yaml
   version: 1.0
   runtime: nodejs18
   build:
     commands:
       build:
         - npm ci
         - npm run build
   run:
     runtime-version: 18
     command: npm start
     network:
       port: 3000
       env: PORT
     env:
       - name: NODE_ENV
         value: production
   ```

2. **Deploy via AWS Console or CLI:**
   ```bash
   aws apprunner create-service \
     --service-name kaya-notifications \
     --source-configuration '{
       "ImageRepository": {
         "ImageIdentifier": "your-ecr-repo:latest",
         "ImageConfiguration": {
           "Port": "3000"
         }
       }
     }'
   ```

### Kubernetes Deployment

**deployment.yaml:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kaya-notifications
spec:
  replicas: 3
  selector:
    matchLabels:
      app: kaya-notifications
  template:
    metadata:
      labels:
        app: kaya-notifications
    spec:
      containers:
      - name: app
        image: your-registry/kaya-notifications:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        envFrom:
        - secretRef:
            name: kaya-notifications-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: kaya-notifications-service
spec:
  selector:
    app: kaya-notifications
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

## External Services

### Email Service Setup

**SendGrid Configuration:**
```javascript
// lib/email/sendgrid.js
const sgMail = require('@sendgrid/mail')
sgMail.setApiKey(process.env.SENDGRID_API_KEY)

const sendEmail = async (to, subject, html, text) => {
  const msg = {
    to,
    from: {
      email: process.env.EMAIL_FROM_ADDRESS,
      name: process.env.EMAIL_FROM_NAME
    },
    subject,
    html,
    text,
    trackingSettings: {
      clickTracking: { enable: true },
      openTracking: { enable: true }
    }
  }
  
  return await sgMail.send(msg)
}
```

### Push Notification Setup

**VAPID Key Generation:**
```bash
# Generate VAPID keys
npx web-push generate-vapid-keys

# Output:
# Public Key: BEl62iUYgUivxIkv69yViEuiBIa40HI8YlOU...
# Private Key: 4f_go3C-hajdM1B9N-R2bmRscg-ES2gx...
```

**Service Worker Registration:**
```javascript
// Register service worker
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js')
    .then(registration => {
      console.log('SW registered:', registration)
    })
    .catch(error => {
      console.log('SW registration failed:', error)
    })
}
```

## Monitoring Setup

### Health Check Endpoints

Create health check endpoints for monitoring:

```typescript
// pages/api/health.ts
export default function handler(req, res) {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  })
}

// pages/api/ready.ts
export default async function handler(req, res) {
  try {
    // Check database connection
    const { data, error } = await supabase
      .from('notifications')
      .select('count')
      .limit(1)
    
    if (error) throw error
    
    res.status(200).json({
      status: 'ready',
      database: 'connected',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      error: error.message
    })
  }
}
```

### Prometheus Metrics

```typescript
// lib/metrics.ts
import { register, Counter, Histogram, Gauge } from 'prom-client'

export const notificationCounter = new Counter({
  name: 'notifications_total',
  help: 'Total number of notifications sent',
  labelNames: ['type', 'status', 'org_id']
})

export const notificationDuration = new Histogram({
  name: 'notification_duration_seconds',
  help: 'Time taken to process notifications',
  labelNames: ['type', 'operation']
})

export const activeConnections = new Gauge({
  name: 'websocket_connections_active',
  help: 'Number of active WebSocket connections'
})

// Expose metrics endpoint
// pages/api/metrics.ts
export default async function handler(req, res) {
  res.setHeader('Content-Type', register.contentType)
  res.send(await register.metrics())
}
```

## Security Configuration

### Rate Limiting

```typescript
// lib/rateLimit.ts
import { Redis } from 'ioredis'
import { NextApiRequest, NextApiResponse } from 'next'

const redis = new Redis(process.env.RATE_LIMIT_REDIS_URL)

export function rateLimit(
  limit: number = 100,
  window: number = 60,
  keyGenerator: (req: NextApiRequest) => string = (req) => req.ip
) {
  return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
    const key = `rate_limit:${keyGenerator(req)}`
    const current = await redis.incr(key)
    
    if (current === 1) {
      await redis.expire(key, window)
    }
    
    if (current > limit) {
      return res.status(429).json({
        error: 'Rate limit exceeded',
        retryAfter: await redis.ttl(key)
      })
    }
    
    res.setHeader('X-RateLimit-Limit', limit)
    res.setHeader('X-RateLimit-Remaining', Math.max(0, limit - current))
    
    next()
  }
}
```

### Content Security Policy

```typescript
// next.config.js
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: `
      default-src 'self';
      script-src 'self' 'unsafe-eval' 'unsafe-inline' *.supabase.co;
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: blob: *.supabase.co;
      connect-src 'self' *.supabase.co wss://*.supabase.co;
      font-src 'self';
      frame-src 'none';
      object-src 'none';
      base-uri 'self';
      form-action 'self';
      frame-ancestors 'none';
      upgrade-insecure-requests;
    `.replace(/\s{2,}/g, ' ').trim()
  }
]

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders
      }
    ]
  }
}
```

## Performance Optimization

### Caching Strategy

```typescript
// lib/cache.ts
import { Redis } from 'ioredis'

const redis = new Redis(process.env.CACHE_REDIS_URL)

export class CacheManager {
  static async get<T>(key: string): Promise<T | null> {
    const cached = await redis.get(key)
    return cached ? JSON.parse(cached) : null
  }
  
  static async set(key: string, value: any, ttl: number = 300): Promise<void> {
    await redis.setex(key, ttl, JSON.stringify(value))
  }
  
  static async invalidate(pattern: string): Promise<void> {
    const keys = await redis.keys(pattern)
    if (keys.length > 0) {
      await redis.del(...keys)
    }
  }
}

// Usage in API routes
export default async function handler(req, res) {
  const cacheKey = `notifications:${userId}:${orgId}`
  
  // Try cache first
  let notifications = await CacheManager.get(cacheKey)
  
  if (!notifications) {
    // Fetch from database
    notifications = await fetchNotifications(userId, orgId)
    
    // Cache for 5 minutes
    await CacheManager.set(cacheKey, notifications, 300)
  }
  
  res.json(notifications)
}
```

### Database Connection Pooling

```typescript
// lib/db.ts
import { Pool } from 'pg'

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

export { pool }
```

## Backup and Recovery

### Automated Backups

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="your_database"

# Create backup
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Upload to S3
aws s3 cp $BACKUP_DIR/backup_$DATE.sql.gz s3://your-backup-bucket/

# Clean up old backups (keep last 30 days)
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

### Recovery Procedures

```bash
# Restore from backup
gunzip backup_20250628_120000.sql.gz
psql -h $DB_HOST -U $DB_USER -d $DB_NAME < backup_20250628_120000.sql

# Point-in-time recovery (if using WAL-E or similar)
wal-e backup-fetch /var/lib/postgresql/9.6/main LATEST
```

## Troubleshooting

### Common Issues

1. **High Memory Usage:**
   - Check for memory leaks in real-time connections
   - Monitor notification batch sizes
   - Review caching strategies

2. **Database Performance:**
   - Analyze slow queries with `EXPLAIN ANALYZE`
   - Check index usage
   - Monitor connection pool

3. **Real-time Connection Issues:**
   - Verify WebSocket proxy configuration
   - Check firewall settings
   - Monitor connection limits

### Debugging Commands

```bash
# Check application logs
docker logs kaya-notifications

# Monitor database connections
SELECT count(*) FROM pg_stat_activity WHERE state = 'active';

# Check notification queue
SELECT type, count(*) FROM notifications 
WHERE created_at > NOW() - INTERVAL '1 hour' 
GROUP BY type;

# Monitor real-time subscriptions
SELECT channel, count(*) FROM pg_stat_subscription;
```

### Performance Monitoring

```sql
-- Slow query monitoring
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE query LIKE '%notification%'
ORDER BY mean_time DESC
LIMIT 10;

-- Index usage analysis
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
WHERE tablename LIKE '%notification%'
ORDER BY idx_scan DESC;
```
