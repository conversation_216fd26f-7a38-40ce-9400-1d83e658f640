import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  BookOpen, 
  Users, 
  Receipt, 
  FileText, 
  CreditCard, 
  Settings, 
  Search,
  ChevronRight,
  Building2,
  DollarSign,
  BarChart3,
  Shield,
  Database,
  Keyboard,
  MessageCircle,
  Mail,
  ExternalLink,
  CheckCircle,
  ArrowRight,
  HelpCircle,
  Lightbulb,
  Target,
  Zap
} from 'lucide-react'

interface HelpSection {
  id: string
  title: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  content: React.ReactNode
}

export default function Help() {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeSection, setActiveSection] = useState('overview')

  const helpSections: HelpSection[] = [
    {
      id: 'overview',
      title: 'System Overview',
      icon: BookOpen,
      description: 'Introduction to Kaya Finance and key concepts',
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3 flex items-center gap-2">
              <Target className="h-5 w-5 text-primary" />
              Welcome to Kaya Finance
            </h3>
            <p className="text-muted-foreground mb-4">
              Kaya Finance is a comprehensive accounting solution designed specifically for Ugandan businesses. 
              Built with modern double-entry accounting principles, it helps you maintain accurate financial records 
              while staying compliant with URA requirements.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <Card className="border-l-4 border-l-primary">
                <CardContent className="pt-4">
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Zap className="h-4 w-4 text-primary" />
                    Double-Entry Accounting
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Every transaction automatically creates balanced journal entries, ensuring your books are always accurate.
                  </p>
                </CardContent>
              </Card>
              
              <Card className="border-l-4 border-l-green-500">
                <CardContent className="pt-4">
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Shield className="h-4 w-4 text-green-500" />
                    URA Compliance
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Built-in VAT calculations, TIN validation, and reporting features to meet Uganda Revenue Authority requirements.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Core Concepts</h4>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <Building2 className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">Organizations</p>
                  <p className="text-sm text-muted-foreground">
                    Your business entity with its own chart of accounts, users, and financial data.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Users className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">User Roles</p>
                  <p className="text-sm text-muted-foreground">
                    <strong>Owner/Admin:</strong> Full access to all features and settings<br/>
                    <strong>Accountant:</strong> Manage financial data and transactions<br/>
                    <strong>User:</strong> View-only access to financial information
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: Lightbulb,
      description: 'Initial setup and configuration steps',
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-4">Initial Setup Checklist</h3>
            <div className="space-y-4">
              {[
                { step: 'Complete organization profile', desc: 'Add company details, address, and TIN number' },
                { step: 'Set up chart of accounts', desc: 'Configure your account structure for proper categorization' },
                { step: 'Add team members', desc: 'Invite users and assign appropriate roles' },
                { step: 'Configure accounting settings', desc: 'Set fiscal year, currency, and tax rates' },
                { step: 'Import existing data', desc: 'Upload customer, vendor, and opening balance information' }
              ].map((item, index) => (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground text-sm flex items-center justify-center font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{item.step}</p>
                    <p className="text-sm text-muted-foreground">{item.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'workflows',
      title: 'Complete Workflows',
      icon: ArrowRight,
      description: 'End-to-end process guides for all major functions',
      content: (
        <div className="space-y-6">
          <div className="space-y-6">
            <h3 className="text-xl font-semibold mb-4">Complete Business Workflows</h3>

            <div className="grid grid-cols-1 gap-6">
              {/* Invoice to Payment Workflow */}
              <Card className="border-l-4 border-l-blue-500">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Receipt className="h-5 w-5 text-blue-500" />
                    Invoice to Payment Workflow
                  </CardTitle>
                  <CardDescription>Complete process from creating an invoice to receiving payment</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    {[
                      { step: '1', title: 'Create Customer', desc: 'Add customer details, contact info, and payment terms' },
                      { step: '2', title: 'Generate Invoice', desc: 'Create invoice with line items, VAT calculations' },
                      { step: '3', title: 'Send Invoice', desc: 'Export PDF and send to customer via email' },
                      { step: '4', title: 'Record Payment', desc: 'Log payment received from customer' },
                      { step: '5', title: 'Reconcile', desc: 'Match payment to invoice and update AR' }
                    ].map((item, index) => (
                      <div key={index} className="text-center">
                        <div className="w-8 h-8 rounded-full bg-blue-500 text-white text-sm font-medium flex items-center justify-center mx-auto mb-2">
                          {item.step}
                        </div>
                        <h4 className="font-medium text-sm mb-1">{item.title}</h4>
                        <p className="text-xs text-muted-foreground">{item.desc}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Bill to Payment Workflow */}
              <Card className="border-l-4 border-l-green-500">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-green-500" />
                    Bill to Payment Workflow
                  </CardTitle>
                  <CardDescription>Process for managing vendor bills and payments</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    {[
                      { step: '1', title: 'Add Vendor', desc: 'Register vendor with contact and payment details' },
                      { step: '2', title: 'Record Bill', desc: 'Enter bill details with expense categorization' },
                      { step: '3', title: 'Get Approval', desc: 'Route for approval based on amount thresholds' },
                      { step: '4', title: 'Process Payment', desc: 'Record payment made to vendor' },
                      { step: '5', title: 'Mark Paid', desc: 'Update bill status and AP balance' }
                    ].map((item, index) => (
                      <div key={index} className="text-center">
                        <div className="w-8 h-8 rounded-full bg-green-500 text-white text-sm font-medium flex items-center justify-center mx-auto mb-2">
                          {item.step}
                        </div>
                        <h4 className="font-medium text-sm mb-1">{item.title}</h4>
                        <p className="text-xs text-muted-foreground">{item.desc}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Month-End Closing */}
              <Card className="border-l-4 border-l-purple-500">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-purple-500" />
                    Month-End Closing Process
                  </CardTitle>
                  <CardDescription>Essential steps for monthly financial close</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-3">Pre-Closing Tasks</h4>
                      <ul className="space-y-2">
                        {[
                          'Reconcile all bank accounts',
                          'Review and approve pending transactions',
                          'Record accruals and deferrals',
                          'Update inventory valuations',
                          'Review AR and AP aging'
                        ].map((task, index) => (
                          <li key={index} className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-4 w-4 text-purple-500" />
                            {task}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium mb-3">Closing Tasks</h4>
                      <ul className="space-y-2">
                        {[
                          'Generate trial balance',
                          'Review financial statements',
                          'Calculate and record VAT',
                          'Prepare management reports',
                          'Archive period documents'
                        ].map((task, index) => (
                          <li key={index} className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-4 w-4 text-purple-500" />
                            {task}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'features',
      title: 'Feature Guides',
      icon: Settings,
      description: 'Detailed guides for specific features and modules',
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                title: 'Navigation & Search',
                icon: Search,
                features: [
                  'Use Ctrl+K (Cmd+K) to open global search',
                  'Search across customers, vendors, invoices, bills',
                  'Navigate with arrow keys and Enter',
                  'View recent search history'
                ]
              },
              {
                title: 'Form Validation',
                icon: CheckCircle,
                features: [
                  'Phone numbers auto-add +256 prefix',
                  'TIN validation with helpful guidance',
                  'Email validation with helper text',
                  'Password fields with show/hide toggle'
                ]
              },
              {
                title: 'PDF Exports',
                icon: FileText,
                features: [
                  'Include proper page margins',
                  'Add audit information (user, company, period)',
                  'Compliance and tracking purposes',
                  'Professional formatting'
                ]
              },
              {
                title: 'Integration Setup',
                icon: Zap,
                features: [
                  'Mobile Money integration',
                  'Bank account configuration',
                  'URA compliance features',
                  'Enterprise features available'
                ]
              },
              {
                title: 'Data Security',
                icon: Shield,
                features: [
                  'Row Level Security (RLS)',
                  'Organization-based data isolation',
                  'Role-based access control',
                  'Audit trail logging'
                ]
              },
              {
                title: 'Backup & Restore',
                icon: Database,
                features: [
                  'Automated backup scheduling',
                  'AES-256-GCM encryption',
                  'Organization-specific keys',
                  'SHA-256 checksums'
                ]
              }
            ].map((feature, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <feature.icon className="h-4 w-4 text-primary" />
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-1">
                    {feature.features.map((item, itemIndex) => (
                      <li key={itemIndex} className="text-sm flex items-start gap-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )
    },
    {
      id: 'shortcuts',
      title: 'Quick Reference',
      icon: Keyboard,
      description: 'Keyboard shortcuts, common tasks, and troubleshooting',
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Keyboard className="h-5 w-5" />
                  Keyboard Shortcuts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { keys: 'Ctrl + K', action: 'Open global search' },
                    { keys: 'Cmd + K', action: 'Open global search (Mac)' },
                    { keys: 'Enter', action: 'Select search result' },
                    { keys: '↑ ↓', action: 'Navigate search results' },
                    { keys: 'Esc', action: 'Close modals/dialogs' }
                  ].map((shortcut, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm">{shortcut.action}</span>
                      <Badge variant="secondary" className="font-mono text-xs">
                        {shortcut.keys}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Common Tasks
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    'Create new invoice from navbar + button',
                    'Record payment against invoice',
                    'Generate financial reports',
                    'Export data to PDF',
                    'Set up recurring transactions',
                    'Manage user permissions'
                  ].map((task, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      {task}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Troubleshooting Common Issues</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    issue: 'Cannot see transactions from other users',
                    solution: 'Check your user role permissions. Only Owners/Admins can see all organization data.'
                  },
                  {
                    issue: 'Phone number validation errors',
                    solution: 'Enter numbers without the leading 0. The system will auto-add +256 prefix.'
                  },
                  {
                    issue: 'PDF exports missing information',
                    solution: 'Ensure you have proper permissions and the document is fully loaded before exporting.'
                  },
                  {
                    issue: 'Search not finding results',
                    solution: 'Try different keywords or check if you have access to the data you\'re searching for.'
                  }
                ].map((item, index) => (
                  <div key={index} className="border-l-4 border-l-orange-500 pl-4">
                    <p className="font-medium text-sm mb-1">{item.issue}</p>
                    <p className="text-sm text-muted-foreground">{item.solution}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )
    },
    {
      id: 'differences',
      title: 'Key Differences',
      icon: Target,
      description: 'How Kaya Finance differs from other accounting systems',
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-4">Transitioning from Other Systems</h3>
            <p className="text-muted-foreground mb-6">
              If you're coming from other accounting software, here are the key differences and advantages of Kaya Finance:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="text-lg text-green-700">What's Better in Kaya</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {[
                    'Automatic double-entry journal creation',
                    'Built-in URA compliance and VAT handling',
                    'Real-time collaboration with role-based access',
                    'Cloud-based with offline capability',
                    'Modern, intuitive user interface',
                    'Integrated payment reconciliation',
                    'Comprehensive audit trails',
                    'Mobile-responsive design'
                  ].map((item, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-blue-500">
              <CardHeader>
                <CardTitle className="text-lg text-blue-700">Key Concepts to Learn</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {[
                    'Organization-based data isolation',
                    'Role-based permissions (Owner/Admin/Accountant/User)',
                    'Automatic journal entry generation',
                    'Payment reconciliation workflow',
                    'Global search functionality (Ctrl+K)',
                    'Modal-based form interactions',
                    'Real-time data synchronization',
                    'Integrated backup and restore'
                  ].map((item, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <Lightbulb className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-amber-50 border-amber-200">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2 text-amber-800">
                <Target className="h-5 w-5" />
                Migration Tips
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2 text-amber-800">Before You Start</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• Export your chart of accounts from old system</li>
                    <li>• Gather customer and vendor contact lists</li>
                    <li>• Prepare opening balances as of transition date</li>
                    <li>• Document your current workflows</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2 text-amber-800">During Migration</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• Set up chart of accounts first</li>
                    <li>• Import customers and vendors</li>
                    <li>• Enter opening balances via journal entries</li>
                    <li>• Test workflows with sample transactions</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }
  ]

  const filteredSections = helpSections.filter(section =>
    section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-3">
          <HelpCircle className="h-8 w-8 text-primary" />
          Getting Started with Kaya Finance
        </h1>
        <p className="text-muted-foreground text-lg">
          Comprehensive guide for accountants transitioning to Kaya Finance
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <Card className="sticky top-6">
            <CardHeader className="pb-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search help topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <ScrollArea className="h-[400px]">
                <div className="space-y-1">
                  {filteredSections.map((section) => (
                    <Button
                      key={section.id}
                      variant={activeSection === section.id ? "secondary" : "ghost"}
                      className="w-full justify-start text-left h-auto p-3"
                      onClick={() => setActiveSection(section.id)}
                    >
                      <section.icon className="h-4 w-4 mr-2 flex-shrink-0" />
                      <div>
                        <div className="font-medium text-sm">{section.title}</div>
                        <div className="text-xs text-muted-foreground">{section.description}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-2">
          <Card>
            <CardContent className="p-8">
              {filteredSections.find(section => section.id === activeSection)?.content}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Contact Section */}
      <Card className="mt-8 border-primary/20 bg-primary/5">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <MessageCircle className="h-5 w-5 text-primary" />
            Need Additional Help?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button asChild className="h-auto p-4 justify-start">
              <a href="https://wa.me/256777959328" target="_blank" rel="noopener noreferrer">
                <MessageCircle className="h-5 w-5 mr-3" />
                <div className="text-left">
                  <div className="font-medium">WhatsApp Support</div>
                  <div className="text-sm opacity-90">+256 777 959 328</div>
                </div>
                <ExternalLink className="h-4 w-4 ml-auto" />
              </a>
            </Button>
            
            <Button variant="outline" asChild className="h-auto p-4 justify-start">
              <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">
                <Mail className="h-5 w-5 mr-3" />
                <div className="text-left">
                  <div className="font-medium">Email Support</div>
                  <div className="text-sm text-muted-foreground"><EMAIL></div>
                </div>
                <ExternalLink className="h-4 w-4 ml-auto" />
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
