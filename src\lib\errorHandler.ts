/**
 * Global Error Handler
 * Centralized error handling, logging, and reporting system
 */

import { config } from '@/lib/config'
import { auditLogger } from '@/lib/auditLogger'

// Define a type for metadata values
type MetadataValue = string | number | boolean | null | undefined | Date | MetadataValue[] | { [key: string]: MetadataValue }

export interface ErrorContext {
  userId?: string
  orgId?: string
  component?: string
  action?: string
  metadata?: Record<string, MetadataValue>
  timestamp?: Date
}

export interface ErrorReport {
  id: string
  error: Error
  context: ErrorContext
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: 'network' | 'validation' | 'business' | 'system' | 'security'
  handled: boolean
  timestamp: Date
}

export class ErrorHandler {
  private static instance: ErrorHandler
  private errorReports: ErrorReport[] = []
  private maxReports = 1000
  private sentryEnabled = false

  private constructor() {
    this.initializeSentry().catch(error => {
      console.warn('Failed to initialize Sentry:', error)
      this.sentryEnabled = false
    })
    this.setupGlobalErrorHandlers()
  }

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  private async initializeSentry(): Promise<void> {
    try {
      // Initialize Sentry if DSN is provided
      const sentryDsn = import.meta.env.VITE_SENTRY_DSN
      if (sentryDsn && typeof window !== 'undefined') {
        try {
          // Dynamic import to avoid bundling Sentry if not needed
          const Sentry = await import('@sentry/react')

          Sentry.init({
            dsn: sentryDsn,
            environment: config.app.environment,
            integrations: [
              new Sentry.BrowserTracing(),
              new Sentry.Replay(),
            ],
            tracesSampleRate: config.app.environment === 'production' ? 0.1 : 1.0,
            replaysSessionSampleRate: 0.1,
            replaysOnErrorSampleRate: 1.0,
            beforeSend: (event) => {
              // Filter out non-critical errors in production
              if (config.app.environment === 'production') {
                if (event.level === 'info' || event.level === 'debug') {
                  return null
                }
              }
              return event
            }
          })
          this.sentryEnabled = true
          console.log('Sentry initialized successfully')
        } catch (importError) {
          console.warn('Failed to import Sentry:', importError)
          this.sentryEnabled = false
        }
      } else {
        console.log('Sentry not configured (no DSN provided)')
        this.sentryEnabled = false
      }
    } catch (error) {
      console.warn('Sentry initialization error:', error)
      this.sentryEnabled = false
    }
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(
        new Error(`Unhandled Promise Rejection: ${event.reason}`),
        {
          component: 'Global',
          action: 'unhandledrejection',
          metadata: { reason: event.reason }
        },
        'high',
        'system'
      )
    })

    // Handle global JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError(
        new Error(`Global Error: ${event.message}`),
        {
          component: 'Global',
          action: 'error',
          metadata: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          }
        },
        'high',
        'system'
      )
    })
  }

  public handleError(
    error: Error,
    context: ErrorContext = {},
    severity: ErrorReport['severity'] = 'medium',
    category: ErrorReport['category'] = 'system',
    handled: boolean = true
  ): string {
    const errorId = this.generateErrorId()
    const timestamp = new Date()

    const errorReport: ErrorReport = {
      id: errorId,
      error,
      context: {
        ...context,
        timestamp
      },
      severity,
      category,
      handled,
      timestamp
    }

    // Store error report
    this.storeErrorReport(errorReport)

    // Log error
    this.logError(errorReport)

    // Report to external services
    this.reportToExternalServices(errorReport)

    // Audit log for security-related errors
    if (category === 'security') {
      this.auditSecurityError(errorReport)
    }

    return errorId
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private storeErrorReport(report: ErrorReport): void {
    this.errorReports.push(report)

    // Maintain maximum number of reports
    if (this.errorReports.length > this.maxReports) {
      this.errorReports = this.errorReports.slice(-this.maxReports)
    }
  }

  private logError(report: ErrorReport): void {
    const logLevel = this.getLogLevel(report.severity)
    const logMessage = `[${report.category.toUpperCase()}] ${report.error.message}`
    const logData = {
      errorId: report.id,
      severity: report.severity,
      category: report.category,
      context: report.context,
      stack: report.error.stack,
      handled: report.handled
    }

    switch (logLevel) {
      case 'error':
        console.error(logMessage, logData)
        break
      case 'warn':
        console.warn(logMessage, logData)
        break
      case 'info':
        console.info(logMessage, logData)
        break
      default:
        console.log(logMessage, logData)
    }
  }

  private getLogLevel(severity: ErrorReport['severity']): string {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'error'
      case 'medium':
        return 'warn'
      case 'low':
        return 'info'
      default:
        return 'log'
    }
  }

  private reportToExternalServices(report: ErrorReport): void {
    // Report to Sentry
    if (this.sentryEnabled && typeof window !== 'undefined') {
      import('@sentry/react').then((Sentry) => {
        Sentry.withScope((scope) => {
          scope.setTag('errorId', report.id)
          scope.setTag('category', report.category)
          scope.setLevel(this.getSentryLevel(report.severity))
          scope.setContext('errorContext', report.context)
          
          if (report.context.userId) {
            scope.setUser({ id: report.context.userId })
          }

          Sentry.captureException(report.error)
        })
      }).catch((error) => {
        console.warn('Failed to report to Sentry:', error)
      })
    }

    // Report critical errors to monitoring service
    if (report.severity === 'critical') {
      this.reportCriticalError(report)
    }
  }

  private getSentryLevel(severity: ErrorReport['severity']): 'fatal' | 'error' | 'warning' | 'info' {
    switch (severity) {
      case 'critical':
        return 'fatal'
      case 'high':
        return 'error'
      case 'medium':
        return 'warning'
      case 'low':
        return 'info'
      default:
        return 'error'
    }
  }

  private reportCriticalError(report: ErrorReport): void {
    // In a production environment, this would send alerts to monitoring services
    // like PagerDuty, Slack, or email notifications
    console.error('CRITICAL ERROR DETECTED:', {
      errorId: report.id,
      message: report.error.message,
      context: report.context,
      timestamp: report.timestamp
    })

    // Store critical error for immediate attention
    localStorage.setItem(`critical_error_${report.id}`, JSON.stringify({
      id: report.id,
      message: report.error.message,
      timestamp: report.timestamp,
      context: report.context
    }))
  }

  private auditSecurityError(report: ErrorReport): void {
    auditLogger.logSecurityEvent({
      event_type: 'security_error',
      user_id: report.context.userId || 'unknown',
      org_id: report.context.orgId || 'unknown',
      details: {
        errorId: report.id,
        errorMessage: report.error.message,
        component: report.context.component,
        action: report.context.action,
        metadata: report.context.metadata
      },
      severity: report.severity,
      ip_address: null // Would be populated by server
    })
  }

  public getErrorReports(filters?: {
    severity?: ErrorReport['severity']
    category?: ErrorReport['category']
    since?: Date
  }): ErrorReport[] {
    let reports = [...this.errorReports]

    if (filters) {
      if (filters.severity) {
        reports = reports.filter(r => r.severity === filters.severity)
      }
      if (filters.category) {
        reports = reports.filter(r => r.category === filters.category)
      }
      if (filters.since) {
        reports = reports.filter(r => r.timestamp >= filters.since!)
      }
    }

    return reports.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  public getErrorStats(): {
    total: number
    bySeverity: Record<string, number>
    byCategory: Record<string, number>
    recent: number
  } {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)

    const bySeverity: Record<string, number> = {}
    const byCategory: Record<string, number> = {}
    let recent = 0

    this.errorReports.forEach(report => {
      bySeverity[report.severity] = (bySeverity[report.severity] || 0) + 1
      byCategory[report.category] = (byCategory[report.category] || 0) + 1
      
      if (report.timestamp >= oneHourAgo) {
        recent++
      }
    })

    return {
      total: this.errorReports.length,
      bySeverity,
      byCategory,
      recent
    }
  }

  public clearErrorReports(): void {
    this.errorReports = []
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance()

// Convenience functions
export const handleError = (
  error: Error,
  context?: ErrorContext,
  severity?: ErrorReport['severity'],
  category?: ErrorReport['category']
) => errorHandler.handleError(error, context, severity, category)

export const handleNetworkError = (error: Error, context?: ErrorContext) =>
  errorHandler.handleError(error, context, 'medium', 'network')

export const handleValidationError = (error: Error, context?: ErrorContext) =>
  errorHandler.handleError(error, context, 'low', 'validation')

export const handleBusinessError = (error: Error, context?: ErrorContext) =>
  errorHandler.handleError(error, context, 'medium', 'business')

export const handleSecurityError = (error: Error, context?: ErrorContext) =>
  errorHandler.handleError(error, context, 'high', 'security')

export const handleCriticalError = (error: Error, context?: ErrorContext) =>
  errorHandler.handleError(error, context, 'critical', 'system')
