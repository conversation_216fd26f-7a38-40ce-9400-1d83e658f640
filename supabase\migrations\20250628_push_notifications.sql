-- =====================================================
-- PUSH NOTIFICATIONS MIGRATION
-- =====================================================
-- Migration: 20250628_push_notifications.sql
-- Description: Push notification subscriptions and tracking
-- Author: Kaya Finance Team
-- Date: 2025-06-28

-- =====================================================
-- STEP 1: CREATE PUSH NOTIFICATION TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📱 Creating push notification tables...';
END $$;

-- Push subscriptions table
CREATE TABLE IF NOT EXISTS push_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    endpoint TEXT NOT NULL,
    p256dh_key TEXT NOT NULL,
    auth_key TEXT NOT NULL,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, endpoint)
);

-- Push notification deliveries table
CREATE TABLE IF NOT EXISTS push_deliveries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id UUID NOT NULL REFERENCES push_subscriptions(id) ON DELETE CASCADE,
    notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
    payload JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'expired')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Push notification clicks table
CREATE TABLE IF NOT EXISTS push_clicks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    delivery_id UUID NOT NULL REFERENCES push_deliveries(id) ON DELETE CASCADE,
    action TEXT,
    clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update notification_preferences to include push_enabled
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification_preferences' 
        AND column_name = 'push_enabled'
    ) THEN
        ALTER TABLE notification_preferences 
        ADD COLUMN push_enabled BOOLEAN DEFAULT true;
        
        RAISE NOTICE '✅ Added push_enabled column to notification_preferences';
    ELSE
        RAISE NOTICE '⚠️ push_enabled column already exists in notification_preferences';
    END IF;
END $$;

-- =====================================================
-- STEP 2: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📊 Creating push notification indexes...';
END $$;

-- Push subscriptions indexes
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON push_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_org_id ON push_subscriptions(org_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_active ON push_subscriptions(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_endpoint ON push_subscriptions(endpoint);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_last_used ON push_subscriptions(last_used_at DESC);

-- Push deliveries indexes
CREATE INDEX IF NOT EXISTS idx_push_deliveries_subscription_id ON push_deliveries(subscription_id);
CREATE INDEX IF NOT EXISTS idx_push_deliveries_notification_id ON push_deliveries(notification_id);
CREATE INDEX IF NOT EXISTS idx_push_deliveries_status ON push_deliveries(status);
CREATE INDEX IF NOT EXISTS idx_push_deliveries_created_at ON push_deliveries(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_push_deliveries_sent_at ON push_deliveries(sent_at DESC);

-- Push clicks indexes
CREATE INDEX IF NOT EXISTS idx_push_clicks_delivery_id ON push_clicks(delivery_id);
CREATE INDEX IF NOT EXISTS idx_push_clicks_clicked_at ON push_clicks(clicked_at DESC);

-- =====================================================
-- STEP 3: CREATE PUSH NOTIFICATION FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating push notification functions...';
END $$;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_push_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_push_subscriptions_updated_at ON push_subscriptions;
CREATE TRIGGER update_push_subscriptions_updated_at 
    BEFORE UPDATE ON push_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_push_updated_at_column();

DROP TRIGGER IF EXISTS update_push_deliveries_updated_at ON push_deliveries;
CREATE TRIGGER update_push_deliveries_updated_at 
    BEFORE UPDATE ON push_deliveries
    FOR EACH ROW EXECUTE FUNCTION update_push_updated_at_column();

-- Function to clean up inactive subscriptions
CREATE OR REPLACE FUNCTION cleanup_inactive_push_subscriptions(
    days_inactive INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM push_subscriptions 
    WHERE is_active = false 
    OR last_used_at < NOW() - INTERVAL '1 day' * days_inactive;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get push subscription analytics
CREATE OR REPLACE FUNCTION get_push_analytics(
    org_id_param UUID,
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS JSONB AS $$
DECLARE
    analytics JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_subscriptions', (
            SELECT COUNT(*) FROM push_subscriptions ps 
            WHERE ps.org_id = org_id_param AND ps.is_active = true
        ),
        'total_sent', COUNT(*),
        'delivered', COUNT(*) FILTER (WHERE status = 'delivered'),
        'failed', COUNT(*) FILTER (WHERE status = 'failed'),
        'pending', COUNT(*) FILTER (WHERE status = 'pending'),
        'delivery_rate', ROUND((COUNT(*) FILTER (WHERE status = 'delivered')::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 2),
        'failure_rate', ROUND((COUNT(*) FILTER (WHERE status = 'failed')::DECIMAL / NULLIF(COUNT(*), 0)) * 100, 2),
        'clicks', (
            SELECT COUNT(*) FROM push_clicks pc
            JOIN push_deliveries pd ON pc.delivery_id = pd.id
            JOIN push_subscriptions ps ON pd.subscription_id = ps.id
            WHERE ps.org_id = org_id_param
            AND DATE(pc.clicked_at) BETWEEN start_date AND end_date
        ),
        'click_rate', ROUND((
            SELECT COUNT(*) FROM push_clicks pc
            JOIN push_deliveries pd ON pc.delivery_id = pd.id
            JOIN push_subscriptions ps ON pd.subscription_id = ps.id
            WHERE ps.org_id = org_id_param
            AND DATE(pc.clicked_at) BETWEEN start_date AND end_date
        )::DECIMAL / NULLIF(COUNT(*) FILTER (WHERE status = 'delivered'), 0) * 100, 2)
    ) INTO analytics
    FROM push_deliveries pd
    JOIN push_subscriptions ps ON pd.subscription_id = ps.id
    WHERE ps.org_id = org_id_param
    AND DATE(pd.created_at) BETWEEN start_date AND end_date;
    
    RETURN COALESCE(analytics, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's active push subscriptions
CREATE OR REPLACE FUNCTION get_user_push_subscriptions(user_id_param UUID)
RETURNS SETOF push_subscriptions AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM push_subscriptions
    WHERE user_id = user_id_param
    AND is_active = true
    ORDER BY last_used_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update subscription last used timestamp
CREATE OR REPLACE FUNCTION update_subscription_last_used(subscription_id_param UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE push_subscriptions 
    SET last_used_at = NOW()
    WHERE id = subscription_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 4: CREATE RLS POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Creating push notification RLS policies...';
END $$;

-- Enable RLS on push tables
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_clicks ENABLE ROW LEVEL SECURITY;

-- Push subscriptions policies
CREATE POLICY "push_subscriptions_select_policy" ON push_subscriptions
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        user_id = auth.uid() OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "push_subscriptions_insert_policy" ON push_subscriptions
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role' OR
        user_id = auth.uid()
    );

CREATE POLICY "push_subscriptions_update_policy" ON push_subscriptions
    FOR UPDATE USING (
        auth.role() = 'service_role' OR
        user_id = auth.uid()
    );

CREATE POLICY "push_subscriptions_delete_policy" ON push_subscriptions
    FOR DELETE USING (
        auth.role() = 'service_role' OR
        user_id = auth.uid()
    );

-- Push deliveries policies
CREATE POLICY "push_deliveries_select_policy" ON push_deliveries
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        subscription_id IN (
            SELECT ps.id FROM push_subscriptions ps
            WHERE ps.user_id = auth.uid()
        )
    );

CREATE POLICY "push_deliveries_insert_policy" ON push_deliveries
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "push_deliveries_update_policy" ON push_deliveries
    FOR UPDATE USING (auth.role() = 'service_role');

-- Push clicks policies
CREATE POLICY "push_clicks_select_policy" ON push_clicks
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        delivery_id IN (
            SELECT pd.id FROM push_deliveries pd
            JOIN push_subscriptions ps ON pd.subscription_id = ps.id
            WHERE ps.user_id = auth.uid()
        )
    );

CREATE POLICY "push_clicks_insert_policy" ON push_clicks
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ PUSH NOTIFICATIONS MIGRATION COMPLETED!';
    RAISE NOTICE '====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '📱 CREATED TABLES:';
    RAISE NOTICE '  • push_subscriptions - User push subscriptions';
    RAISE NOTICE '  • push_deliveries - Push delivery tracking';
    RAISE NOTICE '  • push_clicks - Push click tracking';
    RAISE NOTICE '';
    RAISE NOTICE '📊 CREATED INDEXES:';
    RAISE NOTICE '  • Performance indexes for all push tables';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ CREATED FUNCTIONS:';
    RAISE NOTICE '  • cleanup_inactive_push_subscriptions()';
    RAISE NOTICE '  • get_push_analytics()';
    RAISE NOTICE '  • get_user_push_subscriptions()';
    RAISE NOTICE '  • update_subscription_last_used()';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 CREATED RLS POLICIES:';
    RAISE NOTICE '  • User-specific subscription access';
    RAISE NOTICE '  • Service role administrative access';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Push notifications are ready!';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- EXTERNAL INTEGRATIONS EXTENSION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔗 Adding external integrations support...';
END $$;

-- Notification integrations table
CREATE TABLE IF NOT EXISTS notification_integrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('slack', 'teams', 'webhook', 'sms')),
    name TEXT NOT NULL,
    config JSONB NOT NULL DEFAULT '{}',
    notification_types TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(org_id, name)
);

-- Integration delivery logs
CREATE TABLE IF NOT EXISTS integration_deliveries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID NOT NULL REFERENCES notification_integrations(id) ON DELETE CASCADE,
    notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'retrying')),
    response_code INTEGER,
    response_body TEXT,
    error_message TEXT,
    sent_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for integrations
CREATE INDEX IF NOT EXISTS idx_notification_integrations_org_id ON notification_integrations(org_id);
CREATE INDEX IF NOT EXISTS idx_notification_integrations_type ON notification_integrations(type);
CREATE INDEX IF NOT EXISTS idx_notification_integrations_active ON notification_integrations(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_integration_deliveries_integration_id ON integration_deliveries(integration_id);
CREATE INDEX IF NOT EXISTS idx_integration_deliveries_notification_id ON integration_deliveries(notification_id);
CREATE INDEX IF NOT EXISTS idx_integration_deliveries_status ON integration_deliveries(status);
CREATE INDEX IF NOT EXISTS idx_integration_deliveries_created_at ON integration_deliveries(created_at DESC);

-- RLS policies for integrations
ALTER TABLE notification_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_deliveries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "notification_integrations_select_policy" ON notification_integrations
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "notification_integrations_insert_policy" ON notification_integrations
    FOR INSERT WITH CHECK (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "notification_integrations_update_policy" ON notification_integrations
    FOR UPDATE USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "notification_integrations_delete_policy" ON notification_integrations
    FOR DELETE USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "integration_deliveries_select_policy" ON integration_deliveries
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        integration_id IN (
            SELECT ni.id FROM notification_integrations ni
            JOIN profiles p ON ni.org_id = p.org_id
            WHERE p.id = auth.uid()
        )
    );

CREATE POLICY "integration_deliveries_insert_policy" ON integration_deliveries
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ EXTERNAL INTEGRATIONS ADDED!';
    RAISE NOTICE '====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '🔗 CREATED TABLES:';
    RAISE NOTICE '  • notification_integrations - Integration configs';
    RAISE NOTICE '  • integration_deliveries - Delivery tracking';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 External integrations are ready!';
    RAISE NOTICE '';
END $$;
