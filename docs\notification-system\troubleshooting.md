# Troubleshooting Guide

Comprehensive troubleshooting guide for the Kaya Finance notification system.

## Table of Contents

1. [Common Issues](#common-issues)
2. [Database Issues](#database-issues)
3. [Real-time Connection Issues](#real-time-connection-issues)
4. [Email Delivery Issues](#email-delivery-issues)
5. [Push Notification Issues](#push-notification-issues)
6. [Performance Issues](#performance-issues)
7. [Integration Issues](#integration-issues)
8. [Debugging Tools](#debugging-tools)
9. [Error Codes](#error-codes)
10. [Support Resources](#support-resources)

## Common Issues

### Notifications Not Appearing

**Symptoms:**
- Notifications created but not visible in UI
- Real-time updates not working
- Notification count incorrect

**Diagnosis:**
```typescript
// Check if notifications exist in database
const { data, error } = await supabase
  .from('notifications')
  .select('*')
  .eq('user_id', userId)
  .eq('org_id', orgId)
  .order('created_at', { ascending: false })
  .limit(10)

console.log('Recent notifications:', data)
```

**Solutions:**
1. **Check RLS Policies:**
   ```sql
   -- Verify user can access notifications
   SELECT * FROM notifications 
   WHERE user_id = 'user-id' OR (user_id IS NULL AND org_id = 'org-id')
   LIMIT 5;
   ```

2. **Verify Real-time Connection:**
   ```typescript
   const { isConnected, connectionError } = useNotificationRealtime()
   console.log('Real-time status:', { isConnected, connectionError })
   ```

3. **Check Browser Console:**
   - Look for JavaScript errors
   - Verify network requests are successful
   - Check WebSocket connection status

### Slow Notification Loading

**Symptoms:**
- Long loading times for notification list
- Timeout errors
- Poor pagination performance

**Diagnosis:**
```sql
-- Check query performance
EXPLAIN ANALYZE 
SELECT * FROM notifications 
WHERE user_id = 'user-id' 
ORDER BY created_at DESC 
LIMIT 20;
```

**Solutions:**
1. **Optimize Database Queries:**
   ```sql
   -- Add missing indexes
   CREATE INDEX CONCURRENTLY idx_notifications_user_created 
   ON notifications(user_id, created_at DESC);
   ```

2. **Implement Caching:**
   ```typescript
   // Add React Query caching
   const { data } = useQuery({
     queryKey: ['notifications', userId, filters],
     queryFn: () => fetchNotifications(userId, filters),
     staleTime: 5 * 60 * 1000, // 5 minutes
     cacheTime: 10 * 60 * 1000 // 10 minutes
   })
   ```

3. **Use Virtual Scrolling:**
   ```typescript
   <VirtualizedNotificationList
     notifications={notifications}
     height={600}
     itemHeight={80}
   />
   ```

### Permission Denied Errors

**Symptoms:**
- "Permission denied" errors in console
- Unable to create/update notifications
- RLS policy violations

**Diagnosis:**
```sql
-- Check user's organization membership
SELECT p.id, p.org_id, o.name 
FROM profiles p 
JOIN organizations o ON p.org_id = o.id 
WHERE p.id = 'user-id';

-- Test RLS policy
SET ROLE authenticated;
SET request.jwt.claims TO '{"sub": "user-id"}';
SELECT * FROM notifications WHERE org_id = 'org-id' LIMIT 1;
```

**Solutions:**
1. **Verify User Profile:**
   ```typescript
   const { data: profile } = await supabase
     .from('profiles')
     .select('id, org_id, role')
     .eq('id', userId)
     .single()
   
   if (!profile?.org_id) {
     console.error('User not associated with organization')
   }
   ```

2. **Check RLS Policies:**
   ```sql
   -- List all policies for notifications table
   SELECT policyname, permissive, roles, cmd, qual 
   FROM pg_policies 
   WHERE tablename = 'notifications';
   ```

## Database Issues

### Connection Pool Exhaustion

**Symptoms:**
- "Too many connections" errors
- Slow database responses
- Connection timeouts

**Diagnosis:**
```sql
-- Check active connections
SELECT count(*) as active_connections 
FROM pg_stat_activity 
WHERE state = 'active';

-- Check connection by application
SELECT application_name, count(*) 
FROM pg_stat_activity 
GROUP BY application_name;
```

**Solutions:**
1. **Optimize Connection Pool:**
   ```typescript
   // Adjust pool settings
   const pool = new Pool({
     max: 20, // Reduce if too high
     idleTimeoutMillis: 30000,
     connectionTimeoutMillis: 2000,
   })
   ```

2. **Close Unused Connections:**
   ```typescript
   // Properly close connections
   try {
     const result = await pool.query('SELECT * FROM notifications')
     return result.rows
   } finally {
     // Connection automatically returned to pool
   }
   ```

### Migration Failures

**Symptoms:**
- Migration scripts fail to execute
- Missing tables or columns
- Constraint violations

**Diagnosis:**
```sql
-- Check migration status
SELECT * FROM supabase_migrations.schema_migrations 
ORDER BY version DESC;

-- Verify table structure
\d notifications
\d notification_preferences
```

**Solutions:**
1. **Manual Migration Recovery:**
   ```bash
   # Rollback failed migration
   supabase db reset
   
   # Apply migrations one by one
   psql -f supabase/migrations/20250628_notifications_system.sql
   ```

2. **Fix Constraint Violations:**
   ```sql
   -- Remove invalid data before migration
   DELETE FROM notifications 
   WHERE type NOT IN ('payment_pending_approval', 'invoice_overdue', ...);
   ```

## Real-time Connection Issues

### WebSocket Connection Failures

**Symptoms:**
- Real-time updates not working
- Connection drops frequently
- Fallback to polling

**Diagnosis:**
```typescript
// Monitor connection status
const { isConnected, connectionError, isUsingPolling } = useNotificationRealtime()

console.log('Connection status:', {
  isConnected,
  error: connectionError?.message,
  usingPolling: isUsingPolling
})
```

**Solutions:**
1. **Check Network Configuration:**
   ```javascript
   // Verify WebSocket URL
   const wsUrl = process.env.NEXT_PUBLIC_SUPABASE_URL?.replace('https://', 'wss://') + '/realtime/v1'
   console.log('WebSocket URL:', wsUrl)
   ```

2. **Configure Proxy Settings:**
   ```nginx
   # Nginx configuration for WebSocket
   location /realtime/ {
     proxy_pass http://backend;
     proxy_http_version 1.1;
     proxy_set_header Upgrade $http_upgrade;
     proxy_set_header Connection "upgrade";
     proxy_set_header Host $host;
   }
   ```

3. **Implement Reconnection Logic:**
   ```typescript
   const reconnect = useCallback(() => {
     if (retryCount < MAX_RETRIES) {
       setTimeout(() => {
         setRetryCount(prev => prev + 1)
         initializeConnection()
       }, Math.pow(2, retryCount) * 1000) // Exponential backoff
     }
   }, [retryCount])
   ```

### Subscription Channel Issues

**Symptoms:**
- Not receiving updates for specific tables
- Partial updates only
- Duplicate notifications

**Diagnosis:**
```typescript
// Check subscription status
const subscription = supabase
  .channel('notifications')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'notifications'
  }, (payload) => {
    console.log('Received update:', payload)
  })
  .subscribe((status) => {
    console.log('Subscription status:', status)
  })
```

**Solutions:**
1. **Verify RLS on Subscriptions:**
   ```sql
   -- Ensure RLS allows real-time access
   SELECT * FROM notifications 
   WHERE user_id = auth.uid() 
   OR (user_id IS NULL AND org_id IN (
     SELECT org_id FROM profiles WHERE id = auth.uid()
   ));
   ```

2. **Filter Subscriptions Properly:**
   ```typescript
   const subscription = supabase
     .channel(`notifications:${orgId}`)
     .on('postgres_changes', {
       event: '*',
       schema: 'public',
       table: 'notifications',
       filter: `org_id=eq.${orgId}`
     }, handleUpdate)
   ```

## Email Delivery Issues

### Emails Not Sending

**Symptoms:**
- Email notifications not received
- High bounce rates
- Delivery failures

**Diagnosis:**
```sql
-- Check email delivery status
SELECT status, count(*) 
FROM email_deliveries 
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY status;

-- Check recent failures
SELECT email, error_message, created_at 
FROM email_deliveries 
WHERE status = 'failed' 
ORDER BY created_at DESC 
LIMIT 10;
```

**Solutions:**
1. **Verify Email Service Configuration:**
   ```typescript
   // Test email service connection
   const testEmail = async () => {
     try {
       const result = await emailService.send({
         to: '<EMAIL>',
         subject: 'Test Email',
         html: '<p>Test message</p>'
       })
       console.log('Email sent successfully:', result)
     } catch (error) {
       console.error('Email failed:', error)
     }
   }
   ```

2. **Check DNS and SPF Records:**
   ```bash
   # Verify DNS records
   dig TXT your-domain.com
   
   # Check SPF record
   dig TXT your-domain.com | grep "v=spf1"
   ```

3. **Monitor Bounce Rates:**
   ```sql
   -- Check bounce patterns
   SELECT 
     DATE(bounced_at) as date,
     count(*) as bounces
   FROM email_deliveries 
   WHERE status = 'bounced'
   AND bounced_at > NOW() - INTERVAL '7 days'
   GROUP BY DATE(bounced_at)
   ORDER BY date DESC;
   ```

### Template Rendering Issues

**Symptoms:**
- Broken email layouts
- Missing variables
- Encoding problems

**Diagnosis:**
```typescript
// Test template rendering
const testTemplate = async () => {
  const template = await getEmailTemplate('payment_pending_approval')
  const rendered = await renderTemplate(template, {
    user_name: 'Test User',
    amount: '$1,000',
    payee: 'Test Vendor'
  })
  console.log('Rendered template:', rendered)
}
```

**Solutions:**
1. **Validate Template Variables:**
   ```typescript
   const validateTemplate = (template: string, data: Record<string, any>) => {
     const variables = template.match(/\{\{(\w+)\}\}/g) || []
     const missing = variables.filter(v => {
       const key = v.replace(/[{}]/g, '')
       return !(key in data)
     })
     
     if (missing.length > 0) {
       console.warn('Missing template variables:', missing)
     }
   }
   ```

2. **Test Email Rendering:**
   ```typescript
   // Preview email in development
   if (process.env.NODE_ENV === 'development') {
     const previewUrl = await emailService.getPreviewUrl(emailData)
     console.log('Email preview:', previewUrl)
   }
   ```

## Push Notification Issues

### Service Worker Registration Failures

**Symptoms:**
- Push notifications not working
- Service worker errors
- Registration failures

**Diagnosis:**
```javascript
// Check service worker status
navigator.serviceWorker.getRegistrations().then(registrations => {
  console.log('Service worker registrations:', registrations)
  registrations.forEach(registration => {
    console.log('SW state:', registration.active?.state)
  })
})
```

**Solutions:**
1. **Verify Service Worker File:**
   ```javascript
   // Check if sw.js is accessible
   fetch('/sw.js')
     .then(response => {
       if (!response.ok) {
         console.error('Service worker file not found')
       }
     })
   ```

2. **Fix Registration Issues:**
   ```javascript
   // Proper service worker registration
   if ('serviceWorker' in navigator && 'PushManager' in window) {
     navigator.serviceWorker.register('/sw.js', { scope: '/' })
       .then(registration => {
         console.log('SW registered:', registration)
       })
       .catch(error => {
         console.error('SW registration failed:', error)
       })
   }
   ```

### Push Subscription Failures

**Symptoms:**
- Unable to subscribe to push notifications
- Permission denied errors
- VAPID key issues

**Diagnosis:**
```javascript
// Check push notification support
console.log('Push supported:', 'PushManager' in window)
console.log('Notification permission:', Notification.permission)
console.log('Service worker supported:', 'serviceWorker' in navigator)
```

**Solutions:**
1. **Verify VAPID Configuration:**
   ```javascript
   // Check VAPID key format
   const vapidKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
   if (!vapidKey || vapidKey.length !== 88) {
     console.error('Invalid VAPID public key')
   }
   ```

2. **Handle Permission Requests:**
   ```javascript
   const requestPermission = async () => {
     if (Notification.permission === 'default') {
       const permission = await Notification.requestPermission()
       return permission === 'granted'
     }
     return Notification.permission === 'granted'
   }
   ```

## Performance Issues

### High Memory Usage

**Symptoms:**
- Application crashes with out-of-memory errors
- Slow response times
- Memory leaks

**Diagnosis:**
```javascript
// Monitor memory usage
const memoryUsage = process.memoryUsage()
console.log('Memory usage:', {
  rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
  heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
  heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB'
})
```

**Solutions:**
1. **Optimize React Query Cache:**
   ```typescript
   const queryClient = new QueryClient({
     defaultOptions: {
       queries: {
         cacheTime: 5 * 60 * 1000, // 5 minutes
         staleTime: 1 * 60 * 1000,  // 1 minute
         refetchOnWindowFocus: false
       }
     }
   })
   ```

2. **Clean Up Subscriptions:**
   ```typescript
   useEffect(() => {
     const subscription = supabase.channel('notifications')
       .subscribe()
     
     return () => {
       subscription.unsubscribe()
     }
   }, [])
   ```

### Database Query Performance

**Symptoms:**
- Slow notification loading
- High database CPU usage
- Query timeouts

**Diagnosis:**
```sql
-- Find slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE query LIKE '%notification%'
ORDER BY mean_time DESC
LIMIT 10;
```

**Solutions:**
1. **Add Missing Indexes:**
   ```sql
   -- Analyze query plans
   EXPLAIN ANALYZE 
   SELECT * FROM notifications 
   WHERE user_id = 'user-id' 
   AND is_read = false 
   ORDER BY created_at DESC;
   
   -- Add appropriate index
   CREATE INDEX CONCURRENTLY idx_notifications_user_unread 
   ON notifications(user_id, is_read, created_at DESC) 
   WHERE is_read = false;
   ```

2. **Optimize Queries:**
   ```typescript
   // Use specific columns instead of SELECT *
   const { data } = await supabase
     .from('notifications')
     .select('id, title, message, created_at, is_read')
     .eq('user_id', userId)
     .order('created_at', { ascending: false })
     .limit(20)
   ```

## Integration Issues

### Slack Integration Failures

**Symptoms:**
- Slack notifications not delivered
- Webhook errors
- Authentication failures

**Diagnosis:**
```typescript
// Test Slack webhook
const testSlackWebhook = async (webhookUrl: string) => {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text: 'Test notification from Kaya Finance'
      })
    })
    
    console.log('Slack response:', response.status, await response.text())
  } catch (error) {
    console.error('Slack webhook failed:', error)
  }
}
```

**Solutions:**
1. **Verify Webhook URL:**
   ```typescript
   // Validate webhook URL format
   const isValidSlackWebhook = (url: string) => {
     return url.startsWith('https://hooks.slack.com/services/')
   }
   ```

2. **Handle Rate Limits:**
   ```typescript
   // Implement retry logic for Slack API
   const sendToSlackWithRetry = async (webhook: string, payload: any, retries = 3) => {
     for (let i = 0; i < retries; i++) {
       try {
         const response = await fetch(webhook, {
           method: 'POST',
           headers: { 'Content-Type': 'application/json' },
           body: JSON.stringify(payload)
         })
         
         if (response.status === 429) {
           // Rate limited, wait and retry
           const retryAfter = parseInt(response.headers.get('Retry-After') || '60')
           await new Promise(resolve => setTimeout(resolve, retryAfter * 1000))
           continue
         }
         
         return response
       } catch (error) {
         if (i === retries - 1) throw error
         await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)))
       }
     }
   }
   ```

## Debugging Tools

### Logging Configuration

```typescript
// lib/logger.ts
import winston from 'winston'

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
})

export { logger }
```

### Debug Utilities

```typescript
// lib/debug.ts
export const debugNotifications = {
  logNotificationCreation: (notification: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.group('🔔 Notification Created')
      console.log('Type:', notification.type)
      console.log('Title:', notification.title)
      console.log('User ID:', notification.user_id)
      console.log('Org ID:', notification.org_id)
      console.log('Data:', notification.data)
      console.groupEnd()
    }
  },
  
  logRealtimeEvent: (event: string, payload: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`📡 Real-time ${event}:`, payload)
    }
  }
}
```

## Error Codes

### Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `NOTIFICATION_001` | Invalid notification type | Check notification type against allowed values |
| `NOTIFICATION_002` | Missing required fields | Verify all required fields are provided |
| `NOTIFICATION_003` | User not found | Check user ID and organization membership |
| `NOTIFICATION_004` | Permission denied | Verify RLS policies and user permissions |
| `NOTIFICATION_005` | Rate limit exceeded | Implement backoff and retry logic |
| `EMAIL_001` | Email service unavailable | Check email service configuration |
| `EMAIL_002` | Invalid email template | Verify template syntax and variables |
| `PUSH_001` | Service worker not registered | Register service worker properly |
| `PUSH_002` | Push permission denied | Request notification permission |
| `REALTIME_001` | WebSocket connection failed | Check network and proxy configuration |
| `REALTIME_002` | Subscription failed | Verify RLS policies for real-time access |

## Support Resources

### Documentation Links
- [API Reference](./api-reference.md)
- [Component Guide](./components.md)
- [Database Schema](./database-schema.md)
- [Deployment Guide](./deployment.md)

### External Resources
- [Supabase Documentation](https://supabase.com/docs)
- [React Query Documentation](https://tanstack.com/query/latest)
- [Web Push Protocol](https://web.dev/push-notifications/)
- [PostgreSQL Performance Tuning](https://wiki.postgresql.org/wiki/Performance_Optimization)

### Getting Help
1. Check this troubleshooting guide
2. Search existing GitHub issues
3. Create a new issue with:
   - Error messages and logs
   - Steps to reproduce
   - Environment details
   - Expected vs actual behavior

### Emergency Contacts
- **Production Issues**: <EMAIL>
- **Security Issues**: <EMAIL>
- **Database Issues**: <EMAIL>

### Performance Monitoring Commands

```bash
# Monitor notification creation rate
SELECT
  DATE_TRUNC('hour', created_at) as hour,
  COUNT(*) as notifications_created
FROM notifications
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY hour
ORDER BY hour DESC;

# Check real-time connection health
SELECT
  COUNT(*) as active_connections,
  AVG(EXTRACT(EPOCH FROM (NOW() - backend_start))) as avg_connection_age
FROM pg_stat_activity
WHERE application_name LIKE '%realtime%';

# Monitor email delivery performance
SELECT
  status,
  COUNT(*) as count,
  AVG(EXTRACT(EPOCH FROM (delivered_at - sent_at))) as avg_delivery_time
FROM email_deliveries
WHERE sent_at > NOW() - INTERVAL '24 hours'
GROUP BY status;
```
