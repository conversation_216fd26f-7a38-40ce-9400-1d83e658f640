import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Trash2 } from 'lucide-react'
import type { InvoiceLineData } from '@/types/invoices'

interface InvoiceLineItemProps {
  line: InvoiceLineData
  index: number
  onUpdate: (index: number, field: keyof InvoiceLineData, value: string | number) => void
  onRemove: (index: number) => void
  isLast: boolean
}

export function InvoiceLineItem({
  line,
  index,
  onUpdate,
  onRemove,
  isLast
}: InvoiceLineItemProps) {
  return (
    <div className="grid grid-cols-12 gap-4 items-end">
      <div className="col-span-3">
        <Label>Item Name</Label>
        <Input
          value={line.item}
          onChange={(e) => onUpdate(index, 'item', e.target.value)}
          placeholder="Item name"
        />
      </div>

      <div className="col-span-4">
        <Label>Item Description</Label>
        <Input
          value={line.description}
          onChange={(e) => onUpdate(index, 'description', e.target.value)}
          placeholder="Item description"
        />
      </div>

      <div className="col-span-1">
        <Label>Qty</Label>
        <Input
          type="number"
          min="1"
          value={line.quantity}
          onChange={(e) => onUpdate(index, 'quantity', parseInt(e.target.value) || 0)}
        />
      </div>

      <div className="col-span-2">
        <Label>Unit Price</Label>
        <Input
          type="number"
          min="0"
          step="0.01"
          value={line.unit_price}
          onChange={(e) => onUpdate(index, 'unit_price', parseFloat(e.target.value) || 0)}
        />
      </div>

      <div className="col-span-1">
        <Label>Tax %</Label>
        <Input
          type="number"
          min="0"
          max="100"
          value={line.tax_rate_pct}
          onChange={(e) => onUpdate(index, 'tax_rate_pct', parseFloat(e.target.value) || 0)}
        />
      </div>

      <div className="col-span-1">
        {!isLast && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => onRemove(index)}
            className="h-10"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
} 
