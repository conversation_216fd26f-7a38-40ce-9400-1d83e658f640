/**
 * Notification Analytics Dashboard
 * Comprehensive analytics for notification delivery, engagement, and performance
 */

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading'
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  Mail, 
  Smartphone, 
  Bell, 
  Users, 
  Clock, 
  Target,
  Download,
  RefreshCw
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/integrations/supabase/client'

interface AnalyticsData {
  overview: {
    total_sent: number
    delivered: number
    opened: number
    clicked: number
    failed: number
    delivery_rate: number
    open_rate: number
    click_rate: number
    failure_rate: number
  }
  email_analytics: {
    total: number
    sent: number
    delivered: number
    opened: number
    clicked: number
    bounced: number
    failed: number
    delivery_rate: number
    open_rate: number
    click_rate: number
    bounce_rate: number
  }
  push_analytics: {
    total_subscriptions: number
    total_sent: number
    delivered: number
    failed: number
    pending: number
    delivery_rate: number
    failure_rate: number
    clicks: number
    click_rate: number
  }
  daily_volume: Array<{
    date: string
    email: number
    push: number
    in_app: number
    total: number
  }>
  by_type: Array<{
    type: string
    count: number
    delivery_rate: number
    engagement_rate: number
  }>
  by_priority: Array<{
    priority: string
    count: number
    avg_response_time: number
  }>
}

interface NotificationAnalyticsProps {
  className?: string
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1']

export function NotificationAnalytics({ className = '' }: NotificationAnalyticsProps) {
  const { profile } = useAuth()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [dateRange, setDateRange] = useState('30')
  const [refreshing, setRefreshing] = useState(false)

  // Fetch analytics data
  const fetchAnalytics = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      setIsLoading(true)
      
      // Fetch overview analytics
      const { data: overviewData, error: overviewError } = await supabase
        .rpc('get_notification_analytics', {
          org_id_param: profile.org_id,
          days_back: parseInt(dateRange)
        })

      if (overviewError) throw overviewError

      // Fetch email analytics
      const { data: emailData, error: emailError } = await supabase
        .rpc('get_email_analytics', {
          org_id_param: profile.org_id,
          start_date: new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end_date: new Date().toISOString().split('T')[0]
        })

      if (emailError) throw emailError

      // Fetch push analytics
      const { data: pushData, error: pushError } = await supabase
        .rpc('get_push_analytics', {
          org_id_param: profile.org_id,
          start_date: new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end_date: new Date().toISOString().split('T')[0]
        })

      if (pushError) throw pushError

      // Fetch daily volume data
      const { data: dailyData, error: dailyError } = await supabase
        .from('notifications')
        .select('created_at, type, data')
        .eq('org_id', profile.org_id)
        .gte('created_at', new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true })

      if (dailyError) throw dailyError

      // Process daily volume data
      const dailyVolume = processDailyVolume(dailyData || [])

      // Fetch notification type analytics
      const { data: typeData, error: typeError } = await supabase
        .from('notifications')
        .select('type, is_read, created_at')
        .eq('org_id', profile.org_id)
        .gte('created_at', new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000).toISOString())

      if (typeError) throw typeError

      const byType = processTypeAnalytics(typeData || [])

      // Fetch priority analytics
      const { data: priorityData, error: priorityError } = await supabase
        .from('notifications')
        .select('priority, created_at, read_at')
        .eq('org_id', profile.org_id)
        .gte('created_at', new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000).toISOString())

      if (priorityError) throw priorityError

      const byPriority = processPriorityAnalytics(priorityData || [])

      setAnalytics({
        overview: overviewData || getEmptyOverview(),
        email_analytics: emailData || getEmptyEmailAnalytics(),
        push_analytics: pushData || getEmptyPushAnalytics(),
        daily_volume: dailyVolume,
        by_type: byType,
        by_priority: byPriority
      })
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setIsLoading(false)
      setRefreshing(false)
    }
  }, [profile?.org_id, dateRange])

  useEffect(() => {
    fetchAnalytics()
  }, [fetchAnalytics])

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchAnalytics()
  }

  const handleExport = async () => {
    if (!analytics) return

    const csvData = generateCSVReport(analytics)
    const blob = new Blob([csvData], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `notification-analytics-${dateRange}days-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Calculate trends
  const trends = useMemo(() => {
    if (!analytics) return null

    return {
      delivery_trend: analytics.overview.delivery_rate > 90 ? 'up' : 'down',
      engagement_trend: analytics.overview.open_rate > 20 ? 'up' : 'down',
      volume_trend: analytics.daily_volume.length > 0 ? 'up' : 'stable'
    }
  }, [analytics])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="md" text="Loading analytics..." />
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center p-8">
        <p className="text-muted-foreground">No analytics data available</p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Notification Analytics</h2>
          <p className="text-muted-foreground">
            Track delivery performance and user engagement
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sent</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.overview.total_sent.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Last {dateRange} days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivery Rate</CardTitle>
            {trends?.delivery_trend === 'up' ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.overview.delivery_rate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {analytics.overview.delivered.toLocaleString()} delivered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Rate</CardTitle>
            {trends?.engagement_trend === 'up' ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.overview.open_rate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {analytics.overview.opened.toLocaleString()} opened
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Click Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.overview.click_rate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {analytics.overview.clicked.toLocaleString()} clicked
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="push">Push</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Daily Volume Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Daily Notification Volume</CardTitle>
              <CardDescription>
                Notification volume over the last {dateRange} days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analytics.daily_volume}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="email" stroke="#8884d8" name="Email" />
                  <Line type="monotone" dataKey="push" stroke="#82ca9d" name="Push" />
                  <Line type="monotone" dataKey="in_app" stroke="#ffc658" name="In-App" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Notification Types */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>By Notification Type</CardTitle>
                <CardDescription>
                  Performance by notification type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={analytics.by_type}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>By Priority</CardTitle>
                <CardDescription>
                  Response time by priority level
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={analytics.by_priority}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ priority, count }) => `${priority}: ${count}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {analytics.by_priority.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Email Delivery Rate</CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.email_analytics.delivery_rate.toFixed(1)}%</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.email_analytics.delivered} of {analytics.email_analytics.total} delivered
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Email Open Rate</CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.email_analytics.open_rate.toFixed(1)}%</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.email_analytics.opened} opened
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Email Click Rate</CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.email_analytics.click_rate.toFixed(1)}%</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.email_analytics.clicked} clicked
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="push" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.push_analytics.total_subscriptions}</div>
                <p className="text-xs text-muted-foreground">
                  Active push subscriptions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Push Delivery Rate</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.push_analytics.delivery_rate.toFixed(1)}%</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.push_analytics.delivered} of {analytics.push_analytics.total_sent} delivered
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Push Click Rate</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.push_analytics.click_rate.toFixed(1)}%</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.push_analytics.clicks} clicks
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
                <CardDescription>
                  Key performance indicators
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Overall Delivery Rate</span>
                  <Badge variant={analytics.overview.delivery_rate > 90 ? 'default' : 'destructive'}>
                    {analytics.overview.delivery_rate.toFixed(1)}%
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Engagement Rate</span>
                  <Badge variant={analytics.overview.open_rate > 20 ? 'default' : 'secondary'}>
                    {analytics.overview.open_rate.toFixed(1)}%
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Failure Rate</span>
                  <Badge variant={analytics.overview.failure_rate < 5 ? 'default' : 'destructive'}>
                    {analytics.overview.failure_rate.toFixed(1)}%
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
                <CardDescription>
                  Suggestions to improve performance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {analytics.overview.delivery_rate < 90 && (
                  <p className="text-sm text-yellow-600">
                    • Consider reviewing email templates and delivery settings
                  </p>
                )}
                {analytics.overview.open_rate < 20 && (
                  <p className="text-sm text-yellow-600">
                    • Improve subject lines and send timing
                  </p>
                )}
                {analytics.overview.click_rate < 5 && (
                  <p className="text-sm text-yellow-600">
                    • Enhance call-to-action buttons and content relevance
                  </p>
                )}
                {analytics.overview.delivery_rate >= 90 && analytics.overview.open_rate >= 20 && (
                  <p className="text-sm text-green-600">
                    ✓ Your notification performance is excellent!
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Helper functions
interface NotificationDataItem {
  created_at: string
  type: string
  data?: Record<string, unknown>
}

interface DailyVolumeItem {
  date: string
  email: number
  push: number
  in_app: number
  total: number
}

function processDailyVolume(data: NotificationDataItem[]): DailyVolumeItem[] {
  const dailyMap = new Map<string, DailyVolumeItem>()

  data.forEach(item => {
    const date = new Date(item.created_at).toISOString().split('T')[0]
    if (!dailyMap.has(date)) {
      dailyMap.set(date, { date, email: 0, push: 0, in_app: 0, total: 0 })
    }

    const day = dailyMap.get(date)!
    day.total++

    // Categorize by delivery method (simplified)
    if (item.data?.email_sent) day.email++
    else if (item.data?.push_sent) day.push++
    else day.in_app++
  })

  return Array.from(dailyMap.values()).sort((a, b) => a.date.localeCompare(b.date))
}

interface TypeAnalyticsItem {
  type: string
  is_read: boolean
  created_at: string
}

interface TypeAnalyticsResult {
  type: string
  count: number
  delivery_rate: number
  engagement_rate: number
}

function processTypeAnalytics(data: TypeAnalyticsItem[]): TypeAnalyticsResult[] {
  const typeMap = new Map<string, { type: string; count: number; opened: number }>()

  data.forEach(item => {
    if (!typeMap.has(item.type)) {
      typeMap.set(item.type, { type: item.type, count: 0, opened: 0 })
    }

    const type = typeMap.get(item.type)!
    type.count++
    if (item.is_read) type.opened++
  })

  return Array.from(typeMap.values()).map(type => ({
    ...type,
    delivery_rate: 100, // Simplified
    engagement_rate: type.count > 0 ? (type.opened / type.count) * 100 : 0
  }))
}

interface PriorityAnalyticsItem {
  priority?: string
  created_at: string
  read_at?: string | null
}

interface PriorityAnalyticsResult {
  priority: string
  count: number
  avg_response_time: number
}

function processPriorityAnalytics(data: PriorityAnalyticsItem[]): PriorityAnalyticsResult[] {
  const priorityMap = new Map<string, { priority: string; count: number; total_response_time: number }>()

  data.forEach(item => {
    const priority = item.priority || 'normal'
    if (!priorityMap.has(priority)) {
      priorityMap.set(priority, { priority, count: 0, total_response_time: 0 })
    }

    const p = priorityMap.get(priority)!
    p.count++

    if (item.read_at) {
      const responseTime = new Date(item.read_at).getTime() - new Date(item.created_at).getTime()
      p.total_response_time += responseTime
    }
  })

  return Array.from(priorityMap.values()).map(p => ({
    ...p,
    avg_response_time: p.count > 0 ? p.total_response_time / p.count / 1000 / 60 : 0 // minutes
  }))
}

function getEmptyOverview() {
  return {
    total_sent: 0,
    delivered: 0,
    opened: 0,
    clicked: 0,
    failed: 0,
    delivery_rate: 0,
    open_rate: 0,
    click_rate: 0,
    failure_rate: 0
  }
}

function getEmptyEmailAnalytics() {
  return {
    total: 0,
    sent: 0,
    delivered: 0,
    opened: 0,
    clicked: 0,
    bounced: 0,
    failed: 0,
    delivery_rate: 0,
    open_rate: 0,
    click_rate: 0,
    bounce_rate: 0
  }
}

function getEmptyPushAnalytics() {
  return {
    total_subscriptions: 0,
    total_sent: 0,
    delivered: 0,
    failed: 0,
    pending: 0,
    delivery_rate: 0,
    failure_rate: 0,
    clicks: 0,
    click_rate: 0
  }
}

function generateCSVReport(analytics: AnalyticsData): string {
  const headers = [
    'Metric',
    'Value',
    'Percentage'
  ]
  
  const rows = [
    ['Total Sent', analytics.overview.total_sent.toString(), ''],
    ['Delivered', analytics.overview.delivered.toString(), `${analytics.overview.delivery_rate.toFixed(1)}%`],
    ['Opened', analytics.overview.opened.toString(), `${analytics.overview.open_rate.toFixed(1)}%`],
    ['Clicked', analytics.overview.clicked.toString(), `${analytics.overview.click_rate.toFixed(1)}%`],
    ['Failed', analytics.overview.failed.toString(), `${analytics.overview.failure_rate.toFixed(1)}%`]
  ]
  
  return [headers, ...rows].map(row => row.join(',')).join('\n')
}
