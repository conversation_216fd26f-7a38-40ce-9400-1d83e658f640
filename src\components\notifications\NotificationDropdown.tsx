import { useState, useRef, useEffect, useCallback } from 'react'
import { <PERSON>, Set<PERSON>s, CheckCheck, Archive, Filter, Wifi, WifiOff, Refresh<PERSON>w } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { NotificationList } from './NotificationList'
import { NotificationFilters } from './NotificationFilters'
import { EmptyNotifications } from './EmptyNotifications'
import {
  useNotifications,
  useNotificationCount,
  useMarkAllNotificationsAsRead
} from '@/hooks/queries/useNotifications'
import { useNotificationRealtime } from '@/hooks/useNotificationRealtime'
import type { NotificationFilters as FilterType } from '@/types/notifications'

interface NotificationDropdownProps {
  className?: string
}

export function NotificationDropdown({ className }: NotificationDropdownProps) {
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('all')
  const [filters, setFilters] = useState<FilterType>({})
  const [showFilters, setShowFilters] = useState(false)

  // Refs for accessibility
  const triggerRef = useRef<HTMLButtonElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  // Real-time connection status
  const { isConnected, connectionError, reconnect, isUsingPolling } = useNotificationRealtime()

  // Queries
  const { data: notificationCount = 0 } = useNotificationCount()
  const { data: allNotifications = [], isLoading, refetch } = useNotifications()
  const { data: unreadNotifications = [] } = useNotifications({ is_read: false })
  const markAllAsRead = useMarkAllNotificationsAsRead()

  // Keyboard navigation handler
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      setOpen(false)
      triggerRef.current?.focus()
    }
  }, [])

  // Auto-focus management
  useEffect(() => {
    if (open && contentRef.current) {
      // Focus first focusable element in dropdown
      const firstFocusable = contentRef.current.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement
      firstFocusable?.focus()
    }
  }, [open])

  // Filter notifications based on active tab
  const getFilteredNotifications = () => {
    switch (activeTab) {
      case 'unread':
        return unreadNotifications
      case 'financial':
        return allNotifications.filter(n => n.category === 'financial')
      case 'system':
        return allNotifications.filter(n => n.category === 'system')
      default:
        return allNotifications
    }
  }

  // Handle manual refresh
  const handleRefresh = useCallback(async () => {
    if (isConnected) {
      await refetch()
    } else {
      reconnect()
    }
  }, [isConnected, refetch, reconnect])

  const filteredNotifications = getFilteredNotifications()

  const handleMarkAllAsRead = () => {
    markAllAsRead.mutate()
  }

  const handleFilterChange = (newFilters: FilterType) => {
    setFilters(newFilters)
  }

  return (
    <TooltipProvider>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                ref={triggerRef}
                variant="ghost"
                size="icon"
                className={`relative ${className}`}
                aria-label={`Notifications${notificationCount > 0 ? ` (${notificationCount} unread)` : ''}`}
                aria-expanded={open}
                aria-haspopup="menu"
              >
                <Bell className="h-5 w-5" />
                {notificationCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                    aria-label={`${notificationCount} unread notifications`}
                  >
                    {notificationCount > 99 ? '99+' : notificationCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>
              {notificationCount > 0
                ? `${notificationCount} unread notification${notificationCount === 1 ? '' : 's'}`
                : 'No unread notifications'
              }
            </p>
          </TooltipContent>
        </Tooltip>

        <DropdownMenuContent
          ref={contentRef}
          align="end"
          className="w-96 p-0 max-h-[80vh] flex flex-col"
          sideOffset={8}
          onKeyDown={handleKeyDown}
          role="menu"
          aria-label="Notifications menu"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b bg-background">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-lg">Notifications</h3>
              {/* Connection status indicator */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    {isConnected ? (
                      <Wifi className="h-3 w-3 text-green-500" aria-label="Connected" />
                    ) : isUsingPolling ? (
                      <RefreshCw className="h-3 w-3 text-yellow-500 animate-spin" aria-label="Polling" />
                    ) : (
                      <WifiOff className="h-3 w-3 text-red-500" aria-label="Disconnected" />
                    )}
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {isConnected
                      ? 'Real-time updates active'
                      : isUsingPolling
                        ? 'Using polling for updates'
                        : connectionError || 'Connection lost'
                    }
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className="flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRefresh}
                    className="h-8 w-8 p-0"
                    aria-label="Refresh notifications"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Refresh notifications</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFilters(!showFilters)}
                    className="h-8 w-8 p-0"
                    aria-label={showFilters ? "Hide filters" : "Show filters"}
                    aria-expanded={showFilters}
                  >
                    <Filter className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{showFilters ? "Hide filters" : "Show filters"}</p>
                </TooltipContent>
              </Tooltip>

              {unreadNotifications.length > 0 && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleMarkAllAsRead}
                      disabled={markAllAsRead.isPending}
                      className="h-8 w-8 p-0"
                      aria-label="Mark all notifications as read"
                    >
                      <CheckCheck className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Mark all as read</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="p-4 border-b bg-muted/50">
            <NotificationFilters
              filters={filters}
              onFiltersChange={handleFilterChange}
            />
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-4 rounded-none border-b bg-background" role="tablist">
            <TabsTrigger
              value="all"
              className="text-xs"
              role="tab"
              aria-selected={activeTab === 'all'}
              aria-controls="all-panel"
            >
              All
              {allNotifications.length > 0 && (
                <Badge variant="secondary" className="ml-1 h-4 text-xs">
                  {allNotifications.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="unread"
              className="text-xs"
              role="tab"
              aria-selected={activeTab === 'unread'}
              aria-controls="unread-panel"
            >
              Unread
              {unreadNotifications.length > 0 && (
                <Badge variant="destructive" className="ml-1 h-4 text-xs">
                  {unreadNotifications.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="financial"
              className="text-xs"
              role="tab"
              aria-selected={activeTab === 'financial'}
              aria-controls="financial-panel"
            >
              Financial
            </TabsTrigger>
            <TabsTrigger
              value="system"
              className="text-xs"
              role="tab"
              aria-selected={activeTab === 'system'}
              aria-controls="system-panel"
            >
              System
            </TabsTrigger>
          </TabsList>

          {/* Content with scroll bars as per user preference */}
          <div className="flex-1 min-h-0">
            <ScrollArea className="h-96 w-full" style={{ scrollbarWidth: 'thin' }}>
              <TabsContent
                value="all"
                className="m-0 p-0"
                role="tabpanel"
                id="all-panel"
                aria-labelledby="all-tab"
              >
                {filteredNotifications.length > 0 ? (
                  <NotificationList
                    notifications={filteredNotifications}
                    onNotificationClick={() => setOpen(false)}
                  />
                ) : (
                  <EmptyNotifications
                    type="all"
                    isLoading={isLoading}
                  />
                )}
              </TabsContent>

              <TabsContent
                value="unread"
                className="m-0 p-0"
                role="tabpanel"
                id="unread-panel"
                aria-labelledby="unread-tab"
              >
                {unreadNotifications.length > 0 ? (
                  <NotificationList
                    notifications={unreadNotifications}
                    onNotificationClick={() => setOpen(false)}
                  />
                ) : (
                  <EmptyNotifications 
                    type="unread"
                    isLoading={isLoading}
                  />
                )}
              </TabsContent>

              <TabsContent
                value="financial"
                className="m-0 p-0"
                role="tabpanel"
                id="financial-panel"
                aria-labelledby="financial-tab"
              >
                {filteredNotifications.length > 0 ? (
                  <NotificationList
                    notifications={filteredNotifications}
                    onNotificationClick={() => setOpen(false)}
                  />
                ) : (
                  <EmptyNotifications
                    type="financial"
                    isLoading={isLoading}
                  />
                )}
              </TabsContent>

              <TabsContent
                value="system"
                className="m-0 p-0"
                role="tabpanel"
                id="system-panel"
                aria-labelledby="system-tab"
              >
                {filteredNotifications.length > 0 ? (
                  <NotificationList
                    notifications={filteredNotifications}
                    onNotificationClick={() => setOpen(false)}
                  />
                ) : (
                  <EmptyNotifications
                    type="system"
                    isLoading={isLoading}
                  />
                )}
              </TabsContent>
            </ScrollArea>
          </div>
        </Tabs>

        {/* Footer */}
        {allNotifications.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <div className="p-2 bg-background">
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-muted-foreground hover:text-foreground"
                onClick={() => {
                  setOpen(false)
                  // TODO: Navigate to full notifications page
                }}
                aria-label="View all notifications in notification center"
              >
                <Settings className="mr-2 h-4 w-4" />
                View All Notifications
              </Button>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
    </TooltipProvider>
  )
}
