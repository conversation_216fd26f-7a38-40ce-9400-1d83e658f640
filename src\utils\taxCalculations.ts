import type { BillLineData } from '@/types/bills'
import type { InvoiceLineData } from '@/types/invoices'
import type { WithholdingTaxRate } from '@/types/database'

export interface TaxCalculationResult {
  subtotal: number
  taxAmount: number
  withholdingAmount: number
  totalAmount: number
}

/**
 * Calculate totals for bill lines including tax and withholding tax
 */
export function calculateBillLineTotals(
  billLines: BillLineData[],
  withholdingTaxRateId: string | null,
  withholdingRates: WithholdingTaxRate[]
): TaxCalculationResult {
  let subtotal = 0
  let taxAmount = 0

  // Calculate subtotal and tax amount from line items
  billLines.forEach(line => {
    const lineTotal = line.quantity * line.unit_price
    subtotal += lineTotal
    taxAmount += lineTotal * (line.tax_rate_pct / 100)
  })

  const totalBeforeWithholding = subtotal + taxAmount
  
  // Calculate withholding tax if applicable
  const withholdingRate = withholdingRates.find(r => r.id === withholdingTaxRateId)
  const withholdingAmount = withholdingRate 
    ? totalBeforeWithholding * (withholdingRate.rate_pct / 100) 
    : 0
  
  const totalAmount = totalBeforeWithholding - withholdingAmount

  return {
    subtotal,
    taxAmount,
    withholdingAmount,
    totalAmount
  }
}

/**
 * Calculate totals for invoice lines including tax
 */
export function calculateInvoiceLineTotals(
  invoiceLines: InvoiceLineData[]
): Omit<TaxCalculationResult, 'withholdingAmount'> {
  let subtotal = 0
  let taxAmount = 0

  invoiceLines.forEach(line => {
    const lineTotal = line.quantity * line.unit_price
    subtotal += lineTotal
    taxAmount += lineTotal * (line.tax_rate_pct / 100)
  })

  return {
    subtotal,
    taxAmount,
    totalAmount: subtotal + taxAmount
  }
}

/**
 * Calculate tax amount for a single line item
 */
export function calculateLineTax(
  quantity: number,
  unitPrice: number,
  taxRatePercent: number
): number {
  const lineTotal = quantity * unitPrice
  return lineTotal * (taxRatePercent / 100)
}

/**
 * Calculate withholding tax amount
 */
export function calculateWithholdingTax(
  totalAmount: number,
  withholdingRatePercent: number
): number {
  return totalAmount * (withholdingRatePercent / 100)
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number, currency: string = 'UGX'): string {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount)
}
