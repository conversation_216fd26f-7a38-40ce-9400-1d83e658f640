// Backup Performance Optimization and Large Dataset Handling
// Provides streaming, chunking, and resource management for large backup operations

import { supabase } from './supabase'
import { auditLogger } from './auditLogger'

export interface PerformanceConfig {
  chunkSize: number
  maxConcurrentOperations: number
  memoryThreshold: number // MB
  streamingEnabled: boolean
  compressionEnabled: boolean
  batchSize: number
}

export interface PerformanceMetrics {
  startTime: number
  endTime?: number
  duration?: number
  recordsProcessed: number
  bytesProcessed: number
  chunksProcessed: number
  averageChunkTime: number
  memoryUsage: {
    peak: number
    average: number
    current: number
  }
  throughput: {
    recordsPerSecond: number
    bytesPerSecond: number
  }
}

export interface StreamingBackupResult {
  success: boolean
  totalRecords: number
  totalSize: number
  chunks: number
  metrics: PerformanceMetrics
  error?: string
}

/**
 * High-Performance Backup Manager for Large Datasets
 */
export class BackupPerformanceManager {
  private static readonly DEFAULT_CONFIG: PerformanceConfig = {
    chunkSize: 1000,
    maxConcurrentOperations: 3,
    memoryThreshold: 512, // 512MB
    streamingEnabled: true,
    compressionEnabled: true,
    batchSize: 100
  }

  private static performanceMetrics: PerformanceMetrics = {
    startTime: 0,
    recordsProcessed: 0,
    bytesProcessed: 0,
    chunksProcessed: 0,
    averageChunkTime: 0,
    memoryUsage: {
      peak: 0,
      average: 0,
      current: 0
    },
    throughput: {
      recordsPerSecond: 0,
      bytesPerSecond: 0
    }
  }

  /**
   * Perform streaming backup for large datasets
   */
  static async performStreamingBackup(
    orgId: string,
    tables: string[],
    config: Partial<PerformanceConfig> = {}
  ): Promise<StreamingBackupResult> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config }
    
    this.initializeMetrics()
    
    try {
      const backupData: Record<string, unknown[]> = {}
      let totalRecords = 0
      let totalSize = 0
      let totalChunks = 0

      // Process tables with resource management
      const semaphore = new Semaphore(finalConfig.maxConcurrentOperations)
      
      const tablePromises = tables.map(async (table) => {
        await semaphore.acquire()
        
        try {
          const tableResult = await this.streamTableData(
            table,
            orgId,
            finalConfig
          )
          
          backupData[table] = tableResult.data
          totalRecords += tableResult.recordCount
          totalSize += tableResult.sizeBytes
          totalChunks += tableResult.chunks
          
          return tableResult
        } finally {
          semaphore.release()
        }
      })

      await Promise.all(tablePromises)

      this.finalizeMetrics()

      return {
        success: true,
        totalRecords,
        totalSize,
        chunks: totalChunks,
        metrics: this.performanceMetrics
      }

    } catch (error) {
      return {
        success: false,
        totalRecords: this.performanceMetrics.recordsProcessed,
        totalSize: this.performanceMetrics.bytesProcessed,
        chunks: this.performanceMetrics.chunksProcessed,
        metrics: this.performanceMetrics,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Stream data from a single table with chunking
   */
  private static async streamTableData(
    table: string,
    orgId: string,
    config: PerformanceConfig
  ): Promise<{
    data: unknown[]
    recordCount: number
    sizeBytes: number
    chunks: number
  }> {
    const allData: unknown[] = []
    let offset = 0
    let hasMore = true
    let chunks = 0
    let totalSize = 0

    console.log(`Starting streaming backup for table: ${table}`)

    while (hasMore) {
      const chunkStartTime = Date.now()
      
      // Check memory usage before processing chunk
      await this.checkMemoryUsage(config.memoryThreshold)
      
      // Build query with pagination
      let query = supabase
        .from(table)
        .select('*')
        .range(offset, offset + config.chunkSize - 1)
        .order('created_at', { ascending: true })

      // Add org_id filter for organization-specific tables
      if (this.isOrgSpecificTable(table)) {
        query = query.eq('org_id', orgId)
      } else if (table === 'organizations') {
        query = query.eq('id', orgId)
      }

      const { data: chunk, error } = await query

      if (error) {
        throw new Error(`Failed to fetch chunk for table ${table}: ${error.message}`)
      }

      if (!chunk || chunk.length === 0) {
        hasMore = false
        break
      }

      // Process chunk
      const chunkData = config.compressionEnabled 
        ? await this.compressChunk(chunk)
        : chunk

      allData.push(...chunkData)
      
      // Update metrics
      const chunkSize = this.calculateDataSize(chunk)
      totalSize += chunkSize
      chunks++
      
      this.updateMetrics(chunk.length, chunkSize, Date.now() - chunkStartTime)

      // Check if we got fewer records than requested (end of data)
      if (chunk.length < config.chunkSize) {
        hasMore = false
      } else {
        offset += config.chunkSize
      }

      // Yield control to prevent blocking
      await this.yieldControl()

      console.log(`Processed chunk ${chunks} for table ${table}: ${chunk.length} records`)
    }

    console.log(`Completed streaming backup for table ${table}: ${allData.length} total records`)

    return {
      data: allData,
      recordCount: allData.length,
      sizeBytes: totalSize,
      chunks
    }
  }

  /**
   * Perform streaming restoration with memory management
   */
  static async performStreamingRestore(
    backupData: Record<string, unknown[]>,
    orgId: string,
    config: Partial<PerformanceConfig> = {}
  ): Promise<StreamingBackupResult> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config }
    
    this.initializeMetrics()
    
    try {
      let totalRecords = 0
      let totalSize = 0
      let totalChunks = 0

      // Process tables sequentially to avoid overwhelming the database
      for (const [table, data] of Object.entries(backupData)) {
        if (!Array.isArray(data) || data.length === 0) continue

        console.log(`Starting streaming restore for table: ${table}`)

        const tableResult = await this.streamTableRestore(
          table,
          data,
          orgId,
          finalConfig
        )

        totalRecords += tableResult.recordCount
        totalSize += tableResult.sizeBytes
        totalChunks += tableResult.chunks
      }

      this.finalizeMetrics()

      return {
        success: true,
        totalRecords,
        totalSize,
        chunks: totalChunks,
        metrics: this.performanceMetrics
      }

    } catch (error) {
      return {
        success: false,
        totalRecords: this.performanceMetrics.recordsProcessed,
        totalSize: this.performanceMetrics.bytesProcessed,
        chunks: this.performanceMetrics.chunksProcessed,
        metrics: this.performanceMetrics,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Stream data restoration for a single table
   */
  private static async streamTableRestore(
    table: string,
    data: unknown[],
    orgId: string,
    config: PerformanceConfig
  ): Promise<{
    recordCount: number
    sizeBytes: number
    chunks: number
  }> {
    let totalSize = 0
    let chunks = 0

    // Process data in chunks to avoid memory issues
    for (let i = 0; i < data.length; i += config.chunkSize) {
      const chunkStartTime = Date.now()
      
      // Check memory usage
      await this.checkMemoryUsage(config.memoryThreshold)
      
      const chunk = data.slice(i, i + config.chunkSize)
      
      // Insert chunk with retry logic
      await this.insertChunkWithRetry(table, chunk, 3)
      
      // Update metrics
      const chunkSize = this.calculateDataSize(chunk)
      totalSize += chunkSize
      chunks++
      
      this.updateMetrics(chunk.length, chunkSize, Date.now() - chunkStartTime)
      
      // Yield control
      await this.yieldControl()
      
      console.log(`Restored chunk ${chunks} for table ${table}: ${chunk.length} records`)
    }

    return {
      recordCount: data.length,
      sizeBytes: totalSize,
      chunks
    }
  }

  /**
   * Insert chunk with retry logic for resilience
   */
  private static async insertChunkWithRetry(
    table: string,
    chunk: unknown[],
    maxRetries: number
  ): Promise<void> {
    let retries = 0
    
    while (retries < maxRetries) {
      try {
        const { error } = await supabase
          .from(table)
          .insert(chunk)
        
        if (error) {
          throw error
        }
        
        return // Success
        
      } catch (error) {
        retries++
        
        if (retries >= maxRetries) {
          throw new Error(`Failed to insert chunk after ${maxRetries} retries: ${error}`)
        }
        
        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, retries), 10000)
        await new Promise(resolve => setTimeout(resolve, delay))
        
        console.log(`Retrying chunk insertion (attempt ${retries + 1}/${maxRetries})`)
      }
    }
  }

  /**
   * Check memory usage and trigger garbage collection if needed
   */
  private static async checkMemoryUsage(threshold: number): Promise<void> {
    if (typeof performance !== 'undefined' && performance.memory) {
      const memoryUsage = performance.memory.usedJSHeapSize / (1024 * 1024) // MB
      
      this.performanceMetrics.memoryUsage.current = memoryUsage
      this.performanceMetrics.memoryUsage.peak = Math.max(
        this.performanceMetrics.memoryUsage.peak,
        memoryUsage
      )
      
      if (memoryUsage > threshold) {
        console.log(`Memory usage (${memoryUsage.toFixed(2)}MB) exceeds threshold (${threshold}MB)`)
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc()
          console.log('Triggered garbage collection')
        }
        
        // Add small delay to allow memory cleanup
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
  }

  /**
   * Compress chunk data to reduce memory usage
   */
  private static async compressChunk(chunk: unknown[]): Promise<unknown[]> {
    // In a real implementation, this would use compression algorithms
    // For now, we'll just return the data as-is
    // Could implement gzip compression here
    return chunk
  }

  /**
   * Calculate approximate data size in bytes
   */
  private static calculateDataSize(data: unknown[]): number {
    try {
      return JSON.stringify(data).length
    } catch {
      // Fallback estimation
      return data.length * 100 // Rough estimate
    }
  }

  /**
   * Check if table is organization-specific
   */
  private static isOrgSpecificTable(table: string): boolean {
    return [
      'customers', 'vendors', 'invoices', 'bills', 'payments', 
      'accounts', 'journal_entries', 'transaction_lines', 'profiles'
    ].includes(table)
  }

  /**
   * Initialize performance metrics
   */
  private static initializeMetrics(): void {
    this.performanceMetrics = {
      startTime: Date.now(),
      recordsProcessed: 0,
      bytesProcessed: 0,
      chunksProcessed: 0,
      averageChunkTime: 0,
      memoryUsage: {
        peak: 0,
        average: 0,
        current: 0
      },
      throughput: {
        recordsPerSecond: 0,
        bytesPerSecond: 0
      }
    }
  }

  /**
   * Update performance metrics
   */
  private static updateMetrics(records: number, bytes: number, chunkTime: number): void {
    this.performanceMetrics.recordsProcessed += records
    this.performanceMetrics.bytesProcessed += bytes
    this.performanceMetrics.chunksProcessed++
    
    // Update average chunk time
    const totalChunks = this.performanceMetrics.chunksProcessed
    this.performanceMetrics.averageChunkTime = 
      (this.performanceMetrics.averageChunkTime * (totalChunks - 1) + chunkTime) / totalChunks
  }

  /**
   * Finalize performance metrics
   */
  private static finalizeMetrics(): void {
    this.performanceMetrics.endTime = Date.now()
    this.performanceMetrics.duration = this.performanceMetrics.endTime - this.performanceMetrics.startTime
    
    if (this.performanceMetrics.duration > 0) {
      this.performanceMetrics.throughput.recordsPerSecond = 
        (this.performanceMetrics.recordsProcessed / this.performanceMetrics.duration) * 1000
      
      this.performanceMetrics.throughput.bytesPerSecond = 
        (this.performanceMetrics.bytesProcessed / this.performanceMetrics.duration) * 1000
    }
    
    // Calculate average memory usage
    this.performanceMetrics.memoryUsage.average = 
      this.performanceMetrics.memoryUsage.peak / 2 // Rough estimate
  }

  /**
   * Yield control to prevent blocking the event loop
   */
  private static async yieldControl(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 0))
  }

  /**
   * Get performance recommendations based on metrics
   */
  static getPerformanceRecommendations(metrics: PerformanceMetrics): string[] {
    const recommendations: string[] = []
    
    if (metrics.averageChunkTime > 5000) {
      recommendations.push('Consider reducing chunk size for better responsiveness')
    }
    
    if (metrics.memoryUsage.peak > 1024) {
      recommendations.push('High memory usage detected - consider enabling compression')
    }
    
    if (metrics.throughput.recordsPerSecond < 100) {
      recommendations.push('Low throughput detected - consider increasing concurrent operations')
    }
    
    if (metrics.chunksProcessed > 1000) {
      recommendations.push('Large number of chunks - consider increasing chunk size')
    }
    
    return recommendations
  }
}

/**
 * Semaphore for controlling concurrent operations
 */
class Semaphore {
  private permits: number
  private waiting: (() => void)[] = []

  constructor(permits: number) {
    this.permits = permits
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--
      return
    }

    return new Promise(resolve => {
      this.waiting.push(resolve)
    })
  }

  release(): void {
    this.permits++
    
    if (this.waiting.length > 0) {
      const resolve = this.waiting.shift()!
      this.permits--
      resolve()
    }
  }
}
