/**
 * Invoice PDF Export Utilities
 * Functions for generating and downloading invoice PDFs
 */

import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import { formatCurrency } from '@/lib/utils'
import type { InvoiceWithCustomer } from '@/types/invoices'
import type { Customer, Organization } from '@/types/database'

export interface InvoiceLineItem {
  id: string
  account_id: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number
  line_total: number
  tax_amount: number
  accounts?: {
    code: string
    name: string
  }
}

export interface InvoicePdfData extends InvoiceWithCustomer {
  invoice_lines?: InvoiceLineItem[]
  organization?: Organization
}

/**
 * Generate PDF from HTML element
 */
export async function generatePdfFromElement(
  element: HTMLElement,
  filename: string
): Promise<void> {
  try {
    // Create canvas from HTML element
    const canvas = await html2canvas(element, {
      scale: 2, // Higher resolution
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false
    })

    // Calculate dimensions
    const imgWidth = 210 // A4 width in mm
    const pageHeight = 295 // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    let heightLeft = imgHeight

    // Create PDF
    const pdf = new jsPDF('p', 'mm', 'a4')
    let position = 0

    // Add first page
    pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight)
    heightLeft -= pageHeight

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight
      pdf.addPage()
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight
    }

    // Download the PDF
    pdf.save(filename)
  } catch (error) {
    console.error('Error generating PDF:', error)
    throw new Error('Failed to generate PDF')
  }
}

/**
 * Create a printable HTML template for invoice
 */
export function createInvoiceHtmlTemplate(
  invoice: InvoicePdfData,
  organization?: Organization
): string {
  const subtotal = invoice.invoice_lines?.reduce((sum, line) => sum + line.line_total, 0) || 0
  const totalTax = invoice.invoice_lines?.reduce((sum, line) => sum + line.tax_amount, 0) || 0

  return `
    <div style="
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 40px;
      background: white;
      color: #333;
      line-height: 1.6;
    ">
      <!-- Header -->
      <div style="
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 40px;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 20px;
      ">
        <div>
          <h1 style="
            font-size: 32px;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 8px 0;
          ">${organization?.name || 'Your Organization'}</h1>
          <div style="color: #6b7280; font-size: 14px;">
            ${organization?.address || ''}<br>
            ${organization?.phone || ''}<br>
            ${organization?.email || ''}
          </div>
        </div>
        <div style="text-align: right;">
          <h2 style="
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 8px 0;
          ">INVOICE</h2>
          <div style="font-size: 16px; color: #6b7280;">
            <strong>${invoice.invoice_number}</strong>
          </div>
        </div>
      </div>

      <!-- Invoice Details -->
      <div style="
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        margin-bottom: 40px;
      ">
        <!-- Bill To -->
        <div>
          <h3 style="
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 12px 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          ">Bill To</h3>
          <div style="
            background: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
          ">
            <div style="font-weight: bold; font-size: 16px; margin-bottom: 8px;">
              ${invoice.customers?.name || 'Unknown Customer'}
            </div>
            <div style="color: #6b7280; font-size: 14px;">
              ${invoice.customers?.email || ''}<br>
              ${invoice.customers?.phone || ''}<br>
              ${invoice.customers?.address || ''}
            </div>
          </div>
        </div>

        <!-- Invoice Info -->
        <div>
          <h3 style="
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 12px 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          ">Invoice Details</h3>
          <div style="
            background: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
          ">
            <div style="margin-bottom: 8px;">
              <span style="color: #6b7280;">Date Issued:</span>
              <strong style="margin-left: 8px;">
                ${new Date(invoice.date_issued).toLocaleDateString('en-UG', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </strong>
            </div>
            <div style="margin-bottom: 8px;">
              <span style="color: #6b7280;">Due Date:</span>
              <strong style="margin-left: 8px;">
                ${new Date(invoice.due_date).toLocaleDateString('en-UG', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </strong>
            </div>
            <div>
              <span style="color: #6b7280;">Status:</span>
              <span style="
                margin-left: 8px;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                text-transform: uppercase;
                ${getStatusStyles(invoice.status)}
              ">${invoice.status}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Line Items -->
      <div style="margin-bottom: 40px;">
        <h3 style="
          font-size: 16px;
          font-weight: bold;
          color: #1f2937;
          margin: 0 0 16px 0;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        ">Items</h3>
        
        <table style="
          width: 100%;
          border-collapse: collapse;
          background: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        ">
          <thead>
            <tr style="background: #f3f4f6;">
              <th style="
                padding: 12px;
                text-align: left;
                font-weight: bold;
                color: #374151;
                border-bottom: 1px solid #e5e7eb;
              ">Description</th>
              <th style="
                padding: 12px;
                text-align: center;
                font-weight: bold;
                color: #374151;
                border-bottom: 1px solid #e5e7eb;
              ">Qty</th>
              <th style="
                padding: 12px;
                text-align: right;
                font-weight: bold;
                color: #374151;
                border-bottom: 1px solid #e5e7eb;
              ">Unit Price</th>
              <th style="
                padding: 12px;
                text-align: right;
                font-weight: bold;
                color: #374151;
                border-bottom: 1px solid #e5e7eb;
              ">Tax %</th>
              <th style="
                padding: 12px;
                text-align: right;
                font-weight: bold;
                color: #374151;
                border-bottom: 1px solid #e5e7eb;
              ">Total</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.invoice_lines?.map((line, index) => `
              <tr style="border-bottom: 1px solid #f3f4f6;">
                <td style="padding: 12px; color: #374151;">
                  <div style="font-weight: 500;">${line.description}</div>
                  ${line.accounts ? `<div style="font-size: 12px; color: #6b7280;">${line.accounts.code} - ${line.accounts.name}</div>` : ''}
                </td>
                <td style="padding: 12px; text-align: center; color: #374151;">
                  ${line.quantity}
                </td>
                <td style="padding: 12px; text-align: right; color: #374151;">
                  ${formatCurrency(line.unit_price)}
                </td>
                <td style="padding: 12px; text-align: right; color: #374151;">
                  ${line.tax_rate_pct}%
                </td>
                <td style="padding: 12px; text-align: right; font-weight: 500; color: #374151;">
                  ${formatCurrency(line.line_total + line.tax_amount)}
                </td>
              </tr>
            `).join('') || '<tr><td colspan="5" style="padding: 20px; text-align: center; color: #6b7280;">No line items</td></tr>'}
          </tbody>
        </table>
      </div>

      <!-- Totals -->
      <div style="
        display: flex;
        justify-content: flex-end;
        margin-bottom: 40px;
      ">
        <div style="
          background: #f9fafb;
          padding: 20px;
          border-radius: 8px;
          min-width: 300px;
          border: 1px solid #e5e7eb;
        ">
          <div style="
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            color: #6b7280;
          ">
            <span>Subtotal:</span>
            <span>${formatCurrency(subtotal)}</span>
          </div>
          <div style="
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            color: #6b7280;
          ">
            <span>Tax:</span>
            <span>${formatCurrency(totalTax)}</span>
          </div>
          <div style="
            display: flex;
            justify-content: space-between;
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            border-top: 2px solid #e5e7eb;
            padding-top: 12px;
          ">
            <span>Total:</span>
            <span>${formatCurrency(invoice.total_amount)}</span>
          </div>
        </div>
      </div>

      <!-- Notes -->
      ${invoice.notes ? `
        <div style="margin-bottom: 40px;">
          <h3 style="
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 12px 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          ">Notes</h3>
          <div style="
            background: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #6b7280;
            color: #374151;
            white-space: pre-wrap;
          ">${invoice.notes}</div>
        </div>
      ` : ''}

      <!-- Footer -->
      <div style="
        text-align: center;
        color: #6b7280;
        font-size: 12px;
        border-top: 1px solid #e5e7eb;
        padding-top: 20px;
      ">
        <p>Thank you for your business!</p>
        <p>Generated on ${new Date().toLocaleDateString('en-UG', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}</p>
      </div>
    </div>
  `
}

/**
 * Get status-specific styles
 */
function getStatusStyles(status: string): string {
  switch (status?.toLowerCase()) {
    case 'paid':
      return 'background: #d1fae5; color: #065f46;'
    case 'sent':
      return 'background: #dbeafe; color: #1e40af;'
    case 'overdue':
      return 'background: #fee2e2; color: #991b1b;'
    case 'draft':
      return 'background: #f3f4f6; color: #374151;'
    case 'cancelled':
      return 'background: #fee2e2; color: #991b1b;'
    default:
      return 'background: #f3f4f6; color: #374151;'
  }
}

/**
 * Generate and download invoice PDF
 */
export async function exportInvoiceToPdf(
  invoice: InvoicePdfData,
  organization?: Organization
): Promise<void> {
  try {
    // Create temporary container
    const container = document.createElement('div')
    container.style.position = 'absolute'
    container.style.left = '-9999px'
    container.style.top = '-9999px'
    container.style.width = '800px'
    
    // Set HTML content
    container.innerHTML = createInvoiceHtmlTemplate(invoice, organization)
    
    // Add to DOM temporarily
    document.body.appendChild(container)
    
    // Generate filename
    const filename = `invoice-${invoice.invoice_number}-${new Date().toISOString().split('T')[0]}.pdf`
    
    // Generate PDF
    await generatePdfFromElement(container, filename)
    
    // Clean up
    document.body.removeChild(container)
    
  } catch (error) {
    console.error('Error exporting invoice to PDF:', error)
    throw error
  }
}
