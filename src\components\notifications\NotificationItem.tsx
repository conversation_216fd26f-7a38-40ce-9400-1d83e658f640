import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { MoreHorizontal, Archive, ExternalLink, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import { NotificationActions } from './NotificationActions'
import {
  useMarkNotificationAsRead,
  useArchiveNotification
} from '@/hooks/queries/useNotifications'
import {
  getNotificationPriorityConfig,
  getNotificationCategoryConfig,
  getEntityUrl
} from '@/types/notifications'
import type { NotificationWithMeta } from '@/types/notifications'

interface NotificationItemProps {
  notification: NotificationWithMeta
  onClick?: () => void
}

export function NotificationItem({ notification, onClick }: NotificationItemProps) {
  const navigate = useNavigate()
  const [isHovered, setIsHovered] = useState(false)
  
  const markAsRead = useMarkNotificationAsRead()
  const archiveNotification = useArchiveNotification()
  
  const priorityConfig = getNotificationPriorityConfig(notification.priority as 'low' | 'normal' | 'high' | 'urgent')
  const categoryConfig = getNotificationCategoryConfig(notification.category as 'system' | 'financial' | 'approval' | 'reminder' | 'alert')
  
  const handleClick = () => {
    // Mark as read if not already read
    if (!notification.is_read) {
      markAsRead.mutate(notification.id)
    }
    
    // Navigate to entity if available
    const entityUrl = getEntityUrl(notification.entity_type, notification.entity_id)
    if (entityUrl) {
      navigate(entityUrl)
    }
    
    onClick?.()
  }
  
  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation()
    markAsRead.mutate(notification.id)
  }
  
  const handleArchive = (e: React.MouseEvent) => {
    e.stopPropagation()
    archiveNotification.mutate(notification.id)
  }
  
  const handleViewEntity = (e: React.MouseEvent) => {
    e.stopPropagation()
    const entityUrl = getEntityUrl(notification.entity_type, notification.entity_id)
    if (entityUrl) {
      navigate(entityUrl)
      onClick?.()
    }
  }

  return (
    <div
      className={cn(
        "relative p-4 hover:bg-muted/50 cursor-pointer transition-colors",
        !notification.is_read && "bg-blue-50/50 border-l-4 border-l-blue-500",
        notification.isExpired && "opacity-60"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
    >
      <div className="flex items-start gap-3">
        {/* Priority indicator */}
        <div className={cn("w-2 h-2 rounded-full mt-2 flex-shrink-0", priorityConfig.bgColor)} />
        
        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              {/* Title and category */}
              <div className="flex items-center gap-2 mb-1">
                <h4 className={cn(
                  "text-sm font-medium truncate",
                  !notification.is_read && "font-semibold"
                )}>
                  {notification.title}
                </h4>
                <Badge 
                  variant="secondary" 
                  className={cn("text-xs", categoryConfig.color)}
                >
                  {categoryConfig.label}
                </Badge>
              </div>
              
              {/* Message */}
              <p className="text-sm text-muted-foreground mb-2 overflow-hidden" style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical'
              }}>
                {notification.message}
              </p>
              
              {/* Metadata */}
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{notification.timeAgo}</span>
                {notification.entity_type && (
                  <>
                    <span>•</span>
                    <span className="capitalize">{notification.entity_type}</span>
                  </>
                )}
                {notification.priority !== 'normal' && (
                  <>
                    <span>•</span>
                    <Badge 
                      variant="outline" 
                      className={cn("text-xs", priorityConfig.color)}
                    >
                      {notification.priority}
                    </Badge>
                  </>
                )}
                {notification.isExpired && (
                  <>
                    <span>•</span>
                    <span className="text-red-500">Expired</span>
                  </>
                )}
              </div>
            </div>
            
            {/* Enhanced Actions */}
            {isHovered && (
              <div onClick={(e) => e.stopPropagation()}>
                <NotificationActions
                  notification={notification}
                  compact={true}
                  onActionComplete={() => {
                    // Refresh or handle action completion
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Unread indicator */}
      {!notification.is_read && (
        <div className="absolute top-4 right-4 w-2 h-2 bg-blue-500 rounded-full" />
      )}
    </div>
  )
}
