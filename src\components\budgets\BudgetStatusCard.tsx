import React from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { TrendingUp, TrendingDown, AlertTriangle, CheckCircle } from 'lucide-react'
import { BudgetStatus, BudgetValidationResult } from '@/hooks/queries/useBudgetValidation'

interface BudgetStatusCardProps {
  budgetStatus: BudgetStatus | null
  validation?: BudgetValidationResult
  billAmount?: number
  isLoading?: boolean
  className?: string
}

export function BudgetStatusCard({ 
  budgetStatus, 
  validation, 
  billAmount = 0, 
  isLoading = false,
  className = ""
}: BudgetStatusCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Budget Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-2 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!budgetStatus) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Budget Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            No active budget found for this account
          </div>
        </CardContent>
      </Card>
    )
  }

  const getStatusColor = () => {
    if (budgetStatus.isOverBudget || (validation?.wouldExceedBudget)) return 'text-red-600'
    if (budgetStatus.utilizationPercent >= 90) return 'text-orange-600'
    if (budgetStatus.utilizationPercent >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  const getStatusIcon = () => {
    if (budgetStatus.isOverBudget || (validation?.wouldExceedBudget)) 
      return <AlertTriangle className="h-4 w-4 text-red-600" />
    if (budgetStatus.utilizationPercent >= 90) 
      return <TrendingUp className="h-4 w-4 text-orange-600" />
    return <CheckCircle className="h-4 w-4 text-green-600" />
  }

  const getStatusBadge = () => {
    if (budgetStatus.isOverBudget) return <Badge variant="destructive">Over Budget</Badge>
    if (validation?.wouldExceedBudget) return <Badge variant="destructive">Would Exceed</Badge>
    if (budgetStatus.utilizationPercent >= 90) return <Badge variant="secondary">Near Limit</Badge>
    if (budgetStatus.utilizationPercent >= 75) return <Badge variant="outline">High Usage</Badge>
    return <Badge variant="default">On Track</Badge>
  }

  const projectedAmount = budgetStatus.actualAmount + billAmount
  const projectedUtilization = budgetStatus.budgetAmount > 0 
    ? (projectedAmount / budgetStatus.budgetAmount) * 100 
    : 0

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm flex items-center gap-2">
            {getStatusIcon()}
            Budget Status
          </CardTitle>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Account Info */}
        <div>
          <div className="font-medium text-sm">{budgetStatus.accountName}</div>
          <div className="text-xs text-muted-foreground">{budgetStatus.accountCode}</div>
          <div className="text-xs text-muted-foreground">
            Budget: {budgetStatus.budgetPeriod.budgetName}
          </div>
        </div>

        {/* Current Status */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Current Usage:</span>
            <span className={getStatusColor()}>
              {budgetStatus.utilizationPercent.toFixed(1)}%
            </span>
          </div>
          <Progress 
            value={Math.min(budgetStatus.utilizationPercent, 100)} 
            className="h-2"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Spent: {formatCurrency(budgetStatus.actualAmount)}</span>
            <span>Budget: {formatCurrency(budgetStatus.budgetAmount)}</span>
          </div>
        </div>

        {/* Projected Impact (if bill amount provided) */}
        {billAmount > 0 && (
          <div className="space-y-2 pt-2 border-t">
            <div className="text-sm font-medium">After This Bill:</div>
            <div className="flex justify-between text-sm">
              <span>Projected Usage:</span>
              <span className={projectedUtilization > 100 ? 'text-red-600' : 'text-blue-600'}>
                {projectedUtilization.toFixed(1)}%
              </span>
            </div>
            <Progress 
              value={Math.min(projectedUtilization, 100)} 
              className="h-2"
            />
            <div className="flex justify-between text-xs">
              <span>New Total: {formatCurrency(projectedAmount)}</span>
              <span className={budgetStatus.remainingAmount - billAmount < 0 ? 'text-red-600' : 'text-green-600'}>
                Remaining: {formatCurrency(budgetStatus.remainingAmount - billAmount)}
              </span>
            </div>
          </div>
        )}

        {/* Validation Alert */}
        {validation && (
          <Alert className={validation.wouldExceedBudget ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
            <AlertDescription className="text-sm">
              {validation.message}
              {validation.wouldExceedBudget && (
                <div className="mt-1 font-medium text-red-700">
                  Exceedance: {formatCurrency(validation.exceedanceAmount)}
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Budget Period */}
        <div className="text-xs text-muted-foreground pt-1 border-t">
          Period: {new Date(budgetStatus.budgetPeriod.startDate).toLocaleDateString()} - {new Date(budgetStatus.budgetPeriod.endDate).toLocaleDateString()}
        </div>
      </CardContent>
    </Card>
  )
}
