import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { queryKeys } from '@/lib/queryKeys'

export interface BudgetAlert {
  id: string
  accountId: string
  accountName: string
  accountCode: string
  budgetId: string
  budgetName: string
  budgetAmount: number
  actualAmount: number
  utilizationPercent: number
  alertLevel: 'warning' | 'critical' | 'exceeded'
  threshold: number
  message: string
  isActive: boolean
  createdAt: string
}

export interface BudgetAlertSummary {
  totalAlerts: number
  warningAlerts: number
  criticalAlerts: number
  exceededAlerts: number
  alerts: BudgetAlert[]
}

/**
 * Hook to get budget alerts for the organization
 */
export const useBudgetAlerts = (enabled = true) => {
  const { profile } = useAuth()

  return useQuery({
    queryKey: queryKeys.budgets.alerts(profile?.org_id || ''),
    queryFn: async (): Promise<BudgetAlertSummary> => {
      if (!profile?.org_id) {
        return {
          totalAlerts: 0,
          warningAlerts: 0,
          criticalAlerts: 0,
          exceededAlerts: 0,
          alerts: []
        }
      }

      // Get current active budgets with their lines
      const currentDate = new Date().toISOString().split('T')[0]
      
      const { data: budgetLines, error } = await supabase
        .from('budget_lines')
        .select(`
          amount,
          budget_id,
          account_id,
          accounts!inner(name, code, type),
          budgets!inner(
            id,
            name,
            start_date,
            end_date,
            status
          )
        `)
        .eq('budgets.status', 'approved')
        .lte('budgets.start_date', currentDate)
        .gte('budgets.end_date', currentDate)

      if (error) throw error
      if (!budgetLines || budgetLines.length === 0) {
        return {
          totalAlerts: 0,
          warningAlerts: 0,
          criticalAlerts: 0,
          exceededAlerts: 0,
          alerts: []
        }
      }

      const alerts: BudgetAlert[] = []

      // Check each budget line for alerts
      for (const line of budgetLines) {
        const budget = line.budgets
        const account = line.accounts

        // Get actual spending for this account in the budget period
        const { data: transactions, error: transError } = await supabase
          .from('transaction_lines')
          .select('debit, credit')
          .eq('org_id', profile.org_id)
          .eq('account_id', line.account_id)
          .gte('created_at', `${budget.start_date}T00:00:00`)
          .lte('created_at', `${budget.end_date}T23:59:59`)

        if (transError) continue // Skip this line if error

        // Calculate actual amount based on account type
        const debitTotal = transactions?.reduce((sum, t) => sum + (t.debit || 0), 0) || 0
        const creditTotal = transactions?.reduce((sum, t) => sum + (t.credit || 0), 0) || 0
        
        let actualAmount = 0
        if (account.type === 'expense') {
          actualAmount = debitTotal - creditTotal
        } else if (account.type === 'income') {
          actualAmount = creditTotal - debitTotal
        } else if (account.type === 'asset') {
          actualAmount = debitTotal - creditTotal
        } else {
          actualAmount = creditTotal - debitTotal
        }

        const budgetAmount = line.amount || 0
        const utilizationPercent = budgetAmount > 0 ? (actualAmount / budgetAmount) * 100 : 0

        // Determine alert level and create alert if needed
        let alertLevel: 'warning' | 'critical' | 'exceeded' | null = null
        let threshold = 0
        let message = ''

        if (utilizationPercent >= 100) {
          alertLevel = 'exceeded'
          threshold = 100
          message = `Budget exceeded by ${(utilizationPercent - 100).toFixed(1)}%`
        } else if (utilizationPercent >= 90) {
          alertLevel = 'critical'
          threshold = 90
          message = `Budget utilization at ${utilizationPercent.toFixed(1)}% - Critical level`
        } else if (utilizationPercent >= 75) {
          alertLevel = 'warning'
          threshold = 75
          message = `Budget utilization at ${utilizationPercent.toFixed(1)}% - Warning level`
        }

        if (alertLevel) {
          alerts.push({
            id: `${line.budget_id}-${line.account_id}`,
            accountId: line.account_id,
            accountName: account.name,
            accountCode: account.code,
            budgetId: line.budget_id,
            budgetName: budget.name,
            budgetAmount,
            actualAmount,
            utilizationPercent,
            alertLevel,
            threshold,
            message,
            isActive: true,
            createdAt: new Date().toISOString()
          })
        }
      }

      // Sort alerts by severity (exceeded > critical > warning) and utilization
      alerts.sort((a, b) => {
        const severityOrder = { exceeded: 3, critical: 2, warning: 1 }
        if (severityOrder[a.alertLevel] !== severityOrder[b.alertLevel]) {
          return severityOrder[b.alertLevel] - severityOrder[a.alertLevel]
        }
        return b.utilizationPercent - a.utilizationPercent
      })

      const summary = {
        totalAlerts: alerts.length,
        warningAlerts: alerts.filter(a => a.alertLevel === 'warning').length,
        criticalAlerts: alerts.filter(a => a.alertLevel === 'critical').length,
        exceededAlerts: alerts.filter(a => a.alertLevel === 'exceeded').length,
        alerts
      }

      return summary
    },
    enabled: enabled && !!profile?.org_id,
    staleTime: 60 * 1000, // 1 minute - frequent updates for alerts
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  })
}

/**
 * Hook to get budget alerts for a specific account
 */
export const useAccountBudgetAlerts = (accountId: string | null, enabled = true) => {
  const { data: allAlerts } = useBudgetAlerts(enabled)

  const accountAlerts = allAlerts?.alerts.filter(alert => alert.accountId === accountId) || []

  return {
    alerts: accountAlerts,
    hasAlerts: accountAlerts.length > 0,
    highestSeverity: accountAlerts.length > 0 ? accountAlerts[0].alertLevel : null
  }
}

/**
 * Hook to check if there are any critical budget alerts
 */
export const useHasCriticalBudgetAlerts = () => {
  const { data: alerts } = useBudgetAlerts()
  
  return {
    hasCritical: (alerts?.criticalAlerts || 0) > 0 || (alerts?.exceededAlerts || 0) > 0,
    criticalCount: (alerts?.criticalAlerts || 0) + (alerts?.exceededAlerts || 0),
    totalAlerts: alerts?.totalAlerts || 0
  }
}
