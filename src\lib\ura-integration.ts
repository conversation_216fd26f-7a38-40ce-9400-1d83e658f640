import { config } from './config'
import { monitoring } from './monitoring'

// URA API Types
export interface URAConfig {
  baseUrl: string
  apiKey: string
  environment: 'sandbox' | 'production'
  timeout: number
}

export interface TaxpayerInfo {
  tin: string
  name: string
  status: 'active' | 'inactive' | 'suspended'
  category: string
  registrationDate: string
  address?: string
  phone?: string
  email?: string
}

export interface TaxReturn {
  tin: string
  period: string
  type: 'VAT' | 'PAYE' | 'WHT' | 'CIT'
  grossAmount: number
  taxAmount: number
  netAmount: number
  dueDate: string
  status: 'draft' | 'submitted' | 'processed' | 'paid'
}

export interface URAResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: Record<string, unknown>
  }
  timestamp: string
  requestId: string
}

class URAIntegrationService {
  private static instance: URAIntegrationService
  private config: URAConfig
  private initialized = false

  constructor() {
    this.config = {
      baseUrl: import.meta.env.VITE_URA_API_BASE_URL || 'https://api.ura.go.ug/v1',
      apiKey: import.meta.env.VITE_URA_API_KEY || '',
      environment: (import.meta.env.VITE_URA_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
      timeout: 30000
    }
  }

  static getInstance(): URAIntegrationService {
    if (!URAIntegrationService.instance) {
      URAIntegrationService.instance = new URAIntegrationService()
    }
    return URAIntegrationService.instance
  }

  // Initialize URA integration
  initialize(): boolean {
    if (this.initialized) return true

    // Validate configuration
    if (!this.config.apiKey) {
      console.warn('URA API key not configured. URA integration disabled.')
      return false
    }

    // Validate environment
    if (config.app.environment === 'production' && this.config.environment === 'sandbox') {
      console.error('Production environment cannot use URA sandbox')
      return false
    }

    this.initialized = true
    console.log(`🏛️ URA integration initialized (${this.config.environment})`)
    return true
  }

  // Check if URA integration is available
  isAvailable(): boolean {
    return this.initialized && !!this.config.apiKey
  }

  // Validate TIN number with URA
  async validateTIN(tin: string): Promise<URAResponse<TaxpayerInfo>> {
    if (!this.isAvailable()) {
      return this.createErrorResponse('URA_NOT_AVAILABLE', 'URA integration not available')
    }

    const startTime = Date.now()

    try {
      const response = await this.makeRequest<TaxpayerInfo>('GET', `/taxpayers/${tin}`)
      
      monitoring.trackApiCall(
        `${this.config.baseUrl}/taxpayers/${tin}`,
        'GET',
        response.success ? 200 : 400,
        Date.now() - startTime
      )

      return response
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'ura_integration',
          operation: 'validate_tin'
        },
        extra: { tin }
      })

      return this.createErrorResponse(
        'VALIDATION_FAILED',
        error instanceof Error ? error.message : 'TIN validation failed'
      )
    }
  }

  // Submit tax return to URA
  async submitTaxReturn(taxReturn: TaxReturn): Promise<URAResponse<{ submissionId: string }>> {
    if (!this.isAvailable()) {
      return this.createErrorResponse('URA_NOT_AVAILABLE', 'URA integration not available')
    }

    const startTime = Date.now()

    try {
      // Validate tax return data
      const validation = this.validateTaxReturn(taxReturn)
      if (!validation.isValid) {
        return this.createErrorResponse('INVALID_DATA', validation.message)
      }

      const response = await this.makeRequest<{ submissionId: string }>(
        'POST',
        '/tax-returns',
        taxReturn
      )

      monitoring.trackApiCall(
        `${this.config.baseUrl}/tax-returns`,
        'POST',
        response.success ? 200 : 400,
        Date.now() - startTime
      )

      // Track successful submission
      if (response.success) {
        monitoring.trackUserAction('tax_return_submitted', {
          type: taxReturn.type,
          period: taxReturn.period,
          amount: taxReturn.taxAmount
        })
      }

      return response
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'ura_integration',
          operation: 'submit_tax_return'
        },
        extra: { taxReturn }
      })

      return this.createErrorResponse(
        'SUBMISSION_FAILED',
        error instanceof Error ? error.message : 'Tax return submission failed'
      )
    }
  }

  // Get tax rates from URA
  async getTaxRates(): Promise<URAResponse<{ vat: number; paye: Record<string, unknown>; wht: Record<string, unknown> }>> {
    if (!this.isAvailable()) {
      return this.createErrorResponse('URA_NOT_AVAILABLE', 'URA integration not available')
    }

    try {
      const response = await this.makeRequest<{ vat: number; paye: Record<string, unknown>; wht: Record<string, unknown> }>(
        'GET',
        '/tax-rates'
      )

      return response
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'ura_integration',
          operation: 'get_tax_rates'
        }
      })

      return this.createErrorResponse(
        'FETCH_FAILED',
        error instanceof Error ? error.message : 'Failed to fetch tax rates'
      )
    }
  }

  // Check tax return status
  async getTaxReturnStatus(submissionId: string): Promise<URAResponse<{ status: string; details: Record<string, unknown> }>> {
    if (!this.isAvailable()) {
      return this.createErrorResponse('URA_NOT_AVAILABLE', 'URA integration not available')
    }

    try {
      const response = await this.makeRequest<{ status: string; details: Record<string, unknown> }>(
        'GET',
        `/tax-returns/${submissionId}/status`
      )

      return response
    } catch (error) {
      monitoring.captureError(error as Error, {
        tags: {
          service: 'ura_integration',
          operation: 'get_tax_return_status'
        },
        extra: { submissionId }
      })

      return this.createErrorResponse(
        'STATUS_CHECK_FAILED',
        error instanceof Error ? error.message : 'Failed to check tax return status'
      )
    }
  }

  // Make HTTP request to URA API
  private async makeRequest<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: Record<string, unknown>
  ): Promise<URAResponse<T>> {
    const url = `${this.config.baseUrl}${endpoint}`
    const requestId = this.generateRequestId()

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`,
      'X-Request-ID': requestId,
      'X-Environment': this.config.environment,
      'User-Agent': `KayaFinance/${config.app.version}`
    }

    const requestOptions: RequestInit = {
      method,
      headers,
      signal: AbortSignal.timeout(this.config.timeout)
    }

    if (data && (method === 'POST' || method === 'PUT')) {
      requestOptions.body = JSON.stringify(data)
    }

    const response = await fetch(url, requestOptions)
    const responseData = await response.json()

    if (!response.ok) {
      throw new Error(`URA API error: ${response.status} - ${responseData.message || 'Unknown error'}`)
    }

    return {
      success: true,
      data: responseData.data,
      timestamp: new Date().toISOString(),
      requestId
    }
  }

  // Validate tax return data
  private validateTaxReturn(taxReturn: TaxReturn): { isValid: boolean; message: string } {
    if (!taxReturn.tin || taxReturn.tin.length !== 10) {
      return { isValid: false, message: 'Invalid TIN number' }
    }

    if (!taxReturn.period || !taxReturn.type) {
      return { isValid: false, message: 'Period and type are required' }
    }

    if (taxReturn.grossAmount < 0 || taxReturn.taxAmount < 0) {
      return { isValid: false, message: 'Amounts cannot be negative' }
    }

    if (!taxReturn.dueDate || new Date(taxReturn.dueDate) < new Date()) {
      return { isValid: false, message: 'Invalid due date' }
    }

    return { isValid: true, message: 'Valid' }
  }

  // Create error response
  private createErrorResponse<T>(code: string, message: string): URAResponse<T> {
    return {
      success: false,
      error: {
        code,
        message
      },
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId()
    }
  }

  // Generate unique request ID
  private generateRequestId(): string {
    return `kaya-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  // Get integration status
  getStatus(): {
    available: boolean
    environment: string
    lastCheck?: string
    configuration: {
      baseUrl: string
      hasApiKey: boolean
      timeout: number
    }
  } {
    return {
      available: this.isAvailable(),
      environment: this.config.environment,
      configuration: {
        baseUrl: this.config.baseUrl,
        hasApiKey: !!this.config.apiKey,
        timeout: this.config.timeout
      }
    }
  }
}

// Export singleton instance
export const uraIntegration = URAIntegrationService.getInstance()

// React hook for URA integration
export function useURAIntegration() {
  const [status, setStatus] = React.useState(uraIntegration.getStatus())

  React.useEffect(() => {
    // Initialize URA integration
    uraIntegration.initialize()
    setStatus(uraIntegration.getStatus())
  }, [])

  const validateTIN = async (tin: string) => {
    return await uraIntegration.validateTIN(tin)
  }

  const submitTaxReturn = async (taxReturn: TaxReturn) => {
    return await uraIntegration.submitTaxReturn(taxReturn)
  }

  const getTaxRates = async () => {
    return await uraIntegration.getTaxRates()
  }

  return {
    status,
    validateTIN,
    submitTaxReturn,
    getTaxRates,
    isAvailable: status.available
  }
}
