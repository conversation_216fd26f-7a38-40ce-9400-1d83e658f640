import { useState, useEffect, useCallback } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  FileText, 
  Building, 
  Calendar, 
  DollarSign,
  Phone,
  Mail,
  MapPin,
  Percent
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { LoadingSpinner } from '@/components/ui/loading'
import type { BillWithVendor } from '@/types/bills'
import type { Vendor } from '@/types/database'
import { formatCurrency } from '@/lib/utils'

interface BillDetailsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  billId: string
}

interface BillLineWithAccount {
  id: string
  account_id: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number
  line_total: number
  tax_amount: number
  accounts?: {
    code: string
    name: string
  }
}

interface BillDetails extends BillWithVendor {
  bill_lines?: BillLineWithAccount[]
  withholding_tax_rates?: {
    name: string
    rate_pct: number
  }
}

export function BillDetailsModal({ 
  open, 
  onOpenChange, 
  billId 
}: BillDetailsModalProps) {
  const { profile } = useAuth()
  const [bill, setBill] = useState<BillDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchBillDetails = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Fetch bill details with vendor
      const { data: billData, error: billError } = await supabase
        .from('bills')
        .select(`
          *,
          vendors(*),
          withholding_tax_rates(name, rate_pct)
        `)
        .eq('id', billId)
        .single()

      if (billError) throw billError

      // Fetch bill lines with account details
      const { data: linesData, error: linesError } = await supabase
        .from('bill_lines')
        .select(`
          *,
          accounts(code, name)
        `)
        .eq('bill_id', billId)
        .order('created_at')

      if (linesError) throw linesError

      // Calculate line totals
      const enhancedLines = linesData?.map(line => ({
        ...line,
        line_total: line.quantity * line.unit_price,
        tax_amount: (line.quantity * line.unit_price) * (line.tax_rate_pct / 100)
      })) || []

      const enhancedBill: BillDetails = {
        ...billData,
        vendor: billData.vendors,
        bill_lines: enhancedLines
      }

      setBill(enhancedBill)

    } catch (error) {
      console.error('Error fetching bill details:', error)
      setError('Failed to load bill details')
    } finally {
      setLoading(false)
    }
  }, [billId])

  useEffect(() => {
    if (open && billId) {
      fetchBillDetails()
    }
  }, [open, billId, fetchBillDetails])

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'approved':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <LoadingSpinner size="lg" text="Loading bill details..." showText className="h-64" />
        </DialogContent>
      </Dialog>
    )
  }

  if (error || !bill) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <div className="text-center py-8">
            <p className="text-destructive">{error || 'Bill not found'}</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  const subtotal = bill.bill_lines?.reduce((sum, line) => sum + line.line_total, 0) || 0
  const totalTax = bill.bill_lines?.reduce((sum, line) => sum + line.tax_amount, 0) || 0

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Bill Details - {bill.bill_number}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Bill Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Bill Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label className="text-muted-foreground">Total Amount</Label>
                  <p className="text-2xl font-bold text-primary">
                    {formatCurrency(bill.total_amount)}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Status</Label>
                  <Badge className={getStatusColor(bill.status)}>
                    {bill.status}
                  </Badge>
                </div>
                <div>
                  <Label className="text-muted-foreground">Date Issued</Label>
                  <p className="font-medium">
                    {new Date(bill.date_issued).toLocaleDateString('en-UG', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Due Date</Label>
                  <p className="font-medium">
                    {new Date(bill.due_date).toLocaleDateString('en-UG', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Vendor Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Vendor Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-muted-foreground">Name</Label>
                  <p className="font-medium">{bill.vendor?.name || 'Unknown Vendor'}</p>
                </div>
                {bill.vendor?.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{bill.vendor.email}</span>
                  </div>
                )}
                {bill.vendor?.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{bill.vendor.phone}</span>
                  </div>
                )}
                {bill.vendor?.address && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{bill.vendor.address}</span>
                  </div>
                )}
                {bill.vendor?.payment_terms && (
                  <div>
                    <Label className="text-muted-foreground">Payment Terms</Label>
                    <p className="text-sm">{bill.vendor.payment_terms} days</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Bill Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Bill Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-muted-foreground">Bill Number</Label>
                  <p className="font-mono text-sm bg-muted p-2 rounded">
                    {bill.bill_number}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Tax Amount</Label>
                  <p className="font-medium">{formatCurrency(bill.tax_amount || 0)}</p>
                </div>
                {bill.withholding_amount && bill.withholding_amount > 0 && (
                  <div>
                    <Label className="text-muted-foreground">Withholding Tax</Label>
                    <p className="font-medium">{formatCurrency(bill.withholding_amount)}</p>
                    {bill.withholding_tax_rates && (
                      <p className="text-sm text-muted-foreground">
                        {bill.withholding_tax_rates.name} ({bill.withholding_tax_rates.rate_pct}%)
                      </p>
                    )}
                  </div>
                )}
                <div>
                  <Label className="text-muted-foreground">Created</Label>
                  <p className="text-sm">{new Date(bill.created_at).toLocaleString()}</p>
                </div>
                {bill.updated_at && (
                  <div>
                    <Label className="text-muted-foreground">Last Updated</Label>
                    <p className="text-sm">{new Date(bill.updated_at).toLocaleString()}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Bill Lines */}
          {bill.bill_lines && bill.bill_lines.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Bill Line Items</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Qty</TableHead>
                      <TableHead className="text-right">Unit Price</TableHead>
                      <TableHead className="text-right">Tax %</TableHead>
                      <TableHead className="text-right">Line Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {bill.bill_lines.map((line) => (
                      <TableRow key={line.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{line.accounts?.name || 'Unknown Account'}</p>
                            <p className="text-sm text-muted-foreground">{line.accounts?.code}</p>
                          </div>
                        </TableCell>
                        <TableCell>{line.description}</TableCell>
                        <TableCell className="text-right">{line.quantity}</TableCell>
                        <TableCell className="text-right">{formatCurrency(line.unit_price)}</TableCell>
                        <TableCell className="text-right">{line.tax_rate_pct}%</TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(line.line_total)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Totals Summary */}
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(subtotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax:</span>
                      <span>{formatCurrency(totalTax)}</span>
                    </div>
                    {bill.withholding_amount && bill.withholding_amount > 0 && (
                      <div className="flex justify-between">
                        <span>Withholding Tax:</span>
                        <span className="text-red-600">-{formatCurrency(bill.withholding_amount)}</span>
                      </div>
                    )}
                    <div className="flex justify-between font-bold border-t pt-2">
                      <span>Total:</span>
                      <span>{formatCurrency(bill.total_amount)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {bill.notes && (
            <Card>
              <CardHeader>
                <CardTitle>Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">{bill.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
