import { useState } from 'react'
import { Search, Eye, Send } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Edit, Check } from 'lucide-react'
import { BillDetailsModal } from './BillDetailsModal'
import type { BillListProps } from '@/types/bills'

export function BillList({ bills, onEdit, onStatusChange, onSubmitForApproval, searchTerm, onSearchChange }: BillListProps) {
  const [viewingBillId, setViewingBillId] = useState<string | null>(null)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)

  const handleViewBill = (billId: string) => {
    setViewingBillId(billId)
    setIsViewModalOpen(true)
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-500'
      case 'approved': return 'bg-blue-500'
      case 'paid': return 'bg-green-500'
      case 'overdue': return 'bg-red-500'
      case 'cancelled': return 'bg-gray-800'
      default: return 'bg-gray-500'
    }
  }

  const filteredBills = bills.filter(bill =>
    bill.bill_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (bill.vendor?.name || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>Bills List</CardTitle>
        <CardDescription>
          {bills.length} bill{bills.length !== 1 ? 's' : ''} total
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Bill #</TableHead>
              <TableHead>Vendor</TableHead>
              <TableHead>Date Issued</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredBills.map((bill) => (
              <TableRow key={bill.id}>
                <TableCell className="font-medium">{bill.bill_number}</TableCell>
                <TableCell>{bill.vendor?.name || 'Unknown Vendor'}</TableCell>
                <TableCell>{new Date(bill.date_issued).toLocaleDateString()}</TableCell>
                <TableCell>{new Date(bill.due_date).toLocaleDateString()}</TableCell>
                <TableCell>UGX {bill.total_amount.toLocaleString()}</TableCell>
                <TableCell>
                  <Badge className={getStatusBadgeColor(bill.status)}>
                    {bill.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewBill(bill.id)}
                      title="View bill details"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(bill)}
                      title="Edit bill"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    {bill.status === 'draft' && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onStatusChange(bill, 'approved')}
                          title="Approve Bill"
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                        {onSubmitForApproval && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onSubmitForApproval(bill)}
                            title="Submit for Approval"
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        )}
                      </>
                    )}
                    {bill.status === 'approved' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onStatusChange(bill, 'paid')}
                        title="Mark as Paid"
                        className="text-green-600 hover:text-green-700"
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {filteredBills.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  {searchTerm ? 'No bills found matching your search.' : 'No bills yet. Create your first bill!'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>

      {/* Bill Details Modal */}
      {viewingBillId && (
        <BillDetailsModal
          open={isViewModalOpen}
          onOpenChange={setIsViewModalOpen}
          billId={viewingBillId}
        />
      )}
    </Card>
  )
}
