
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PasswordInput, PasswordConfirmInput } from '@/components/ui/password-input';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { isPasswordSecure, validatePasswordConfirmation, type PasswordValidationResult } from '@/lib/passwordValidation';

interface LoginModalProps {
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function LoginModal({ trigger, open: controlledOpen, onOpenChange }: LoginModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [passwordValidation, setPasswordValidation] = useState<PasswordValidationResult | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();

  const open = controlledOpen ?? isOpen;
  const setOpen = onOpenChange ?? setIsOpen;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (isSignUp) {
        // Validate password security
        if (!isPasswordSecure(password)) {
          toast({
            title: "Password Error",
            description: "Password does not meet security requirements. Please ensure it has at least 8 characters, including uppercase and lowercase letters.",
            variant: "destructive"
          });
          setIsLoading(false);
          return;
        }

        // Validate password confirmation
        const confirmValidation = validatePasswordConfirmation(password, confirmPassword);
        if (!confirmValidation.isValid) {
          toast({
            title: "Password Error",
            description: confirmValidation.message || "Passwords do not match",
            variant: "destructive"
          });
          setIsLoading(false);
          return;
        }

        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              role: 'accountant'
            }
          }
        });
        
        if (error) throw error;
        
        if (data.user) {
          toast({
            title: "Success",
            description: "Account created successfully! You will be redirected to complete your setup.",
          });
          
          setOpen(false);
          // The auth state change will handle the redirect to onboarding
        }
      } else {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });
        if (error) throw error;
        
        toast({
          title: "Success",
          description: "Welcome back!",
        });
        
        setOpen(false);
        // The auth state change will handle the redirect
      }
    } catch (error: unknown) {
      toast({
        title: isSignUp ? "Sign up failed" : "Login failed",
        description: error instanceof Error ? error.message : "Please check your credentials and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {trigger && (
        <div onClick={() => setOpen(true)}>
          {trigger}
        </div>
      )}
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[425px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>{isSignUp ? 'Create Account' : 'Welcome Back'}</DialogTitle>
            <DialogDescription>
              {isSignUp 
                ? 'Create your account to get started with KAYA Finance' 
                : 'Enter your credentials to access your account'
              }
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            
            {isSignUp ? (
              <>
                <PasswordInput
                  id="password"
                  label="Password"
                  placeholder="Create a secure password"
                  value={password}
                  onChange={setPassword}
                  onValidationChange={setPasswordValidation}
                  showRequirements={true}
                  showStrengthIndicator={true}
                  required={true}
                />

                <PasswordConfirmInput
                  id="confirmPassword"
                  label="Confirm Password"
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  originalPassword={password}
                  onChange={setConfirmPassword}
                  required={true}
                />
              </>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
            )}

            <Button
              type="submit"
              className="w-full bg-green-600 hover:bg-green-700"
              disabled={isLoading}
            >
              {isLoading ? (isSignUp ? 'Creating Account...' : 'Signing in...') : (isSignUp ? 'Create Account' : 'Sign in')}
            </Button>
          </form>

          <div className="text-center text-sm">
            {isSignUp ? 'Already have an account?' : "Don't have an account?"}{' '}
            <Button
              variant="link"
              className="p-0 text-green-600 hover:text-green-700"
              onClick={() => setIsSignUp(!isSignUp)}
            >
              {isSignUp ? 'Sign in' : 'Sign up'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
