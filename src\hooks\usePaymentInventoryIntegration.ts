import { useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from 'sonner'
import { queryKeys } from '@/lib/queryKeys'

export interface PaymentInventoryProcessingData {
  paymentId: string
  documentType: 'invoice' | 'bill'
  documentId: string
  documentNumber: string
  amount: number
}

/**
 * Hook for processing inventory movements when payments are made
 */
export function usePaymentInventoryIntegration() {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  /**
   * Process inventory movements when an invoice is paid
   */
  const processInvoicePayment = useMutation({
    mutationFn: async ({
      paymentId,
      documentId,
      documentNumber,
      amount
    }: Omit<PaymentInventoryProcessingData, 'documentType'>) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Get invoice lines with product information
      const { data: invoiceLines, error: linesError } = await supabase
        .from('invoice_lines')
        .select(`
          *,
          product:products(
            id,
            name,
            track_inventory,
            cost_price
          )
        `)
        .eq('invoice_id', documentId)

      if (linesError) throw linesError

      // Get default location
      const { data: defaultLocation } = await supabase
        .from('inventory_locations')
        .select('id')
        .eq('org_id', profile.org_id)
        .eq('is_default', true)
        .eq('is_active', true)
        .single()

      if (!defaultLocation) {
        console.warn('No default inventory location found')
        return { processedLines: 0 }
      }

      let processedLines = 0

      // Process each line that has a product
      for (const line of invoiceLines || []) {
        if (line.product_id && line.product?.track_inventory) {
          try {
            // Create inventory transaction for the sale
            const { error: transactionError } = await supabase
              .from('inventory_transactions')
              .insert({
                org_id: profile.org_id,
                product_id: line.product_id,
                location_id: defaultLocation.id,
                transaction_type: 'sale',
                quantity: -line.quantity, // Negative for outgoing
                unit_cost: line.product.cost_price || line.unit_price,
                reference_type: 'invoice_payment',
                reference_id: paymentId,
                reference_number: `PAY-${documentNumber}`,
                notes: `Sale payment for invoice ${documentNumber}`,
                created_by: profile.id,
                transaction_date: new Date().toISOString(),
                total_cost: (line.product.cost_price || line.unit_price) * line.quantity
              })

            if (transactionError) {
              console.error('Failed to create inventory transaction:', transactionError)
              continue
            }

            // Update stock levels using the database function
            const { error: stockError } = await supabase.rpc('update_stock_level_from_transaction', {
              p_org_id: profile.org_id,
              p_product_id: line.product_id,
              p_location_id: defaultLocation.id,
              p_quantity_change: -line.quantity,
              p_unit_cost: line.product.cost_price || line.unit_price
            })

            if (stockError) {
              console.error('Failed to update stock level:', stockError)
            }

            // Fulfill stock reservations for this invoice
            const { error: fulfillError } = await supabase.rpc('fulfill_stock_reservations', {
              p_reference_type: 'invoice',
              p_reference_id: documentId,
              p_org_id: profile.org_id
            })

            if (fulfillError) {
              console.error('Failed to fulfill stock reservations:', fulfillError)
            }

            processedLines++
          } catch (error) {
            console.error(`Failed to process inventory for line ${line.id}:`, error)
          }
        }
      }

      return { processedLines }
    },
    onSuccess: ({ processedLines }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: ['stock-reservations', profile?.org_id] })
      
      if (processedLines > 0) {
        toast.success(`Processed inventory for ${processedLines} product line(s)`)
      }
    },
    onError: (error) => {
      console.error('Failed to process invoice payment inventory:', error)
      toast.error('Payment processed but inventory update failed')
    }
  })

  /**
   * Process inventory movements when a bill is paid
   */
  const processBillPayment = useMutation({
    mutationFn: async ({
      paymentId,
      documentId,
      documentNumber,
      amount
    }: Omit<PaymentInventoryProcessingData, 'documentType'>) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Get bill lines with product information
      const { data: billLines, error: linesError } = await supabase
        .from('bill_lines')
        .select(`
          *,
          product:products(
            id,
            name,
            track_inventory,
            cost_price
          )
        `)
        .eq('bill_id', documentId)

      if (linesError) throw linesError

      // Get default location
      const { data: defaultLocation } = await supabase
        .from('inventory_locations')
        .select('id')
        .eq('org_id', profile.org_id)
        .eq('is_default', true)
        .eq('is_active', true)
        .single()

      if (!defaultLocation) {
        console.warn('No default inventory location found')
        return { processedLines: 0 }
      }

      let processedLines = 0

      // Process each line that has a product
      for (const line of billLines || []) {
        if (line.product_id && line.product?.track_inventory) {
          try {
            // Create inventory transaction for the purchase
            const { error: transactionError } = await supabase
              .from('inventory_transactions')
              .insert({
                org_id: profile.org_id,
                product_id: line.product_id,
                location_id: defaultLocation.id,
                transaction_type: 'purchase',
                quantity: line.quantity, // Positive for incoming
                unit_cost: line.unit_price,
                reference_type: 'bill_payment',
                reference_id: paymentId,
                reference_number: `PAY-${documentNumber}`,
                notes: `Purchase payment for bill ${documentNumber}`,
                created_by: profile.id,
                transaction_date: new Date().toISOString(),
                total_cost: line.unit_price * line.quantity
              })

            if (transactionError) {
              console.error('Failed to create inventory transaction:', transactionError)
              continue
            }

            // Update stock levels using the database function
            const { error: stockError } = await supabase.rpc('update_stock_level_from_transaction', {
              p_org_id: profile.org_id,
              p_product_id: line.product_id,
              p_location_id: defaultLocation.id,
              p_quantity_change: line.quantity,
              p_unit_cost: line.unit_price
            })

            if (stockError) {
              console.error('Failed to update stock level:', stockError)
            }

            processedLines++
          } catch (error) {
            console.error(`Failed to process inventory for line ${line.id}:`, error)
          }
        }
      }

      return { processedLines }
    },
    onSuccess: ({ processedLines }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      
      if (processedLines > 0) {
        toast.success(`Processed inventory for ${processedLines} product line(s)`)
      }
    },
    onError: (error) => {
      console.error('Failed to process bill payment inventory:', error)
      toast.error('Payment processed but inventory update failed')
    }
  })

  /**
   * Main function to process payment inventory integration
   */
  const processPaymentInventory = useMutation({
    mutationFn: async (data: PaymentInventoryProcessingData) => {
      if (data.documentType === 'invoice') {
        return await processInvoicePayment.mutateAsync(data)
      } else if (data.documentType === 'bill') {
        return await processBillPayment.mutateAsync(data)
      } else {
        throw new Error(`Unsupported document type: ${data.documentType}`)
      }
    }
  })

  /**
   * Reverse inventory movements when a payment is cancelled or refunded
   */
  const reversePaymentInventory = useMutation({
    mutationFn: async ({
      paymentId,
      documentType,
      documentId
    }: Pick<PaymentInventoryProcessingData, 'paymentId' | 'documentType' | 'documentId'>) => {
      if (!profile?.org_id) throw new Error('Organization ID is required')

      // Find existing inventory transactions for this payment
      const { data: transactions, error: findError } = await supabase
        .from('inventory_transactions')
        .select('*')
        .eq('org_id', profile.org_id)
        .eq('reference_type', `${documentType}_payment`)
        .eq('reference_id', paymentId)

      if (findError) throw findError

      let reversedTransactions = 0

      // Create reverse transactions
      for (const transaction of transactions || []) {
        try {
          // Create reverse transaction
          const { error: reverseError } = await supabase
            .from('inventory_transactions')
            .insert({
              org_id: profile.org_id,
              product_id: transaction.product_id,
              location_id: transaction.location_id,
              transaction_type: 'adjustment',
              quantity: -transaction.quantity, // Reverse the quantity
              unit_cost: transaction.unit_cost,
              reference_type: 'payment_reversal',
              reference_id: paymentId,
              reference_number: `REV-${transaction.reference_number}`,
              notes: `Reversal of payment transaction: ${transaction.notes}`,
              created_by: profile.id,
              transaction_date: new Date().toISOString(),
              total_cost: transaction.total_cost
            })

          if (reverseError) {
            console.error('Failed to create reverse transaction:', reverseError)
            continue
          }

          // Update stock levels
          const { error: stockError } = await supabase.rpc('update_stock_level_from_transaction', {
            p_org_id: profile.org_id,
            p_product_id: transaction.product_id,
            p_location_id: transaction.location_id,
            p_quantity_change: -transaction.quantity,
            p_unit_cost: transaction.unit_cost || 0
          })

          if (stockError) {
            console.error('Failed to update stock level for reversal:', stockError)
          }

          reversedTransactions++
        } catch (error) {
          console.error(`Failed to reverse transaction ${transaction.id}:`, error)
        }
      }

      return { reversedTransactions }
    },
    onSuccess: ({ reversedTransactions }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.stockLevels.all(profile?.org_id || '') })
      queryClient.invalidateQueries({ queryKey: queryKeys.inventoryTransactions.all(profile?.org_id || '') })
      
      if (reversedTransactions > 0) {
        toast.success(`Reversed ${reversedTransactions} inventory transaction(s)`)
      }
    },
    onError: (error) => {
      console.error('Failed to reverse payment inventory:', error)
      toast.error('Failed to reverse inventory movements')
    }
  })

  return {
    processPaymentInventory: processPaymentInventory.mutateAsync,
    reversePaymentInventory: reversePaymentInventory.mutateAsync,
    
    // Individual processors
    processInvoicePayment: processInvoicePayment.mutateAsync,
    processBillPayment: processBillPayment.mutateAsync,
    
    // Loading states
    isProcessingPayment: processPaymentInventory.isPending,
    isReversingPayment: reversePaymentInventory.isPending,
    isProcessingInvoice: processInvoicePayment.isPending,
    isProcessingBill: processBillPayment.isPending
  }
}
