import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { toast } from '@/components/ui/toast-utils'
import { ApprovalEngine } from '@/lib/approval-engine/core'
import { ApprovalInstanceManager } from '@/lib/approval-engine/instance-manager'
import { ApprovalActionHandler } from '@/lib/approval-engine/action-handler'
import { WorkflowTemplateService } from '@/lib/approval-engine/workflow-template-service'
import { AuthorizationService } from '@/lib/approval-engine/authorization-service'
import type {
  ApprovalInstanceWithDetails,
  ApprovalFilters,
  ApprovalSortOptions,
  ApprovalStats,
  ApprovalActionRequest,
  BulkApprovalRequest,
  CreateWorkflowTemplatePayload,
  WorkflowTemplateWithSteps
} from '@/types/approval-workflow'
import type { DocumentType } from '@/types/database'

// Query Keys
export const approvalQueryKeys = {
  all: ['approvals'] as const,
  instances: () => [...approvalQueryKeys.all, 'instances'] as const,
  instance: (id: string) => [...approvalQueryKeys.instances(), id] as const,
  pendingForUser: (userId: string) => [...approvalQueryKeys.all, 'pending', userId] as const,
  stats: (orgId: string) => [...approvalQueryKeys.all, 'stats', orgId] as const,
  templates: () => [...approvalQueryKeys.all, 'templates'] as const,
  template: (id: string) => [...approvalQueryKeys.templates(), id] as const,
  actions: (instanceId: string) => [...approvalQueryKeys.all, 'actions', instanceId] as const,
}

/**
 * Hook to fetch approval instances with filters
 */
export const useApprovalInstances = (
  filters?: ApprovalFilters,
  sortOptions?: ApprovalSortOptions,
  page: number = 1,
  pageSize: number = 20
) => {
  const { profile } = useAuth()

  return useQuery({
    queryKey: [...approvalQueryKeys.instances(), filters, sortOptions, page, pageSize],
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('Organization ID required')
      
      return ApprovalInstanceManager.getApprovalInstances(
        profile.org_id,
        filters,
        sortOptions,
        page,
        pageSize
      )
    },
    enabled: !!profile?.org_id,
    staleTime: 30000, // 30 seconds
  })
}

/**
 * Hook to fetch a specific approval instance
 */
export const useApprovalInstance = (instanceId: string) => {
  return useQuery({
    queryKey: approvalQueryKeys.instance(instanceId),
    queryFn: () => ApprovalInstanceManager.getApprovalInstance(instanceId),
    enabled: !!instanceId,
  })
}

/**
 * Hook to fetch pending approvals for current user
 */
export const usePendingApprovals = () => {
  const { user, profile } = useAuth()

  return useQuery({
    queryKey: approvalQueryKeys.pendingForUser(user?.id || ''),
    queryFn: async () => {
      if (!user?.id || !profile?.org_id) return []
      
      return ApprovalInstanceManager.getPendingApprovalsForUser(user.id, profile.org_id)
    },
    enabled: !!user?.id && !!profile?.org_id,
    refetchInterval: 60000, // Refetch every minute
  })
}

/**
 * Hook to fetch approval statistics
 */
export const useApprovalStats = () => {
  const { user, profile } = useAuth()

  return useQuery({
    queryKey: approvalQueryKeys.stats(profile?.org_id || ''),
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('Organization ID required')
      
      return ApprovalInstanceManager.getApprovalStats(profile.org_id, user?.id)
    },
    enabled: !!profile?.org_id,
    staleTime: 60000, // 1 minute
  })
}

/**
 * Hook to submit document for approval
 */
export const useSubmitForApproval = () => {
  const queryClient = useQueryClient()
  const { user, profile } = useAuth()

  return useMutation({
    mutationFn: async ({
      documentType,
      documentId,
      documentAmount,
      currencyCode = 'UGX',
      metadata
    }: {
      documentType: DocumentType
      documentId: string
      documentAmount: number
      currencyCode?: string
      metadata?: Record<string, unknown>
    }) => {
      if (!user?.id || !profile?.org_id) {
        throw new Error('User authentication required')
      }

      return ApprovalEngine.submitForApproval(
        documentType,
        documentId,
        documentAmount,
        currencyCode,
        user.id,
        profile.org_id,
        metadata
      )
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.instances() })
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.stats(profile?.org_id || '') })
      toast.success('Document submitted for approval successfully')
    },
    onError: (error) => {
      console.error('Error submitting for approval:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to submit for approval')
    },
  })
}

/**
 * Hook to process approval action
 */
export const useApprovalAction = () => {
  const queryClient = useQueryClient()
  const { user, profile } = useAuth()

  return useMutation({
    mutationFn: async ({
      approvalInstanceId,
      actionRequest
    }: {
      approvalInstanceId: string
      actionRequest: ApprovalActionRequest
    }) => {
      if (!user?.id) throw new Error('User authentication required')

      return ApprovalActionHandler.processAction(
        approvalInstanceId,
        user.id,
        actionRequest
      )
    },
    onSuccess: (result, variables) => {
      if (result.success) {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: approvalQueryKeys.instance(variables.approvalInstanceId) })
        queryClient.invalidateQueries({ queryKey: approvalQueryKeys.instances() })
        queryClient.invalidateQueries({ queryKey: approvalQueryKeys.pendingForUser(user?.id || '') })
        queryClient.invalidateQueries({ queryKey: approvalQueryKeys.actions(variables.approvalInstanceId) })
        queryClient.invalidateQueries({ queryKey: approvalQueryKeys.stats(profile?.org_id || '') })

        toast.success(result.message)
      } else {
        toast.error(result.message)
      }
    },
    onError: (error) => {
      console.error('Error processing approval action:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to process approval action')
    },
  })
}

/**
 * Hook to process bulk approval actions
 */
export const useBulkApprovalAction = () => {
  const queryClient = useQueryClient()
  const { user, profile } = useAuth()

  return useMutation({
    mutationFn: async (bulkRequest: BulkApprovalRequest) => {
      if (!user?.id) throw new Error('User authentication required')

      return ApprovalActionHandler.processBulkActions(bulkRequest, user.id)
    },
    onSuccess: (result) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.instances() })
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.pendingForUser(user?.id || '') })
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.stats(profile?.org_id || '') })
      
      toast.success(`Bulk action completed: ${result.successful.length} successful, ${result.failed.length} failed`)
    },
    onError: (error) => {
      console.error('Error processing bulk approval action:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to process bulk approval action')
    },
  })
}

/**
 * Hook to fetch workflow templates
 */
export const useWorkflowTemplates = () => {
  const { profile } = useAuth()

  return useQuery({
    queryKey: approvalQueryKeys.templates(),
    queryFn: async () => {
      if (!profile?.org_id) throw new Error('Organization ID required')
      
      return WorkflowTemplateService.getWorkflowTemplates(profile.org_id)
    },
    enabled: !!profile?.org_id,
  })
}

/**
 * Hook to fetch a specific workflow template
 */
export const useWorkflowTemplate = (templateId: string) => {
  return useQuery({
    queryKey: approvalQueryKeys.template(templateId),
    queryFn: () => WorkflowTemplateService.getWorkflowTemplate(templateId),
    enabled: !!templateId,
  })
}

/**
 * Hook to create workflow template
 */
export const useCreateWorkflowTemplate = () => {
  const queryClient = useQueryClient()
  const { user, profile } = useAuth()

  return useMutation({
    mutationFn: async (payload: CreateWorkflowTemplatePayload) => {
      if (!user?.id || !profile?.org_id) {
        throw new Error('User authentication required')
      }

      return WorkflowTemplateService.createWorkflowTemplate(
        payload,
        profile.org_id,
        user.id
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.templates() })
      toast.success('Workflow template created successfully')
    },
    onError: (error) => {
      console.error('Error creating workflow template:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create workflow template')
    },
  })
}

/**
 * Hook to update workflow template
 */
export const useUpdateWorkflowTemplate = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      templateId,
      payload
    }: {
      templateId: string
      payload: Partial<CreateWorkflowTemplatePayload>
    }) => {
      return WorkflowTemplateService.updateWorkflowTemplate(templateId, payload)
    },
    onSuccess: (result, variables) => {
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.templates() })
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.template(variables.templateId) })
      toast.success('Workflow template updated successfully')
    },
    onError: (error) => {
      console.error('Error updating workflow template:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update workflow template')
    },
  })
}

/**
 * Hook to delete workflow template
 */
export const useDeleteWorkflowTemplate = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (templateId: string) => WorkflowTemplateService.deleteWorkflowTemplate(templateId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: approvalQueryKeys.templates() })
      toast.success('Workflow template deleted successfully')
    },
    onError: (error) => {
      console.error('Error deleting workflow template:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete workflow template')
    },
  })
}

/**
 * Hook to check approval authority
 */
export const useApprovalAuthority = (
  documentType: DocumentType,
  amount: number,
  currencyCode: string = 'UGX'
) => {
  const { user } = useAuth()

  return useQuery({
    queryKey: ['approval-authority', user?.id, documentType, amount, currencyCode],
    queryFn: async () => {
      if (!user?.id) throw new Error('User authentication required')
      
      return AuthorizationService.checkApprovalAuthority(
        user.id,
        documentType,
        amount,
        currencyCode
      )
    },
    enabled: !!user?.id && !!documentType && amount > 0,
    staleTime: 300000, // 5 minutes
  })
}

/**
 * Hook to fetch approval actions for an instance
 */
export const useApprovalActions = (instanceId: string) => {
  return useQuery({
    queryKey: approvalQueryKeys.actions(instanceId),
    queryFn: () => ApprovalActionHandler.getApprovalActions(instanceId),
    enabled: !!instanceId,
  })
}
