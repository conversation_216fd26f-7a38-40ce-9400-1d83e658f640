import { useState } from 'react'
import { X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { NOTIFICATION_CONFIG } from '@/types/notifications'
import type { NotificationFilters as FilterType } from '@/types/notifications'

interface NotificationFiltersProps {
  filters: FilterType
  onFiltersChange: (filters: FilterType) => void
}

export function NotificationFilters({ 
  filters, 
  onFiltersChange 
}: NotificationFiltersProps) {
  const [localFilters, setLocalFilters] = useState<FilterType>(filters)

  const handleFilterChange = (key: keyof FilterType, value: string | boolean | undefined) => {
    const newFilters = { ...localFilters, [key]: value }
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const clearFilter = (key: keyof FilterType) => {
    const newFilters = { ...localFilters }
    delete newFilters[key]
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const clearAllFilters = () => {
    setLocalFilters({})
    onFiltersChange({})
  }

  const activeFilterCount = Object.keys(localFilters).length

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Filters</Label>
        {activeFilterCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-6 text-xs"
          >
            Clear all
          </Button>
        )}
      </div>

      {/* Active filters */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-1">
          {localFilters.category && (
            <Badge variant="secondary" className="text-xs">
              Category: {NOTIFICATION_CONFIG.CATEGORIES[localFilters.category].label}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1"
                onClick={() => clearFilter('category')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {localFilters.priority && (
            <Badge variant="secondary" className="text-xs">
              Priority: {localFilters.priority}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1"
                onClick={() => clearFilter('priority')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {localFilters.entity_type && (
            <Badge variant="secondary" className="text-xs">
              Type: {localFilters.entity_type}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1"
                onClick={() => clearFilter('entity_type')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Filter controls */}
      <div className="grid grid-cols-2 gap-3">
        {/* Category filter */}
        <div className="space-y-1">
          <Label className="text-xs">Category</Label>
          <Select
            value={localFilters.category || 'all'}
            onValueChange={(value) =>
              handleFilterChange('category', value === 'all' ? undefined : value)
            }
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder="All categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All categories</SelectItem>
              {Object.entries(NOTIFICATION_CONFIG.CATEGORIES).map(([key, config]) => (
                <SelectItem key={key} value={key}>
                  {config.icon} {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Priority filter */}
        <div className="space-y-1">
          <Label className="text-xs">Priority</Label>
          <Select
            value={localFilters.priority || 'all'}
            onValueChange={(value) =>
              handleFilterChange('priority', value === 'all' ? undefined : value)
            }
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder="All priorities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All priorities</SelectItem>
              {Object.keys(NOTIFICATION_CONFIG.PRIORITIES).map((priority) => (
                <SelectItem key={priority} value={priority}>
                  <span className="capitalize">{priority}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Entity type filter */}
        <div className="space-y-1">
          <Label className="text-xs">Entity Type</Label>
          <Select
            value={localFilters.entity_type || 'all'}
            onValueChange={(value) =>
              handleFilterChange('entity_type', value === 'all' ? undefined : value)
            }
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All types</SelectItem>
              <SelectItem value="invoice">Invoice</SelectItem>
              <SelectItem value="bill">Bill</SelectItem>
              <SelectItem value="payment">Payment</SelectItem>
              <SelectItem value="customer">Customer</SelectItem>
              <SelectItem value="vendor">Vendor</SelectItem>
              <SelectItem value="account">Account</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Read status filter */}
        <div className="space-y-1">
          <Label className="text-xs">Status</Label>
          <Select
            value={
              localFilters.is_read === true ? 'read' :
              localFilters.is_read === false ? 'unread' : 'all'
            }
            onValueChange={(value) => {
              if (value === 'read') {
                handleFilterChange('is_read', true)
              } else if (value === 'unread') {
                handleFilterChange('is_read', false)
              } else {
                handleFilterChange('is_read', undefined)
              }
            }}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder="All status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All status</SelectItem>
              <SelectItem value="unread">Unread</SelectItem>
              <SelectItem value="read">Read</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}
