/**
 * Rich Email Notification Templates
 * HTML email templates with organization branding and customization
 */

import type { NotificationType, NotificationTemplateData } from '@/types/notifications'

// Email template configuration
export interface EmailTemplateConfig {
  subject: string
  preheader: string
  template: string
  variables: string[]
  category: string
}

// Base email template with organization branding
const BASE_EMAIL_TEMPLATE = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .content {
            padding: 30px;
        }
        .notification-card {
            background: #f8f9fa;
            border-left: 4px solid {{priority_color}};
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .notification-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }
        .notification-message {
            color: #4a5568;
            margin-bottom: 15px;
        }
        .notification-meta {
            font-size: 12px;
            color: #718096;
            border-top: 1px solid #e2e8f0;
            padding-top: 10px;
        }
        .action-button {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 15px 0;
        }
        .footer {
            background: #f7fafc;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #718096;
            border-top: 1px solid #e2e8f0;
        }
        .priority-urgent { border-left-color: #e53e3e; }
        .priority-high { border-left-color: #dd6b20; }
        .priority-normal { border-left-color: #3182ce; }
        .priority-low { border-left-color: #38a169; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{org_name}}</div>
            <div>{{org_tagline}}</div>
        </div>
        
        <div class="content">
            <h1>{{email_title}}</h1>
            <p>{{email_greeting}}</p>
            
            <div class="notification-card priority-{{priority}}">
                <div class="notification-title">{{title}}</div>
                <div class="notification-message">{{message}}</div>
                {{#if action_url}}
                <a href="{{action_url}}" class="action-button">{{action_text}}</a>
                {{/if}}
                <div class="notification-meta">
                    <strong>Category:</strong> {{category}} | 
                    <strong>Priority:</strong> {{priority}} | 
                    <strong>Time:</strong> {{created_at}}
                </div>
            </div>
            
            {{#if additional_content}}
            <div style="margin-top: 20px;">
                {{additional_content}}
            </div>
            {{/if}}
        </div>
        
        <div class="footer">
            <p>This notification was sent by {{org_name}}.</p>
            <p>
                <a href="{{unsubscribe_url}}">Unsubscribe</a> | 
                <a href="{{preferences_url}}">Manage Preferences</a> | 
                <a href="{{support_url}}">Support</a>
            </p>
        </div>
    </div>
</body>
</html>
`

// Email templates for different notification types
export const EMAIL_TEMPLATES: Record<NotificationType, EmailTemplateConfig> = {
  // Payment notifications
  payment_pending_approval: {
    subject: 'Payment Approval Required - {{amount}} to {{payee_name}}',
    preheader: 'A payment requires your approval',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['amount', 'payee_name', 'reason', 'payment_id'],
    category: 'approval'
  },
  
  payment_approved: {
    subject: 'Payment Approved - {{amount}} to {{payee_name}}',
    preheader: 'Your payment has been approved',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['amount', 'payee_name', 'payment_id'],
    category: 'financial'
  },
  
  payment_rejected: {
    subject: 'Payment Rejected - {{amount}} to {{payee_name}}',
    preheader: 'Your payment has been rejected',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['amount', 'payee_name', 'reason', 'payment_id'],
    category: 'financial'
  },

  // Invoice notifications
  invoice_overdue: {
    subject: 'Overdue Invoice - {{invoice_number}} ({{days_overdue}} days)',
    preheader: 'Invoice payment is overdue',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['invoice_number', 'customer_name', 'days_overdue', 'amount', 'invoice_id'],
    category: 'financial'
  },
  
  invoice_due_soon: {
    subject: 'Invoice Due Soon - {{invoice_number}}',
    preheader: 'Invoice payment is due in {{days_until_due}} days',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['invoice_number', 'customer_name', 'days_until_due', 'amount', 'invoice_id'],
    category: 'reminder'
  },
  
  invoice_paid: {
    subject: 'Invoice Paid - {{invoice_number}}',
    preheader: 'Invoice has been paid successfully',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['invoice_number', 'customer_name', 'amount', 'invoice_id'],
    category: 'financial'
  },

  // Bill notifications
  bill_due_soon: {
    subject: 'Bill Due Soon - {{bill_number}}',
    preheader: 'Bill payment is due in {{days_until_due}} days',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['bill_number', 'vendor_name', 'days_until_due', 'amount', 'bill_id'],
    category: 'reminder'
  },
  
  bill_overdue: {
    subject: 'Overdue Bill - {{bill_number}} ({{days_overdue}} days)',
    preheader: 'Bill payment is overdue',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['bill_number', 'vendor_name', 'days_overdue', 'amount', 'bill_id'],
    category: 'financial'
  },

  // Budget notifications
  budget_exceeded: {
    subject: 'Budget Exceeded - {{entity_name}}',
    preheader: 'Budget limit has been exceeded',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['entity_name', 'amount', 'limit', 'percentage'],
    category: 'financial'
  },
  
  budget_warning: {
    subject: 'Budget Warning - {{entity_name}} at {{percentage}}%',
    preheader: 'Approaching budget limit',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['entity_name', 'amount', 'limit', 'percentage'],
    category: 'financial'
  },

  // System notifications
  user_invited: {
    subject: 'Welcome to {{org_name}} - Account Invitation',
    preheader: 'You have been invited to join {{org_name}}',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['email', 'invited_by', 'invitation_url'],
    category: 'system'
  },
  
  backup_completed: {
    subject: 'System Backup Completed Successfully',
    preheader: 'Your data backup has been completed',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['details', 'timestamp', 'backup_size'],
    category: 'system'
  },
  
  backup_failed: {
    subject: 'System Backup Failed - Action Required',
    preheader: 'System backup encountered an error',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['details', 'timestamp', 'error_code'],
    category: 'system'
  },
  
  system_maintenance: {
    subject: 'Scheduled System Maintenance - {{maintenance_date}}',
    preheader: 'System maintenance notification',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['details', 'maintenance_date', 'duration'],
    category: 'system'
  },
  
  audit_alert: {
    subject: 'Security Audit Alert - Immediate Attention Required',
    preheader: 'Security audit has detected an issue',
    template: BASE_EMAIL_TEMPLATE,
    variables: ['details', 'severity', 'action_required'],
    category: 'security'
  }
}

/**
 * Email template renderer
 */
export class EmailTemplateRenderer {
  private orgConfig: {
    name: string
    tagline: string
    logo_url?: string
    primary_color?: string
    support_email?: string
    website_url?: string
  }

  constructor(orgConfig: Partial<{
    name: string
    tagline: string
    logo_url: string
    primary_color: string
    support_email: string
    website_url: string
  }>) {
    this.orgConfig = {
      name: orgConfig.name || 'Kaya Finance',
      tagline: orgConfig.tagline || 'Financial Management Made Simple',
      logo_url: orgConfig.logo_url,
      primary_color: orgConfig.primary_color || '#667eea',
      support_email: orgConfig.support_email || '<EMAIL>',
      website_url: orgConfig.website_url || 'https://kayafinance.com'
    }
  }

  /**
   * Render email template with data
   */
  renderTemplate(
    notificationType: NotificationType,
    templateData: NotificationTemplateData,
    userEmail: string,
    baseUrl: string = 'https://app.kayafinance.com'
  ): { subject: string; html: string; text: string } {
    const template = EMAIL_TEMPLATES[notificationType]
    if (!template) {
      throw new Error(`No email template found for notification type: ${notificationType}`)
    }

    // Prepare template variables
    const variables = {
      ...templateData,
      org_name: this.orgConfig.name,
      org_tagline: this.orgConfig.tagline,
      email_title: this.getEmailTitle(notificationType),
      email_greeting: this.getEmailGreeting(userEmail),
      priority_color: this.getPriorityColor(templateData.priority || 'normal'),
      action_url: this.getActionUrl(notificationType, templateData, baseUrl),
      action_text: this.getActionText(notificationType),
      unsubscribe_url: `${baseUrl}/notifications/unsubscribe?email=${encodeURIComponent(userEmail)}`,
      preferences_url: `${baseUrl}/notifications/preferences`,
      support_url: `mailto:${this.orgConfig.support_email}`,
      created_at: new Date().toLocaleString()
    }

    // Render subject
    const subject = this.replaceVariables(template.subject, variables)

    // Render HTML
    const html = this.replaceVariables(template.template, variables)

    // Generate text version
    const text = this.htmlToText(html)

    return { subject, html, text }
  }

  /**
   * Replace template variables
   */
  private replaceVariables(template: string, variables: Record<string, string | number | boolean | null | undefined>): string {
    let result = template

    // Replace simple variables {{variable}}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g')
      result = result.replace(regex, String(value || ''))
    })

    // Handle conditional blocks {{#if variable}}...{{/if}}
    result = result.replace(/{{#if\s+(\w+)}}(.*?){{\/if}}/gs, (match, variable, content) => {
      return variables[variable] ? content : ''
    })

    return result
  }

  /**
   * Get email title based on notification type
   */
  private getEmailTitle(type: NotificationType): string {
    const titles: Record<string, string> = {
      payment_pending_approval: 'Payment Approval Required',
      payment_approved: 'Payment Approved',
      payment_rejected: 'Payment Rejected',
      invoice_overdue: 'Invoice Overdue',
      invoice_due_soon: 'Invoice Due Soon',
      invoice_paid: 'Invoice Paid',
      bill_due_soon: 'Bill Due Soon',
      bill_overdue: 'Bill Overdue',
      budget_exceeded: 'Budget Exceeded',
      budget_warning: 'Budget Warning',
      user_invited: 'Account Invitation',
      backup_completed: 'Backup Completed',
      backup_failed: 'Backup Failed',
      system_maintenance: 'System Maintenance',
      audit_alert: 'Security Alert'
    }
    return titles[type] || 'Notification'
  }

  /**
   * Get personalized greeting
   */
  private getEmailGreeting(email: string): string {
    const name = email.split('@')[0]
    return `Hello ${name},`
  }

  /**
   * Get priority color
   */
  private getPriorityColor(priority: string): string {
    const colors: Record<string, string> = {
      urgent: '#e53e3e',
      high: '#dd6b20',
      normal: '#3182ce',
      low: '#38a169'
    }
    return colors[priority] || colors.normal
  }

  /**
   * Get action URL for notification type
   */
  private getActionUrl(
    type: NotificationType, 
    data: NotificationTemplateData, 
    baseUrl: string
  ): string | null {
    const urlMap: Record<string, string> = {
      payment_pending_approval: `/payments/${data.payment_id}`,
      invoice_overdue: `/invoices/${data.invoice_id}`,
      invoice_due_soon: `/invoices/${data.invoice_id}`,
      bill_due_soon: `/bills/${data.bill_id}`,
      bill_overdue: `/bills/${data.bill_id}`,
      user_invited: data.invitation_url as string
    }

    const path = urlMap[type]
    return path ? `${baseUrl}${path}` : null
  }

  /**
   * Get action button text
   */
  private getActionText(type: NotificationType): string {
    const textMap: Record<string, string> = {
      payment_pending_approval: 'Review Payment',
      invoice_overdue: 'View Invoice',
      invoice_due_soon: 'View Invoice',
      bill_due_soon: 'View Bill',
      bill_overdue: 'View Bill',
      user_invited: 'Accept Invitation'
    }
    return textMap[type] || 'View Details'
  }

  /**
   * Convert HTML to plain text
   */
  private htmlToText(html: string): string {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/\s+/g, ' ')
      .trim()
  }
}
