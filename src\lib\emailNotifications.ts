/**
 * Email Notification Service
 * Handles sending email notifications through Supabase Edge Functions
 */

import { supabase } from '@/lib/supabase'
import { logger } from '@/lib/logger'

export interface InvitationEmailData {
  inviteeEmail: string
  inviterName: string
  inviterEmail: string
  organizationName: string
  organizationId: string
  role: 'admin' | 'accountant'
  customMessage?: string
  inviteToken?: string
}

export interface EmailNotificationResult {
  success: boolean
  messageId?: string
  error?: string
}

/**
 * Send user invitation email
 */
export async function sendInvitationEmail(data: InvitationEmailData): Promise<EmailNotificationResult> {
  try {
    logger.info('Sending invitation email', {
      component: 'emailNotifications',
      action: 'sendInvitationEmail',
      metadata: {
        inviteeEmail: data.inviteeEmail,
        organizationName: data.organizationName,
        role: data.role
      }
    })

    // Generate invitation link
    const inviteLink = generateInvitationLink(data.inviteeEmail, data.inviteToken, data.organizationId)

    // Call Supabase Edge Function for email sending
    const { data: result, error } = await supabase.functions.invoke('send-email', {
      body: {
        type: 'user_invited',
        to: data.inviteeEmail,
        data: {
          email: data.inviteeEmail,
          invited_by: data.inviterName,
          inviter_email: data.inviterEmail,
          org_name: data.organizationName,
          org_id: data.organizationId,
          role: data.role,
          invitation_url: inviteLink,
          custom_message: data.customMessage
        }
      }
    })

    if (error) {
      logger.error('Failed to send invitation email', {
        component: 'emailNotifications',
        action: 'sendInvitationEmail',
        metadata: {
          error: error.message,
          inviteeEmail: data.inviteeEmail
        }
      })
      return { success: false, error: error.message }
    }

    logger.info('Invitation email sent successfully', {
      component: 'emailNotifications',
      action: 'sendInvitationEmail',
      metadata: {
        inviteeEmail: data.inviteeEmail,
        messageId: result?.messageId
      }
    })

    return { success: true, messageId: result?.messageId }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    logger.error('Unexpected error sending invitation email', {
      component: 'emailNotifications',
      action: 'sendInvitationEmail',
      metadata: {
        error: errorMessage,
        inviteeEmail: data.inviteeEmail
      }
    })
    return { success: false, error: errorMessage }
  }
}

/**
 * Send welcome email to new user
 */
export async function sendWelcomeEmail(
  userEmail: string,
  userName: string,
  organizationName: string
): Promise<EmailNotificationResult> {
  try {
    logger.info('Sending welcome email', {
      component: 'emailNotifications',
      action: 'sendWelcomeEmail',
      metadata: {
        userEmail,
        organizationName
      }
    })

    const loginLink = `${window.location.origin}/`

    const { data: result, error } = await supabase.functions.invoke('send-email', {
      body: {
        type: 'welcome',
        to: userEmail,
        data: {
          user_name: userName,
          org_name: organizationName,
          login_url: loginLink
        }
      }
    })

    if (error) {
      logger.error('Failed to send welcome email', {
        component: 'emailNotifications',
        action: 'sendWelcomeEmail',
        metadata: {
          error: error.message,
          userEmail
        }
      })
      return { success: false, error: error.message }
    }

    logger.info('Welcome email sent successfully', {
      component: 'emailNotifications',
      action: 'sendWelcomeEmail',
      metadata: {
        userEmail,
        messageId: result?.messageId
      }
    })

    return { success: true, messageId: result?.messageId }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    logger.error('Unexpected error sending welcome email', {
      component: 'emailNotifications',
      action: 'sendWelcomeEmail',
      metadata: {
        error: errorMessage,
        userEmail
      }
    })
    return { success: false, error: errorMessage }
  }
}

/**
 * Generate invitation link with token
 */
function generateInvitationLink(email: string, token?: string, orgId?: string): string {
  const baseUrl = window.location.origin
  const params = new URLSearchParams()
  
  params.append('email', email)
  if (token) params.append('token', token)
  if (orgId) params.append('org', orgId)
  
  return `${baseUrl}/signup?${params.toString()}`
}

/**
 * Validate email address format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(email: string): Promise<EmailNotificationResult> {
  try {
    logger.info('Sending password reset email', {
      component: 'emailNotifications',
      action: 'sendPasswordResetEmail',
      metadata: { email }
    })

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    })

    if (error) {
      logger.error('Failed to send password reset email', {
        component: 'emailNotifications',
        action: 'sendPasswordResetEmail',
        metadata: {
          error: error.message,
          email
        }
      })
      return { success: false, error: error.message }
    }

    logger.info('Password reset email sent successfully', {
      component: 'emailNotifications',
      action: 'sendPasswordResetEmail',
      metadata: { email }
    })

    return { success: true }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    logger.error('Unexpected error sending password reset email', {
      component: 'emailNotifications',
      action: 'sendPasswordResetEmail',
      metadata: {
        error: errorMessage,
        email
      }
    })
    return { success: false, error: errorMessage }
  }
}

/**
 * Test email configuration
 */
export async function testEmailConfiguration(): Promise<EmailNotificationResult> {
  try {
    const { data: result, error } = await supabase.functions.invoke('send-email', {
      body: {
        type: 'test',
        to: '<EMAIL>',
        data: {
          test_message: 'Email configuration test'
        }
      }
    })

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true, messageId: result?.messageId }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return { success: false, error: errorMessage }
  }
}

/**
 * Batch send emails (for multiple invitations)
 */
export async function sendBatchInvitations(
  invitations: InvitationEmailData[]
): Promise<{ success: number; failed: number; results: EmailNotificationResult[] }> {
  const results: EmailNotificationResult[] = []
  let success = 0
  let failed = 0

  for (const invitation of invitations) {
    const result = await sendInvitationEmail(invitation)
    results.push(result)
    
    if (result.success) {
      success++
    } else {
      failed++
    }

    // Add small delay between emails to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  logger.info('Batch invitation emails completed', {
    component: 'emailNotifications',
    action: 'sendBatchInvitations',
    metadata: {
      total: invitations.length,
      success,
      failed
    }
  })

  return { success, failed, results }
}
