/**
 * Global Teardown for E2E Tests
 * Cleans up test environment and data
 */

import { chromium, FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test environment cleanup...')

  const { baseURL } = config.projects[0].use
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // Navigate to the application
    await page.goto(baseURL || 'http://localhost:3000')

    // Clean up test data
    await cleanupTestData(page)

    // Clean up authentication
    await cleanupTestAuthentication(page)

    // Clean up browser storage
    await cleanupBrowserStorage(page)

    console.log('✅ E2E test environment cleanup complete')
  } catch (error) {
    console.error('❌ E2E test environment cleanup failed:', error)
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await browser.close()
  }
}

async function cleanupTestData(page: import('playwright').Page) {
  console.log('🗑️ Cleaning up test data...')

  // Remove test data from localStorage
  await page.evaluate(() => {
    localStorage.removeItem('e2e-test-org')
    localStorage.removeItem('e2e-test-user')
    localStorage.removeItem('e2e-test-notifications')
    localStorage.removeItem('e2e-test-integrations')
    localStorage.removeItem('e2e-test-preferences')
  })

  console.log('✅ Test data cleanup complete')
}

async function cleanupTestAuthentication(page: import('playwright').Page) {
  console.log('🔓 Cleaning up test authentication...')

  // Remove auth tokens
  await page.evaluate(() => {
    localStorage.removeItem('supabase.auth.token')
    sessionStorage.removeItem('supabase.auth.token')
    localStorage.removeItem('auth-storage')
    sessionStorage.removeItem('auth-storage')
  })

  console.log('✅ Test authentication cleanup complete')
}

async function cleanupBrowserStorage(page: import('playwright').Page) {
  console.log('💾 Cleaning up browser storage...')

  // Clear all localStorage and sessionStorage
  await page.evaluate(() => {
    // Clear notification-related storage
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (
        key.includes('notification') ||
        key.includes('test') ||
        key.includes('e2e') ||
        key.includes('push-subscription') ||
        key.includes('integration')
      )) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      sessionStorage.removeItem(key)
    })
  })

  // Clear cookies related to testing
  const context = page.context()
  await context.clearCookies()

  console.log('✅ Browser storage cleanup complete')
}

export default globalTeardown
