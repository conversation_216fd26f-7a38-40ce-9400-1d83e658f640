# Budget Functionality Test Plan

## ✅ Migration Verification

The database migration has been successfully completed:

1. **✅ Database Schema Updated**
   - Added `account_id` column to `budgets` table
   - Added foreign key constraint to `accounts` table
   - Added performance index
   - Migrated existing data from budget lines

2. **✅ Application Build**
   - All TypeScript compilation successful
   - No build errors
   - Development server running on http://localhost:8082

## 🧪 Manual Testing Checklist

### Budget Creation Form
- [ ] Navigate to Budgets page
- [ ] Click "Create Budget" button
- [ ] Verify account selection is present next to "Add Line" button
- [ ] Verify account selection is required
- [ ] Add budget line items with: item name, amount, notes
- [ ] Verify no account selection per line item
- [ ] Save budget and verify it appears in the table

### Budget Display
- [ ] Verify budgets table shows Account column
- [ ] Verify account information displays correctly (code - name)
- [ ] Click on a budget to view details
- [ ] Verify budget approval dialog shows account information
- [ ] Verify budget lines display simplified structure (item, amount)

### Budget Editing
- [ ] Edit an existing budget
- [ ] Verify account can be changed at budget level
- [ ] Verify line items maintain simplified structure
- [ ] Save changes and verify updates

### Data Migration Verification
- [ ] Check existing budgets have account information
- [ ] Verify backward compatibility with old budget data
- [ ] Ensure no data loss during migration

## 🔍 Database Verification Queries

Run these in Supabase SQL Editor to verify migration:

```sql
-- Check budget accounts
SELECT 
    b.name as budget_name,
    a.code as account_code,
    a.name as account_name,
    COUNT(bl.id) as line_count
FROM budgets b
LEFT JOIN accounts a ON b.account_id = a.id
LEFT JOIN budget_lines bl ON bl.budget_id = b.id
GROUP BY b.id, b.name, a.code, a.name
ORDER BY b.created_at DESC;

-- Check budget lines structure
SELECT 
    b.name as budget_name,
    bl.notes as item_description,
    bl.amount,
    a.code as account_code
FROM budget_lines bl
JOIN budgets b ON bl.budget_id = b.id
JOIN accounts a ON bl.account_id = a.id
ORDER BY b.created_at DESC, bl.created_at;
```

## 🎯 Expected Results

1. **Form Behavior**: Account selection at form level, simplified line items
2. **Data Structure**: All budget lines inherit budget's account_id
3. **Display**: Account information prominently shown in tables and dialogs
4. **Compatibility**: Existing budgets work with new structure
5. **User Experience**: Streamlined data entry following invoice/bill pattern

## 🚀 Success Criteria

- ✅ Database migration completed without errors
- ✅ Application builds and runs successfully
- ✅ New budget form follows invoice/bill pattern
- ✅ Existing budgets display correctly
- ✅ Account information is properly associated
- ✅ Line items are simplified (item, amount, notes)
- ✅ No data loss or corruption

## 📋 Next Steps

1. Test the application manually using the checklist above
2. Create a new budget to verify the new form works
3. Edit an existing budget to verify compatibility
4. Verify reporting and approval workflows still function
5. Consider removing `account_id` from `budget_lines` in future migration once fully tested
