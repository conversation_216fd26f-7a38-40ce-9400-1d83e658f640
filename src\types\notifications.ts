import type { Notification, NotificationPreference, NotificationTemplate } from './database'

// Notification types
export type NotificationType = 
  | 'payment_pending_approval'
  | 'payment_approved' 
  | 'payment_rejected'
  | 'invoice_overdue'
  | 'invoice_due_soon'
  | 'invoice_paid'
  | 'bill_due_soon'
  | 'bill_overdue'
  | 'user_invited'
  | 'backup_completed'
  | 'backup_failed'
  | 'budget_exceeded'
  | 'budget_warning'
  | 'audit_alert'
  | 'system_maintenance'

// Notification categories
export type NotificationCategory = 
  | 'financial'
  | 'system' 
  | 'approval'
  | 'reminder'
  | 'security'

// Notification priorities
export type NotificationPriority = 
  | 'low'
  | 'normal'
  | 'high'
  | 'urgent'

// Extended notification interface with computed properties
export interface NotificationWithMeta extends Notification {
  isExpired?: boolean
  timeAgo?: string
  entityDisplayName?: string
  actionUrl?: string
}

// Notification creation payload
export interface CreateNotificationPayload {
  org_id: string
  user_id?: string | null
  type: NotificationType
  category: NotificationCategory
  title: string
  message: string
  data?: Record<string, unknown>
  entity_type?: string
  entity_id?: string
  priority?: NotificationPriority
  expires_at?: string
}

// Notification update payload
export interface UpdateNotificationPayload {
  is_read?: boolean
  is_archived?: boolean
  read_at?: string
}

// Notification filter options
export interface NotificationFilters {
  category?: NotificationCategory
  priority?: NotificationPriority
  is_read?: boolean
  is_archived?: boolean
  entity_type?: string
  date_from?: string
  date_to?: string
}

// Notification preferences payload
export interface NotificationPreferencePayload {
  user_id: string
  notification_type: NotificationType
  enabled?: boolean
  email_enabled?: boolean
  in_app_enabled?: boolean
}

// Notification template data for rendering
export interface NotificationTemplateData {
  // Payment data
  amount?: string
  payee_name?: string
  reason?: string
  
  // Invoice data
  invoice_number?: string
  customer_name?: string
  days_overdue?: number
  days_until_due?: number
  
  // Bill data
  bill_number?: string
  vendor_name?: string
  
  // User data
  email?: string
  user_name?: string
  
  // Generic data
  entity_name?: string
  entity_id?: string
  [key: string]: unknown
}

// Notification statistics
export interface NotificationStats {
  total: number
  unread: number
  by_category: Record<NotificationCategory, number>
  by_priority: Record<NotificationPriority, number>
}

// Notification configuration
export const NOTIFICATION_CONFIG = {
  PRIORITIES: {
    low: {
      color: 'text-gray-500',
      bgColor: 'bg-gray-100',
      icon: '●'
    },
    normal: {
      color: 'text-blue-500',
      bgColor: 'bg-blue-100',
      icon: '●'
    },
    high: {
      color: 'text-orange-500',
      bgColor: 'bg-orange-100',
      icon: '●'
    },
    urgent: {
      color: 'text-red-500',
      bgColor: 'bg-red-100',
      icon: '●'
    }
  },
  CATEGORIES: {
    financial: {
      label: 'Financial',
      color: 'text-green-600',
      icon: '💰'
    },
    system: {
      label: 'System',
      color: 'text-blue-600',
      icon: '⚙️'
    },
    approval: {
      label: 'Approval',
      color: 'text-purple-600',
      icon: '✅'
    },
    reminder: {
      label: 'Reminder',
      color: 'text-yellow-600',
      icon: '⏰'
    },
    security: {
      label: 'Security',
      color: 'text-red-600',
      icon: '🔒'
    }
  },
  MAX_NOTIFICATIONS_DISPLAY: 50,
  POLLING_INTERVAL: 30000, // 30 seconds
  AUTO_MARK_READ_DELAY: 3000 // 3 seconds
} as const

// Helper functions
export const getNotificationPriorityConfig = (priority: NotificationPriority) => {
  return NOTIFICATION_CONFIG.PRIORITIES[priority]
}

export const getNotificationCategoryConfig = (category: NotificationCategory) => {
  return NOTIFICATION_CONFIG.CATEGORIES[category]
}

export const isNotificationExpired = (notification: Notification): boolean => {
  if (!notification.expires_at) return false
  return new Date(notification.expires_at) < new Date()
}

export const getNotificationTimeAgo = (createdAt: string): string => {
  const now = new Date()
  const created = new Date(createdAt)
  const diffInMinutes = Math.floor((now.getTime() - created.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  
  const diffInWeeks = Math.floor(diffInDays / 7)
  return `${diffInWeeks}w ago`
}

// Entity URL mapping for navigation
export const getEntityUrl = (entityType?: string, entityId?: string): string | null => {
  if (!entityType || !entityId) return null
  
  const urlMap: Record<string, string> = {
    invoice: `/invoices`,
    bill: `/bills`,
    payment: `/payments`,
    customer: `/customers`,
    vendor: `/vendors`,
    account: `/accounts`,
    journal_entry: `/journal-entries`
  }
  
  return urlMap[entityType] || null
}
