import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Package, 
  Search, 
  X,
  Tag,
  DollarSign
} from 'lucide-react'
import { ProductSearch } from './ProductSearch'
import type { ProductOption } from '@/types/inventory'

interface ProductSelectorProps {
  selectedProduct?: ProductOption | null
  onProductSelect: (product: ProductOption | null) => void
  placeholder?: string
  disabled?: boolean
  includeInactive?: boolean
  showStockInfo?: boolean
  trigger?: React.ReactNode
  className?: string
}

export function ProductSelector({
  selectedProduct,
  onProductSelect,
  placeholder = "Select a product",
  disabled = false,
  includeInactive = false,
  showStockInfo = true,
  trigger,
  className = ""
}: ProductSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleProductSelect = (product: ProductOption) => {
    onProductSelect(product)
    setIsOpen(false)
  }

  const handleClearSelection = (e: React.MouseEvent) => {
    e.stopPropagation()
    onProductSelect(null)
  }

  const defaultTrigger = (
    <Button
      variant="outline"
      className={`w-full justify-start text-left font-normal ${className}`}
      disabled={disabled}
    >
      {selectedProduct ? (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <Package className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <div className="truncate font-medium">
                {selectedProduct.name}
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <span className="font-mono">{selectedProduct.sku}</span>
                <span>•</span>
                <span>${selectedProduct.selling_price.toFixed(2)}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            {!selectedProduct.is_active && (
              <Badge variant="secondary" className="text-xs">
                Inactive
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-red-100"
              onClick={handleClearSelection}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex items-center gap-2 text-gray-500">
          <Search className="h-4 w-4" />
          <span>{placeholder}</span>
        </div>
      )}
    </Button>
  )

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Select Product</DialogTitle>
          <DialogDescription>
            Search and select a product from your inventory
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <ProductSearch
            onProductSelect={handleProductSelect}
            placeholder="Search products by name, SKU, or barcode..."
            includeInactive={includeInactive}
            showStockInfo={showStockInfo}
            selectedProductId={selectedProduct?.id}
          />
          
          {selectedProduct && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-medium text-blue-900 mb-2">
                    Selected Product
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">{selectedProduct.name}</span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Tag className="h-3 w-3 text-gray-500" />
                        <span className="text-gray-600">SKU:</span>
                        <span className="font-mono">{selectedProduct.sku}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-3 w-3 text-gray-500" />
                        <span className="text-gray-600">Price:</span>
                        <span className="font-medium">
                          ${selectedProduct.selling_price.toFixed(2)}
                        </span>
                      </div>
                    </div>

                    {selectedProduct.description && (
                      <p className="text-sm text-gray-600 mt-2">
                        {selectedProduct.description}
                      </p>
                    )}

                    <div className="flex items-center gap-2 mt-2">
                      {selectedProduct.category && (
                        <Badge variant="outline" className="text-xs">
                          {selectedProduct.category.name}
                        </Badge>
                      )}
                      
                      <Badge variant="outline" className="text-xs">
                        {selectedProduct.unit_of_measure}
                      </Badge>

                      {!selectedProduct.is_active && (
                        <Badge variant="secondary" className="text-xs">
                          Inactive
                        </Badge>
                      )}

                      {!selectedProduct.is_sellable && (
                        <Badge variant="outline" className="text-xs text-orange-600">
                          Not Sellable
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onProductSelect(null)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Quick inline product selector for forms
interface QuickProductSelectorProps {
  selectedProduct?: ProductOption | null
  onProductSelect: (product: ProductOption | null) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function QuickProductSelector({
  selectedProduct,
  onProductSelect,
  placeholder = "Search products...",
  disabled = false,
  className = ""
}: QuickProductSelectorProps) {
  return (
    <div className={className}>
      {selectedProduct ? (
        <div className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <Package className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <div className="truncate font-medium text-sm">
                {selectedProduct.name}
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <span className="font-mono">{selectedProduct.sku}</span>
                <span>•</span>
                <span>${selectedProduct.selling_price.toFixed(2)}</span>
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-red-100"
            onClick={() => onProductSelect(null)}
            disabled={disabled}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      ) : (
        <ProductSearch
          onProductSelect={onProductSelect}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full"
        />
      )}
    </div>
  )
}
